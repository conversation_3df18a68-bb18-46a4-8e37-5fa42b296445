FROM registry.cn-beijing.aliyuncs.com/zksh/docker-webman-8.1:latest

# 环境变量添加
ARG DEBUG

ARG DB_HOST
ARG DB_PORT
ARG DB_NAME
ARG DB_USER
ARG DB_PASSWORD

ARG REDIS_HOSTNAME
ARG PORT
ARG REDIS_PASSWORD
ARG SELECT

ARG APP_NAME

ENV DEBUG=$DEBUG
ENV DB_HOST=$DB_HOST
ENV DB_PORT=$DB_PORT
ENV DB_NAME=$DB_NAME
ENV DB_USER=$DB_USER
ENV DB_PASSWORD=$DB_PASSWORD
ENV REDIS_HOSTNAME=$REDIS_HOSTNAME
ENV PORT=$PORT
ENV REDIS_PASSWORD=$REDIS_PASSWORD
ENV SELECT=$SELECT
ENV APP_NAME=$APP_NAME

# 复制项目代码
COPY . /app
