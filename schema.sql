/*
 Navicat Premium Dump SQL

 Source Server         : 腾讯云2核4G
 Source Server Type    : MySQL
 Source Server Version : 50718 (5.7.18-txsql-log)
 Source Host           : bj-cdb-r63onvls.sql.tencentcdb.com:29299
 Source Schema         : pension_admin

 Target Server Type    : MySQL
 Target Server Version : 50718 (5.7.18-txsql-log)
 File Encoding         : 65001

 Date: 23/07/2025 14:16:27
*/

SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for company_account
-- ----------------------------
DROP TABLE IF EXISTS `company_account`;
CREATE TABLE `company_account`
(
    `company_account_id`     bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '账户编号',
    `company_account_type`   tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '账户类型(1:工资账户)',
    `company_account_name`   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '默认余额账户' COMMENT '账户名称',
    `company_account_status` tinyint(4) NULL DEFAULT 1 COMMENT '账户状态（1:正常、2:关闭）',
    `company_cid`            bigint(20) UNSIGNED NOT NULL COMMENT '机构编号',
    `company_balance`        decimal(10, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '账户余额',
    `created_by`             bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`             bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`            datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`            datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`company_account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构账户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_depart
-- ----------------------------
DROP TABLE IF EXISTS `company_depart`;
CREATE TABLE `company_depart`
(
    `company_cid`       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '机构ID',
    `company_name`      varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构名称',
    `company_parent_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '父级ID',
    `company_label`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司标签',
    `sort`              int(10) UNSIGNED NULL DEFAULT 100 COMMENT '排序',
    `created_by`        int(11) NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`        int(11) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`       datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`company_cid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公司机构表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_expense_reimbursement
-- ----------------------------
DROP TABLE IF EXISTS `company_expense_reimbursement`;
CREATE TABLE `company_expense_reimbursement`
(
    `id`                 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报销申请id',
    `e_invoice_image`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电子发票图片(多图)',
    `payment_screenshot` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '付款截图',
    `invoice_amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '报销金额',
    `status`             tinyint(4) NOT NULL DEFAULT 0 COMMENT '报销状态(0:待审核、1:审核驳回、2:审核通过、3:已报销、-1:已取消)',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `employee_id`        bigint(20) NOT NULL COMMENT '老人id',
    `bank_card_id`       bigint(20) NOT NULL COMMENT '银行卡id',
    `applicant_id`       bigint(20) NOT NULL COMMENT '申请人id',
    `reviewer_id`        bigint(20) NULL DEFAULT NULL COMMENT '审核人员id',
    `review_time`        datetime NULL DEFAULT NULL COMMENT '审核时间',
    `review_remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
    `created_by`         int(11) NULL DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime NULL DEFAULT NULL COMMENT '创建时间',
    `updated_by`         int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`        datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '报销申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_financial_records
-- ----------------------------
DROP TABLE IF EXISTS `company_financial_records`;
CREATE TABLE `company_financial_records`
(
    `id`                        bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '收支记录id',
    `transaction_type`          tinyint(4) NOT NULL DEFAULT 1 COMMENT '收支类型(1:收入、2:支出)',
    `transaction_category`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收支账目分类',
    `payment_id`                bigint(20) NULL DEFAULT NULL COMMENT '缴费id',
    `transaction_amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '收支金额',
    `transaction_serial number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流水单号',
    `remark`                    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`                int(11) NOT NULL COMMENT '创建人',
    `create_time`               datetime                                                      NOT NULL COMMENT '创建时间',
    `updated_by`                int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`               datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`               datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '收支记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_invoice_applications
-- ----------------------------
DROP TABLE IF EXISTS `company_invoice_applications`;
CREATE TABLE `company_invoice_applications`
(
    `id`                             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '发票申请id',
    `invoice_title`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票抬头',
    `taxpayer_identification_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纳税人识别号',
    `invoice_name`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
    `invoice_email`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
    `status`                         tinyint(4) NOT NULL DEFAULT 0 COMMENT '申请状态(0:待开票、1:申请作废、2:已开票、-1:开票撤回、-2:已作废)',
    `amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '发票金额',
    `applicant_id`                   bigint(20) NOT NULL COMMENT '申请人id',
    `created_by`                     int(11) NULL DEFAULT NULL COMMENT '创建人',
    `create_time`                    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `updated_by`                     int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`                    datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                    datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发票申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_management_orders
-- ----------------------------
DROP TABLE IF EXISTS `company_management_orders`;
CREATE TABLE `company_management_orders`
(
    `id`                bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '场地管理订单id',
    `usage_time`        datetime NOT NULL COMMENT '使用时间',
    `usage_duration`    float    NOT NULL COMMENT '使用时长',
    `usable_area`       float    NOT NULL DEFAULT 0 COMMENT '使用面积',
    `usage_purpose`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用用途',
    `usage_amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '使用金额',
    `transaction_proof` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易凭证图片',
    `status`            tinyint(4) NOT NULL DEFAULT 0 COMMENT '订单状态(0:待支付、1:待使用、2:已使用、-1:已取消)',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `applicant_id`      bigint(20) NULL DEFAULT NULL COMMENT '申请人id',
    `created_by`        int(11) NOT NULL COMMENT '创建人',
    `create_time`       datetime NOT NULL COMMENT '创建时间',
    `updated_by`        int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '场地管理订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_purchase_applications
-- ----------------------------
DROP TABLE IF EXISTS `company_purchase_applications`;
CREATE TABLE `company_purchase_applications`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '采购申请id',
    `applicant_id`  bigint(20) NOT NULL COMMENT '申请人id',
    `name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请名称',
    `time`          datetime NULL DEFAULT NULL COMMENT '采购时间',
    `amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '总金额',
    `status`        tinyint(4) NOT NULL DEFAULT 0 COMMENT '申请状态(0:待审核、2:审核通过、2:采购中、3:采购完成、-1:已取消、-2:审核驳回)',
    `remark`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `reviewer_id`   bigint(20) NULL DEFAULT NULL COMMENT '审核人员id',
    `review_time`   datetime NULL DEFAULT NULL COMMENT '审核时间',
    `review_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
    `created_by`    int(11) NULL DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime NULL DEFAULT NULL COMMENT '创建时间',
    `updated_by`    int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`   datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`   datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_purchase_items
-- ----------------------------
DROP TABLE IF EXISTS `company_purchase_items`;
CREATE TABLE `company_purchase_items`
(
    `id`                      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '采购清单id',
    `purchase_application_id` bigint(20) NOT NULL COMMENT '采购申请id',
    `purchase_merchants`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购商家',
    `purchase_name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '采购商品名称',
    `purchase_price` double(10, 0) NOT NULL DEFAULT 0 COMMENT '采购单价',
    `purchase_quantity` double(10, 0) NOT NULL DEFAULT 0 COMMENT '采购数量',
    `shipping_cost` double(10, 0) NOT NULL DEFAULT 0 COMMENT '采购运费',
    `purchase_amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '采购金额',
    `remark`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `applicant_id`            bigint(20) NOT NULL COMMENT '申请人id',
    `created_by`              int(11) NOT NULL COMMENT '创建人',
    `create_time`             datetime                                                      NOT NULL COMMENT '创建时间',
    `updated_by`              int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购清单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_refund_applications
-- ----------------------------
DROP TABLE IF EXISTS `company_refund_applications`;
CREATE TABLE `company_refund_applications`
(
    `id`                 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '退款申请id',
    `elderly_id`         bigint(20) NOT NULL COMMENT '老人id',
    `refund_method`      tinyint(4) NOT NULL DEFAULT 0 COMMENT '退款方式(1:余额清零、2:小程序退款)',
    `bank_card_id`       bigint(20) NOT NULL COMMENT '银行卡id',
    `transaction_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流水单号',
    `usage_amount` double(10, 0) NOT NULL DEFAULT 0 COMMENT '退款金额',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `status`             tinyint(4) NOT NULL DEFAULT 0 COMMENT '申请状态(0:待审核、1:审核驳回、2:审核通过、3:已退款、-1:已取消)',
    `applicant_id`       bigint(20) NOT NULL COMMENT '申请人id',
    `reviewer_id`        bigint(20) NULL DEFAULT NULL COMMENT '审核人员id',
    `review_time`        datetime NULL DEFAULT NULL COMMENT '审核时间',
    `review_remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
    `created_by`         int(11) NOT NULL COMMENT '创建人',
    `create_time`        datetime NOT NULL COMMENT '创建时间',
    `updated_by`         int(11) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`        datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款申请表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for company_transaction
-- ----------------------------
DROP TABLE IF EXISTS `company_transaction`;
CREATE TABLE `company_transaction`
(
    `company_transaction_id`   int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `company_account_id`       bigint(20) NOT NULL COMMENT '交易账户Id',
    `company_cid`              bigint(20) UNSIGNED NOT NULL COMMENT '机构编号',
    `company_transaction_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '交易类型(1:收入、2:提现)',
    `company_amount`           decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额',
    `company_transaction_time` datetime       NOT NULL COMMENT '交易时间',
    `company_balance_after`    decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易后账户金额',
    `company_note`             varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `company_info`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '交易信息(json数组格式、name:交易名称、link_type:关联类型、link_id:关联Id）',
    PRIMARY KEY (`company_transaction_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '机构交易表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for contract_management
-- ----------------------------
DROP TABLE IF EXISTS `contract_management`;
CREATE TABLE `contract_management`
(
    `contract_id`                    bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '合同ID',
    `cust_contract_classification`   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同分类',
    `contract_number`                varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '合同编号',
    `haidian_district_dystem_number` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '海淀区系统编号',
    `order_id`                       bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '主订单ID',
    `cust_uid`                       bigint(20) UNSIGNED NOT NULL COMMENT '客户编号',
    `personnel_type`                 tinyint(4) NULL DEFAULT 1 COMMENT '人员类型',
    `service_type`                   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '按需上门服务' COMMENT '服务类型',
    `contract_type`                  bigint(20) NULL DEFAULT NULL COMMENT '合同服务项目',
    `service_address`                varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务地址',
    `customer_phone`                 varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户联系电话',
    `customer_note`                  varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户备注',
    `custom_terms`                   varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义条款',
    `order_no`                       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方订单编号',
    `customer_service_fee`           decimal(8, 2) NULL DEFAULT 0.00 COMMENT '平台服务费/客户服务费',
    `employee_uid`                   bigint(20) NULL DEFAULT NULL COMMENT '服务人员',
    `employee_salary`                decimal(8, 2) NULL DEFAULT 0.00 COMMENT '服务员工资',
    `contract_period_start`          date NULL DEFAULT NULL COMMENT '合同期限开始时间',
    `contract_period_end`            date NULL DEFAULT NULL COMMENT '合同期限结束时间',
    `cust_contract_amount`           varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0.00' COMMENT '合同金额',
    `contract_status`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '进行中' COMMENT '合同状态',
    `contract_documents`             varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同文件',
    `order_source`                   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单来源',
    `working_days`                   int(10) UNSIGNED NULL DEFAULT 0 COMMENT '月工作天数',
    `working_hour`                   int(10) UNSIGNED NULL DEFAULT 0 COMMENT '日工作小时',
    `employee_service_fee`           decimal(8, 2) NULL DEFAULT 0.00 COMMENT '服务员服务费/小时工单价',
    `value_added_expenses`           decimal(8, 2) NULL DEFAULT 0.00 COMMENT '增值服务费',
    `salary_payment_date`            int(11) NULL DEFAULT 5 COMMENT '工资发放日',
    `accommodation`                  varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住宿',
    `expected_delivery_date`         date NULL DEFAULT NULL COMMENT '预产期',
    `agreed_deposit`                 decimal(8, 2) NULL DEFAULT 0.00 COMMENT '约定定金',
    `agreed_final_payment`           decimal(8, 2) NULL DEFAULT 0.00 COMMENT '约定尾款',
    `sales_attribution_uid`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '派工人员/销售归属',
    `after_sales_uid`                varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后归属',
    `cust_contract_service_site`     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属机构',
    `created_by`                     bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                     bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                    datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                    datetime NULL DEFAULT NULL COMMENT '删除时间',
    `contract_renewal`               tinyint(5) NULL DEFAULT 0 COMMENT '是否续签合同(0或1)',
    `contract_documents2`            varchar(640) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同文件(多图)',
    `not_renewing_reason`            varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '不续签原因',
    PRIMARY KEY (`contract_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5840 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_account
-- ----------------------------
DROP TABLE IF EXISTS `cust_account`;
CREATE TABLE `cust_account`
(
    `cust_account_id`     bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '账户编号',
    `cust_account_type`   tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '账户类型(1:余额账户、2:补贴账户)',
    `cust_account_name`   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '默认余额账户' COMMENT '账户名称',
    `cust_account_status` tinyint(4) NULL DEFAULT 1 COMMENT '账户状态（1:正常、2:关闭）',
    `cust_uid`            bigint(20) UNSIGNED NOT NULL COMMENT '客户id',
    `cust_balance`        decimal(10, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '账户余额',
    `created_by`          bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`          bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`         datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`         datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '被照护人账户主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_balance
-- ----------------------------
DROP TABLE IF EXISTS `cust_balance`;
CREATE TABLE `cust_balance`
(
    `balance_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '客户余额id',
    `balance_name`    varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '余额账户' COMMENT '账户名称',
    `cust_uid`        bigint(20) UNSIGNED NOT NULL COMMENT '客户id',
    `now_amount`      decimal(11, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '当前余额',
    `recharge_amount` decimal(11, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '累计充值金额',
    `spending_amount` decimal(11, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '累计消费金额',
    `created_by`      bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`      bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`     datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`     datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`balance_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5527 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户余额表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_balance_record
-- ----------------------------
DROP TABLE IF EXISTS `cust_balance_record`;
CREATE TABLE `cust_balance_record`
(
    `balance_record_id`       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '余额记录id',
    `balance_id`              bigint(20) UNSIGNED NOT NULL COMMENT '客户余额id',
    `cust_uid`                bigint(20) UNSIGNED NOT NULL COMMENT '客户id',
    `link_id`                 varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '关联id',
    `pm`                      tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
    `usage_date`              date NULL DEFAULT NULL COMMENT '使用日期',
    `record_title`            varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '记录标题',
    `record_details_category` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '明细种类',
    `record_details_type`     varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '明细类型0套餐消费、1每月账户结余转移、2缴费、3服务订单消费、4运营补贴、5缴费作废',
    `subsidy_id`              bigint(20) NULL DEFAULT NULL COMMENT '补贴id',
    `subsidy_money`           decimal(8, 2) NULL DEFAULT 0.00 COMMENT '补贴金额',
    `number`                  decimal(8, 2)                                          NOT NULL DEFAULT 0.00 COMMENT '明细数字',
    `forward`                 decimal(11, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '修改前金额',
    `balance`                 decimal(11, 2)                                         NOT NULL DEFAULT 0.00 COMMENT '修改后金额',
    `record_mark`             varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '无' COMMENT '记录备注',
    `record_status`           tinyint(1) NOT NULL DEFAULT 1 COMMENT '记录状态(0 = 待确定 1 = 有效 -1 = 无效)',
    `created_by`              bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`              bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`             datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`balance_record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 237877 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户余额记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_bill
-- ----------------------------
DROP TABLE IF EXISTS `cust_bill`;
CREATE TABLE `cust_bill`
(
    `payment_id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '缴费纪录id',
    `cust_uid`               bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '客户id',
    `balance_id`             bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '客户余额id',
    `order_id`               bigint(20) NULL DEFAULT NULL COMMENT '订单Id',
    `order_type`             varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单类型',
    `payment_voucher_number` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费凭证单号',
    `payment_voucher_image`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费凭证图片',
    `service_type`           varchar(666) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '营养餐' COMMENT '服务项目',
    `payment_type`           varchar(666) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '市场化' COMMENT '收入类型',
    `payment_remark`         varchar(888) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费备注',
    `amount_due`             decimal(10, 2) NULL DEFAULT 0.00 COMMENT '应缴费金额',
    `amount_paid`            decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际缴费金额',
    `payment_method`         varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费方式',
    `payment_time`           date NULL DEFAULT NULL COMMENT '缴费时间',
    `payment_status`         varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待审核' COMMENT '缴费状态',
    `service_remark`         varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作废备注',
    `service_month`          text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务月份',
    `auditor_id`             bigint(20) NULL DEFAULT NULL COMMENT '审核员',
    `auditor_time`           datetime NULL DEFAULT NULL COMMENT '审核时间',
    `auditor_remark`         varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人员备注',
    `created_by`             bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`             bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`            datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`            datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`payment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35819 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '缴费记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_bill_log
-- ----------------------------
DROP TABLE IF EXISTS `cust_bill_log`;
CREATE TABLE `cust_bill_log`
(
    `id`                 bigint(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `payment_id`         bigint(20) NULL DEFAULT NULL,
    `old_payment_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '旧状态',
    `new_payment_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '新状态',
    `created_by`         bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime NULL DEFAULT NULL COMMENT '创建时间',
    `delete_time`        datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15465 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_max_number
-- ----------------------------
DROP TABLE IF EXISTS `cust_max_number`;
CREATE TABLE `cust_max_number`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT COMMENT '\"ID号\"',
    `label`       varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\"标识\"',
    `max_number`  int(11) NULL DEFAULT NULL COMMENT '\"最大数\"',
    `delete_time` datetime NULL DEFAULT NULL,
    `update_time` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户编号最大数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_plan
-- ----------------------------
DROP TABLE IF EXISTS `cust_plan`;
CREATE TABLE `cust_plan`
(
    `cust_plan_id`                  bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规划编号',
    `cust_plan_name`                varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规划名称',
    `cust_uid`                      bigint(20) NULL DEFAULT NULL COMMENT '用户编号',
    `creator_employee_uid`          bigint(20) NULL DEFAULT NULL COMMENT '制定人',
    `reviewer_employee_uid`         bigint(20) NULL DEFAULT NULL COMMENT '审核人',
    `reviewer_time`                 datetime NULL DEFAULT NULL COMMENT '审核时间',
    `cust_service_requirement`      varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务需求',
    `cust_service_objective`        varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务目标',
    `cust_risk_assessment`          varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风险评估',
    `cust_service_delivery_method`  varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务提供方式',
    `cust_risk_mitigation_measures` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风险防范措施',
    `cust_plant_start_time`         datetime NULL DEFAULT NULL COMMENT '规划开始时间',
    `cust_plant_end_time`           datetime NULL DEFAULT NULL COMMENT '规划结束时间',
    `cust_plant_execution_status`   tinyint(1) NOT NULL DEFAULT 1 COMMENT '服务规划执行状态',
    `cust_plant_review_status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '服务规划审核状态:',
    `created_by`                    bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                    bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                   datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                   datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                   datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_plan_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2806 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户服务规划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_plan_item
-- ----------------------------
DROP TABLE IF EXISTS `cust_plan_item`;
CREATE TABLE `cust_plan_item`
(
    `cust_plan_item_id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规划服务项目',
    `cust_plan_item_template_id`    bigint(20) NULL DEFAULT NULL COMMENT '服务项目模板Id',
    `cust_plan_id`                  bigint(20) NOT NULL COMMENT '规划编号',
    `cust_plan_item_name`           varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务项目名称',
    `cust_plan_item_details`        varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细服务描述',
    `cust_plan_item_frequency_cate` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务频次类',
    `cust_plan_item_frequency_num`  varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务频次数',
    `cust_plan_item_start_time`     datetime NULL DEFAULT NULL COMMENT '服务开始时间',
    `cust_plan_item_end_time`       datetime NULL DEFAULT NULL COMMENT '服务结束时间',
    `cust_plan_sort`                int(11) NULL DEFAULT 100 COMMENT '规划项目排序',
    `created_by`                    bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                    bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                   datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                   datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                   datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_plan_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2481 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户服务规划项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_plan_item_template
-- ----------------------------
DROP TABLE IF EXISTS `cust_plan_item_template`;
CREATE TABLE `cust_plan_item_template`
(
    `cust_plan_item_template_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '规划项目模板ID',
    `cust_plan_item_template_pid_id`  bigint(20) NULL DEFAULT NULL COMMENT '规划服务项目父级Id',
    `cust_plan_item_template_name`    varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规划项目模板名称',
    `cust_plan_item_template_details` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `cust_plan_item_template_sort`    int(11) NULL DEFAULT 100,
    `created_by`                      bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                      bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                     datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                     datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                     datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_plan_item_template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 97 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户服务规划项目模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_service_record
-- ----------------------------
DROP TABLE IF EXISTS `cust_service_record`;
CREATE TABLE `cust_service_record`
(
    `id`                       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '序号',
    `order_id`                 bigint(20) NULL DEFAULT NULL COMMENT '主订单ID',
    `cust_uid`                 bigint(20) NULL DEFAULT NULL COMMENT '服务老人',
    `cust_name`                varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名(废弃)',
    `cust_evaluation_level`    varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失能等级',
    `cust_is_bed`              varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人员类型',
    `cust_service_address`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务地址(废弃)',
    `cust_phone`               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号(废弃)',
    `service_items`            bigint(20) NULL DEFAULT NULL COMMENT '服务项目',
    `service_date`             datetime NULL DEFAULT NULL COMMENT '服务日期',
    `service_start_date`       datetime NULL DEFAULT NULL COMMENT '服务开始时间',
    `service_end_date`         datetime NULL DEFAULT NULL COMMENT '服务结束时间',
    `service_duration`         varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务时长',
    `service_amount`           varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务金额',
    `payment_method`           varchar(0) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '付款方式',
    `dispatch_personnel`       bigint(20) NULL DEFAULT NULL COMMENT '派工人员',
    `service_personnel`        bigint(20) NULL DEFAULT NULL COMMENT '服务人员',
    `elderly_care_consultant`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '养老顾问',
    `service_organization`     bigint(20) NULL DEFAULT NULL COMMENT '服务机构',
    `service_record_personnel` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '录入人员',
    `status`                   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待分配' COMMENT '状态',
    `service_remarks`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务备注',
    `created_by`               bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`               bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`              datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`              datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`              datetime NULL DEFAULT NULL COMMENT '删除时间',
    `service_pricing_manner`   varchar(0) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计价方式',
    `service_price`            varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务单价',
    `service_number`           varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务数量',
    `is_haidian`               bigint(20) NULL DEFAULT 0 COMMENT '是否是海淀家床服务记录  0:否   1: 是',
    `haidian_service_items`    varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家床服务项目',
    `haidian_date`             datetime NULL DEFAULT NULL COMMENT '登记时间',
    `haidian_service_type`     varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补贴类别',
    `service_picture`          varchar(640) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务图片',
    `cust_signature`           varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户签名',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38662 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_subsidy_bill
-- ----------------------------
DROP TABLE IF EXISTS `cust_subsidy_bill`;
CREATE TABLE `cust_subsidy_bill`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '补贴记录id',
    `cust_uid`        bigint(20) NULL DEFAULT NULL COMMENT '客户id',
    `type`            tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '补贴类型(1、补贴；2：消费；3:运营补贴)',
    `input_amount`    decimal(8, 2) NULL DEFAULT 0.00 COMMENT '录入金额',
    `real_amount`     decimal(8, 2) NULL DEFAULT NULL COMMENT '实际金额',
    `spending_amount` decimal(8, 2) NULL DEFAULT NULL COMMENT '消费金额',
    `created_by`      bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`      bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`     datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`     datetime NULL DEFAULT NULL COMMENT '删除时间',
    `status`          tinyint(1) NULL DEFAULT 1 COMMENT '状态(1:有效、-1:无效)',
    `month`           varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '月份',
    `start_time`      date NULL DEFAULT NULL COMMENT '可使用开始时间',
    `end_time`        date NULL DEFAULT NULL COMMENT '可使用结束时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2304 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '补贴记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cust_survey_assessment
-- ----------------------------
DROP TABLE IF EXISTS `cust_survey_assessment`;
CREATE TABLE `cust_survey_assessment`
(
    `cust_survey_assessment_id`   bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '问卷记录Id',
    `cust_survey_name`            varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估名称',
    `cust_survey_content`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评估内容',
    `cust_survey_evaluation_time` datetime NULL DEFAULT NULL COMMENT '评估日期',
    `cust_survey_assessment`      varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估等级',
    `cust_sign_image`             varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签字截图',
    `cust_sign_time`              datetime NULL DEFAULT NULL COMMENT '签字日期',
    `cust_uid`                    bigint(20) NOT NULL COMMENT '老人编号',
    `employee_uid`                bigint(20) NULL DEFAULT NULL COMMENT '调查人员(暂时不进行关联 废弃)',
    `employee_name`               varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调查人员姓名',
    `company_cid`                 bigint(20) NOT NULL COMMENT '机构编号',
    `created_by`                  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                 datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                 datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                 datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_survey_assessment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2812 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评估记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_survey_record
-- ----------------------------
DROP TABLE IF EXISTS `cust_survey_record`;
CREATE TABLE `cust_survey_record`
(
    `cust_survey_record_id`       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '问卷记录Id',
    `cust_survey_template_id`     bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '问卷模板',
    `cust_survey_name`            varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问卷名称',
    `cust_survey_content`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '问卷内容',
    `cust_survey_evaluation_time` datetime NULL DEFAULT NULL COMMENT '评估日期',
    `cust_sign_image`             varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签字截图',
    `cust_uid`                    bigint(20) NOT NULL COMMENT '老人编号',
    `employee_uid`                bigint(20) NOT NULL COMMENT '调查人员',
    `company_cid`                 bigint(20) NOT NULL COMMENT '机构编号',
    `created_by`                  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                 datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                 datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                 datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_survey_record_id`) USING BTREE,
    INDEX                         `template`(`cust_survey_template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '问卷记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_survey_template
-- ----------------------------
DROP TABLE IF EXISTS `cust_survey_template`;
CREATE TABLE `cust_survey_template`
(
    `cust_survey_template_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '表单模板ID',
    `name`                    varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单名称',
    `content`                 text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `type`                    tinyint(4) NULL DEFAULT 1 COMMENT '表单类型(1:nocodb、2:自建)',
    `table_id`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格Id',
    `view_id`                 varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视图Id',
    `form_url`                varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表单填写链接',
    `view_url`                varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '视图链接',
    `employee_uid`            bigint(20) NULL DEFAULT NULL COMMENT '负责人',
    `created_by`              bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`              bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`             datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_survey_template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '表单模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_sync
-- ----------------------------
DROP TABLE IF EXISTS `cust_sync`;
CREATE TABLE `cust_sync`
(
    `id`           int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Id',
    `old_cust_uid` int(11) NULL DEFAULT NULL COMMENT '旧系统Id',
    `new_cust_uid` int(11) NULL DEFAULT NULL COMMENT '新系统Id',
    `create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2371 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '旧系统用户同步表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_temp
-- ----------------------------
DROP TABLE IF EXISTS `cust_temp`;
CREATE TABLE `cust_temp`
(
    `cust_uid`        bigint(20) UNSIGNED NOT NULL,
    `recharge_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计充值',
    `spending_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '累计消费',
    `num`             decimal(10, 2) NULL DEFAULT NULL COMMENT '误差值',
    PRIMARY KEY (`cust_uid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_transaction
-- ----------------------------
DROP TABLE IF EXISTS `cust_transaction`;
CREATE TABLE `cust_transaction`
(
    `cust_transaction_id`   bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '交易ID',
    `cust_account_id`       bigint(20) NOT NULL COMMENT '交易账户Id',
    `cust_uid`              bigint(20) UNSIGNED NOT NULL COMMENT '客户id',
    `cust_transaction_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '交易类型(1:支付、2:存款)',
    `cust_amount`           decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额',
    `cust_transaction_time` datetime       NOT NULL COMMENT '交易时间',
    `cust_balance_after`    decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易后账户金额',
    `cust_note`             varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `cust_info`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '交易信息(json数组格式、name:交易名称、link_type:关联类型、link_id:关联Id）',
    PRIMARY KEY (`cust_transaction_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '被照护人交易主表 ' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_user
-- ----------------------------
DROP TABLE IF EXISTS `cust_user`;
CREATE TABLE `cust_user`
(
    `cust_uid`                    bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '客户id',
    `cust_name`                   varchar(56) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '客户姓名',
    `cust_sex`                    tinyint(4) NOT NULL DEFAULT 1 COMMENT '客户性别（0:未知、1:男、2:女）',
    `cust_id_card`                varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户身份证号',
    `cust_id_type`                tinyint(4) NULL DEFAULT 1 COMMENT '身份证类型（1: 身份证、2:护照、3:港澳通行证、4:其他）',
    `cust_birth`                  datetime NULL DEFAULT NULL COMMENT '客户出生年月',
    `cust_nation`                 varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '民族',
    `cust_private_phone`          varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '私人电话',
    `cust_home_phone`             varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家庭电话',
    `cust_marital_status`         tinyint(4) NOT NULL DEFAULT 6 COMMENT '婚姻状态（1:未婚、2：已婚、3:丧偶、4:离婚、5:分居、6:不详）',
    `cust_live_status`            tinyint(4) NOT NULL DEFAULT 1 COMMENT '居住状况(1:不详、2:空巢、3:子女未共同居住且丧偶的老人、4:无子女无配偶的孤寡老人、5:其他)',
    `cust_identity_type`          tinyint(4) NOT NULL DEFAULT 1 COMMENT '身份类别（1:不详、2:特困家庭、3:低保、4:计划生育特殊家庭、5:退伍军人、6:其他）',
    `cust_is_bed`                 tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否家床(1:非家床、2:家床)',
    `cust_avatar`                 varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'https://img.zkshlm.com/zksh/pension/182084461477757563.png' COMMENT '客户头像',
    `cust_belief`                 tinyint(4) NOT NULL DEFAULT 1 COMMENT '宗教信仰(1:不详、2:无、3:佛教、4:道教、5:伊斯兰教、6:基督教、7:其他)',
    `cust_regist_address`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '户籍地址',
    `cust_live_address`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '居住详细地址',
    `cust_longitude`              decimal(10, 8) NULL DEFAULT NULL COMMENT '经度地址',
    `cust_latitude`               decimal(10, 8) NULL DEFAULT NULL COMMENT '纬度地址',
    `cust_disability_type`        tinyint(4) NOT NULL DEFAULT 1 COMMENT '残疾类别(1:不详、2:无、3:视力、4:听力、5:言语、6:肢体、7:智力、8:精神、9:多重)',
    `cust_disability_level`       tinyint(4) NOT NULL DEFAULT 1 COMMENT '残疾等级(1:不详、2:无、3:一级、4:二级、5:三级、6:四级)',
    `cust_disability_card`        varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '残疾证号',
    `cust_area`                   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '中关村' COMMENT '所属片区',
    `cust_remark`                 varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户备注',
    `cust_evaluation_level`       tinyint(4) NULL DEFAULT 1 COMMENT '评估等级(1:能力完好、2:轻度失能、3:中度失能、4:重度失能)',
    `cust_aging`                  varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '未改造' COMMENT '适老化改造',
    `cust_work_unit`              varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原工作单位',
    `cust_service_site`           bigint(20) NOT NULL COMMENT '服务机构',
    `cust_guardian_name`          varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人姓名',
    `cust_guardian_id_card`       varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人身份证',
    `cust_guardian_phone`         varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人电话',
    `cust_guardian_address`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人家庭住址',
    `cust_guardian_relationship`  varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '监护人与老人关系',
    `cust_community`              varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '社区',
    `cust_unit`                   varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位名称',
    `cust_city`                   varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
    `cust_street`                 varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '街道名称',
    `cust_content`                text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '自定义内容',
    `cust_consultant`             bigint(20) NULL DEFAULT NULL COMMENT '养老顾问',
    `cust_medical_insurance_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医保类别',
    `created_by`                  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                 datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                 datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                 datetime NULL DEFAULT NULL COMMENT '删除时间',
    `cust_signature`              text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '本人签字',
    `cust_guardian_signature`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '监护人签字',
    `cust_health_records`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '健康档案',
    `cust_health_records2`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '医生入户健康档案',
    `cust_internal_number`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内部编号',
    `cust_status`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '服务中' COMMENT '老人状态',
    `cust_automatic_renewal`      tinyint(5) NULL DEFAULT 0 COMMENT '是否自动续签(0或1)',
    `cust_haidian_number`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '家床海淀区编号',
    PRIMARY KEY (`cust_uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5527 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '被照护人信息主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cust_user_consultant_log
-- ----------------------------
DROP TABLE IF EXISTS `cust_user_consultant_log`;
CREATE TABLE `cust_user_consultant_log`
(
    `cust_consultant_log_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '档案顾问变更记录id',
    `cust_uid`               bigint(20) NOT NULL COMMENT '变更客户id',
    `cust_name`              varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更客户姓名',
    `former_consultant_id`   bigint(20) NOT NULL COMMENT '原养老顾问id',
    `new_consultant_id`      bigint(20) NOT NULL COMMENT '新养老顾问id',
    `former_service_site`    bigint(20) NOT NULL COMMENT '原服务机构',
    `new_service_site`       bigint(20) NULL DEFAULT NULL COMMENT '新服务机构',
    `change_time`            datetime NULL DEFAULT NULL COMMENT '变更时间',
    `change_reasons`         varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更原因',
    `remark`                 varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `change_state`           varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更状态',
    `create_time`            datetime NULL DEFAULT NULL COMMENT '创建时间',
    `created_by`             bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`             bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `update_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`            datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`cust_consultant_log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '老人养老顾问变更记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for eb_system_config
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_config`;
CREATE TABLE `eb_system_config`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `group_id`           int(11) NULL DEFAULT NULL COMMENT '组id',
    `key`                varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置键名',
    `value`              varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置值',
    `name`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置名称',
    `input_type`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据输入类型',
    `config_select_data` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置选项数据',
    `sort`               smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`, `key`) USING BTREE,
    INDEX                `group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_config_group
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_config_group`;
CREATE TABLE `eb_system_config_group`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典名称',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典标示',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_crontab
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_crontab`;
CREATE TABLE `eb_system_crontab`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务名称',
    `type`        smallint(6) NULL DEFAULT 4 COMMENT '任务类型 (1 command, 2 class, 3 url, 4 eval)',
    `target`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调用任务字符串',
    `parameter`   varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调用任务参数',
    `rule`        varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务执行表达式',
    `singleton`   smallint(6) NULL DEFAULT 1 COMMENT '是否单次执行 (1 是 2 不是)',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_crontab_log
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_crontab_log`;
CREATE TABLE `eb_system_crontab_log`
(
    `id`             int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `crontab_id`     int(10) UNSIGNED NULL DEFAULT NULL COMMENT '任务ID',
    `name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务名称',
    `target`         varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务调用目标字符串',
    `parameter`      varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务调用参数',
    `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '异常信息',
    `status`         smallint(6) NULL DEFAULT 1 COMMENT '执行状态 (1成功 2失败)',
    `create_time`    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`    datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26965 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务执行日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_dept
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_dept`;
CREATE TABLE `eb_system_dept`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_id`   int(10) UNSIGNED NULL DEFAULT NULL COMMENT '父ID',
    `level`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组级集合',
    `name`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门名称',
    `leader`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '负责人',
    `phone`       varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `sort`        smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_dept_leader
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_dept_leader`;
CREATE TABLE `eb_system_dept_leader`
(
    `dept_id` int(10) UNSIGNED NOT NULL COMMENT '部门主键',
    `user_id` int(10) UNSIGNED NOT NULL COMMENT '角色主键',
    PRIMARY KEY (`user_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门领导关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_dict_data`;
CREATE TABLE `eb_system_dict_data`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `type_id`     int(10) UNSIGNED NULL DEFAULT NULL COMMENT '字典类型ID',
    `label`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典标签',
    `value`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典值',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典标示',
    `sort`        smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `type_id`(`type_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 346 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_dict_type`;
CREATE TABLE `eb_system_dict_type`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典名称',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典标示',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_login_log
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_login_log`;
CREATE TABLE `eb_system_login_log`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `username`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
    `ip`          varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录IP地址',
    `ip_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP所属地',
    `os`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作系统',
    `browser`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '浏览器',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '登录状态 (1成功 2失败)',
    `message`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提示消息',
    `login_time`  datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '登录时间',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5447 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '登录日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_menu
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_menu`;
CREATE TABLE `eb_system_menu`
(
    `id`           int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `parent_id`    int(10) UNSIGNED NULL DEFAULT NULL COMMENT '父ID',
    `level`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组级集合',
    `name`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单名称',
    `code`         varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单标识代码',
    `icon`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
    `route`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由地址',
    `component`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
    `redirect`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转地址',
    `is_hidden`    smallint(6) NULL DEFAULT 1 COMMENT '是否隐藏 (1是 2否)',
    `type`         char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型, (M菜单 B按钮 L链接 I iframe)',
    `status`       smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `sort`         smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `remark`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`   int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`   int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`  datetime NULL DEFAULT NULL COMMENT '删除时间',
    `generate_id`  int(11) NULL DEFAULT 0 COMMENT '生成id',
    `generate_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成key',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5353 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_notice
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_notice`;
CREATE TABLE `eb_system_notice`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `message_id`  int(11) NULL DEFAULT NULL COMMENT '消息ID',
    `title`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
    `type`        smallint(6) NULL DEFAULT NULL COMMENT '公告类型(1通知 2公告)',
    `content`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '公告内容',
    `click_num`   int(11) NULL DEFAULT 0 COMMENT '浏览次数',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `message_id`(`message_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_oper_log`;
CREATE TABLE `eb_system_oper_log`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `username`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
    `app`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用名称',
    `method`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求方式',
    `router`         varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求路由',
    `service_name`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务名称',
    `ip`             varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求IP地址',
    `ip_location`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'IP所属地',
    `operation_time` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '操作时间包含毫秒时间',
    `operation_code` int(11) NULL DEFAULT 200 COMMENT '状态码',
    `operation_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求响应Body',
    `request_data`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求数据',
    `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`     int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`     int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time`    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime NULL DEFAULT NULL COMMENT '更新时间',
    `delete_time`    datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX            `username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 850119 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_post
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_post`;
CREATE TABLE `eb_system_post`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位名称',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位代码',
    `sort`        smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_role
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_role`;
CREATE TABLE `eb_system_role`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '角色名称',
    `code`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '角色代码',
    `data_scope`  smallint(6) NULL DEFAULT 1 COMMENT '数据范围(1:全部数据权限 2:自定义数据权限 3:本部门数据权限 4:本部门及以下数据权限 5:本人数据权限)',
    `status`      smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `sort`        smallint(5) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `remark`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`  int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`  int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_role_dept`;
CREATE TABLE `eb_system_role_dept`
(
    `role_id` int(10) UNSIGNED NOT NULL COMMENT '用户主键',
    `dept_id` int(10) UNSIGNED NOT NULL COMMENT '角色主键',
    PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色与部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_role_menu`;
CREATE TABLE `eb_system_role_menu`
(
    `role_id` int(10) UNSIGNED NOT NULL COMMENT '角色主键',
    `menu_id` int(10) UNSIGNED NOT NULL COMMENT '菜单主键',
    PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色与菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_uploadfile
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_uploadfile`;
CREATE TABLE `eb_system_uploadfile`
(
    `id`           int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `storage_mode` smallint(6) NULL DEFAULT 1 COMMENT '存储模式 (1 本地 2 阿里云 3 七牛云 4 腾讯云)',
    `origin_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '原文件名',
    `object_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '新文件名',
    `hash`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件hash',
    `mime_type`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '资源类型',
    `storage_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '存储目录',
    `suffix`       varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件后缀',
    `size_byte`    bigint(20) NULL DEFAULT NULL COMMENT '字节数',
    `size_info`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文件大小',
    `url`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'url地址',
    `remark`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`   int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`   int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time`  datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`  datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `hash`(`hash`) USING BTREE,
    INDEX          `storage_path`(`storage_path`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1668 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '上传文件信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_user
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_user`;
CREATE TABLE `eb_system_user`
(
    `id`              int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID,主键',
    `username`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
    `password`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
    `zksh_openid`     varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中科生活公众号Openid',
    `third_v1_uid`    bigint(20) NULL DEFAULT NULL COMMENT '第一版平台Id',
    `user_type`       varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '100' COMMENT '用户类型:(100系统用户)',
    `nickname`        varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
    `phone`           varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机',
    `email`           varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户邮箱',
    `avatar`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像',
    `signed`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '个人签名',
    `dashboard`       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '后台首页类型',
    `dept_id`         int(10) UNSIGNED NULL DEFAULT NULL COMMENT '部门ID',
    `status`          smallint(6) NULL DEFAULT 1 COMMENT '状态 (1正常 2停用)',
    `login_ip`        varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后登陆IP',
    `login_time`      datetime NULL DEFAULT NULL COMMENT '最后登陆时间',
    `backend_setting` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '后台设置数据',
    `remark`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`      int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`      int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time`     datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`     datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`     datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `username`(`username`) USING BTREE,
    INDEX             `dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_user_post
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_user_post`;
CREATE TABLE `eb_system_user_post`
(
    `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户主键',
    `post_id` int(10) UNSIGNED NOT NULL COMMENT '岗位主键',
    PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_system_user_role
-- ----------------------------
DROP TABLE IF EXISTS `eb_system_user_role`;
CREATE TABLE `eb_system_user_role`
(
    `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户主键',
    `role_id` int(10) UNSIGNED NOT NULL COMMENT '角色主键',
    PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_tool_generate_columns
-- ----------------------------
DROP TABLE IF EXISTS `eb_tool_generate_columns`;
CREATE TABLE `eb_tool_generate_columns`
(
    `id`             int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `table_id`       int(10) UNSIGNED NULL DEFAULT NULL COMMENT '所属表ID',
    `column_name`    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段名称',
    `column_comment` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段注释',
    `column_type`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段类型',
    `default_value`  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认值',
    `is_pk`          smallint(6) NULL DEFAULT 1 COMMENT '1 非主键 2 主键',
    `is_required`    smallint(6) NULL DEFAULT 1 COMMENT '1 非必填 2 必填',
    `is_insert`      smallint(6) NULL DEFAULT 1 COMMENT '1 非插入字段 2 插入字段',
    `is_edit`        smallint(6) NULL DEFAULT 1 COMMENT '1 非编辑字段 2 编辑字段',
    `is_list`        smallint(6) NULL DEFAULT 1 COMMENT '1 非列表显示字段 2 列表显示字段',
    `is_query`       smallint(6) NULL DEFAULT 1 COMMENT '1 非查询字段 2 查询字段',
    `query_type`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'eq' COMMENT '查询方式 eq 等于, neq 不等于, gt 大于, lt 小于, like 范围',
    `view_type`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'text' COMMENT '页面控件,text, textarea, password, select, checkbox, radio, date, upload, ma-upload(封装的上传控件)',
    `dict_type`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典类型',
    `allow_roles`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '允许查看该字段的角色',
    `options`        varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字段其他设置',
    `sort`           tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
    `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`     int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`     int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time`    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`    datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1552 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务字段表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for eb_tool_generate_tables
-- ----------------------------
DROP TABLE IF EXISTS `eb_tool_generate_tables`;
CREATE TABLE `eb_tool_generate_tables`
(
    `id`             int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `table_name`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称',
    `table_comment`  varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表注释',
    `template`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
    `namespace`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '命名空间',
    `package_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '控制器包名',
    `business_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '业务名称',
    `class_name`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类名称',
    `menu_name`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成菜单名',
    `belong_menu_id` int(11) NULL DEFAULT NULL COMMENT '所属菜单',
    `tpl_category`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成类型,single 单表CRUD,tree 树表CRUD,parent_sub父子表CRUD',
    `generate_type`  smallint(6) NULL DEFAULT 1 COMMENT '1 压缩包下载 2 生成到模块',
    `generate_menus` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生成菜单列表',
    `build_menu`     smallint(6) NULL DEFAULT 1 COMMENT '是否构建菜单',
    `component_type` smallint(6) NULL DEFAULT 1 COMMENT '组件显示方式',
    `options`        varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他业务选项',
    `remark`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `source`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据源',
    `created_by`     int(11) NULL DEFAULT NULL COMMENT '创建者',
    `updated_by`     int(11) NULL DEFAULT NULL COMMENT '更新者',
    `create_time`    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`    datetime NULL DEFAULT NULL COMMENT '删除时间',
    `stub`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'saiadmin' COMMENT 'stub类型',
    `generate_path`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'view/zk_pension_web_v2' COMMENT '前端根目录',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代码生成业务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for employee_account
-- ----------------------------
DROP TABLE IF EXISTS `employee_account`;
CREATE TABLE `employee_account`
(
    `employee_account_id`     bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '账户编号',
    `employee_account_type`   tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '账户类型(1:工资账户)',
    `employee_account_name`   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '默认余额账户' COMMENT '账户名称',
    `employee_account_status` tinyint(4) NULL DEFAULT 1 COMMENT '账户状态（1:正常、2:关闭）',
    `employee_uid`            bigint(20) UNSIGNED NOT NULL COMMENT '客户id',
    `cust_balance`            decimal(10, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '账户余额',
    `created_by`              bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`              bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`             datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`employee_account_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工账户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for employee_replace_record
-- ----------------------------
DROP TABLE IF EXISTS `employee_replace_record`;
CREATE TABLE `employee_replace_record`
(
    `replace_record_id`    bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '换人记录id',
    `replace_classify`     varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'contract_management' COMMENT '换人分类',
    `link_id`              bigint(20) NULL DEFAULT NULL COMMENT '关联id',
    `employee_uid`         bigint(20) NULL DEFAULT NULL COMMENT '原服务员',
    `salary_payment_date`  int(11) NULL DEFAULT 25 COMMENT '新工资发放日',
    `working_days`         int(11) NULL DEFAULT 0 COMMENT '新月工作天数',
    `working_hour`         int(11) NULL DEFAULT 0 COMMENT '新日工作小时',
    `employee_service_fee` decimal(8, 2) NULL DEFAULT 0.00 COMMENT '小时工单价',
    `employee_salary`      decimal(6, 2) NULL DEFAULT 0.00 COMMENT '服务员工资',
    `working_start`        date NULL DEFAULT NULL COMMENT '工作开始时间',
    `working_end`          date NULL DEFAULT NULL COMMENT '工作结束时间',
    `created_by`           bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`           bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`          datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`          datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`replace_record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 350 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工更换记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for employee_salary
-- ----------------------------
DROP TABLE IF EXISTS `employee_salary`;
CREATE TABLE `employee_salary`
(
    `employee_salary_id` bigint(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '工资表id',
    `employee_uid`       bigint(10) NOT NULL COMMENT '员工id',
    `salary_month`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工资月份',
    `employee_salary`    decimal(10, 2) NULL DEFAULT 0.00 COMMENT '固定工资',
    `absenteeism_salary` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '缺勤扣除',
    `overtime_salary`    decimal(10, 2) NULL DEFAULT 0.00 COMMENT '加班工资',
    `issued_salary`      decimal(10, 2) NULL DEFAULT 0.00 COMMENT '实发工资',
    `created_by`         bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`         bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人',
    `create_time`        datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`        datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`        datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`employee_salary_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 330 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工工资表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for employee_salary_item
-- ----------------------------
DROP TABLE IF EXISTS `employee_salary_item`;
CREATE TABLE `employee_salary_item`
(
    `employee_salary_item_id` bigint(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '工资附表id',
    `employee_salary_id`      bigint(10) NOT NULL COMMENT '工资主表id',
    `cust_uid`                bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '客户编号(多位老人合并填报时为空)',
    `cust_name`               varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户姓名',
    `employee_uid`            bigint(10) NOT NULL COMMENT '员工编号',
    `salary_classify`         varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'contract_management' COMMENT '工资分类(根据此分类关联对应表)',
    `link_id`                 bigint(20) NOT NULL COMMENT '关联id',
    `cust_type`               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户类型(非家床/家床)',
    `services_type`           bigint(20) NULL DEFAULT NULL COMMENT '服务项目',
    `salary_month`            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工资填报月份',
    `real_amount`             decimal(10, 2) NULL DEFAULT 0.00 COMMENT '上月补贴',
    `value_added_expenses`    decimal(10, 2) NULL DEFAULT 0.00 COMMENT '增值费用',
    `receivable_amount`       decimal(10, 2) NULL DEFAULT 0.00 COMMENT '应收金额',
    `working_days`            int(11) NULL DEFAULT 0 COMMENT '月工作天数(基数)',
    `actual_hour`             int(11) NULL DEFAULT 0 COMMENT '实际工时(天)',
    `issued_salary`           decimal(10, 2) NULL DEFAULT 0.00 COMMENT '实发工资',
    `employee_salary`         decimal(10, 2) NULL DEFAULT 0.00 COMMENT '固定工资',
    `absenteeism_salary`      decimal(10, 2) NULL DEFAULT 0.00 COMMENT '缺勤扣除',
    `overtime_salary`         decimal(10, 2) NULL DEFAULT 0.00 COMMENT '加班工资',
    `salary_payment_date`     int(11) NULL DEFAULT 5 COMMENT '工资发放日',
    `cust_consultant`         bigint(30) NULL DEFAULT NULL COMMENT '养老顾问(老人档案)',
    `sales_attribution_uid`   bigint(30) NULL DEFAULT NULL COMMENT '派工人员(合同)',
    `salary_status`           varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '待确认' COMMENT '填报状态',
    `cancel_reason`           varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '取消原因',
    `auditor_id`              bigint(20) NULL DEFAULT NULL COMMENT '审核员',
    `auditor_time`            datetime NULL DEFAULT NULL COMMENT '审核时间',
    `auditor_remark`          varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人员备注',
    `created_by`              bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`              bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '更新人',
    `create_time`             datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`employee_salary_item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 330 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工工资附表--工资填报表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for employee_transaction
-- ----------------------------
DROP TABLE IF EXISTS `employee_transaction`;
CREATE TABLE `employee_transaction`
(
    `employee_transaction_id`   bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '交易ID',
    `employee_account_id`       bigint(20) NOT NULL COMMENT '交易账户Id',
    `employee_uid`              bigint(20) UNSIGNED NOT NULL COMMENT '员工id',
    `employee_transaction_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '交易类型(1:收入、2:提现)',
    `employee_amount`           decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易金额',
    `employee_transaction_time` datetime       NOT NULL COMMENT '交易时间',
    `employee_balance_after`    decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '交易后账户金额',
    `employee_note`             varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `employee_info`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '交易信息(json数组格式、name:交易名称、link_type:关联类型、link_id:关联Id）',
    PRIMARY KEY (`employee_transaction_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工交易表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for employee_user
-- ----------------------------
DROP TABLE IF EXISTS `employee_user`;
CREATE TABLE `employee_user`
(
    `employee_uid`                        int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `employee_name`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '员工姓名',
    `employee_sex`                        tinyint(4) NOT NULL DEFAULT 0 COMMENT '员工性别（0:未知、1:男、2:女）',
    `employee_phone`                      varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工手机号',
    `employee_id_card`                    varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工身份证号',
    `employee_id_type`                    tinyint(4) NULL DEFAULT 1 COMMENT '身份证类型（1: 身份证、2:护照、3:港澳通行证、4:其他）',
    `employee_birth`                      datetime NULL DEFAULT NULL COMMENT '员工出生年月',
    `employee_nation`                     varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '民族',
    `employee_marital_status`             tinyint(4) NOT NULL DEFAULT 6 COMMENT '婚姻状态（1:未婚、2：已婚、3:丧偶、4:离婚、5:分居、6:不详）',
    `employee_regist_address`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '户籍地址',
    `employee_live_address`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '居住详细地址',
    `employee_bank_name`                  varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属银行',
    `employee_bank_id_card`               varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡号',
    `employee_bank_branch_name`           varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户支行',
    `employee_bank_bind_phone`            varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行绑定手机',
    `employee_bank_branch_num`            varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡行号',
    `employee_card_front_img`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证正面',
    `employee_card_reverse_img`           varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证反面',
    `employee_education`                  tinyint(4) NOT NULL DEFAULT 1 COMMENT '学历(1:高中、2:大专、3:本科、4:高中以下)',
    `employee_status`                     tinyint(4) NOT NULL DEFAULT 1 COMMENT '员工状态（1:上班、2:未入职、3:已离职）',
    `employee_service_site`               bigint(20) NULL DEFAULT 14 COMMENT '所属机构',
    `employee_consultant`                 bigint(20) NULL DEFAULT NULL COMMENT '负责人',
    `employee_bank_bind_img`              varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡照片',
    `employee_signature_picture`          varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签字图片',
    `employee_covid19_vaccine_screenshot` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '新冠疫苗截屏',
    `created_by`                          bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`                          bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`                         datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`                         datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`                         datetime NULL DEFAULT NULL COMMENT '删除时间',
    `employee_position`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工职位',
    `employee_id_card_issuing_authority`  varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证签发机关',
    `employee_id_card_validity`           varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证有效期',
    PRIMARY KEY (`employee_uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1804 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工信息主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_category
-- ----------------------------
DROP TABLE IF EXISTS `food_category`;
CREATE TABLE `food_category`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类编号',
    `parent_id`   bigint(20) NULL DEFAULT 0 COMMENT '父级id',
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
    `sort`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类排序',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_content
-- ----------------------------
DROP TABLE IF EXISTS `food_content`;
CREATE TABLE `food_content`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '菜品编号',
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜品名称',
    `cate_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
    `sort`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '100' COMMENT '菜品排序',
    `status`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '菜品状态(1:正常；0:禁用)',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 469 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_content_material
-- ----------------------------
DROP TABLE IF EXISTS `food_content_material`;
CREATE TABLE `food_content_material`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `content_id`  bigint(20) UNSIGNED NOT NULL COMMENT '菜品编号',
    `material_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '原材料编号',
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原材料名称',
    `weight` double NULL DEFAULT NULL COMMENT '重量 单位g',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜品关联原材料表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_emergency
-- ----------------------------
DROP TABLE IF EXISTS `food_emergency`;
CREATE TABLE `food_emergency`
(
    `id`                  bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '紧急订单编号',
    `label`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签',
    `type`                tinyint(1) NULL DEFAULT 1 COMMENT '(1:添加、-1:退餐)',
    `content`             text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
    `delivery_option`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配送方式',
    `delivery_site`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配送站点',
    `package_type`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '老年餐' COMMENT '套餐类型',
    `delivery_type`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '自费支付' COMMENT '送餐费支付方式',
    `delivery_fee`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配送费',
    `delivery_start_date` date NULL DEFAULT NULL COMMENT '配送开始日期',
    `delivery_end_date`   date NULL DEFAULT NULL COMMENT '配送结束日期',
    `user_longitude`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '老人地址经纬度',
    `user_latitude`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '老人地址经纬度',
    `user_address`        varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '老人地址',
    `cust_uid`            int(10) UNSIGNED NULL DEFAULT NULL COMMENT '订餐老人',
    `employee_uid`        bigint(20) NULL DEFAULT NULL COMMENT '送餐员',
    `auditor_uid`         bigint(20) NULL DEFAULT NULL COMMENT '审核员',
    `auditor_time`        datetime NULL DEFAULT NULL COMMENT '审核时间',
    `admin_remark`        varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核备注',
    `auditor_status`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待审核' COMMENT '审核状态',
    `payment_method`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付方式',
    `discount_amount`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补贴金额-当天',
    `discounts_price`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠金额-当天',
    `payment_amount`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付金额-当天',
    `payment_status`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否支付',
    `discount_setting`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '折扣设置',
    `remark`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '特殊要求',
    `is_wx_order`         tinyint(1) NULL DEFAULT -1 COMMENT '是否微信小程序创建(-1:不是，1:是)',
    `wx_refund_order_id`  bigint(20) NULL DEFAULT NULL COMMENT '小程序订单id',
    `created_by`          bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`          bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`         datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`         datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`         datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1609 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐紧急订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_material
-- ----------------------------
DROP TABLE IF EXISTS `food_material`;
CREATE TABLE `food_material`
(
    `material_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '原材料编号',
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原材料名称',
    `sort`        int(11) NULL DEFAULT 100 COMMENT '排序',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`material_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 116 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '原材料表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_package
-- ----------------------------
DROP TABLE IF EXISTS `food_package`;
CREATE TABLE `food_package`
(
    `package_id`  int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套餐编号',
    `config_id`   int(10) UNSIGNED NULL DEFAULT NULL COMMENT '套餐模板编号',
    `type`        tinyint(1) NOT NULL DEFAULT 0 COMMENT '套餐类型(0:养老餐、1:照料餐)',
    `name`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '套餐名称',
    `price`       decimal(8, 2) NULL DEFAULT 0.00 COMMENT '套餐价格',
    `day_time`    date NULL DEFAULT NULL COMMENT '有效日期',
    `sort`        decimal(8, 2) NULL DEFAULT 100.00 COMMENT '排序',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`package_id`) USING BTREE,
    INDEX         `day_time`(`day_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21007 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐包表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_package_config
-- ----------------------------
DROP TABLE IF EXISTS `food_package_config`;
CREATE TABLE `food_package_config`
(
    `config_id`   int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套餐配置编号',
    `name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
    `price`       decimal(8, 2) NULL DEFAULT 0.00 COMMENT '价格',
    `info`        varchar(888) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '介绍',
    `sort`        decimal(8, 2) NULL DEFAULT 100.00 COMMENT '排序',
    `status`      int(11) NULL DEFAULT 0 COMMENT '状态',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for food_package_item
-- ----------------------------
DROP TABLE IF EXISTS `food_package_item`;
CREATE TABLE `food_package_item`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '编号',
    `package_id`  int(10) UNSIGNED NULL DEFAULT NULL COMMENT '套餐编号',
    `food_id`     int(10) UNSIGNED NOT NULL COMMENT '菜品编号',
    `food_name`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜品名称',
    `food_cate`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜品分类',
    `num`         int(11) NULL DEFAULT 1 COMMENT '数量',
    `created_by`  bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time` datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5869 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '套餐包副表-套餐菜品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_discounts
-- ----------------------------
DROP TABLE IF EXISTS `order_discounts`;
CREATE TABLE `order_discounts`
(
    `order_discounts_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`           bigint(20) NOT NULL COMMENT '订单ID',
    `discounts_price`    decimal(8, 2) NULL DEFAULT NULL COMMENT '折扣金额',
    `discounts_text`     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '折扣内容',
    PRIMARY KEY (`order_discounts_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单折扣表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_food
-- ----------------------------
DROP TABLE IF EXISTS `order_food`;
CREATE TABLE `order_food`
(
    `order_food_id`     bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套餐订单ID',
    `order_id`          varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '主订单号',
    `cust_uid`          bigint(20) UNSIGNED NOT NULL COMMENT '客户编号',
    `employee_uid`      bigint(20) NULL DEFAULT NULL COMMENT '员工编号',
    `delivery_num`      bigint(20) NULL DEFAULT 0 COMMENT '配送数量',
    `delivery_option`   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配送方式',
    `delivery_site`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配送站点',
    `delivery_time`     date                                                          NOT NULL COMMENT '配送日期',
    `package_type`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '老年餐' COMMENT '套餐类型',
    `total_num`         int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '购买总数量',
    `pay_price`         decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '支付金额（套餐+邮费-政府补贴）',
    `sum_amount`        decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总金额',
    `cust_delivery_fee` decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '客户支付配送费',
    `gov_fee`           decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '政府补贴金额',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '特殊要求',
    `order_status`      tinyint(10) NOT NULL DEFAULT 0 COMMENT '订单状态（0:待完成、1:已完成）',
    `discounts_text`    varchar(160) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '折扣名称',
    `discounts_price`   decimal(8, 2)                                                 NOT NULL DEFAULT 0.00 COMMENT '折扣金额',
    `created_by`        bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`        bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`       datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    `user_longitude`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户经纬度',
    `user_latitude`     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户经纬度',
    `user_address`      varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户地址',
    PRIMARY KEY (`order_food_id`) USING BTREE,
    UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
    INDEX               `time`(`delivery_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 204730 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单附表-套餐表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_food_delivery
-- ----------------------------
DROP TABLE IF EXISTS `order_food_delivery`;
CREATE TABLE `order_food_delivery`
(
    `order_food_delivery_id`  bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`                varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单id',
    `cust_uid`                bigint(20) NOT NULL COMMENT '客户',
    `employee_uid`            bigint(20) NOT NULL COMMENT '配送员',
    `estimated_delivery_time` datetime NULL DEFAULT NULL COMMENT '预计配送时间',
    `actual_delivery_time`    datetime NULL DEFAULT NULL COMMENT '实际配送时间',
    `cour_delivery_fee`       decimal(10, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '送餐员获得配送费',
    `cust_delivery_fee`       decimal(10, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '客户支付配送费',
    `delivery_num`            int(4) NOT NULL DEFAULT 1 COMMENT '配送数量',
    `delivery_status`         tinyint(255) NOT NULL DEFAULT 0 COMMENT '配送状态(0待配送、1配送中、2已送达、-1已取消)',
    `created_by`              bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`              bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`             datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`             datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`             datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`order_food_delivery_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单配送表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_food_item
-- ----------------------------
DROP TABLE IF EXISTS `order_food_item`;
CREATE TABLE `order_food_item`
(
    `order_food_item_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '套餐订单附表ID',
    `order_id`           varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '主订单号',
    `cust_uid`           bigint(20) UNSIGNED NOT NULL COMMENT '客户编号',
    `employee_uid`       bigint(20) NULL DEFAULT NULL COMMENT '员工编号',
    `delivery_option`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配送方式',
    `delivery_site`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配送站点',
    `delivery_time`      date                                                          NOT NULL COMMENT '配送日期',
    `num`                int(10) UNSIGNED NULL DEFAULT 0 COMMENT '购买数量',
    `price`              decimal(6, 2) NULL DEFAULT 0.00 COMMENT '套餐单价',
    `pay_price`          decimal(10, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '支付金额',
    `package_id`         bigint(20) NULL DEFAULT NULL COMMENT '套餐编号',
    `package_type`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '老年餐' COMMENT '套餐类型',
    `package_name`       varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '套餐名称',
    `remark`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '特殊要求',
    `created_by`         bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`         bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`        datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`        datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`        datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`order_food_item_id`) USING BTREE,
    INDEX                `order_id`(`order_id`) USING BTREE,
    INDEX                `time`(`delivery_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3106521 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单附表-套餐附表表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_intent
-- ----------------------------
DROP TABLE IF EXISTS `order_intent`;
CREATE TABLE `order_intent`
(
    `order_intent_order`       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `cust_uid`                 bigint(20) UNSIGNED NOT NULL COMMENT '客户编号',
    `customer_phone`           varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户联系电话',
    `customer_note`            varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户要求',
    `customer_source`          varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户来源',
    `customer_request_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '客户要求',
    `budget_amount`            decimal(7, 2) NULL DEFAULT NULL COMMENT '预算金额',
    `sales_employee_uid`       bigint(20) NULL DEFAULT NULL COMMENT '业务员',
    `intent_status`            varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待跟进' COMMENT '意向状态（待跟进、跟进中、已签约、失效）',
    `created_by`               bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`               bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`              datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`              datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`              datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`order_intent_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '意向订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_main
-- ----------------------------
DROP TABLE IF EXISTS `order_main`;
CREATE TABLE `order_main`
(
    `id`                 bigint(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单Id',
    `order_id`           varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '订单号',
    `order_type`         tinyint(3) UNSIGNED NOT NULL DEFAULT 1 COMMENT '订单类型(1:套餐订单、2:助洁服务、3:六助服务、4:医护服务、5:日间照料、6:修脚足疗、7:理发服务、8:康复理疗)',
    `order_status`       varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待付款' COMMENT '(待付款、待完成、已完成、待审核、申请退款、已退款、取消订单)',
    `order_confirm_time` datetime NULL DEFAULT NULL COMMENT '订单支付时间',
    `product_name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
    `product_image`      varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'https://img.zkshlm.com/zksh/no.png' COMMENT '商品图片',
    `cust_uid`           bigint(20) NOT NULL COMMENT '客户编号',
    `is_pay_status`      tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付状态(1:已支付、-1:已退款)',
    `pay_price`          decimal(8, 2)                                                 NOT NULL DEFAULT 0.00 COMMENT '支付金额',
    `remark`             varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_by`         bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`         bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`        datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`        datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`        datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_record
-- ----------------------------
DROP TABLE IF EXISTS `order_record`;
CREATE TABLE `order_record`
(
    `order_record_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单记录Id',
    `order_id`        varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '订单id',
    `change_type`     tinyint(4) NOT NULL DEFAULT 0 COMMENT '操作类型（0:订单信息变更、1:套餐订单配送信息变更）',
    `change_message`  varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '操作备注',
    `change_time`     int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作时间',
    PRIMARY KEY (`order_record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单状态记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_service
-- ----------------------------
DROP TABLE IF EXISTS `order_service`;
CREATE TABLE `order_service`
(
    `order_service_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id`              varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主订单ID',
    `cust_uid`              bigint(20) UNSIGNED NOT NULL COMMENT '客户编号',
    `order_no`              varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方订单编号',
    `customer_phone`        varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户联系电话',
    `customer_note`         varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户备注',
    `service_start_time`    datetime                                                     NOT NULL COMMENT '服务开始时间',
    `service_end_time`      datetime                                                     NOT NULL COMMENT '服务结束时间',
    `service_confirm_time`  datetime NULL DEFAULT NULL COMMENT '服务确认上户时间',
    `service_down_time`     datetime NULL DEFAULT NULL COMMENT '服务下户时间',
    `service_duration_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实际有效工时',
    `is_outsourc`           tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否外包(1:自有服务、2:其他部门、3:其他公司)',
    `employee_uid`          bigint(20) NULL DEFAULT NULL COMMENT '员工编号',
    `employee_salary`       decimal(8, 2) NULL DEFAULT 0.00 COMMENT '员工收入',
    `created_by`            bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`            bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`           datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`           datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`order_service_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单服务附表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for order_transaction
-- ----------------------------
DROP TABLE IF EXISTS `order_transaction`;
CREATE TABLE `order_transaction`
(
    `order_transaction_id`     bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单交易ID',
    `order_id`                 varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '订单主表Id',
    `from_cust_account_id`     bigint(20) NOT NULL DEFAULT 0 COMMENT '支付方账户Id',
    `from_amount`              decimal(8, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '支付方支出金额',
    `to_account_type`          tinyint(4) NOT NULL DEFAULT 1 COMMENT '(1:员工、2:企业)',
    `to_account_id`            bigint(20) NOT NULL DEFAULT 0 COMMENT '接收方账户',
    `to_amount`                decimal(8, 2)                                                NOT NULL DEFAULT 0.00 COMMENT '接收方收入金额',
    `tax_rate`                 decimal(4, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '税点',
    `amount_taxed`             decimal(10, 2)                                               NOT NULL DEFAULT 0.00 COMMENT '税费',
    `order_transaction_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（0:预支付、1:已支付、2:处理中、4:退款中、-1:已退款）',
    `created_by`               bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`               bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`              datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`              datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`              datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`order_transaction_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单附表-交易表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_main
-- ----------------------------
DROP TABLE IF EXISTS `product_main`;
CREATE TABLE `product_main`
(
    `product_id`           bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `product_payment_type` tinyint(1) NULL DEFAULT 1 COMMENT '商品支付类型（1:预支付订单、2:信用订单、3:长期订单、4:第三方订单）',
    PRIMARY KEY (`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for record_logs
-- ----------------------------
DROP TABLE IF EXISTS `record_logs`;
CREATE TABLE `record_logs`
(
    `logs_id`       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '日志id',
    `logs_classify` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志分类',
    `link_id`       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联id',
    `logs_content`  varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '日志内容',
    `created_by`    bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`    bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`   datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`   datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`   datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`logs_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 249 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for smart_water
-- ----------------------------
DROP TABLE IF EXISTS `smart_water`;
CREATE TABLE `smart_water`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `sn`               varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备SN码',
    `pkey`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备IMEI',
    `cust_uid`         bigint(20) NULL DEFAULT NULL COMMENT '绑定老人ID',
    `third_user_id`    varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方设备ID',
    `online`           int(4) NULL DEFAULT NULL COMMENT '	\r\n1 在线 0 离线',
    `csq`              int(4) NULL DEFAULT NULL COMMENT '信号强度',
    `last_active_time` datetime NULL DEFAULT NULL COMMENT '上次活动时间',
    `create_time`      datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`      datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`      datetime NULL DEFAULT NULL COMMENT '删除时间',
    `created_by`       bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`       bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `smart_sn`(`sn`) USING BTREE COMMENT '设备ID唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for smart_water_event
-- ----------------------------
DROP TABLE IF EXISTS `smart_water_event`;
CREATE TABLE `smart_water_event`
(
    `id`               bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `device_id`        varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备Id',
    `event_type`       varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件类型',
    `last_active_time` datetime NULL DEFAULT NULL COMMENT '	\r\n上次活动时间',
    `last_start_time`  datetime NULL DEFAULT NULL COMMENT '	\r\n最近采集开始时间',
    `last_end_time`    datetime NULL DEFAULT NULL COMMENT '	\r\n最近采集结束时间',
    `csq`              int(4) NULL DEFAULT 0 COMMENT '	\r\n信号强度',
    `online`           int(4) NULL DEFAULT 1 COMMENT '1 在线 0 离线',
    `last_mun`         int(10) NULL DEFAULT NULL COMMENT '	\r\n最近上传周期数',
    `event_desc`       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '事件描述',
    `water`            float(8, 1
) NULL DEFAULT NULL COMMENT '用水量',
  `ts` datetime NULL DEFAULT NULL COMMENT '用水时间',
  `alarm_head_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警事件ID',
  `alarm_time` datetime NULL DEFAULT NULL COMMENT '报警时间',
  `alarm_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '	\r\n报警类型',
  `alarm_content` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警内容',
  `alarm_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50098 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_area
-- ----------------------------
DROP TABLE IF EXISTS `sys_area`;
CREATE TABLE `sys_area`
(
    `id`             bigint(20) NOT NULL COMMENT '区划代码',
    `name`           varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
    `pid`            bigint(20) NULL DEFAULT NULL COMMENT '父级区划代码',
    `province_code`  bigint(20) NULL DEFAULT NULL COMMENT '省/直辖市代码',
    `city_code`      bigint(20) NULL DEFAULT NULL COMMENT '市代码',
    `area_code`      bigint(20) NULL DEFAULT NULL COMMENT '区/县代码',
    `street_code`    bigint(20) NULL DEFAULT NULL COMMENT '街道/镇代码',
    `committee_code` bigint(20) NULL DEFAULT NULL COMMENT '社区/乡村代码',
    `committee_type` bigint(20) NULL DEFAULT NULL COMMENT '城乡分类代码',
    `sort`           int(11) NULL DEFAULT NULL COMMENT '排序',
    `level`          int(11) NULL DEFAULT NULL COMMENT '级别: 1-省/直辖市, 2-市, 3-区/县/地级市, 4-街道/镇, 5-社区/乡村',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_group
-- ----------------------------
DROP TABLE IF EXISTS `sys_group`;
CREATE TABLE `sys_group`
(
    `sys_group_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `sys_group_pid`     int(11) NOT NULL DEFAULT 0 COMMENT '上级id',
    `sys_group_type`    tinyint(1) NOT NULL DEFAULT 1 COMMENT '类型（1:订单标签、2:服务标签、3:客户标签、4:员工标签）',
    `sys_group_name`    varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名称',
    `sys_group_is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示(1:展示、-1:不展示)',
    `sort`              int(11) NOT NULL DEFAULT 0 COMMENT '排序',
    `created_by`        bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`        bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`       datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`       datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`       datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`sys_group_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标签分组表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_label
-- ----------------------------
DROP TABLE IF EXISTS `sys_label`;
CREATE TABLE `sys_label`
(
    `sys_label_id`   bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `sys_group_id`   bigint(20) UNSIGNED NOT NULL,
    `sys_label_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签名称',
    `sort`           int(11) NOT NULL DEFAULT 0 COMMENT '排序',
    `created_by`     bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`     bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`    datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`    datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`    datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`sys_label_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`
(
    `sys_notice_id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `sys_notice_relation_id` bigint(20) NULL DEFAULT NULL COMMENT '通知关联编号',
    `sys_notice_type`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '1' COMMENT '通知类型',
    `sys_notice_title`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '通知标题',
    `sys_notice_content`     varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '通知内容',
    `sys_notice_uid`         bigint(20) NULL DEFAULT NULL COMMENT '通知人',
    `sys_notice_method`      varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '微信公众号通知' COMMENT '通知方式',
    `sys_notice_time`        datetime NULL DEFAULT NULL COMMENT '通知提醒时间',
    `sys_notice_status`      tinyint(1) NULL DEFAULT -1 COMMENT '通知状态(-1:未通知、1:已通知)',
    `created_by`             bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`             bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`            datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`            datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`sys_notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_oauth_code
-- ----------------------------
DROP TABLE IF EXISTS `sys_oauth_code`;
CREATE TABLE `sys_oauth_code`
(
    `sys_code_id`  bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '授权编号',
    `code`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '授权码',
    `token`        varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `third_v1_uid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `is_status`    tinyint(1) NOT NULL DEFAULT 1 COMMENT '授权状态',
    `expire_time`  datetime                                                      NOT NULL COMMENT '有效期',
    `update_time`  datetime NULL DEFAULT NULL,
    `create_time`  datetime                                                      NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`sys_code_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35262 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_record
-- ----------------------------
DROP TABLE IF EXISTS `sys_record`;
CREATE TABLE `sys_record`
(
    `sys_record_id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `sys_record_relation_id` bigint(20) NULL DEFAULT NULL COMMENT '记录关联编号',
    `sys_record_type`        tinyint(4) NULL DEFAULT 1 COMMENT '记录类型',
    `sys_record_dept`        varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '记录人部门',
    `sys_record_content`     varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '记录内容',
    `sys_record_file`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '记录附件',
    `created_by`             bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
    `updated_by`             bigint(20) NULL DEFAULT NULL COMMENT '更新人',
    `create_time`            datetime NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`            datetime NULL DEFAULT NULL COMMENT '修改时间',
    `delete_time`            datetime NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`sys_record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据变更记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_table_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_table_log`;
CREATE TABLE `sys_table_log`
(
    `record_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `table_name`     varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
    `table_id`       bigint(20) NULL DEFAULT NULL COMMENT '主键id',
    `change_message` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更记录',
    `change_time`    datetime NULL DEFAULT NULL,
    PRIMARY KEY (`record_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wx_store_category
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_category`;
CREATE TABLE `wx_store_category`
(
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品分类表ID',
    `pid`        int(11) NOT NULL DEFAULT 0 COMMENT '父id',
    `cate_name`  varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
    `sort`       int(11) NOT NULL DEFAULT 0 COMMENT '排序',
    `pic`        varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图标',
    `is_show`    tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否推荐',
    `big_pic`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分类大图',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_external_order
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_external_order`;
CREATE TABLE `wx_store_external_order`
(
    `id`          bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `external_id` bigint(20) UNSIGNED NULL DEFAULT NULL,
    `order_id`    bigint(20) UNSIGNED NULL DEFAULT 0,
    `type`        varchar(666) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `created_at`  datetime NULL DEFAULT NULL,
    `updated_at`  datetime NULL DEFAULT NULL,
    `deleted_at`  datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order`;
CREATE TABLE `wx_store_order`
(
    `order_id`       bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_type`     tinyint(1) NULL DEFAULT 0 COMMENT '订单类型（0:普通订单、1:家政订单、2:生活服务订单）',
    `order_no`       varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方订单号',
    `product_id`     bigint(20) UNSIGNED NULL DEFAULT NULL,
    `sku_id`         bigint(20) NULL DEFAULT 0 COMMENT '商品规格Id',
    `home_id`        bigint(20) NULL DEFAULT NULL,
    `created_by`     bigint(20) NULL DEFAULT NULL,
    `updated_by`     bigint(20) NULL DEFAULT NULL,
    `created_at`     datetime NULL DEFAULT NULL,
    `updated_at`     datetime NULL DEFAULT NULL,
    `deleted_at`     datetime NULL DEFAULT NULL,
    `uid`            bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '微信用户id',
    `main_uid`       bigint(20) NULL DEFAULT NULL COMMENT '老人id',
    `real_name`      varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '用户姓名',
    `user_phone`     varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '用户电话',
    `user_address`   varchar(800) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
    `user_notes`     varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `user_longitude` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `user_latitude`  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `total_num`      int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单商品总数',
    `total_price`    decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单总价',
    `pay_price`      decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
    `pay_info`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '支付信息',
    `pay_type`       varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '支付方式',
    `pay_time`       datetime NULL DEFAULT NULL,
    `pay_status`     tinyint(1) NULL DEFAULT 0 COMMENT '0 未支付 1已支付 -1已退款',
    `order_status`   int(11) NULL DEFAULT 0 COMMENT '订单状态（-2:订单已退款、-1:订单已取消、0:订单待支付、1:订单已支付、2:订单已完成、3:订单退款中）',
    `refund_status`  tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 未退款 1 申请中 2 已退款',
    `refund_type`    tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款申请类型',
    `order_info`     text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '商品详情',
    `is_admin`       tinyint(1) NULL DEFAULT 0 COMMENT '(0:客户端下单、1:后台下单)',
    `is_comment`     tinyint(1) NULL DEFAULT 0 COMMENT '(0:未评价、1:已评价)',
    `remark`         varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '管理员备注',
    PRIMARY KEY (`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 984 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order_bill
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order_bill`;
CREATE TABLE `wx_store_order_bill`
(
    `bill_id`                bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id`               bigint(20) UNSIGNED NULL DEFAULT NULL,
    `created_by`             bigint(20) NULL DEFAULT NULL,
    `updated_by`             bigint(20) NULL DEFAULT NULL,
    `created_at`             datetime NULL DEFAULT NULL,
    `updated_at`             datetime NULL DEFAULT NULL,
    `deleted_at`             datetime NULL DEFAULT NULL,
    `payment_voucher_number` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费凭证单号',
    `payment_voucher_image`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费凭证图片',
    `amount_due`             decimal(9, 2) NULL DEFAULT 0.00 COMMENT '应缴费金额',
    `amount_paid`            decimal(9, 2) NULL DEFAULT 0.00 COMMENT '实际缴费金额',
    `payment_method`         varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缴费方式',
    `payment_time`           datetime NULL DEFAULT NULL COMMENT '缴费时间',
    `payment_status`         varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待审核' COMMENT '缴费状态',
    `auditor_id`             bigint(20) NULL DEFAULT NULL COMMENT '审核员',
    `auditor_time`           datetime NULL DEFAULT NULL COMMENT '审核时间',
    `admin_remark`           varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管理员备注',
    PRIMARY KEY (`bill_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order_package
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order_package`;
CREATE TABLE `wx_store_order_package`
(
    `id`            bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `created_by`    bigint(20) NULL DEFAULT NULL,
    `updated_by`    bigint(20) NULL DEFAULT NULL,
    `created_at`    datetime NULL DEFAULT NULL,
    `updated_at`    datetime NULL DEFAULT NULL,
    `deleted_at`    datetime NULL DEFAULT NULL,
    `food_order_id` bigint(20) UNSIGNED NULL DEFAULT NULL,
    `wx_order_id`   bigint(20) NULL DEFAULT NULL,
    `item_id`       bigint(20) UNSIGNED NULL DEFAULT NULL,
    `num`           int(11) NULL DEFAULT 0,
    `package_id`    bigint(20) NULL DEFAULT NULL,
    `price`         decimal(6, 2) NULL DEFAULT 0.00,
    `time`          date NULL DEFAULT NULL,
    `fee`           decimal(6, 2) NULL DEFAULT 0.00,
    `status`        tinyint(1) NULL DEFAULT 1,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10916 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order_refund
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order_refund`;
CREATE TABLE `wx_store_order_refund`
(
    `refund_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '退款单id',
    `refund_info`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `order_id`       bigint(20) UNSIGNED NOT NULL COMMENT '支付订单id',
    `auditor_id`     bigint(20) NULL DEFAULT NULL,
    `auditor_time`   datetime NULL DEFAULT NULL,
    `refund_sn`      varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '退款单号',
    `uid`            bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户 id',
    `phone`          varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '联系电话',
    `user_mark`      varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `admin_mark`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户备注',
    `pics`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
    `refund_type`    tinyint(1) NOT NULL DEFAULT 1 COMMENT '退款类型 1:退款 2:退款退货',
    `refund_card`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款卡号',
    `refund_message` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '退款原因',
    `refund_price`   decimal(8, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '退款金额',
    `fail_message`   varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '未通过原因',
    `refund_status`  varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '待审核' COMMENT '退款状态',
    `created_by`     bigint(20) NULL DEFAULT NULL,
    `updated_by`     bigint(20) NULL DEFAULT NULL,
    `created_at`     datetime NULL DEFAULT NULL,
    `updated_at`     datetime NULL DEFAULT NULL,
    `deleted_at`     datetime NULL DEFAULT NULL,
    PRIMARY KEY (`refund_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order_service
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order_service`;
CREATE TABLE `wx_store_order_service`
(
    `id`              bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id`        bigint(20) NULL DEFAULT NULL,
    `uid`             bigint(20) NULL DEFAULT NULL,
    `created_by`      bigint(20) NULL DEFAULT NULL,
    `updated_by`      bigint(20) NULL DEFAULT NULL,
    `created_at`      datetime NULL DEFAULT NULL,
    `updated_at`      datetime NULL DEFAULT NULL,
    `deleted_at`      datetime NULL DEFAULT NULL,
    `service_info`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务信息',
    `user_info`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '客户信息',
    `service_time`    datetime NULL DEFAULT NULL COMMENT '服务时间',
    `payment_mode`    tinyint(1) NULL DEFAULT 0 COMMENT '(0:先付款再服务、1:先服务再付款)',
    `remark`          varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务备注',
    `status`          tinyint(1) NULL DEFAULT 0 COMMENT '(0:待分配服务、1:已分配服务、2:上户中服务、3:下户中服务、4已完成服务、-1服务已失效)',
    `signature_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签字图片',
    `signatory`       varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '签字人身份',
    `evaluation`      varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评价',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 310 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order_status
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order_status`;
CREATE TABLE `wx_store_order_status`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id`       bigint(20) UNSIGNED NOT NULL,
    `created_by`     bigint(20) NULL DEFAULT NULL,
    `updated_by`     bigint(20) NULL DEFAULT NULL,
    `created_at`     datetime NULL DEFAULT NULL,
    `updated_at`     datetime NULL DEFAULT NULL,
    `deleted_at`     datetime NULL DEFAULT NULL,
    `change_type`    varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '操作类型',
    `change_message` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '操作备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1893 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_order_sync
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_order_sync`;
CREATE TABLE `wx_store_order_sync`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id`   bigint(20) NULL DEFAULT NULL,
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    `order_type` tinyint(1) NULL DEFAULT 0 COMMENT '(0:家政订单、1:保洁订单)',
    `table_id`   bigint(20) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product`;
CREATE TABLE `wx_store_product`
(
    `product_id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `created_by`            bigint(20) NULL DEFAULT NULL,
    `updated_by`            bigint(20) NULL DEFAULT NULL,
    `created_at`            datetime NULL DEFAULT NULL,
    `updated_at`            datetime NULL DEFAULT NULL,
    `deleted_at`            datetime NULL DEFAULT NULL,
    `recommend_image`       varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '推荐图',
    `slider_image`          varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '轮播图',
    `redirect_url`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '跳转Url',
    `store_name`            varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商品名称',
    `store_info`            varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商品简介',
    `cate_id`               varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci   NOT NULL DEFAULT '' COMMENT '分类id',
    `price_str`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '价格区间描述',
    `price`                 decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品价格',
    `unit`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '次' COMMENT '商品单位',
    `sort`                  smallint(6) NOT NULL DEFAULT 0 COMMENT '排序',
    `sales`                 mediumint(8) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
    `stock`                 mediumint(9) NOT NULL DEFAULT 0 COMMENT '库存',
    `is_show`               tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（0：未上架，1：上架）',
    `spu`                   char(19) CHARACTER SET utf8 COLLATE utf8_general_ci      NOT NULL DEFAULT '' COMMENT '商品SPU',
    `type`                  tinyint(1) NULL DEFAULT 1 COMMENT '规格（0：多规格，1：单规格）',
    `service_type`          tinyint(1) NULL DEFAULT 0 COMMENT '(0:无服务、1:家政服务、2:生活服务、3:第三方服务)',
    `is_phone`              tinyint(1) NULL DEFAULT 0 COMMENT '(0:非电话咨询、1:电话咨询)',
    `payment_mode`          tinyint(1) NULL DEFAULT 0 COMMENT '(0:先付款再服务、1:先服务再付款)',
    `phone`                 varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `home_id`               bigint(20) NULL DEFAULT NULL COMMENT '第三方id',
    `purchase_instructions` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '购买须知',
    `detail_type`           tinyint(1) NULL DEFAULT 1 COMMENT '1:默认类型，2：养老餐、3:单点',
    `ori_price`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原价描述',
    `price_desc_type`       tinyint(1) NULL DEFAULT 1 COMMENT '1:默认价格，2:单独说明',
    `price_desc`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '价格描述',
    `min_store_num`         int(11) NULL DEFAULT 1 COMMENT '最小购买数量',
    `button_type`           tinyint(1) NULL DEFAULT 1 COMMENT '按钮类型',
    PRIMARY KEY (`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 237 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_attr
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_attr`;
CREATE TABLE `wx_store_product_attr`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    `rule`       text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '属性名',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 85 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_attr_value
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_attr_value`;
CREATE TABLE `wx_store_product_attr_value`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `product_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
    `home_id`    bigint(20) NULL DEFAULT NULL COMMENT '第三方id',
    `sku`        varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品属性索引值 (attr_value|attr_value[|....])',
    `stock`      int(11) NOT NULL DEFAULT 0 COMMENT '属性对应的库存',
    `sales`      int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
    `price`      decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '属性金额',
    `image`      varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
    `unique`     char(32) CHARACTER SET utf8 COLLATE utf8_general_ci     NOT NULL DEFAULT '' COMMENT '唯一值',
    `bar_code`   varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL DEFAULT '' COMMENT '商品条码',
    `weight`     decimal(8, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '重量',
    `volume`     decimal(8, 2)                                           NOT NULL DEFAULT 0.00 COMMENT '体积',
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_cate
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_cate`;
CREATE TABLE `wx_store_product_cate`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `product_id` int(11) NOT NULL DEFAULT 0 COMMENT '商品id',
    `cate_id`    int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
    `cate_pid`   int(11) NOT NULL DEFAULT 0 COMMENT '一级分类id',
    `status`     tinyint(1) NOT NULL DEFAULT 0 COMMENT '商品状态',
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 838 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_description
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_description`;
CREATE TABLE `wx_store_product_description`
(
    `product_id`  int(11) NOT NULL DEFAULT 0 COMMENT '商品ID',
    `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '商品详情',
    `created_by`  bigint(20) NULL DEFAULT NULL,
    `updated_by`  bigint(20) NULL DEFAULT NULL,
    `created_at`  datetime NULL DEFAULT NULL,
    `updated_at`  datetime NULL DEFAULT NULL,
    `deleted_at`  datetime NULL DEFAULT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_reply
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_reply`;
CREATE TABLE `wx_store_product_reply`
(
    `reply_id`      bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `uid`           bigint(20) NOT NULL COMMENT '用户ID',
    `order_id`      bigint(20) NOT NULL COMMENT '支付订单ID',
    `product_id`    bigint(20) NOT NULL COMMENT '商品id',
    `product_score` tinyint(1) NOT NULL COMMENT '服务分数',
    `comment`       varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '评论内容',
    `nickname`      varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci  NOT NULL COMMENT '用户名称',
    `avatar`        varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户头像',
    `created_by`    bigint(20) NULL DEFAULT NULL,
    `updated_by`    bigint(20) NULL DEFAULT NULL,
    `created_at`    datetime NULL DEFAULT NULL,
    `updated_at`    datetime NULL DEFAULT NULL,
    `deleted_at`    datetime NULL DEFAULT NULL,
    PRIMARY KEY (`reply_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_rule
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_rule`;
CREATE TABLE `wx_store_product_rule`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `rule_name`  varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '规格名称',
    `rule_value` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '规格值',
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_status
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_status`;
CREATE TABLE `wx_store_product_status`
(
    `id`             bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `product_id`     bigint(20) NOT NULL,
    `sku_id`         bigint(20) NULL DEFAULT 0,
    `change_type`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `change_message` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `deleted_at`     datetime NULL DEFAULT NULL,
    `created_at`     datetime NULL DEFAULT NULL,
    `updated_at`     datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 566 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_store_product_sync
-- ----------------------------
DROP TABLE IF EXISTS `wx_store_product_sync`;
CREATE TABLE `wx_store_product_sync`
(
    `id`              bigint(20) NOT NULL,
    `created_by`      bigint(20) NULL DEFAULT NULL,
    `updated_by`      bigint(20) NULL DEFAULT NULL,
    `created_at`      datetime NULL DEFAULT NULL,
    `updated_at`      datetime NULL DEFAULT NULL,
    `deleted_at`      datetime NULL DEFAULT NULL,
    `product_id`      bigint(20) NULL DEFAULT NULL,
    `product_attr_id` bigint(20) NULL DEFAULT NULL,
    `type`            tinyint(1) NULL DEFAULT 0 COMMENT '(0:单规格、1:多规格)',
    `sync_table`      varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '同步table名称',
    `sync_table_id`   varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '同步表主键',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_user
-- ----------------------------
DROP TABLE IF EXISTS `wx_user`;
CREATE TABLE `wx_user`
(
    `uid`                 bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'H5科苑养老登录用户id',
    `created_by`          bigint(20) NULL DEFAULT NULL,
    `updated_by`          bigint(20) NULL DEFAULT NULL,
    `created_at`          datetime NULL DEFAULT NULL,
    `updated_at`          datetime NULL DEFAULT NULL,
    `deleted_at`          datetime NULL DEFAULT NULL,
    `truename`            varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
    `phone`               varchar(66) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
    `nickname`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '昵称',
    `headimgurl`          varchar(455) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '头像',
    `sex`                 varchar(22) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '0' COMMENT '性别',
    `user_address`        varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
    `user_address_detail` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
    `birthday`            varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `unionid`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `mp_openid`           varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信小程序openid',
    `h5_openid`           varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'H5openid',
    `ip`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `register_source`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `role`                varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '普通用户' COMMENT '角色',
    `admin_remark`        varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '无' COMMENT '后台管理员备注',
    PRIMARY KEY (`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1525 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'H5用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for wx_user_address
-- ----------------------------
DROP TABLE IF EXISTS `wx_user_address`;
CREATE TABLE `wx_user_address`
(
    `id`         bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `uid`        bigint(20) NULL DEFAULT NULL,
    `name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `phone`      char(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `area`       varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `address`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `longitude`  varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
    `latitude`   varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
    `is_default` int(11) NULL DEFAULT 0 COMMENT '0非默认、1默认',
    `sort`       int(11) NULL DEFAULT 100,
    `created_by` bigint(20) NULL DEFAULT NULL,
    `updated_by` bigint(20) NULL DEFAULT NULL,
    `created_at` datetime NULL DEFAULT NULL,
    `updated_at` datetime NULL DEFAULT NULL,
    `deleted_at` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for zk_order_log
-- ----------------------------
DROP TABLE IF EXISTS `zk_order_log`;
CREATE TABLE `zk_order_log`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id`    varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `old_price`   decimal(10, 2) NULL DEFAULT NULL,
    `new_price`   decimal(10, 2) NULL DEFAULT NULL,
    `create_time` datetime NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

SET
FOREIGN_KEY_CHECKS = 1;
