<!doctype html><html lang="en" class="theme-light"><head><script>self["MonacoEnvironment"] = (function (paths) {
          return {
            globalAPI: false,
            getWorkerUrl : function (moduleId, label) {
              var result =  paths[label];
              if (/^((http:)|(https:)|(file:)|(\/\/))/.test(result)) {
                var currentUrl = String(window.location);
                var currentOrigin = currentUrl.substr(0, currentUrl.length - window.location.hash.length - window.location.search.length - window.location.pathname.length);
                if (result.substring(0, currentOrigin.length) !== currentOrigin) {
                  var js = '/*' + label + '*/importScripts("' + result + '");';
                  var blob = new Blob([js], { type: 'application/javascript' });
                  return URL.createObjectURL(blob);
                }
              }
              return result;
            }
          };
        })({
  "editorWorkerService": "./monacoeditorwork/editor.worker.bundle.js",
  "json": "./monacoeditorwork/json.worker.bundle.js",
  "typescript": "./monacoeditorwork/ts.worker.bundle.js",
  "javascript": "./monacoeditorwork/ts.worker.bundle.js"
});</script><meta charset="UTF-8"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta http-equiv="X-UA-Compatible" content="IE=edge"><link rel="icon" href="./favicon.ico"/><title>Apidoc</title><script type="module" crossorigin src="./assets/index.d0b2fdb6.js"></script><link rel="stylesheet" href="./assets/index.bb5f7dec.css"></head><body><div id="app"></div><script src="./config.js?v=3.1.1"></script></body></html>