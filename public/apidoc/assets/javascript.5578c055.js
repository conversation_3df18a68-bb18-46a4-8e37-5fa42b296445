import{conf as e,language as t}from"./typescript.a73e8ddd.js";import"./index.d0b2fdb6.js";
/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.33.0(4b1abad427e58dbedc1215d99a0902ffc885fcd4)
 * Released under the MIT license
 * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
 *-----------------------------------------------------------------------------*/var s=e,i={defaultToken:"invalid",tokenPostfix:".js",keywords:["break","case","catch","class","continue","const","constructor","debugger","default","delete","do","else","export","extends","false","finally","for","from","function","get","if","import","in","instanceof","let","new","null","return","set","super","switch","symbol","this","throw","true","try","typeof","undefined","var","void","while","with","yield","async","await","of"],typeKeywords:[],operators:t.operators,symbols:t.symbols,escapes:t.escapes,digits:t.digits,octaldigits:t.octaldigits,binarydigits:t.binarydigits,hexdigits:t.hexdigits,regexpctl:t.regexpctl,regexpesc:t.regexpesc,tokenizer:t.tokenizer};export{s as conf,i as language};
