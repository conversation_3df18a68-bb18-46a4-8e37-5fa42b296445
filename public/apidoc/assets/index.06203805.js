var __defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=(e,t,n)=>t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,__spreadValues=(e,t)=>{for(var n in t||(t={}))__hasOwnProp.call(t,n)&&__defNormalProp(e,n,t[n]);if(__getOwnPropSymbols)for(var n of __getOwnPropSymbols(t))__propIsEnum.call(t,n)&&__defNormalProp(e,n,t[n]);return e},__spreadProps=(e,t)=>__defProps(e,__getOwnPropDescs(t)),__async=(e,t,n)=>new Promise(((a,r)=>{var o=e=>{try{i(n.next(e))}catch(t){r(t)}},l=e=>{try{i(n.throw(e))}catch(t){r(t)}},i=e=>e.done?a(e.value):Promise.resolve(e.value).then(o,l);i((n=n.apply(e,t)).next())}));import{d as defineComponent,u as useConfigInject,h as getPropsSlot,c as createVNode,Y as _objectSpread2,P as PropTypes,a1 as DownOutlined,a2 as Dropdown,a as flattenChildren,a3 as Menu,a4 as warning,O as _typeof,a5 as cloneVNode,_ as _defineProperty$7,a6 as _toConsumableArray,a7 as AntdIcon,a8 as inject,K as ref,$ as cloneElement,a9 as classNames,C as Col,aa as isStringElement,e as isEmptyElement,ab as initDefaultProps,ac as provide,ad as toRef,j as computed,S as watch,W as _extends,M as useBreakpoint,N as eagerComputed,Q as responsiveArray,ae as __unplugin_components_2$1,R as Row,af as Spin,V as onMounted,ag as KeyCode,w as withInstall,ah as useDestroyed,ai as isEmptyContent,X as ResizeObserver,aj as filterEmpty,ak as LocaleReceiver,al as CodeEditor,am as h,l as useAppStore,A as useI18n,an as useSlots,q as openBlock,s as createElementBlock,H as createBlock,z as withCtx,r as renderSlot,y as createCommentVNode,v as unref,ao as normalizeProps,ap as mergeProps,F as Fragment,aq as CheckOutlined,x as toDisplayString,ar as textToHtml,B as createTextVNode,E as __unplugin_components_1,as as cloneDeep,n as reactive,t as createBaseVNode,at as __unplugin_components_4,T as Tabs,au as commonjsGlobal,av as createIdcard,aw as trim,p as watchEffect,ax as EditTable,ay as createRandKey,az as useApidocOutsideStore,aA as useAppOutsideStore,aB as getObjectValueByKey,aC as isArray,aD as isObject,aE as request,m as useApidocStore,aF as Button,aG as __unplugin_components_2$2,D as renderList,I as __unplugin_components_1$1,aH as Badge,k as _export_sfc,aI as normalizeClass,aJ as __unplugin_components_0$1,o as __unplugin_components_0$2,aK as useRoute,aL as Error$1,aM as globalApi,aN as handleTableDataRowKey,aO as handleApidocHttpError,aP as RedoOutlined,aQ as DeviceEnum,G as normalizeStyle,aR as withDirectives,aS as vModelText,aT as CopyOutlined,aU as copyTextToClipboard,aV as message,aW as __unplugin_components_3$1,aX as SelectOption}from"./index.d0b2fdb6.js";import{A as Avatar}from"./index.0b2cc4f2.js";import{m as mdModal,M as Markdown,S as Skeleton}from"./Skeleton.aa5f4cc6.js";var __rest$3=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n},breadcrumbItemProps=function(){return{prefixCls:String,href:String,separator:PropTypes.any,overlay:PropTypes.any,onClick:Function}},BreadcrumbItem=defineComponent({name:"ABreadcrumbItem",inheritAttrs:!1,__ANT_BREADCRUMB_ITEM:!0,props:breadcrumbItemProps(),slots:["separator","overlay"],setup:function(e,t){var n=t.slots,a=t.attrs,r=useConfigInject("breadcrumb",e).prefixCls;return function(){var t,o,l,i,s,u=null!==(t=getPropsSlot(n,e,"separator"))&&void 0!==t?t:"/",c=getPropsSlot(n,e),d=a.class,p=a.style,m=__rest$3(a,["class","style"]);return o=void 0!==e.href?createVNode("a",_objectSpread2({class:"".concat(r.value,"-link"),onClick:e.onClick},m),[c]):createVNode("span",_objectSpread2({class:"".concat(r.value,"-link"),onClick:e.onClick},m),[c]),l=o,i=r.value,o=(s=getPropsSlot(n,e,"overlay"))?createVNode(Dropdown,{overlay:s,placement:"bottom"},{default:function(){return[createVNode("span",{class:"".concat(i,"-overlay-link")},[l,createVNode(DownOutlined,null,null)])]}}):l,c?createVNode("span",{class:d,style:p},[o,u&&createVNode("span",{class:"".concat(r.value,"-separator")},[u])]):null}}}),breadcrumbProps=function(){return{prefixCls:String,routes:{type:Array},params:PropTypes.any,separator:PropTypes.any,itemRender:{type:Function}}};function getBreadcrumbName(e,t){if(!e.breadcrumbName)return null;var n=Object.keys(t).join("|");return e.breadcrumbName.replace(new RegExp(":(".concat(n,")"),"g"),(function(e,n){return t[n]||e}))}function defaultItemRender(e){var t=e.route,n=e.params,a=e.routes,r=e.paths,o=a.indexOf(t)===a.length-1,l=getBreadcrumbName(t,n);return o?createVNode("span",null,[l]):createVNode("a",{href:"#/".concat(r.join("/"))},[l])}var Breadcrumb=defineComponent({name:"ABreadcrumb",props:breadcrumbProps(),slots:["separator","itemRender"],setup:function(e,t){var n=t.slots,a=useConfigInject("breadcrumb",e),r=a.prefixCls,o=a.direction,l=function(e,t){return e=(e||"").replace(/^\//,""),Object.keys(t).forEach((function(n){e=e.replace(":".concat(n),t[n])})),e},i=function(e,t,n){var a=_toConsumableArray(e),r=l(t||"",n);return r&&a.push(r),a};return function(){var t,a,s,u=e.routes,c=e.params,d=void 0===c?{}:c,p=flattenChildren(getPropsSlot(n,e)),m=null!==(a=getPropsSlot(n,e,"separator"))&&void 0!==a?a:"/",f=e.itemRender||n.itemRender||defaultItemRender;u&&u.length>0?s=function(e){var t=e.routes,n=void 0===t?[]:t,a=e.params,r=void 0===a?{}:a,o=e.separator,s=e.itemRender,u=void 0===s?defaultItemRender:s,c=[];return n.map((function(e){var t=l(e.path,r);t&&c.push(t);var a=[].concat(c),s=null;return e.children&&e.children.length&&(s=createVNode(Menu,null,{default:function(){return[e.children.map((function(e){return createVNode(Menu.Item,{key:e.path||e.breadcrumbName},{default:function(){return[u({route:e,params:r,routes:n,paths:i(a,e.path,r)})]}})}))]}})),createVNode(BreadcrumbItem,{overlay:s,separator:o,key:t||e.breadcrumbName},{default:function(){return[u({route:e,params:r,routes:n,paths:a})]}})}))}({routes:u,params:d,separator:m,itemRender:f}):p.length&&(s=p.map((function(e,t){return warning("object"===_typeof(e.type)&&(e.type.__ANT_BREADCRUMB_ITEM||e.type.__ANT_BREADCRUMB_SEPARATOR),"Breadcrumb","Only accepts Breadcrumb.Item and Breadcrumb.Separator as it's children"),cloneVNode(e,{separator:m,key:t})})));var h=(_defineProperty$7(t={},r.value,!0),_defineProperty$7(t,"".concat(r.value,"-rtl"),"rtl"===o.value),t);return createVNode("div",{class:h},[s])}}}),__rest$2=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n},breadcrumbSeparatorProps=function(){return{prefixCls:String}},BreadcrumbSeparator=defineComponent({name:"ABreadcrumbSeparator",__ANT_BREADCRUMB_SEPARATOR:!0,inheritAttrs:!1,props:breadcrumbSeparatorProps(),setup:function(e,t){var n=t.slots,a=t.attrs,r=useConfigInject("breadcrumb",e).prefixCls;return function(){var e;a.separator;var t=a.class,o=__rest$2(a,["separator","class"]),l=flattenChildren(null===(e=n.default)||void 0===e?void 0:e.call(n));return createVNode("span",_objectSpread2({class:["".concat(r.value,"-separator"),t]},o),[l.length>0?l:"/"])}}});Breadcrumb.Item=BreadcrumbItem,Breadcrumb.Separator=BreadcrumbSeparator,Breadcrumb.install=function(e){return e.component(Breadcrumb.name,Breadcrumb),e.component(BreadcrumbItem.name,BreadcrumbItem),e.component(BreadcrumbSeparator.name,BreadcrumbSeparator),e};var ClockCircleOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"},ClockCircleOutlinedSvg=ClockCircleOutlined$2;function _objectSpread$6(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty$6(e,t,n[t])}))}return e}function _defineProperty$6(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ClockCircleOutlined=function(e,t){var n=_objectSpread$6({},e,t.attrs);return createVNode(AntdIcon,_objectSpread$6({},n,{icon:ClockCircleOutlinedSvg}),null)};ClockCircleOutlined.displayName="ClockCircleOutlined",ClockCircleOutlined.inheritAttrs=!1;var ClockCircleOutlined$1=ClockCircleOutlined,listItemMetaProps=function(){return{avatar:PropTypes.any,description:PropTypes.any,prefixCls:String,title:PropTypes.any}},ItemMeta=defineComponent({name:"AListItemMeta",props:listItemMetaProps(),displayName:"AListItemMeta",__ANT_LIST_ITEM_META:!0,slots:["avatar","description","title"],setup:function(e,t){var n=t.slots,a=useConfigInject("list",e).prefixCls;return function(){var t,r,o,l,i,s,u="".concat(a.value,"-item-meta"),c=null!==(t=e.title)&&void 0!==t?t:null===(r=n.title)||void 0===r?void 0:r.call(n),d=null!==(o=e.description)&&void 0!==o?o:null===(l=n.description)||void 0===l?void 0:l.call(n),p=null!==(i=e.avatar)&&void 0!==i?i:null===(s=n.avatar)||void 0===s?void 0:s.call(n),m=createVNode("div",{class:"".concat(a.value,"-item-meta-content")},[c&&createVNode("h4",{class:"".concat(a.value,"-item-meta-title")},[c]),d&&createVNode("div",{class:"".concat(a.value,"-item-meta-description")},[d])]);return createVNode("div",{class:u},[p&&createVNode("div",{class:"".concat(a.value,"-item-meta-avatar")},[p]),(c||d)&&m])}}}),ListContextKey=Symbol("ListContextKey"),__rest$1=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n},listItemProps=function(){return{prefixCls:String,extra:PropTypes.any,actions:PropTypes.array,grid:Object,colStyle:{type:Object,default:void 0}}},__unplugin_components_2=defineComponent({name:"AListItem",inheritAttrs:!1,Meta:ItemMeta,props:listItemProps(),slots:["actions","extra"],setup:function(e,t){var n=t.slots,a=t.attrs,r=inject(ListContextKey,{grid:ref(),itemLayout:ref()}),o=r.itemLayout,l=r.grid,i=useConfigInject("list",e).prefixCls,s=function(){var t,a,r=null!==(t=e.extra)&&void 0!==t?t:null===(a=n.extra)||void 0===a?void 0:a.call(n);return"vertical"===o.value?!!r:!function(){var e,t,a=(null===(e=n.default)||void 0===e?void 0:e.call(n))||[];return a.forEach((function(e){isStringElement(e)&&!isEmptyElement(e)&&(t=!0)})),t&&a.length>1}()};return function(){var t,r,u,c,d,p=a.class,m=__rest$1(a,["class"]),f=i.value,h=null!==(t=e.extra)&&void 0!==t?t:null===(r=n.extra)||void 0===r?void 0:r.call(n),g=null===(u=n.default)||void 0===u?void 0:u.call(n),v=null!==(c=e.actions)&&void 0!==c?c:flattenChildren(null===(d=n.actions)||void 0===d?void 0:d.call(n)),y=(v=v&&!Array.isArray(v)?[v]:v)&&v.length>0&&createVNode("ul",{class:"".concat(f,"-item-action"),key:"actions"},[v.map((function(e,t){return createVNode("li",{key:"".concat(f,"-item-action-").concat(t)},[e,t!==v.length-1&&createVNode("em",{class:"".concat(f,"-item-action-split")},null)])}))]),_=l.value?"div":"li",b=createVNode(_,_objectSpread2(_objectSpread2({},m),{},{class:classNames("".concat(f,"-item"),_defineProperty$7({},"".concat(f,"-item-no-flex"),!s()),p)}),{default:function(){return["vertical"===o.value&&h?[createVNode("div",{class:"".concat(f,"-item-main"),key:"content"},[g,y]),createVNode("div",{class:"".concat(f,"-item-extra"),key:"extra"},[h])]:[g,y,cloneElement(h,{key:"extra"})]]}});return l.value?createVNode(Col,{flex:1,style:e.colStyle},{default:function(){return[b]}}):b}}}),listProps=function(){return{bordered:{type:Boolean,default:void 0},dataSource:PropTypes.array,extra:PropTypes.any,grid:{type:Object,default:void 0},itemLayout:String,loading:{type:[Boolean,Object],default:void 0},loadMore:PropTypes.any,pagination:{type:[Boolean,Object],default:void 0},prefixCls:String,rowKey:[String,Number,Function],renderItem:Function,size:String,split:{type:Boolean,default:void 0},header:PropTypes.any,footer:PropTypes.any,locale:{type:Object}}},List=defineComponent({name:"AList",Item:__unplugin_components_2,props:initDefaultProps(listProps(),{dataSource:[],bordered:!1,split:!0,loading:!1,pagination:!1}),slots:["extra","loadMore","renderItem","header","footer"],setup:function(e,t){var n,a,r=t.slots;provide(ListContextKey,{grid:toRef(e,"grid"),itemLayout:toRef(e,"itemLayout")});var o={current:1,total:0},l=useConfigInject("list",e),i=l.prefixCls,s=l.direction,u=l.renderEmpty,c=computed((function(){return e.pagination&&"object"===_typeof(e.pagination)?e.pagination:{}})),d=ref(null!==(n=c.value.defaultCurrent)&&void 0!==n?n:1),p=ref(null!==(a=c.value.defaultPageSize)&&void 0!==a?a:10);watch(c,(function(){"current"in c.value&&(d.value=c.value.current),"pageSize"in c.value&&(p.value=c.value.pageSize)}));var m=[],f=function(e){return function(t,n){d.value=t,p.value=n,c.value[e]&&c.value[e](t,n)}},h=f("onChange"),g=f("onShowSizeChange"),v=computed((function(){return"boolean"==typeof e.loading?{spinning:e.loading}:e.loading})),y=computed((function(){return v.value&&v.value.spinning})),_=computed((function(){var t="";switch(e.size){case"large":t="lg";break;case"small":t="sm"}return t})),b=computed((function(){var t;return _defineProperty$7(t={},"".concat(i.value),!0),_defineProperty$7(t,"".concat(i.value,"-vertical"),"vertical"===e.itemLayout),_defineProperty$7(t,"".concat(i.value,"-").concat(_.value),_.value),_defineProperty$7(t,"".concat(i.value,"-split"),e.split),_defineProperty$7(t,"".concat(i.value,"-bordered"),e.bordered),_defineProperty$7(t,"".concat(i.value,"-loading"),y.value),_defineProperty$7(t,"".concat(i.value,"-grid"),!!e.grid),_defineProperty$7(t,"".concat(i.value,"-rtl"),"rtl"===s.value),t})),k=computed((function(){var t=_extends(_extends(_extends({},o),{total:e.dataSource.length,current:d.value,pageSize:p.value}),e.pagination||{}),n=Math.ceil(t.total/t.pageSize);return t.current>n&&(t.current=n),t})),x=computed((function(){var t=_toConsumableArray(e.dataSource);return e.pagination&&e.dataSource.length>(k.value.current-1)*k.value.pageSize&&(t=_toConsumableArray(e.dataSource).splice((k.value.current-1)*k.value.pageSize,k.value.pageSize)),t})),C=useBreakpoint(),P=eagerComputed((function(){for(var e=0;e<responsiveArray.length;e+=1){var t=responsiveArray[e];if(C.value[t])return t}})),E=computed((function(){if(e.grid){var t=P.value&&e.grid[P.value]?e.grid[P.value]:e.grid.column;return t?{width:"".concat(100/t,"%"),maxWidth:"".concat(100/t,"%")}:void 0}}));return function(){var t,n,a,o,l,s,c,d=null!==(t=e.loadMore)&&void 0!==t?t:null===(n=r.loadMore)||void 0===n?void 0:n.call(r),p=null!==(a=e.footer)&&void 0!==a?a:null===(o=r.footer)||void 0===o?void 0:o.call(r),f=null!==(l=e.header)&&void 0!==l?l:null===(s=r.header)||void 0===s?void 0:s.call(r),_=flattenChildren(null===(c=r.default)||void 0===c?void 0:c.call(r)),C=!!(d||e.pagination||p),P=_extends(_extends({},b.value),_defineProperty$7({},"".concat(i.value,"-something-after-last-item"),C)),w=e.pagination?createVNode("div",{class:"".concat(i.value,"-pagination")},[createVNode(__unplugin_components_2$1,_objectSpread2(_objectSpread2({},k.value),{},{onChange:h,onShowSizeChange:g}),null)]):null,S=y.value&&createVNode("div",{style:{minHeight:"53px"}},null);if(x.value.length>0){m.length=0;var B=x.value.map((function(t,n){return function(t,n){var a,o,l=null!==(a=e.renderItem)&&void 0!==a?a:r.renderItem;if(!l)return null;var i=_typeof(e.rowKey);return(o="function"===i?e.rowKey(t):"string"===i||"number"===i?t[e.rowKey]:t.key)||(o="list-item-".concat(n)),m[n]=o,l({item:t,index:n})}(t,n)})),O=B.map((function(e,t){return createVNode("div",{key:m[t],style:E.value},[e])}));S=e.grid?createVNode(Row,{gutter:e.grid.gutter},{default:function(){return[O]}}):createVNode("ul",{class:"".concat(i.value,"-items")},[B])}else _.length||y.value||(S=function(t){var n;return createVNode("div",{class:"".concat(i.value,"-empty-text")},[(null===(n=e.locale)||void 0===n?void 0:n.emptyText)||t("List")])}(u.value));var T=k.value.position||"bottom";return createVNode("div",{class:P},[("top"===T||"both"===T)&&w,f&&createVNode("div",{class:"".concat(i.value,"-header")},[f]),createVNode(Spin,v.value,{default:function(){return[S,_]}}),p&&createVNode("div",{class:"".concat(i.value,"-footer")},[p]),d||("bottom"===T||"both"===T)&&w])}}});List.install=function(e){return e.component(List.name,List),e.component(List.Item.name,List.Item),e.component(List.Item.Meta.name,List.Item.Meta),e};var __unplugin_components_3=List,ArrowLeftOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},ArrowLeftOutlinedSvg=ArrowLeftOutlined$2;function _objectSpread$5(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty$5(e,t,n[t])}))}return e}function _defineProperty$5(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ArrowLeftOutlined=function(e,t){var n=_objectSpread$5({},e,t.attrs);return createVNode(AntdIcon,_objectSpread$5({},n,{icon:ArrowLeftOutlinedSvg}),null)};ArrowLeftOutlined.displayName="ArrowLeftOutlined",ArrowLeftOutlined.inheritAttrs=!1;var ArrowLeftOutlined$1=ArrowLeftOutlined,ArrowRightOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"},ArrowRightOutlinedSvg=ArrowRightOutlined$2;function _objectSpread$4(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty$4(e,t,n[t])}))}return e}function _defineProperty$4(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ArrowRightOutlined=function(e,t){var n=_objectSpread$4({},e,t.attrs);return createVNode(AntdIcon,_objectSpread$4({},n,{icon:ArrowRightOutlinedSvg}),null)};ArrowRightOutlined.displayName="ArrowRightOutlined",ArrowRightOutlined.inheritAttrs=!1;var ArrowRightOutlined$1=ArrowRightOutlined,__rest=globalThis&&globalThis.__rest||function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]])}return n},inlineStyle={border:0,background:"transparent",padding:0,lineHeight:"inherit",display:"inline-block"},TransButton=defineComponent({name:"TransButton",inheritAttrs:!1,props:{noStyle:{type:Boolean,default:void 0},onClick:Function,disabled:{type:Boolean,default:void 0},autofocus:{type:Boolean,default:void 0}},setup:function(e,t){var n=t.slots,a=t.emit,r=t.attrs,o=t.expose,l=ref(),i=function(e){e.keyCode===KeyCode.ENTER&&e.preventDefault()},s=function(e){e.keyCode===KeyCode.ENTER&&a("click",e)},u=function(e){a("click",e)},c=function(){l.value&&l.value.focus()};return onMounted((function(){e.autofocus&&c()})),o({focus:c,blur:function(){l.value&&l.value.blur()}}),function(){var t,a=e.noStyle,o=e.disabled,c=__rest(e,["noStyle","disabled"]),d={};return a||(d=_extends({},inlineStyle)),o&&(d.pointerEvents="none"),createVNode("div",_objectSpread2(_objectSpread2(_objectSpread2({role:"button",tabindex:0,ref:l},c),r),{},{onClick:u,onKeydown:i,onKeyup:s,style:_extends(_extends({},d),r.style||{})}),[null===(t=n.default)||void 0===t?void 0:t.call(n)])}}}),TransButton$1=TransButton,pageHeaderProps=function(){return{backIcon:PropTypes.any,prefixCls:String,title:PropTypes.any,subTitle:PropTypes.any,breadcrumb:PropTypes.object,tags:PropTypes.any,footer:PropTypes.any,extra:PropTypes.any,avatar:PropTypes.object,ghost:{type:Boolean,default:void 0},onBack:Function}},PageHeader=defineComponent({name:"APageHeader",props:pageHeaderProps(),slots:["backIcon","avatar","breadcrumb","title","subTitle","tags","extra","footer"],setup:function(e,t){var n=t.emit,a=t.slots,r=useConfigInject("page-header",e),o=r.prefixCls,l=r.direction,i=r.pageHeader,s=ref(!1),u=useDestroyed(),c=function(e){var t=e.width;u.value||(s.value=t<768)},d=computed((function(){var t,n,a;return null===(a=null!==(t=e.ghost)&&void 0!==t?t:null===(n=i.value)||void 0===n?void 0:n.ghost)||void 0===a||a})),p=function(){var t;return e.breadcrumb?createVNode(Breadcrumb,e.breadcrumb,null):null===(t=a.breadcrumb)||void 0===t?void 0:t.call(a)},m=function(){var t,r,i,s,u,c,d,p,m,f=e.avatar,h=null!==(t=e.title)&&void 0!==t?t:null===(r=a.title)||void 0===r?void 0:r.call(a),g=null!==(i=e.subTitle)&&void 0!==i?i:null===(s=a.subTitle)||void 0===s?void 0:s.call(a),v=null!==(u=e.tags)&&void 0!==u?u:null===(c=a.tags)||void 0===c?void 0:c.call(a),y=null!==(d=e.extra)&&void 0!==d?d:null===(p=a.extra)||void 0===p?void 0:p.call(a),_="".concat(o.value,"-heading"),b=h||g||v||y;if(!b)return null;var k=function(){var t,n,r;return null!==(r=null!==(t=e.backIcon)&&void 0!==t?t:null===(n=a.backIcon)||void 0===n?void 0:n.call(a))&&void 0!==r?r:"rtl"===l.value?createVNode(ArrowRightOutlined$1,null,null):createVNode(ArrowLeftOutlined$1,null,null)}(),x=function(t){return t&&e.onBack?createVNode(LocaleReceiver,{componentName:"PageHeader",children:function(e){var a=e.back;return createVNode("div",{class:"".concat(o.value,"-back")},[createVNode(TransButton$1,{onClick:function(e){n("back",e)},class:"".concat(o.value,"-back-button"),"aria-label":a},{default:function(){return[t]}})])}},null):null}(k);return createVNode("div",{class:_},[(x||f||b)&&createVNode("div",{class:"".concat(_,"-left")},[x,f?createVNode(Avatar,f,null):null===(m=a.avatar)||void 0===m?void 0:m.call(a),h&&createVNode("span",{class:"".concat(_,"-title"),title:"string"==typeof h?h:void 0},[h]),g&&createVNode("span",{class:"".concat(_,"-sub-title"),title:"string"==typeof g?g:void 0},[g]),v&&createVNode("span",{class:"".concat(_,"-tags")},[v])]),y&&createVNode("span",{class:"".concat(_,"-extra")},[y])])},f=function(){var t,n,r=null!==(t=e.footer)&&void 0!==t?t:filterEmpty(null===(n=a.footer)||void 0===n?void 0:n.call(a));return isEmptyContent(r)?null:createVNode("div",{class:"".concat(o.value,"-footer")},[r])},h=function(e){return createVNode("div",{class:"".concat(o.value,"-content")},[e])};return function(){var t,n,r,i=(null===(n=e.breadcrumb)||void 0===n?void 0:n.routes)||a.breadcrumb,u=e.footer||a.footer,g=flattenChildren(null===(r=a.default)||void 0===r?void 0:r.call(a)),v=classNames(o.value,(_defineProperty$7(t={"has-breadcrumb":i,"has-footer":u},"".concat(o.value,"-ghost"),d.value),_defineProperty$7(t,"".concat(o.value,"-rtl"),"rtl"===l.value),_defineProperty$7(t,"".concat(o.value,"-compact"),s.value),t));return createVNode(ResizeObserver,{onResize:c},{default:function(){return[createVNode("div",{class:v},[p(),m(),g.length?h(g):null,f()])]}})}}}),__unplugin_components_0=withInstall(PageHeader),BookOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-260 72h96v209.9L621.5 312 572 347.4V136zm220 752H232V136h280v296.9c0 3.3 1 6.6 3 9.3a15.9 15.9 0 0022.3 3.7l83.8-59.9 81.4 59.4c2.7 2 6 3.1 9.4 3.1 8.8 0 16-7.2 16-16V136h64v752z"}}]},name:"book",theme:"outlined"},BookOutlinedSvg=BookOutlined$2;function _objectSpread$3(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty$3(e,t,n[t])}))}return e}function _defineProperty$3(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var BookOutlined=function(e,t){var n=_objectSpread$3({},e,t.attrs);return createVNode(AntdIcon,_objectSpread$3({},n,{icon:BookOutlinedSvg}),null)};BookOutlined.displayName="BookOutlined",BookOutlined.inheritAttrs=!1;var BookOutlined$1=BookOutlined,BugOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"},BugOutlinedSvg=BugOutlined$2;function _objectSpread$2(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty$2(e,t,n[t])}))}return e}function _defineProperty$2(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var BugOutlined=function(e,t){var n=_objectSpread$2({},e,t.attrs);return createVNode(AntdIcon,_objectSpread$2({},n,{icon:BugOutlinedSvg}),null)};BugOutlined.displayName="BugOutlined",BugOutlined.inheritAttrs=!1;var BugOutlined$1=BugOutlined,HourglassOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M742 318V184h86c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h86v134c0 81.5 42.4 153.2 106.4 194-64 40.8-106.4 112.5-106.4 194v134h-86c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h632c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8h-86V706c0-81.5-42.4-153.2-106.4-194 64-40.8 106.4-112.5 106.4-194zm-72 388v134H354V706c0-42.2 16.4-81.9 46.3-111.7C430.1 564.4 469.8 548 512 548s81.9 16.4 111.7 46.3C653.6 624.1 670 663.8 670 706zm0-388c0 42.2-16.4 81.9-46.3 111.7C593.9 459.6 554.2 476 512 476s-81.9-16.4-111.7-46.3A156.63 156.63 0 01354 318V184h316v134z"}}]},name:"hourglass",theme:"outlined"},HourglassOutlinedSvg=HourglassOutlined$2;function _objectSpread$1(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty$1(e,t,n[t])}))}return e}function _defineProperty$1(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var HourglassOutlined=function(e,t){var n=_objectSpread$1({},e,t.attrs);return createVNode(AntdIcon,_objectSpread$1({},n,{icon:HourglassOutlinedSvg}),null)};HourglassOutlined.displayName="HourglassOutlined",HourglassOutlined.inheritAttrs=!1;var HourglassOutlined$1=HourglassOutlined,ReloadOutlined$2={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"},ReloadOutlinedSvg=ReloadOutlined$2;function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},a=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(a=a.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),a.forEach((function(t){_defineProperty(e,t,n[t])}))}return e}function _defineProperty(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ReloadOutlined=function(e,t){var n=_objectSpread({},e,t.attrs);return createVNode(AntdIcon,_objectSpread({},n,{icon:ReloadOutlinedSvg}),null)};ReloadOutlined.displayName="ReloadOutlined",ReloadOutlined.inheritAttrs=!1;var ReloadOutlined$1=ReloadOutlined;const getCodeEditorHeight=e=>{const t=21*e.split("\n").length;return(t>300?300:t)+"px"};var TextGrid$1="",TextGridComponent=defineComponent({components:{},props:{colspan:{type:Number,default:1},labelWidth:{type:[Number,String],default:""}},render(){const{$slots:e,colspan:t,labelWidth:n}=this;let a="";for(let o=0;o<t;o++)a+=" 1fr";let r=e.default&&e.default();return n&&r&&r.length&&(r=r.map((e=>(e.props&&!e.props.labelWidth&&(e.props.labelWidth=n),e)))),h("div",{class:["text-grid"],style:{"grid-template-columns":a}},{default:()=>r})}}),TextItemComponent=defineComponent({components:{},props:{title:{type:[Number,String],default:""},value:{type:[Number,String],default:""},labelWidth:{type:[Number,String],default:80}},render(){const{title:e,value:t,$slots:n,labelWidth:a}=this;let r=a;return"number"==typeof a&&(r=a+"px"),h("div",{class:["text-grid-item"]},[h("div",{class:["text-grid-item_label"],style:{width:r}},[e,n.title&&n.title()]),h("div",{class:["text-grid-item_value"]},[t,n.default&&n.default()])])}});const TextGrid=TextGridComponent,TextItem=TextItemComponent;var index$3="",index$2="";const _hoisted_1$9={class:"table-card"},_hoisted_2$7={key:0},_hoisted_3$7={key:1},_hoisted_4$5={key:3},_hoisted_5$3=["innerHTML"],_hoisted_6$3=["innerHTML"],_hoisted_7$2=createTextVNode("   "),_hoisted_8$2=["onClick"],_sfc_main$e=defineComponent({__name:"TableCard",props:{title:null,subTitle:null,columns:null,data:null,tableProps:null},setup(e){const t=e,n=useAppStore(),{t:a}=useI18n();let r=ref({});!function(){let e=n.feConfig.API_TABLE_PROPS||{};t.tableProps&&(e=__spreadValues(__spreadValues({},e),t.tableProps)),r.value=e}();const o=useSlots();function l(e){var t;let a=e;return(null==(t=n.feConfig.CUSTOM_METHODS)?void 0:t.HANDEL_APIFIELD_DESC)&&(a=n.feConfig.CUSTOM_METHODS.HANDEL_APIFIELD_DESC(e)),textToHtml(a)}return(e,i)=>{const s=__unplugin_components_0,u=__unplugin_components_1;return openBlock(),createElementBlock("div",_hoisted_1$9,[t.title?(openBlock(),createBlock(s,{key:0,title:t.title,"sub-title":t.subTitle},{extra:withCtx((()=>[renderSlot(e.$slots,"extra")])),_:3},8,["title","sub-title"])):createCommentVNode("",!0),createVNode(u,mergeProps({bordered:"",pagination:!1,size:"small",defaultExpandAllRows:""},unref(r),{"data-source":t.data,columns:t.columns}),{bodyCell:withCtx((t=>[unref(o).bodyCell?renderSlot(e.$slots,"bodyCell",normalizeProps(mergeProps({key:0},t))):"require"===t.column.dataIndex?(openBlock(),createElementBlock(Fragment,{key:1},[t.text&&1==t.text?(openBlock(),createBlock(unref(CheckOutlined),{key:0})):createCommentVNode("",!0)],64)):"type"===t.column.dataIndex?(openBlock(),createElementBlock(Fragment,{key:2},["array"==t.text&&t.record.childrenType?(openBlock(),createElementBlock("span",_hoisted_2$7,toDisplayString(t.text)+"<"+toDisplayString(t.record.childrenType)+">",1)):(openBlock(),createElementBlock("span",_hoisted_3$7,toDisplayString(t.text),1))],64)):"desc"===t.column.dataIndex?(openBlock(),createElementBlock("div",_hoisted_4$5,[t.record.html?(openBlock(),createElementBlock("span",{key:0,innerHTML:t.record.html},null,8,_hoisted_5$3)):(openBlock(),createElementBlock("span",{key:1,innerHTML:l(t.text)},null,8,_hoisted_6$3)),_hoisted_7$2,t.record.md||t.record.mdRef?(openBlock(),createElementBlock("a",{key:2,onClick:e=>function(e){var t;let r=e.md;(null==(t=n.feConfig.CUSTOM_METHODS)?void 0:t.HANDEL_APIFIELD_MD)&&(r=n.feConfig.CUSTOM_METHODS.HANDEL_APIFIELD_MD(e.md)),mdModal({title:a("apiPage.mdDetail.title",{name:e.name}),md:r})}(t.record)},toDisplayString(unref(a)("common.view")),9,_hoisted_8$2)):createCommentVNode("",!0)])):(openBlock(),createElementBlock(Fragment,{key:4},[createTextVNode(toDisplayString(t.text),1)],64))])),_:3},16,["data-source","columns"])])}}}),_hoisted_1$8={class:"api-table"},_hoisted_2$6={class:"desc"},_hoisted_3$6=["innerHTML"],_hoisted_4$4={key:4},_sfc_main$d=defineComponent({__name:"Index",props:{detail:null},setup(e){const t=e,n=useAppStore(),{t:a}=useI18n(),r=[{title:a("apiPage.common.field"),dataIndex:"name",width:280},{title:a("apiPage.common.method"),dataIndex:"type",align:"center",width:130},{title:a("apiPage.common.require"),dataIndex:"require",width:100,align:"center"},{title:a("apiPage.common.defaultValue"),dataIndex:"default",align:"center",width:100},{title:a("apiPage.common.desc"),dataIndex:"desc"}],o=cloneDeep(r),l=o.findIndex((e=>"require"===e.dataIndex));o[l].title=a("common.notEmpty");const i=reactive({paramsColumns:r,returnColumns:o,tableScroll:{x:"700px",y:"100%"}}),s=e=>{var t;return(null==(t=n.feConfig.CUSTOM_METHODS)?void 0:t.HANDEL_API_MD)&&(e=n.feConfig.CUSTOM_METHODS.HANDEL_API_MD(e)),e};return(e,r)=>{const o=__unplugin_components_0,l=__unplugin_components_4,u=Tabs;return openBlock(),createElementBlock("div",_hoisted_1$8,[createBaseVNode("div",_hoisted_2$6,[t.detail.desc?(openBlock(),createElementBlock("div",{key:0,innerHTML:(c=t.detail.desc,(null==(d=n.feConfig.CUSTOM_METHODS)?void 0:d.HANDEL_API_DESC)&&(c=n.feConfig.CUSTOM_METHODS.HANDEL_API_DESC(c)),textToHtml(c))},null,8,_hoisted_3$6)):createCommentVNode("",!0),t.detail.md?(openBlock(),createBlock(unref(Markdown),{key:1,md:s(t.detail.md)},null,8,["md"])):createCommentVNode("",!0)]),t.detail.header&&t.detail.header.length?(openBlock(),createBlock(_sfc_main$e,{key:0,title:unref(a)("apiPage.header.title"),columns:i.paramsColumns,data:t.detail.header,tableProps:{scroll:i.tableScroll}},null,8,["title","columns","data","tableProps"])):createCommentVNode("",!0),t.detail.routeParam&&t.detail.routeParam.length?(openBlock(),createBlock(_sfc_main$e,{key:1,title:unref(a)("apiPage.routeParam.title"),columns:i.paramsColumns,data:t.detail.routeParam,tableProps:{scroll:i.tableScroll}},null,8,["title","columns","data","tableProps"])):createCommentVNode("",!0),t.detail.query&&t.detail.query.length?(openBlock(),createBlock(_sfc_main$e,{key:2,title:unref(a)("apiPage.query.title"),columns:i.paramsColumns,data:t.detail.query,tableProps:{scroll:i.tableScroll}},null,8,["title","columns","data","tableProps"])):createCommentVNode("",!0),t.detail.param&&t.detail.param.length?(openBlock(),createBlock(_sfc_main$e,{key:3,title:unref(a)("apiPage.body.title"),columns:i.paramsColumns,data:t.detail.param,tableProps:{scroll:i.tableScroll}},null,8,["title","columns","data","tableProps"])):createCommentVNode("",!0),t.detail.responseSuccess&&t.detail.responseSuccess.length||t.detail.responseError&&t.detail.responseError.length?(openBlock(),createElementBlock("div",_hoisted_4$4,[createVNode(o,{title:unref(a)("apiPage.responses")},null,8,["title"]),createVNode(u,{type:"card",size:"small"},{default:withCtx((()=>[createVNode(l,{key:"success",tab:unref(a)("apiPage.responses.success")},{default:withCtx((()=>[t.detail.responseSuccessMd?(openBlock(),createBlock(unref(Markdown),{key:0,md:t.detail.responseSuccessMd},null,8,["md"])):(openBlock(),createBlock(_sfc_main$e,{key:1,columns:i.returnColumns,data:t.detail.responseSuccess,tableProps:{scroll:i.tableScroll}},null,8,["columns","data","tableProps"]))])),_:1},8,["tab"]),t.detail.responseError&&t.detail.responseError.length?(openBlock(),createBlock(l,{key:"error",tab:unref(a)("apiPage.responses.error")},{default:withCtx((()=>[t.detail.responseErrorMd?(openBlock(),createBlock(unref(Markdown),{key:0,md:t.detail.responseErrorMd},null,8,["md"])):(openBlock(),createBlock(_sfc_main$e,{key:1,columns:i.returnColumns,data:t.detail.responseError,tableProps:{scroll:i.tableScroll}},null,8,["columns","data","tableProps"]))])),_:1},8,["tab"])):createCommentVNode("",!0)])),_:1})])):createCommentVNode("",!0)]);var c,d}}});var mock={exports:{}};(function(module,exports){var factory;factory=function(){return function(e){var t={};function n(a){if(t[a])return t[a].exports;var r=t[a]={exports:{},id:a,loaded:!1};return e[a].call(r.exports,r,r.exports,n),r.loaded=!0,r.exports}return n.m=e,n.c=t,n.p="",n(0)}([function(e,t,n){var a,r=n(1),o=n(3),l=n(5),i=n(20),s=n(23),u=n(25);"undefined"!=typeof window&&(a=n(27))
/*!
            Mock - 模拟请求 & 模拟数据
            https://github.com/nuysoft/Mock
            墨智 <EMAIL> <EMAIL>
        */;var c={Handler:r,Random:l,Util:o,XHR:a,RE:i,toJSONSchema:s,valid:u,heredoc:o.heredoc,setup:function(e){return a.setup(e)},_mocked:{},version:"1.0.1-beta3"};a&&(a.Mock=c),c.mock=function(e,t,n){return 1===arguments.length?r.gen(e):(2===arguments.length&&(n=t,t=void 0),a&&(window.XMLHttpRequest=a),c._mocked[e+(t||"")]={rurl:e,rtype:t,template:n},c)},e.exports=c},function(module,exports,__webpack_require__){var Constant=__webpack_require__(2),Util=__webpack_require__(3),Parser=__webpack_require__(4),Random=__webpack_require__(5),RE=__webpack_require__(20),Handler={extend:Util.extend,gen:function(e,t,n){t=null==t?"":t+"",n={path:(n=n||{}).path||[Constant.GUID],templatePath:n.templatePath||[Constant.GUID++],currentContext:n.currentContext,templateCurrentContext:n.templateCurrentContext||e,root:n.root||n.currentContext,templateRoot:n.templateRoot||n.templateCurrentContext||e};var a,r=Parser.parse(t),o=Util.type(e);return Handler[o]?(a=Handler[o]({type:o,template:e,name:t,parsedName:t?t.replace(Constant.RE_KEY,"$1"):t,rule:r,context:n}),n.root||(n.root=a),a):e}};Handler.extend({array:function(e){var t,n,a=[];if(0===e.template.length)return a;if(e.rule.parameters)if(1===e.rule.min&&void 0===e.rule.max)e.context.path.push(e.name),e.context.templatePath.push(e.name),a=Random.pick(Handler.gen(e.template,void 0,{path:e.context.path,templatePath:e.context.templatePath,currentContext:a,templateCurrentContext:e.template,root:e.context.root||a,templateRoot:e.context.templateRoot||e.template})),e.context.path.pop(),e.context.templatePath.pop();else if(e.rule.parameters[2])e.template.__order_index=e.template.__order_index||0,e.context.path.push(e.name),e.context.templatePath.push(e.name),a=Handler.gen(e.template,void 0,{path:e.context.path,templatePath:e.context.templatePath,currentContext:a,templateCurrentContext:e.template,root:e.context.root||a,templateRoot:e.context.templateRoot||e.template})[e.template.__order_index%e.template.length],e.template.__order_index+=+e.rule.parameters[2],e.context.path.pop(),e.context.templatePath.pop();else for(t=0;t<e.rule.count;t++)for(n=0;n<e.template.length;n++)e.context.path.push(a.length),e.context.templatePath.push(n),a.push(Handler.gen(e.template[n],a.length,{path:e.context.path,templatePath:e.context.templatePath,currentContext:a,templateCurrentContext:e.template,root:e.context.root||a,templateRoot:e.context.templateRoot||e.template})),e.context.path.pop(),e.context.templatePath.pop();else for(t=0;t<e.template.length;t++)e.context.path.push(t),e.context.templatePath.push(t),a.push(Handler.gen(e.template[t],t,{path:e.context.path,templatePath:e.context.templatePath,currentContext:a,templateCurrentContext:e.template,root:e.context.root||a,templateRoot:e.context.templateRoot||e.template})),e.context.path.pop(),e.context.templatePath.pop();return a},object:function(e){var t,n,a,r,o,l,i={};if(null!=e.rule.min)for(t=Util.keys(e.template),t=(t=Random.shuffle(t)).slice(0,e.rule.count),l=0;l<t.length;l++)r=(a=t[l]).replace(Constant.RE_KEY,"$1"),e.context.path.push(r),e.context.templatePath.push(a),i[r]=Handler.gen(e.template[a],a,{path:e.context.path,templatePath:e.context.templatePath,currentContext:i,templateCurrentContext:e.template,root:e.context.root||i,templateRoot:e.context.templateRoot||e.template}),e.context.path.pop(),e.context.templatePath.pop();else{for(a in t=[],n=[],e.template)("function"==typeof e.template[a]?n:t).push(a);for(t=t.concat(n),l=0;l<t.length;l++)r=(a=t[l]).replace(Constant.RE_KEY,"$1"),e.context.path.push(r),e.context.templatePath.push(a),i[r]=Handler.gen(e.template[a],a,{path:e.context.path,templatePath:e.context.templatePath,currentContext:i,templateCurrentContext:e.template,root:e.context.root||i,templateRoot:e.context.templateRoot||e.template}),e.context.path.pop(),e.context.templatePath.pop(),(o=a.match(Constant.RE_KEY))&&o[2]&&"number"===Util.type(e.template[a])&&(e.template[a]+=parseInt(o[2],10))}return i},number:function(e){var t,n;if(e.rule.decimal){for(e.template+="",(n=e.template.split("."))[0]=e.rule.range?e.rule.count:n[0],n[1]=(n[1]||"").slice(0,e.rule.dcount);n[1].length<e.rule.dcount;)n[1]+=n[1].length<e.rule.dcount-1?Random.character("number"):Random.character("123456789");t=parseFloat(n.join("."),10)}else t=e.rule.range&&!e.rule.parameters[2]?e.rule.count:e.template;return t},boolean:function(e){return e.rule.parameters?Random.bool(e.rule.min,e.rule.max,e.template):e.template},string:function(e){var t,n,a,r,o="";if(e.template.length){for(null==e.rule.count&&(o+=e.template),t=0;t<e.rule.count;t++)o+=e.template;for(n=o.match(Constant.RE_PLACEHOLDER)||[],t=0;t<n.length;t++)if(a=n[t],/^\\/.test(a))n.splice(t--,1);else{if(r=Handler.placeholder(a,e.context.currentContext,e.context.templateCurrentContext,e),1===n.length&&a===o&&typeof r!=typeof o){o=r;break}o=o.replace(a,r)}}else o=e.rule.range?Random.string(e.rule.count):e.template;return o},function:function(e){return e.template.call(e.context.currentContext,e)},regexp:function(e){var t="";null==e.rule.count&&(t+=e.template.source);for(var n=0;n<e.rule.count;n++)t+=e.template.source;return RE.Handler.gen(RE.Parser.parse(t))}}),Handler.extend({_all:function(){var e={};for(var t in Random)e[t.toLowerCase()]=t;return e},placeholder:function(placeholder,obj,templateContext,options){Constant.RE_PLACEHOLDER.exec("");var parts=Constant.RE_PLACEHOLDER.exec(placeholder),key=parts&&parts[1],lkey=key&&key.toLowerCase(),okey=this._all()[lkey],params=parts&&parts[2]||"",pathParts=this.splitPathToArray(key);try{params=eval("(function(){ return [].splice.call(arguments, 0 ) })("+params+")")}catch(error){params=parts[2].split(/,\s*/)}if(obj&&key in obj)return obj[key];if("/"===key.charAt(0)||pathParts.length>1)return this.getValueByKeyPath(key,options);if(templateContext&&"object"==typeof templateContext&&key in templateContext&&placeholder!==templateContext[key])return templateContext[key]=Handler.gen(templateContext[key],key,{currentContext:obj,templateCurrentContext:templateContext}),templateContext[key];if(!(key in Random)&&!(lkey in Random)&&!(okey in Random))return placeholder;for(var i=0;i<params.length;i++)Constant.RE_PLACEHOLDER.exec(""),Constant.RE_PLACEHOLDER.test(params[i])&&(params[i]=Handler.placeholder(params[i],obj,templateContext,options));var handle=Random[key]||Random[lkey]||Random[okey];switch(Util.type(handle)){case"array":return Random.pick(handle);case"function":handle.options=options;var re=handle.apply(Random,params);return void 0===re&&(re=""),delete handle.options,re}},getValueByKeyPath:function(e,t){var n=e,a=this.splitPathToArray(e),r=[];"/"===e.charAt(0)?r=[t.context.path[0]].concat(this.normalizePath(a)):a.length>1&&((r=t.context.path.slice(0)).pop(),r=this.normalizePath(r.concat(a)));try{e=a[a.length-1];for(var o=t.context.root,l=t.context.templateRoot,i=1;i<r.length-1;i++)o=o[r[i]],l=l[r[i]];if(o&&e in o)return o[e];if(l&&"object"==typeof l&&e in l&&n!==l[e])return l[e]=Handler.gen(l[e],e,{currentContext:o,templateCurrentContext:l}),l[e]}catch(s){}return"@"+a.join("/")},normalizePath:function(e){for(var t=[],n=0;n<e.length;n++)switch(e[n]){case"..":t.pop();break;case".":break;default:t.push(e[n])}return t},splitPathToArray:function(e){var t=e.split(/\/+/);return t[t.length-1]||(t=t.slice(0,-1)),t[0]||(t=t.slice(1)),t}}),module.exports=Handler},function(e,t){e.exports={GUID:1,RE_KEY:/(.+)\|(?:\+(\d+)|([\+\-]?\d+-?[\+\-]?\d*)?(?:\.(\d+-?\d*))?)/,RE_RANGE:/([\+\-]?\d+)-?([\+\-]?\d+)?/,RE_PLACEHOLDER:/\\*@([^@#%&()\?\s]+)(?:\((.*?)\))?/g}},function(e,t){var n={extend:function(){var e,t,a,r,o,l=arguments[0]||{},i=1,s=arguments.length;for(1===s&&(l=this,i=0);i<s;i++)if(e=arguments[i])for(t in e)a=l[t],l!==(r=e[t])&&void 0!==r&&(n.isArray(r)||n.isObject(r)?(n.isArray(r)&&(o=a&&n.isArray(a)?a:[]),n.isObject(r)&&(o=a&&n.isObject(a)?a:{}),l[t]=n.extend(o,r)):l[t]=r);return l},each:function(e,t,n){var a,r;if("number"===this.type(e))for(a=0;a<e;a++)t(a,a);else if(e.length===+e.length)for(a=0;a<e.length&&!1!==t.call(n,e[a],a,e);a++);else for(r in e)if(!1===t.call(n,e[r],r,e))break},type:function(e){return null==e?String(e):Object.prototype.toString.call(e).match(/\[object (\w+)\]/)[1].toLowerCase()}};n.each("String Object Array RegExp Function".split(" "),(function(e){n["is"+e]=function(t){return n.type(t)===e.toLowerCase()}})),n.isObjectOrArray=function(e){return n.isObject(e)||n.isArray(e)},n.isNumeric=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},n.keys=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t},n.values=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(e[n]);return t},n.heredoc=function(e){return e.toString().replace(/^[^\/]+\/\*!?/,"").replace(/\*\/[^\/]+$/,"").replace(/^[\s\xA0]+/,"").replace(/[\s\xA0]+$/,"")},n.noop=function(){},e.exports=n},function(e,t,n){var a=n(2),r=n(5);e.exports={parse:function(e){var t=((e=null==e?"":e+"")||"").match(a.RE_KEY),n=t&&t[3]&&t[3].match(a.RE_RANGE),o=n&&n[1]&&parseInt(n[1],10),l=n&&n[2]&&parseInt(n[2],10),i=n?n[2]?r.integer(o,l):parseInt(n[1],10):void 0,s=t&&t[4]&&t[4].match(a.RE_RANGE),u=s&&s[1]&&parseInt(s[1],10),c=s&&s[2]&&parseInt(s[2],10),d={parameters:t,range:n,min:o,max:l,count:i,decimal:s,dmin:u,dmax:c,dcount:s?!s[2]&&parseInt(s[1],10)||r.integer(u,c):void 0};for(var p in d)if(null!=d[p])return d;return{}}}},function(e,t,n){var a={extend:n(3).extend};a.extend(n(6)),a.extend(n(7)),a.extend(n(8)),a.extend(n(10)),a.extend(n(13)),a.extend(n(15)),a.extend(n(16)),a.extend(n(17)),a.extend(n(14)),a.extend(n(19)),e.exports=a},function(e,t){e.exports={boolean:function(e,t,n){return void 0!==n?(e=void 0===e||isNaN(e)?1:parseInt(e,10),t=void 0===t||isNaN(t)?1:parseInt(t,10),Math.random()>1/(e+t)*e?!n:n):Math.random()>=.5},bool:function(e,t,n){return this.boolean(e,t,n)},natural:function(e,t){return e=void 0!==e?parseInt(e,10):0,t=void 0!==t?parseInt(t,10):9007199254740992,Math.round(Math.random()*(t-e))+e},integer:function(e,t){return e=void 0!==e?parseInt(e,10):-9007199254740992,t=void 0!==t?parseInt(t,10):9007199254740992,Math.round(Math.random()*(t-e))+e},int:function(e,t){return this.integer(e,t)},float:function(e,t,n,a){n=void 0===n?0:n,n=Math.max(Math.min(n,17),0),a=void 0===a?17:a,a=Math.max(Math.min(a,17),0);for(var r=this.integer(e,t)+".",o=0,l=this.natural(n,a);o<l;o++)r+=o<l-1?this.character("number"):this.character("123456789");return parseFloat(r,10)},character:function(e){var t={lower:"abcdefghijklmnopqrstuvwxyz",upper:"ABCDEFGHIJKLMNOPQRSTUVWXYZ",number:"0123456789",symbol:"!@#$%^&*()[]"};return t.alpha=t.lower+t.upper,t[void 0]=t.lower+t.upper+t.number+t.symbol,(e=t[(""+e).toLowerCase()]||e).charAt(this.natural(0,e.length-1))},char:function(e){return this.character(e)},string:function(e,t,n){var a;switch(arguments.length){case 0:a=this.natural(3,7);break;case 1:a=e,e=void 0;break;case 2:"string"==typeof arguments[0]?a=t:(a=this.natural(e,t),e=void 0);break;case 3:a=this.natural(t,n)}for(var r="",o=0;o<a;o++)r+=this.character(e);return r},str:function(){return this.string.apply(this,arguments)},range:function(e,t,n){arguments.length<=1&&(t=e||0,e=0),e=+e,t=+t,n=+(n=arguments[2]||1);for(var a=Math.max(Math.ceil((t-e)/n),0),r=0,o=new Array(a);r<a;)o[r++]=e,e+=n;return o}}},function(e,t){var n={yyyy:"getFullYear",yy:function(e){return(""+e.getFullYear()).slice(2)},y:"yy",MM:function(e){var t=e.getMonth()+1;return t<10?"0"+t:t},M:function(e){return e.getMonth()+1},dd:function(e){var t=e.getDate();return t<10?"0"+t:t},d:"getDate",HH:function(e){var t=e.getHours();return t<10?"0"+t:t},H:"getHours",hh:function(e){var t=e.getHours()%12;return t<10?"0"+t:t},h:function(e){return e.getHours()%12},mm:function(e){var t=e.getMinutes();return t<10?"0"+t:t},m:"getMinutes",ss:function(e){var t=e.getSeconds();return t<10?"0"+t:t},s:"getSeconds",SS:function(e){var t=e.getMilliseconds();return t<10&&"00"+t||t<100&&"0"+t||t},S:"getMilliseconds",A:function(e){return e.getHours()<12?"AM":"PM"},a:function(e){return e.getHours()<12?"am":"pm"},T:"getTime"};e.exports={_patternLetters:n,_rformat:new RegExp(function(){var e=[];for(var t in n)e.push(t);return"("+e.join("|")+")"}(),"g"),_formatDate:function(e,t){return t.replace(this._rformat,(function t(a,r){return"function"==typeof n[r]?n[r](e):n[r]in n?t(a,n[r]):e[n[r]]()}))},_randomDate:function(e,t){return e=void 0===e?new Date(0):e,t=void 0===t?new Date:t,new Date(Math.random()*(t.getTime()-e.getTime()))},date:function(e){return e=e||"yyyy-MM-dd",this._formatDate(this._randomDate(),e)},time:function(e){return e=e||"HH:mm:ss",this._formatDate(this._randomDate(),e)},datetime:function(e){return e=e||"yyyy-MM-dd HH:mm:ss",this._formatDate(this._randomDate(),e)},now:function(e,t){1===arguments.length&&(/year|month|day|hour|minute|second|week/.test(e)||(t=e,e="")),e=(e||"").toLowerCase(),t=t||"yyyy-MM-dd HH:mm:ss";var n=new Date;switch(e){case"year":n.setMonth(0);case"month":n.setDate(1);case"week":case"day":n.setHours(0);case"hour":n.setMinutes(0);case"minute":n.setSeconds(0);case"second":n.setMilliseconds(0)}return"week"===e&&n.setDate(n.getDate()-n.getDay()),this._formatDate(n,t)}}},function(e,t,n){(function(e){e.exports={_adSize:["300x250","250x250","240x400","336x280","180x150","720x300","468x60","234x60","88x31","120x90","120x60","120x240","125x125","728x90","160x600","120x600","300x600"],_screenSize:["320x200","320x240","640x480","800x480","800x480","1024x600","1024x768","1280x800","1440x900","1920x1200","2560x1600"],_videoSize:["720x480","768x576","1280x720","1920x1080"],image:function(e,t,n,a,r){return 4===arguments.length&&(r=a,a=void 0),3===arguments.length&&(r=n,n=void 0),e||(e=this.pick(this._adSize)),t&&~t.indexOf("#")&&(t=t.slice(1)),n&&~n.indexOf("#")&&(n=n.slice(1)),"http://dummyimage.com/"+e+(t?"/"+t:"")+(n?"/"+n:"")+(a?"."+a:"")+(r?"&text="+r:"")},img:function(){return this.image.apply(this,arguments)},_brandColors:{"4ormat":"#fb0a2a","500px":"#02adea","About.me (blue)":"#00405d","About.me (yellow)":"#ffcc33",Addvocate:"#ff6138",Adobe:"#ff0000",Aim:"#fcd20b",Amazon:"#e47911",Android:"#a4c639","Angie's List":"#7fbb00",AOL:"#0060a3",Atlassian:"#003366",Behance:"#053eff","Big Cartel":"#97b538",bitly:"#ee6123",Blogger:"#fc4f08",Boeing:"#0039a6","Booking.com":"#003580",Carbonmade:"#613854",Cheddar:"#ff7243","Code School":"#3d4944",Delicious:"#205cc0",Dell:"#3287c1",Designmoo:"#e54a4f",Deviantart:"#4e6252","Designer News":"#2d72da",Devour:"#fd0001",DEWALT:"#febd17","Disqus (blue)":"#59a3fc","Disqus (orange)":"#db7132",Dribbble:"#ea4c89",Dropbox:"#3d9ae8",Drupal:"#0c76ab",Dunked:"#2a323a",eBay:"#89c507",Ember:"#f05e1b",Engadget:"#00bdf6",Envato:"#528036",Etsy:"#eb6d20",Evernote:"#5ba525","Fab.com":"#dd0017",Facebook:"#3b5998",Firefox:"#e66000","Flickr (blue)":"#0063dc","Flickr (pink)":"#ff0084",Forrst:"#5b9a68",Foursquare:"#25a0ca",Garmin:"#007cc3",GetGlue:"#2d75a2",Gimmebar:"#f70078",GitHub:"#171515","Google Blue":"#0140ca","Google Green":"#16a61e","Google Red":"#dd1812","Google Yellow":"#fcca03","Google+":"#dd4b39",Grooveshark:"#f77f00",Groupon:"#82b548","Hacker News":"#ff6600",HelloWallet:"#0085ca","Heroku (light)":"#c7c5e6","Heroku (dark)":"#6567a5",HootSuite:"#003366",Houzz:"#73ba37",HTML5:"#ec6231",IKEA:"#ffcc33",IMDb:"#f3ce13",Instagram:"#3f729b",Intel:"#0071c5",Intuit:"#365ebf",Kickstarter:"#76cc1e",kippt:"#e03500",Kodery:"#00af81",LastFM:"#c3000d",LinkedIn:"#0e76a8",Livestream:"#cf0005",Lumo:"#576396",Mixpanel:"#a086d3",Meetup:"#e51937",Nokia:"#183693",NVIDIA:"#76b900",Opera:"#cc0f16",Path:"#e41f11","PayPal (dark)":"#1e477a","PayPal (light)":"#3b7bbf",Pinboard:"#0000e6",Pinterest:"#c8232c",PlayStation:"#665cbe",Pocket:"#ee4056",Prezi:"#318bff",Pusha:"#0f71b4",Quora:"#a82400","QUOTE.fm":"#66ceff",Rdio:"#008fd5",Readability:"#9c0000","Red Hat":"#cc0000",Resource:"#7eb400",Rockpack:"#0ba6ab",Roon:"#62b0d9",RSS:"#ee802f",Salesforce:"#1798c1",Samsung:"#0c4da2",Shopify:"#96bf48",Skype:"#00aff0",Snagajob:"#f47a20",Softonic:"#008ace",SoundCloud:"#ff7700","Space Box":"#f86960",Spotify:"#81b71a",Sprint:"#fee100",Squarespace:"#121212",StackOverflow:"#ef8236",Staples:"#cc0000","Status Chart":"#d7584f",Stripe:"#008cdd",StudyBlue:"#00afe1",StumbleUpon:"#f74425","T-Mobile":"#ea0a8e",Technorati:"#40a800","The Next Web":"#ef4423",Treehouse:"#5cb868",Trulia:"#5eab1f",Tumblr:"#34526f","Twitch.tv":"#6441a5",Twitter:"#00acee",TYPO3:"#ff8700",Ubuntu:"#dd4814",Ustream:"#3388ff",Verizon:"#ef1d1d",Vimeo:"#86c9ef",Vine:"#00a478",Virb:"#06afd8","Virgin Media":"#cc0000",Wooga:"#5b009c","WordPress (blue)":"#21759b","WordPress (orange)":"#d54e21","WordPress (grey)":"#464646",Wunderlist:"#2b88d9",XBOX:"#9bc848",XING:"#126567","Yahoo!":"#720e9e",Yandex:"#ffcc00",Yelp:"#c41200",YouTube:"#c4302b",Zalongo:"#5498dc",Zendesk:"#78a300",Zerply:"#9dcc7a",Zootool:"#5e8b1d"},_brandNames:function(){var e=[];for(var t in this._brandColors)e.push(t);return e},dataImage:function(t,n){var a,r=(a="undefined"!=typeof document?document.createElement("canvas"):new(e.require("canvas")))&&a.getContext&&a.getContext("2d");if(!a||!r)return"";t||(t=this.pick(this._adSize)),n=void 0!==n?n:t,t=t.split("x");var o=parseInt(t[0],10),l=parseInt(t[1],10),i=this._brandColors[this.pick(this._brandNames())];return a.width=o,a.height=l,r.textAlign="center",r.textBaseline="middle",r.fillStyle=i,r.fillRect(0,0,o,l),r.fillStyle="#FFF",r.font="bold 14px sans-serif",r.fillText(n,o/2,l/2,o),a.toDataURL("image/png")}}}).call(t,n(9)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children=[],e.webpackPolyfill=1),e}},function(e,t,n){var a=n(11),r=n(12);e.exports={color:function(e){return e||r[e]?r[e].nicer:this.hex()},hex:function(){var e=this._goldenRatioColor(),t=a.hsv2rgb(e);return a.rgb2hex(t[0],t[1],t[2])},rgb:function(){var e=this._goldenRatioColor(),t=a.hsv2rgb(e);return"rgb("+parseInt(t[0],10)+", "+parseInt(t[1],10)+", "+parseInt(t[2],10)+")"},rgba:function(){var e=this._goldenRatioColor(),t=a.hsv2rgb(e);return"rgba("+parseInt(t[0],10)+", "+parseInt(t[1],10)+", "+parseInt(t[2],10)+", "+Math.random().toFixed(2)+")"},hsl:function(){var e=this._goldenRatioColor(),t=a.hsv2hsl(e);return"hsl("+parseInt(t[0],10)+", "+parseInt(t[1],10)+", "+parseInt(t[2],10)+")"},_goldenRatioColor:function(e,t){return this._goldenRatio=.618033988749895,this._hue=this._hue||Math.random(),this._hue+=this._goldenRatio,this._hue%=1,"number"!=typeof e&&(e=.5),"number"!=typeof t&&(t=.95),[360*this._hue,100*e,100*t]}}},function(e,t){e.exports={rgb2hsl:function(e){var t,n,a=e[0]/255,r=e[1]/255,o=e[2]/255,l=Math.min(a,r,o),i=Math.max(a,r,o),s=i-l;return i==l?t=0:a==i?t=(r-o)/s:r==i?t=2+(o-a)/s:o==i&&(t=4+(a-r)/s),(t=Math.min(60*t,360))<0&&(t+=360),n=(l+i)/2,[t,100*(i==l?0:n<=.5?s/(i+l):s/(2-i-l)),100*n]},rgb2hsv:function(e){var t,n,a=e[0],r=e[1],o=e[2],l=Math.min(a,r,o),i=Math.max(a,r,o),s=i-l;return n=0===i?0:s/i*1e3/10,i==l?t=0:a==i?t=(r-o)/s:r==i?t=2+(o-a)/s:o==i&&(t=4+(a-r)/s),(t=Math.min(60*t,360))<0&&(t+=360),[t,n,i/255*1e3/10]},hsl2rgb:function(e){var t,n,a,r,o,l=e[0]/360,i=e[1]/100,s=e[2]/100;if(0===i)return[o=255*s,o,o];t=2*s-(n=s<.5?s*(1+i):s+i-s*i),r=[0,0,0];for(var u=0;u<3;u++)(a=l+1/3*-(u-1))<0&&a++,a>1&&a--,o=6*a<1?t+6*(n-t)*a:2*a<1?n:3*a<2?t+(n-t)*(2/3-a)*6:t,r[u]=255*o;return r},hsl2hsv:function(e){var t=e[0],n=e[1]/100,a=e[2]/100;return[t,2*(n*=(a*=2)<=1?a:2-a)/(a+n)*100,(a+n)/2*100]},hsv2rgb:function(e){var t=e[0]/60,n=e[1]/100,a=e[2]/100,r=Math.floor(t)%6,o=t-Math.floor(t),l=255*a*(1-n),i=255*a*(1-n*o),s=255*a*(1-n*(1-o));switch(a*=255,r){case 0:return[a,s,l];case 1:return[i,a,l];case 2:return[l,a,s];case 3:return[l,i,a];case 4:return[s,l,a];case 5:return[a,l,i]}},hsv2hsl:function(e){var t,n,a=e[0],r=e[1]/100,o=e[2]/100;return t=r*o,[a,100*(t/=(n=(2-r)*o)<=1?n:2-n),100*(n/=2)]},rgb2hex:function(e,t,n){return"#"+((256+e<<8|t)<<8|n).toString(16).slice(1)},hex2rgb:function(e){return[(e="0x"+e.slice(1).replace(e.length>4?e:/./g,"$&$&")|0)>>16,e>>8&255,255&e]}}},function(e,t){e.exports={navy:{value:"#000080",nicer:"#001F3F"},blue:{value:"#0000ff",nicer:"#0074D9"},aqua:{value:"#00ffff",nicer:"#7FDBFF"},teal:{value:"#008080",nicer:"#39CCCC"},olive:{value:"#008000",nicer:"#3D9970"},green:{value:"#008000",nicer:"#2ECC40"},lime:{value:"#00ff00",nicer:"#01FF70"},yellow:{value:"#ffff00",nicer:"#FFDC00"},orange:{value:"#ffa500",nicer:"#FF851B"},red:{value:"#ff0000",nicer:"#FF4136"},maroon:{value:"#800000",nicer:"#85144B"},fuchsia:{value:"#ff00ff",nicer:"#F012BE"},purple:{value:"#800080",nicer:"#B10DC9"},silver:{value:"#c0c0c0",nicer:"#DDDDDD"},gray:{value:"#808080",nicer:"#AAAAAA"},black:{value:"#000000",nicer:"#111111"},white:{value:"#FFFFFF",nicer:"#FFFFFF"}}},function(e,t,n){var a=n(6),r=n(14);function o(e,t,n,r){return void 0===n?a.natural(e,t):void 0===r?n:a.natural(parseInt(n,10),parseInt(r,10))}e.exports={paragraph:function(e,t){for(var n=o(3,7,e,t),a=[],r=0;r<n;r++)a.push(this.sentence());return a.join(" ")},cparagraph:function(e,t){for(var n=o(3,7,e,t),a=[],r=0;r<n;r++)a.push(this.csentence());return a.join("")},sentence:function(e,t){for(var n=o(12,18,e,t),a=[],l=0;l<n;l++)a.push(this.word());return r.capitalize(a.join(" "))+"."},csentence:function(e,t){for(var n=o(12,18,e,t),a=[],r=0;r<n;r++)a.push(this.cword());return a.join("")+"。"},word:function(e,t){for(var n=o(3,10,e,t),r="",l=0;l<n;l++)r+=a.character("lower");return r},cword:function(e,t,n){var a,r="的一是在不了有和人这中大为上个国我以要他时来用们生到作地于出就分对成会可主发年动同工也能下过子说产种面而方后多定行学法所民得经十三之进着等部度家电力里如水化高自二理起小物现实加量都两体制机当使点从业本去把性好应开它合还因由其些然前外天政四日那社义事平形相全表间样与关各重新线内数正心反你明看原又么利比或但质气第向道命此变条只没结解问意建月公无系军很情者最立代想已通并提直题党程展五果料象员革位入常文总次品式活设及管特件长求老头基资边流路级少图山统接知较将组见计别她手角期根论运农指几九区强放决西被干做必战先回则任取据处队南给色光门即保治北造百规热领七海口东导器压志世金增争济阶油思术极交受联什认六共权收证改清己美再采转更单风切打白教速花带安场身车例真务具万每目至达走积示议声报斗完类八离华名确才科张信马节话米整空元况今集温传土许步群广石记需段研界拉林律叫且究观越织装影算低持音众书布复容儿须际商非验连断深难近矿千周委素技备半办青省列习响约支般史感劳便团往酸历市克何除消构府称太准精值号率族维划选标写存候毛亲快效斯院查江型眼王按格养易置派层片始却专状育厂京识适属圆包火住调满县局照参红细引听该铁价严龙飞";switch(arguments.length){case 0:e=r,a=1;break;case 1:"string"==typeof arguments[0]?a=1:(a=e,e=r);break;case 2:"string"==typeof arguments[0]?a=t:(a=this.natural(e,t),e=r);break;case 3:a=this.natural(t,n)}for(var o="",l=0;l<a;l++)o+=e.charAt(this.natural(0,e.length-1));return o},title:function(e,t){for(var n=o(3,7,e,t),a=[],r=0;r<n;r++)a.push(this.capitalize(this.word()));return a.join(" ")},ctitle:function(e,t){for(var n=o(3,7,e,t),a=[],r=0;r<n;r++)a.push(this.cword());return a.join("")}}},function(e,t,n){var a=n(3);e.exports={capitalize:function(e){return(e+"").charAt(0).toUpperCase()+(e+"").substr(1)},upper:function(e){return(e+"").toUpperCase()},lower:function(e){return(e+"").toLowerCase()},pick:function(e,t,n){return a.isArray(e)?(void 0===t&&(t=1),void 0===n&&(n=t)):(e=[].slice.call(arguments),t=1,n=1),1===t&&1===n?e[this.natural(0,e.length-1)]:this.shuffle(e,t,n)},shuffle:function(e,t,n){for(var a=(e=e||[]).slice(0),r=[],o=0,l=a.length,i=0;i<l;i++)o=this.natural(0,a.length-1),r.push(a[o]),a.splice(o,1);switch(arguments.length){case 0:case 1:return r;case 2:n=t;case 3:return t=parseInt(t,10),n=parseInt(n,10),r.slice(0,this.natural(t,n))}},order:function e(t){e.cache=e.cache||{},arguments.length>1&&(t=[].slice.call(arguments,0));var n=e.options,a=n.context.templatePath.join("."),r=e.cache[a]=e.cache[a]||{index:0,array:t};return r.array[r.index++%r.array.length]}}},function(e,t){e.exports={first:function(){var e=["James","John","Robert","Michael","William","David","Richard","Charles","Joseph","Thomas","Christopher","Daniel","Paul","Mark","Donald","George","Kenneth","Steven","Edward","Brian","Ronald","Anthony","Kevin","Jason","Matthew","Gary","Timothy","Jose","Larry","Jeffrey","Frank","Scott","Eric"].concat(["Mary","Patricia","Linda","Barbara","Elizabeth","Jennifer","Maria","Susan","Margaret","Dorothy","Lisa","Nancy","Karen","Betty","Helen","Sandra","Donna","Carol","Ruth","Sharon","Michelle","Laura","Sarah","Kimberly","Deborah","Jessica","Shirley","Cynthia","Angela","Melissa","Brenda","Amy","Anna"]);return this.pick(e)},last:function(){return this.pick(["Smith","Johnson","Williams","Brown","Jones","Miller","Davis","Garcia","Rodriguez","Wilson","Martinez","Anderson","Taylor","Thomas","Hernandez","Moore","Martin","Jackson","Thompson","White","Lopez","Lee","Gonzalez","Harris","Clark","Lewis","Robinson","Walker","Perez","Hall","Young","Allen"])},name:function(e){return this.first()+" "+(e?this.first()+" ":"")+this.last()},cfirst:function(){var e="王 李 张 刘 陈 杨 赵 黄 周 吴 徐 孙 胡 朱 高 林 何 郭 马 罗 梁 宋 郑 谢 韩 唐 冯 于 董 萧 程 曹 袁 邓 许 傅 沈 曾 彭 吕 苏 卢 蒋 蔡 贾 丁 魏 薛 叶 阎 余 潘 杜 戴 夏 锺 汪 田 任 姜 范 方 石 姚 谭 廖 邹 熊 金 陆 郝 孔 白 崔 康 毛 邱 秦 江 史 顾 侯 邵 孟 龙 万 段 雷 钱 汤 尹 黎 易 常 武 乔 贺 赖 龚 文".split(" ");return this.pick(e)},clast:function(){var e="伟 芳 娜 秀英 敏 静 丽 强 磊 军 洋 勇 艳 杰 娟 涛 明 超 秀兰 霞 平 刚 桂英".split(" ");return this.pick(e)},cname:function(){return this.cfirst()+this.clast()}}},function(e,t){e.exports={url:function(e,t){return(e||this.protocol())+"://"+(t||this.domain())+"/"+this.word()},protocol:function(){return this.pick("http ftp gopher mailto mid cid news nntp prospero telnet rlogin tn3270 wais".split(" "))},domain:function(e){return this.word()+"."+(e||this.tld())},tld:function(){return this.pick("com net org edu gov int mil cn com.cn net.cn gov.cn org.cn 中国 中国互联.公司 中国互联.网络 tel biz cc tv info name hk mobi asia cd travel pro museum coop aero ad ae af ag ai al am an ao aq ar as at au aw az ba bb bd be bf bg bh bi bj bm bn bo br bs bt bv bw by bz ca cc cf cg ch ci ck cl cm cn co cq cr cu cv cx cy cz de dj dk dm do dz ec ee eg eh es et ev fi fj fk fm fo fr ga gb gd ge gf gh gi gl gm gn gp gr gt gu gw gy hk hm hn hr ht hu id ie il in io iq ir is it jm jo jp ke kg kh ki km kn kp kr kw ky kz la lb lc li lk lr ls lt lu lv ly ma mc md mg mh ml mm mn mo mp mq mr ms mt mv mw mx my mz na nc ne nf ng ni nl no np nr nt nu nz om qa pa pe pf pg ph pk pl pm pn pr pt pw py re ro ru rw sa sb sc sd se sg sh si sj sk sl sm sn so sr st su sy sz tc td tf tg th tj tk tm tn to tp tr tt tv tw tz ua ug uk us uy va vc ve vg vn vu wf ws ye yu za zm zr zw".split(" "))},email:function(e){return this.character("lower")+"."+this.word()+"@"+(e||this.word()+"."+this.tld())},ip:function(){return this.natural(0,255)+"."+this.natural(0,255)+"."+this.natural(0,255)+"."+this.natural(0,255)}}},function(e,t,n){var a=n(18),r=["东北","华北","华东","华中","华南","西南","西北"];e.exports={region:function(){return this.pick(r)},province:function(){return this.pick(a).name},city:function(e){var t=this.pick(a),n=this.pick(t.children);return e?[t.name,n.name].join(" "):n.name},county:function(e){var t=this.pick(a),n=this.pick(t.children),r=this.pick(n.children)||{name:"-"};return e?[t.name,n.name,r.name].join(" "):r.name},zip:function(e){for(var t="",n=0;n<(e||6);n++)t+=this.natural(0,9);return t}}},function(e,t){var n={11e4:"北京",110100:"北京市",110101:"东城区",110102:"西城区",110105:"朝阳区",110106:"丰台区",110107:"石景山区",110108:"海淀区",110109:"门头沟区",110111:"房山区",110112:"通州区",110113:"顺义区",110114:"昌平区",110115:"大兴区",110116:"怀柔区",110117:"平谷区",110228:"密云县",110229:"延庆县",110230:"其它区",12e4:"天津",120100:"天津市",120101:"和平区",120102:"河东区",120103:"河西区",120104:"南开区",120105:"河北区",120106:"红桥区",120110:"东丽区",120111:"西青区",120112:"津南区",120113:"北辰区",120114:"武清区",120115:"宝坻区",120116:"滨海新区",120221:"宁河县",120223:"静海县",120225:"蓟县",120226:"其它区",13e4:"河北省",130100:"石家庄市",130102:"长安区",130103:"桥东区",130104:"桥西区",130105:"新华区",130107:"井陉矿区",130108:"裕华区",130121:"井陉县",130123:"正定县",130124:"栾城县",130125:"行唐县",130126:"灵寿县",130127:"高邑县",130128:"深泽县",130129:"赞皇县",130130:"无极县",130131:"平山县",130132:"元氏县",130133:"赵县",130181:"辛集市",130182:"藁城市",130183:"晋州市",130184:"新乐市",130185:"鹿泉市",130186:"其它区",130200:"唐山市",130202:"路南区",130203:"路北区",130204:"古冶区",130205:"开平区",130207:"丰南区",130208:"丰润区",130223:"滦县",130224:"滦南县",130225:"乐亭县",130227:"迁西县",130229:"玉田县",130230:"曹妃甸区",130281:"遵化市",130283:"迁安市",130284:"其它区",130300:"秦皇岛市",130302:"海港区",130303:"山海关区",130304:"北戴河区",130321:"青龙满族自治县",130322:"昌黎县",130323:"抚宁县",130324:"卢龙县",130398:"其它区",130400:"邯郸市",130402:"邯山区",130403:"丛台区",130404:"复兴区",130406:"峰峰矿区",130421:"邯郸县",130423:"临漳县",130424:"成安县",130425:"大名县",130426:"涉县",130427:"磁县",130428:"肥乡县",130429:"永年县",130430:"邱县",130431:"鸡泽县",130432:"广平县",130433:"馆陶县",130434:"魏县",130435:"曲周县",130481:"武安市",130482:"其它区",130500:"邢台市",130502:"桥东区",130503:"桥西区",130521:"邢台县",130522:"临城县",130523:"内丘县",130524:"柏乡县",130525:"隆尧县",130526:"任县",130527:"南和县",130528:"宁晋县",130529:"巨鹿县",130530:"新河县",130531:"广宗县",130532:"平乡县",130533:"威县",130534:"清河县",130535:"临西县",130581:"南宫市",130582:"沙河市",130583:"其它区",130600:"保定市",130602:"新市区",130603:"北市区",130604:"南市区",130621:"满城县",130622:"清苑县",130623:"涞水县",130624:"阜平县",130625:"徐水县",130626:"定兴县",130627:"唐县",130628:"高阳县",130629:"容城县",130630:"涞源县",130631:"望都县",130632:"安新县",130633:"易县",130634:"曲阳县",130635:"蠡县",130636:"顺平县",130637:"博野县",130638:"雄县",130681:"涿州市",130682:"定州市",130683:"安国市",130684:"高碑店市",130699:"其它区",130700:"张家口市",130702:"桥东区",130703:"桥西区",130705:"宣化区",130706:"下花园区",130721:"宣化县",130722:"张北县",130723:"康保县",130724:"沽源县",130725:"尚义县",130726:"蔚县",130727:"阳原县",130728:"怀安县",130729:"万全县",130730:"怀来县",130731:"涿鹿县",130732:"赤城县",130733:"崇礼县",130734:"其它区",130800:"承德市",130802:"双桥区",130803:"双滦区",130804:"鹰手营子矿区",130821:"承德县",130822:"兴隆县",130823:"平泉县",130824:"滦平县",130825:"隆化县",130826:"丰宁满族自治县",130827:"宽城满族自治县",130828:"围场满族蒙古族自治县",130829:"其它区",130900:"沧州市",130902:"新华区",130903:"运河区",130921:"沧县",130922:"青县",130923:"东光县",130924:"海兴县",130925:"盐山县",130926:"肃宁县",130927:"南皮县",130928:"吴桥县",130929:"献县",130930:"孟村回族自治县",130981:"泊头市",130982:"任丘市",130983:"黄骅市",130984:"河间市",130985:"其它区",131e3:"廊坊市",131002:"安次区",131003:"广阳区",131022:"固安县",131023:"永清县",131024:"香河县",131025:"大城县",131026:"文安县",131028:"大厂回族自治县",131081:"霸州市",131082:"三河市",131083:"其它区",131100:"衡水市",131102:"桃城区",131121:"枣强县",131122:"武邑县",131123:"武强县",131124:"饶阳县",131125:"安平县",131126:"故城县",131127:"景县",131128:"阜城县",131181:"冀州市",131182:"深州市",131183:"其它区",14e4:"山西省",140100:"太原市",140105:"小店区",140106:"迎泽区",140107:"杏花岭区",140108:"尖草坪区",140109:"万柏林区",140110:"晋源区",140121:"清徐县",140122:"阳曲县",140123:"娄烦县",140181:"古交市",140182:"其它区",140200:"大同市",140202:"城区",140203:"矿区",140211:"南郊区",140212:"新荣区",140221:"阳高县",140222:"天镇县",140223:"广灵县",140224:"灵丘县",140225:"浑源县",140226:"左云县",140227:"大同县",140228:"其它区",140300:"阳泉市",140302:"城区",140303:"矿区",140311:"郊区",140321:"平定县",140322:"盂县",140323:"其它区",140400:"长治市",140421:"长治县",140423:"襄垣县",140424:"屯留县",140425:"平顺县",140426:"黎城县",140427:"壶关县",140428:"长子县",140429:"武乡县",140430:"沁县",140431:"沁源县",140481:"潞城市",140482:"城区",140483:"郊区",140485:"其它区",140500:"晋城市",140502:"城区",140521:"沁水县",140522:"阳城县",140524:"陵川县",140525:"泽州县",140581:"高平市",140582:"其它区",140600:"朔州市",140602:"朔城区",140603:"平鲁区",140621:"山阴县",140622:"应县",140623:"右玉县",140624:"怀仁县",140625:"其它区",140700:"晋中市",140702:"榆次区",140721:"榆社县",140722:"左权县",140723:"和顺县",140724:"昔阳县",140725:"寿阳县",140726:"太谷县",140727:"祁县",140728:"平遥县",140729:"灵石县",140781:"介休市",140782:"其它区",140800:"运城市",140802:"盐湖区",140821:"临猗县",140822:"万荣县",140823:"闻喜县",140824:"稷山县",140825:"新绛县",140826:"绛县",140827:"垣曲县",140828:"夏县",140829:"平陆县",140830:"芮城县",140881:"永济市",140882:"河津市",140883:"其它区",140900:"忻州市",140902:"忻府区",140921:"定襄县",140922:"五台县",140923:"代县",140924:"繁峙县",140925:"宁武县",140926:"静乐县",140927:"神池县",140928:"五寨县",140929:"岢岚县",140930:"河曲县",140931:"保德县",140932:"偏关县",140981:"原平市",140982:"其它区",141e3:"临汾市",141002:"尧都区",141021:"曲沃县",141022:"翼城县",141023:"襄汾县",141024:"洪洞县",141025:"古县",141026:"安泽县",141027:"浮山县",141028:"吉县",141029:"乡宁县",141030:"大宁县",141031:"隰县",141032:"永和县",141033:"蒲县",141034:"汾西县",141081:"侯马市",141082:"霍州市",141083:"其它区",141100:"吕梁市",141102:"离石区",141121:"文水县",141122:"交城县",141123:"兴县",141124:"临县",141125:"柳林县",141126:"石楼县",141127:"岚县",141128:"方山县",141129:"中阳县",141130:"交口县",141181:"孝义市",141182:"汾阳市",141183:"其它区",15e4:"内蒙古自治区",150100:"呼和浩特市",150102:"新城区",150103:"回民区",150104:"玉泉区",150105:"赛罕区",150121:"土默特左旗",150122:"托克托县",150123:"和林格尔县",150124:"清水河县",150125:"武川县",150126:"其它区",150200:"包头市",150202:"东河区",150203:"昆都仑区",150204:"青山区",150205:"石拐区",150206:"白云鄂博矿区",150207:"九原区",150221:"土默特右旗",150222:"固阳县",150223:"达尔罕茂明安联合旗",150224:"其它区",150300:"乌海市",150302:"海勃湾区",150303:"海南区",150304:"乌达区",150305:"其它区",150400:"赤峰市",150402:"红山区",150403:"元宝山区",150404:"松山区",150421:"阿鲁科尔沁旗",150422:"巴林左旗",150423:"巴林右旗",150424:"林西县",150425:"克什克腾旗",150426:"翁牛特旗",150428:"喀喇沁旗",150429:"宁城县",150430:"敖汉旗",150431:"其它区",150500:"通辽市",150502:"科尔沁区",150521:"科尔沁左翼中旗",150522:"科尔沁左翼后旗",150523:"开鲁县",150524:"库伦旗",150525:"奈曼旗",150526:"扎鲁特旗",150581:"霍林郭勒市",150582:"其它区",150600:"鄂尔多斯市",150602:"东胜区",150621:"达拉特旗",150622:"准格尔旗",150623:"鄂托克前旗",150624:"鄂托克旗",150625:"杭锦旗",150626:"乌审旗",150627:"伊金霍洛旗",150628:"其它区",150700:"呼伦贝尔市",150702:"海拉尔区",150703:"扎赉诺尔区",150721:"阿荣旗",150722:"莫力达瓦达斡尔族自治旗",150723:"鄂伦春自治旗",150724:"鄂温克族自治旗",150725:"陈巴尔虎旗",150726:"新巴尔虎左旗",150727:"新巴尔虎右旗",150781:"满洲里市",150782:"牙克石市",150783:"扎兰屯市",150784:"额尔古纳市",150785:"根河市",150786:"其它区",150800:"巴彦淖尔市",150802:"临河区",150821:"五原县",150822:"磴口县",150823:"乌拉特前旗",150824:"乌拉特中旗",150825:"乌拉特后旗",150826:"杭锦后旗",150827:"其它区",150900:"乌兰察布市",150902:"集宁区",150921:"卓资县",150922:"化德县",150923:"商都县",150924:"兴和县",150925:"凉城县",150926:"察哈尔右翼前旗",150927:"察哈尔右翼中旗",150928:"察哈尔右翼后旗",150929:"四子王旗",150981:"丰镇市",150982:"其它区",152200:"兴安盟",152201:"乌兰浩特市",152202:"阿尔山市",152221:"科尔沁右翼前旗",152222:"科尔沁右翼中旗",152223:"扎赉特旗",152224:"突泉县",152225:"其它区",152500:"锡林郭勒盟",152501:"二连浩特市",152502:"锡林浩特市",152522:"阿巴嘎旗",152523:"苏尼特左旗",152524:"苏尼特右旗",152525:"东乌珠穆沁旗",152526:"西乌珠穆沁旗",152527:"太仆寺旗",152528:"镶黄旗",152529:"正镶白旗",152530:"正蓝旗",152531:"多伦县",152532:"其它区",152900:"阿拉善盟",152921:"阿拉善左旗",152922:"阿拉善右旗",152923:"额济纳旗",152924:"其它区",21e4:"辽宁省",210100:"沈阳市",210102:"和平区",210103:"沈河区",210104:"大东区",210105:"皇姑区",210106:"铁西区",210111:"苏家屯区",210112:"东陵区",210113:"新城子区",210114:"于洪区",210122:"辽中县",210123:"康平县",210124:"法库县",210181:"新民市",210184:"沈北新区",210185:"其它区",210200:"大连市",210202:"中山区",210203:"西岗区",210204:"沙河口区",210211:"甘井子区",210212:"旅顺口区",210213:"金州区",210224:"长海县",210281:"瓦房店市",210282:"普兰店市",210283:"庄河市",210298:"其它区",210300:"鞍山市",210302:"铁东区",210303:"铁西区",210304:"立山区",210311:"千山区",210321:"台安县",210323:"岫岩满族自治县",210381:"海城市",210382:"其它区",210400:"抚顺市",210402:"新抚区",210403:"东洲区",210404:"望花区",210411:"顺城区",210421:"抚顺县",210422:"新宾满族自治县",210423:"清原满族自治县",210424:"其它区",210500:"本溪市",210502:"平山区",210503:"溪湖区",210504:"明山区",210505:"南芬区",210521:"本溪满族自治县",210522:"桓仁满族自治县",210523:"其它区",210600:"丹东市",210602:"元宝区",210603:"振兴区",210604:"振安区",210624:"宽甸满族自治县",210681:"东港市",210682:"凤城市",210683:"其它区",210700:"锦州市",210702:"古塔区",210703:"凌河区",210711:"太和区",210726:"黑山县",210727:"义县",210781:"凌海市",210782:"北镇市",210783:"其它区",210800:"营口市",210802:"站前区",210803:"西市区",210804:"鲅鱼圈区",210811:"老边区",210881:"盖州市",210882:"大石桥市",210883:"其它区",210900:"阜新市",210902:"海州区",210903:"新邱区",210904:"太平区",210905:"清河门区",210911:"细河区",210921:"阜新蒙古族自治县",210922:"彰武县",210923:"其它区",211e3:"辽阳市",211002:"白塔区",211003:"文圣区",211004:"宏伟区",211005:"弓长岭区",211011:"太子河区",211021:"辽阳县",211081:"灯塔市",211082:"其它区",211100:"盘锦市",211102:"双台子区",211103:"兴隆台区",211121:"大洼县",211122:"盘山县",211123:"其它区",211200:"铁岭市",211202:"银州区",211204:"清河区",211221:"铁岭县",211223:"西丰县",211224:"昌图县",211281:"调兵山市",211282:"开原市",211283:"其它区",211300:"朝阳市",211302:"双塔区",211303:"龙城区",211321:"朝阳县",211322:"建平县",211324:"喀喇沁左翼蒙古族自治县",211381:"北票市",211382:"凌源市",211383:"其它区",211400:"葫芦岛市",211402:"连山区",211403:"龙港区",211404:"南票区",211421:"绥中县",211422:"建昌县",211481:"兴城市",211482:"其它区",22e4:"吉林省",220100:"长春市",220102:"南关区",220103:"宽城区",220104:"朝阳区",220105:"二道区",220106:"绿园区",220112:"双阳区",220122:"农安县",220181:"九台市",220182:"榆树市",220183:"德惠市",220188:"其它区",220200:"吉林市",220202:"昌邑区",220203:"龙潭区",220204:"船营区",220211:"丰满区",220221:"永吉县",220281:"蛟河市",220282:"桦甸市",220283:"舒兰市",220284:"磐石市",220285:"其它区",220300:"四平市",220302:"铁西区",220303:"铁东区",220322:"梨树县",220323:"伊通满族自治县",220381:"公主岭市",220382:"双辽市",220383:"其它区",220400:"辽源市",220402:"龙山区",220403:"西安区",220421:"东丰县",220422:"东辽县",220423:"其它区",220500:"通化市",220502:"东昌区",220503:"二道江区",220521:"通化县",220523:"辉南县",220524:"柳河县",220581:"梅河口市",220582:"集安市",220583:"其它区",220600:"白山市",220602:"浑江区",220621:"抚松县",220622:"靖宇县",220623:"长白朝鲜族自治县",220625:"江源区",220681:"临江市",220682:"其它区",220700:"松原市",220702:"宁江区",220721:"前郭尔罗斯蒙古族自治县",220722:"长岭县",220723:"乾安县",220724:"扶余市",220725:"其它区",220800:"白城市",220802:"洮北区",220821:"镇赉县",220822:"通榆县",220881:"洮南市",220882:"大安市",220883:"其它区",222400:"延边朝鲜族自治州",222401:"延吉市",222402:"图们市",222403:"敦化市",222404:"珲春市",222405:"龙井市",222406:"和龙市",222424:"汪清县",222426:"安图县",222427:"其它区",23e4:"黑龙江省",230100:"哈尔滨市",230102:"道里区",230103:"南岗区",230104:"道外区",230106:"香坊区",230108:"平房区",230109:"松北区",230111:"呼兰区",230123:"依兰县",230124:"方正县",230125:"宾县",230126:"巴彦县",230127:"木兰县",230128:"通河县",230129:"延寿县",230181:"阿城区",230182:"双城市",230183:"尚志市",230184:"五常市",230186:"其它区",230200:"齐齐哈尔市",230202:"龙沙区",230203:"建华区",230204:"铁锋区",230205:"昂昂溪区",230206:"富拉尔基区",230207:"碾子山区",230208:"梅里斯达斡尔族区",230221:"龙江县",230223:"依安县",230224:"泰来县",230225:"甘南县",230227:"富裕县",230229:"克山县",230230:"克东县",230231:"拜泉县",230281:"讷河市",230282:"其它区",230300:"鸡西市",230302:"鸡冠区",230303:"恒山区",230304:"滴道区",230305:"梨树区",230306:"城子河区",230307:"麻山区",230321:"鸡东县",230381:"虎林市",230382:"密山市",230383:"其它区",230400:"鹤岗市",230402:"向阳区",230403:"工农区",230404:"南山区",230405:"兴安区",230406:"东山区",230407:"兴山区",230421:"萝北县",230422:"绥滨县",230423:"其它区",230500:"双鸭山市",230502:"尖山区",230503:"岭东区",230505:"四方台区",230506:"宝山区",230521:"集贤县",230522:"友谊县",230523:"宝清县",230524:"饶河县",230525:"其它区",230600:"大庆市",230602:"萨尔图区",230603:"龙凤区",230604:"让胡路区",230605:"红岗区",230606:"大同区",230621:"肇州县",230622:"肇源县",230623:"林甸县",230624:"杜尔伯特蒙古族自治县",230625:"其它区",230700:"伊春市",230702:"伊春区",230703:"南岔区",230704:"友好区",230705:"西林区",230706:"翠峦区",230707:"新青区",230708:"美溪区",230709:"金山屯区",230710:"五营区",230711:"乌马河区",230712:"汤旺河区",230713:"带岭区",230714:"乌伊岭区",230715:"红星区",230716:"上甘岭区",230722:"嘉荫县",230781:"铁力市",230782:"其它区",230800:"佳木斯市",230803:"向阳区",230804:"前进区",230805:"东风区",230811:"郊区",230822:"桦南县",230826:"桦川县",230828:"汤原县",230833:"抚远县",230881:"同江市",230882:"富锦市",230883:"其它区",230900:"七台河市",230902:"新兴区",230903:"桃山区",230904:"茄子河区",230921:"勃利县",230922:"其它区",231e3:"牡丹江市",231002:"东安区",231003:"阳明区",231004:"爱民区",231005:"西安区",231024:"东宁县",231025:"林口县",231081:"绥芬河市",231083:"海林市",231084:"宁安市",231085:"穆棱市",231086:"其它区",231100:"黑河市",231102:"爱辉区",231121:"嫩江县",231123:"逊克县",231124:"孙吴县",231181:"北安市",231182:"五大连池市",231183:"其它区",231200:"绥化市",231202:"北林区",231221:"望奎县",231222:"兰西县",231223:"青冈县",231224:"庆安县",231225:"明水县",231226:"绥棱县",231281:"安达市",231282:"肇东市",231283:"海伦市",231284:"其它区",232700:"大兴安岭地区",232702:"松岭区",232703:"新林区",232704:"呼中区",232721:"呼玛县",232722:"塔河县",232723:"漠河县",232724:"加格达奇区",232725:"其它区",31e4:"上海",310100:"上海市",310101:"黄浦区",310104:"徐汇区",310105:"长宁区",310106:"静安区",310107:"普陀区",310108:"闸北区",310109:"虹口区",310110:"杨浦区",310112:"闵行区",310113:"宝山区",310114:"嘉定区",310115:"浦东新区",310116:"金山区",310117:"松江区",310118:"青浦区",310120:"奉贤区",310230:"崇明县",310231:"其它区",32e4:"江苏省",320100:"南京市",320102:"玄武区",320104:"秦淮区",320105:"建邺区",320106:"鼓楼区",320111:"浦口区",320113:"栖霞区",320114:"雨花台区",320115:"江宁区",320116:"六合区",320124:"溧水区",320125:"高淳区",320126:"其它区",320200:"无锡市",320202:"崇安区",320203:"南长区",320204:"北塘区",320205:"锡山区",320206:"惠山区",320211:"滨湖区",320281:"江阴市",320282:"宜兴市",320297:"其它区",320300:"徐州市",320302:"鼓楼区",320303:"云龙区",320305:"贾汪区",320311:"泉山区",320321:"丰县",320322:"沛县",320323:"铜山区",320324:"睢宁县",320381:"新沂市",320382:"邳州市",320383:"其它区",320400:"常州市",320402:"天宁区",320404:"钟楼区",320405:"戚墅堰区",320411:"新北区",320412:"武进区",320481:"溧阳市",320482:"金坛市",320483:"其它区",320500:"苏州市",320505:"虎丘区",320506:"吴中区",320507:"相城区",320508:"姑苏区",320581:"常熟市",320582:"张家港市",320583:"昆山市",320584:"吴江区",320585:"太仓市",320596:"其它区",320600:"南通市",320602:"崇川区",320611:"港闸区",320612:"通州区",320621:"海安县",320623:"如东县",320681:"启东市",320682:"如皋市",320684:"海门市",320694:"其它区",320700:"连云港市",320703:"连云区",320705:"新浦区",320706:"海州区",320721:"赣榆县",320722:"东海县",320723:"灌云县",320724:"灌南县",320725:"其它区",320800:"淮安市",320802:"清河区",320803:"淮安区",320804:"淮阴区",320811:"清浦区",320826:"涟水县",320829:"洪泽县",320830:"盱眙县",320831:"金湖县",320832:"其它区",320900:"盐城市",320902:"亭湖区",320903:"盐都区",320921:"响水县",320922:"滨海县",320923:"阜宁县",320924:"射阳县",320925:"建湖县",320981:"东台市",320982:"大丰市",320983:"其它区",321e3:"扬州市",321002:"广陵区",321003:"邗江区",321023:"宝应县",321081:"仪征市",321084:"高邮市",321088:"江都区",321093:"其它区",321100:"镇江市",321102:"京口区",321111:"润州区",321112:"丹徒区",321181:"丹阳市",321182:"扬中市",321183:"句容市",321184:"其它区",321200:"泰州市",321202:"海陵区",321203:"高港区",321281:"兴化市",321282:"靖江市",321283:"泰兴市",321284:"姜堰区",321285:"其它区",321300:"宿迁市",321302:"宿城区",321311:"宿豫区",321322:"沭阳县",321323:"泗阳县",321324:"泗洪县",321325:"其它区",33e4:"浙江省",330100:"杭州市",330102:"上城区",330103:"下城区",330104:"江干区",330105:"拱墅区",330106:"西湖区",330108:"滨江区",330109:"萧山区",330110:"余杭区",330122:"桐庐县",330127:"淳安县",330182:"建德市",330183:"富阳市",330185:"临安市",330186:"其它区",330200:"宁波市",330203:"海曙区",330204:"江东区",330205:"江北区",330206:"北仑区",330211:"镇海区",330212:"鄞州区",330225:"象山县",330226:"宁海县",330281:"余姚市",330282:"慈溪市",330283:"奉化市",330284:"其它区",330300:"温州市",330302:"鹿城区",330303:"龙湾区",330304:"瓯海区",330322:"洞头县",330324:"永嘉县",330326:"平阳县",330327:"苍南县",330328:"文成县",330329:"泰顺县",330381:"瑞安市",330382:"乐清市",330383:"其它区",330400:"嘉兴市",330402:"南湖区",330411:"秀洲区",330421:"嘉善县",330424:"海盐县",330481:"海宁市",330482:"平湖市",330483:"桐乡市",330484:"其它区",330500:"湖州市",330502:"吴兴区",330503:"南浔区",330521:"德清县",330522:"长兴县",330523:"安吉县",330524:"其它区",330600:"绍兴市",330602:"越城区",330621:"绍兴县",330624:"新昌县",330681:"诸暨市",330682:"上虞市",330683:"嵊州市",330684:"其它区",330700:"金华市",330702:"婺城区",330703:"金东区",330723:"武义县",330726:"浦江县",330727:"磐安县",330781:"兰溪市",330782:"义乌市",330783:"东阳市",330784:"永康市",330785:"其它区",330800:"衢州市",330802:"柯城区",330803:"衢江区",330822:"常山县",330824:"开化县",330825:"龙游县",330881:"江山市",330882:"其它区",330900:"舟山市",330902:"定海区",330903:"普陀区",330921:"岱山县",330922:"嵊泗县",330923:"其它区",331e3:"台州市",331002:"椒江区",331003:"黄岩区",331004:"路桥区",331021:"玉环县",331022:"三门县",331023:"天台县",331024:"仙居县",331081:"温岭市",331082:"临海市",331083:"其它区",331100:"丽水市",331102:"莲都区",331121:"青田县",331122:"缙云县",331123:"遂昌县",331124:"松阳县",331125:"云和县",331126:"庆元县",331127:"景宁畲族自治县",331181:"龙泉市",331182:"其它区",34e4:"安徽省",340100:"合肥市",340102:"瑶海区",340103:"庐阳区",340104:"蜀山区",340111:"包河区",340121:"长丰县",340122:"肥东县",340123:"肥西县",340192:"其它区",340200:"芜湖市",340202:"镜湖区",340203:"弋江区",340207:"鸠江区",340208:"三山区",340221:"芜湖县",340222:"繁昌县",340223:"南陵县",340224:"其它区",340300:"蚌埠市",340302:"龙子湖区",340303:"蚌山区",340304:"禹会区",340311:"淮上区",340321:"怀远县",340322:"五河县",340323:"固镇县",340324:"其它区",340400:"淮南市",340402:"大通区",340403:"田家庵区",340404:"谢家集区",340405:"八公山区",340406:"潘集区",340421:"凤台县",340422:"其它区",340500:"马鞍山市",340503:"花山区",340504:"雨山区",340506:"博望区",340521:"当涂县",340522:"其它区",340600:"淮北市",340602:"杜集区",340603:"相山区",340604:"烈山区",340621:"濉溪县",340622:"其它区",340700:"铜陵市",340702:"铜官山区",340703:"狮子山区",340711:"郊区",340721:"铜陵县",340722:"其它区",340800:"安庆市",340802:"迎江区",340803:"大观区",340811:"宜秀区",340822:"怀宁县",340823:"枞阳县",340824:"潜山县",340825:"太湖县",340826:"宿松县",340827:"望江县",340828:"岳西县",340881:"桐城市",340882:"其它区",341e3:"黄山市",341002:"屯溪区",341003:"黄山区",341004:"徽州区",341021:"歙县",341022:"休宁县",341023:"黟县",341024:"祁门县",341025:"其它区",341100:"滁州市",341102:"琅琊区",341103:"南谯区",341122:"来安县",341124:"全椒县",341125:"定远县",341126:"凤阳县",341181:"天长市",341182:"明光市",341183:"其它区",341200:"阜阳市",341202:"颍州区",341203:"颍东区",341204:"颍泉区",341221:"临泉县",341222:"太和县",341225:"阜南县",341226:"颍上县",341282:"界首市",341283:"其它区",341300:"宿州市",341302:"埇桥区",341321:"砀山县",341322:"萧县",341323:"灵璧县",341324:"泗县",341325:"其它区",341400:"巢湖市",341421:"庐江县",341422:"无为县",341423:"含山县",341424:"和县",341500:"六安市",341502:"金安区",341503:"裕安区",341521:"寿县",341522:"霍邱县",341523:"舒城县",341524:"金寨县",341525:"霍山县",341526:"其它区",341600:"亳州市",341602:"谯城区",341621:"涡阳县",341622:"蒙城县",341623:"利辛县",341624:"其它区",341700:"池州市",341702:"贵池区",341721:"东至县",341722:"石台县",341723:"青阳县",341724:"其它区",341800:"宣城市",341802:"宣州区",341821:"郎溪县",341822:"广德县",341823:"泾县",341824:"绩溪县",341825:"旌德县",341881:"宁国市",341882:"其它区",35e4:"福建省",350100:"福州市",350102:"鼓楼区",350103:"台江区",350104:"仓山区",350105:"马尾区",350111:"晋安区",350121:"闽侯县",350122:"连江县",350123:"罗源县",350124:"闽清县",350125:"永泰县",350128:"平潭县",350181:"福清市",350182:"长乐市",350183:"其它区",350200:"厦门市",350203:"思明区",350205:"海沧区",350206:"湖里区",350211:"集美区",350212:"同安区",350213:"翔安区",350214:"其它区",350300:"莆田市",350302:"城厢区",350303:"涵江区",350304:"荔城区",350305:"秀屿区",350322:"仙游县",350323:"其它区",350400:"三明市",350402:"梅列区",350403:"三元区",350421:"明溪县",350423:"清流县",350424:"宁化县",350425:"大田县",350426:"尤溪县",350427:"沙县",350428:"将乐县",350429:"泰宁县",350430:"建宁县",350481:"永安市",350482:"其它区",350500:"泉州市",350502:"鲤城区",350503:"丰泽区",350504:"洛江区",350505:"泉港区",350521:"惠安县",350524:"安溪县",350525:"永春县",350526:"德化县",350527:"金门县",350581:"石狮市",350582:"晋江市",350583:"南安市",350584:"其它区",350600:"漳州市",350602:"芗城区",350603:"龙文区",350622:"云霄县",350623:"漳浦县",350624:"诏安县",350625:"长泰县",350626:"东山县",350627:"南靖县",350628:"平和县",350629:"华安县",350681:"龙海市",350682:"其它区",350700:"南平市",350702:"延平区",350721:"顺昌县",350722:"浦城县",350723:"光泽县",350724:"松溪县",350725:"政和县",350781:"邵武市",350782:"武夷山市",350783:"建瓯市",350784:"建阳市",350785:"其它区",350800:"龙岩市",350802:"新罗区",350821:"长汀县",350822:"永定县",350823:"上杭县",350824:"武平县",350825:"连城县",350881:"漳平市",350882:"其它区",350900:"宁德市",350902:"蕉城区",350921:"霞浦县",350922:"古田县",350923:"屏南县",350924:"寿宁县",350925:"周宁县",350926:"柘荣县",350981:"福安市",350982:"福鼎市",350983:"其它区",36e4:"江西省",360100:"南昌市",360102:"东湖区",360103:"西湖区",360104:"青云谱区",360105:"湾里区",360111:"青山湖区",360121:"南昌县",360122:"新建县",360123:"安义县",360124:"进贤县",360128:"其它区",360200:"景德镇市",360202:"昌江区",360203:"珠山区",360222:"浮梁县",360281:"乐平市",360282:"其它区",360300:"萍乡市",360302:"安源区",360313:"湘东区",360321:"莲花县",360322:"上栗县",360323:"芦溪县",360324:"其它区",360400:"九江市",360402:"庐山区",360403:"浔阳区",360421:"九江县",360423:"武宁县",360424:"修水县",360425:"永修县",360426:"德安县",360427:"星子县",360428:"都昌县",360429:"湖口县",360430:"彭泽县",360481:"瑞昌市",360482:"其它区",360483:"共青城市",360500:"新余市",360502:"渝水区",360521:"分宜县",360522:"其它区",360600:"鹰潭市",360602:"月湖区",360622:"余江县",360681:"贵溪市",360682:"其它区",360700:"赣州市",360702:"章贡区",360721:"赣县",360722:"信丰县",360723:"大余县",360724:"上犹县",360725:"崇义县",360726:"安远县",360727:"龙南县",360728:"定南县",360729:"全南县",360730:"宁都县",360731:"于都县",360732:"兴国县",360733:"会昌县",360734:"寻乌县",360735:"石城县",360781:"瑞金市",360782:"南康市",360783:"其它区",360800:"吉安市",360802:"吉州区",360803:"青原区",360821:"吉安县",360822:"吉水县",360823:"峡江县",360824:"新干县",360825:"永丰县",360826:"泰和县",360827:"遂川县",360828:"万安县",360829:"安福县",360830:"永新县",360881:"井冈山市",360882:"其它区",360900:"宜春市",360902:"袁州区",360921:"奉新县",360922:"万载县",360923:"上高县",360924:"宜丰县",360925:"靖安县",360926:"铜鼓县",360981:"丰城市",360982:"樟树市",360983:"高安市",360984:"其它区",361e3:"抚州市",361002:"临川区",361021:"南城县",361022:"黎川县",361023:"南丰县",361024:"崇仁县",361025:"乐安县",361026:"宜黄县",361027:"金溪县",361028:"资溪县",361029:"东乡县",361030:"广昌县",361031:"其它区",361100:"上饶市",361102:"信州区",361121:"上饶县",361122:"广丰县",361123:"玉山县",361124:"铅山县",361125:"横峰县",361126:"弋阳县",361127:"余干县",361128:"鄱阳县",361129:"万年县",361130:"婺源县",361181:"德兴市",361182:"其它区",37e4:"山东省",370100:"济南市",370102:"历下区",370103:"市中区",370104:"槐荫区",370105:"天桥区",370112:"历城区",370113:"长清区",370124:"平阴县",370125:"济阳县",370126:"商河县",370181:"章丘市",370182:"其它区",370200:"青岛市",370202:"市南区",370203:"市北区",370211:"黄岛区",370212:"崂山区",370213:"李沧区",370214:"城阳区",370281:"胶州市",370282:"即墨市",370283:"平度市",370285:"莱西市",370286:"其它区",370300:"淄博市",370302:"淄川区",370303:"张店区",370304:"博山区",370305:"临淄区",370306:"周村区",370321:"桓台县",370322:"高青县",370323:"沂源县",370324:"其它区",370400:"枣庄市",370402:"市中区",370403:"薛城区",370404:"峄城区",370405:"台儿庄区",370406:"山亭区",370481:"滕州市",370482:"其它区",370500:"东营市",370502:"东营区",370503:"河口区",370521:"垦利县",370522:"利津县",370523:"广饶县",370591:"其它区",370600:"烟台市",370602:"芝罘区",370611:"福山区",370612:"牟平区",370613:"莱山区",370634:"长岛县",370681:"龙口市",370682:"莱阳市",370683:"莱州市",370684:"蓬莱市",370685:"招远市",370686:"栖霞市",370687:"海阳市",370688:"其它区",370700:"潍坊市",370702:"潍城区",370703:"寒亭区",370704:"坊子区",370705:"奎文区",370724:"临朐县",370725:"昌乐县",370781:"青州市",370782:"诸城市",370783:"寿光市",370784:"安丘市",370785:"高密市",370786:"昌邑市",370787:"其它区",370800:"济宁市",370802:"市中区",370811:"任城区",370826:"微山县",370827:"鱼台县",370828:"金乡县",370829:"嘉祥县",370830:"汶上县",370831:"泗水县",370832:"梁山县",370881:"曲阜市",370882:"兖州市",370883:"邹城市",370884:"其它区",370900:"泰安市",370902:"泰山区",370903:"岱岳区",370921:"宁阳县",370923:"东平县",370982:"新泰市",370983:"肥城市",370984:"其它区",371e3:"威海市",371002:"环翠区",371081:"文登市",371082:"荣成市",371083:"乳山市",371084:"其它区",371100:"日照市",371102:"东港区",371103:"岚山区",371121:"五莲县",371122:"莒县",371123:"其它区",371200:"莱芜市",371202:"莱城区",371203:"钢城区",371204:"其它区",371300:"临沂市",371302:"兰山区",371311:"罗庄区",371312:"河东区",371321:"沂南县",371322:"郯城县",371323:"沂水县",371324:"苍山县",371325:"费县",371326:"平邑县",371327:"莒南县",371328:"蒙阴县",371329:"临沭县",371330:"其它区",371400:"德州市",371402:"德城区",371421:"陵县",371422:"宁津县",371423:"庆云县",371424:"临邑县",371425:"齐河县",371426:"平原县",371427:"夏津县",371428:"武城县",371481:"乐陵市",371482:"禹城市",371483:"其它区",371500:"聊城市",371502:"东昌府区",371521:"阳谷县",371522:"莘县",371523:"茌平县",371524:"东阿县",371525:"冠县",371526:"高唐县",371581:"临清市",371582:"其它区",371600:"滨州市",371602:"滨城区",371621:"惠民县",371622:"阳信县",371623:"无棣县",371624:"沾化县",371625:"博兴县",371626:"邹平县",371627:"其它区",371700:"菏泽市",371702:"牡丹区",371721:"曹县",371722:"单县",371723:"成武县",371724:"巨野县",371725:"郓城县",371726:"鄄城县",371727:"定陶县",371728:"东明县",371729:"其它区",41e4:"河南省",410100:"郑州市",410102:"中原区",410103:"二七区",410104:"管城回族区",410105:"金水区",410106:"上街区",410108:"惠济区",410122:"中牟县",410181:"巩义市",410182:"荥阳市",410183:"新密市",410184:"新郑市",410185:"登封市",410188:"其它区",410200:"开封市",410202:"龙亭区",410203:"顺河回族区",410204:"鼓楼区",410205:"禹王台区",410211:"金明区",410221:"杞县",410222:"通许县",410223:"尉氏县",410224:"开封县",410225:"兰考县",410226:"其它区",410300:"洛阳市",410302:"老城区",410303:"西工区",410304:"瀍河回族区",410305:"涧西区",410306:"吉利区",410307:"洛龙区",410322:"孟津县",410323:"新安县",410324:"栾川县",410325:"嵩县",410326:"汝阳县",410327:"宜阳县",410328:"洛宁县",410329:"伊川县",410381:"偃师市",410400:"平顶山市",410402:"新华区",410403:"卫东区",410404:"石龙区",410411:"湛河区",410421:"宝丰县",410422:"叶县",410423:"鲁山县",410425:"郏县",410481:"舞钢市",410482:"汝州市",410483:"其它区",410500:"安阳市",410502:"文峰区",410503:"北关区",410505:"殷都区",410506:"龙安区",410522:"安阳县",410523:"汤阴县",410526:"滑县",410527:"内黄县",410581:"林州市",410582:"其它区",410600:"鹤壁市",410602:"鹤山区",410603:"山城区",410611:"淇滨区",410621:"浚县",410622:"淇县",410623:"其它区",410700:"新乡市",410702:"红旗区",410703:"卫滨区",410704:"凤泉区",410711:"牧野区",410721:"新乡县",410724:"获嘉县",410725:"原阳县",410726:"延津县",410727:"封丘县",410728:"长垣县",410781:"卫辉市",410782:"辉县市",410783:"其它区",410800:"焦作市",410802:"解放区",410803:"中站区",410804:"马村区",410811:"山阳区",410821:"修武县",410822:"博爱县",410823:"武陟县",410825:"温县",410881:"济源市",410882:"沁阳市",410883:"孟州市",410884:"其它区",410900:"濮阳市",410902:"华龙区",410922:"清丰县",410923:"南乐县",410926:"范县",410927:"台前县",410928:"濮阳县",410929:"其它区",411e3:"许昌市",411002:"魏都区",411023:"许昌县",411024:"鄢陵县",411025:"襄城县",411081:"禹州市",411082:"长葛市",411083:"其它区",411100:"漯河市",411102:"源汇区",411103:"郾城区",411104:"召陵区",411121:"舞阳县",411122:"临颍县",411123:"其它区",411200:"三门峡市",411202:"湖滨区",411221:"渑池县",411222:"陕县",411224:"卢氏县",411281:"义马市",411282:"灵宝市",411283:"其它区",411300:"南阳市",411302:"宛城区",411303:"卧龙区",411321:"南召县",411322:"方城县",411323:"西峡县",411324:"镇平县",411325:"内乡县",411326:"淅川县",411327:"社旗县",411328:"唐河县",411329:"新野县",411330:"桐柏县",411381:"邓州市",411382:"其它区",411400:"商丘市",411402:"梁园区",411403:"睢阳区",411421:"民权县",411422:"睢县",411423:"宁陵县",411424:"柘城县",411425:"虞城县",411426:"夏邑县",411481:"永城市",411482:"其它区",411500:"信阳市",411502:"浉河区",411503:"平桥区",411521:"罗山县",411522:"光山县",411523:"新县",411524:"商城县",411525:"固始县",411526:"潢川县",411527:"淮滨县",411528:"息县",411529:"其它区",411600:"周口市",411602:"川汇区",411621:"扶沟县",411622:"西华县",411623:"商水县",411624:"沈丘县",411625:"郸城县",411626:"淮阳县",411627:"太康县",411628:"鹿邑县",411681:"项城市",411682:"其它区",411700:"驻马店市",411702:"驿城区",411721:"西平县",411722:"上蔡县",411723:"平舆县",411724:"正阳县",411725:"确山县",411726:"泌阳县",411727:"汝南县",411728:"遂平县",411729:"新蔡县",411730:"其它区",42e4:"湖北省",420100:"武汉市",420102:"江岸区",420103:"江汉区",420104:"硚口区",420105:"汉阳区",420106:"武昌区",420107:"青山区",420111:"洪山区",420112:"东西湖区",420113:"汉南区",420114:"蔡甸区",420115:"江夏区",420116:"黄陂区",420117:"新洲区",420118:"其它区",420200:"黄石市",420202:"黄石港区",420203:"西塞山区",420204:"下陆区",420205:"铁山区",420222:"阳新县",420281:"大冶市",420282:"其它区",420300:"十堰市",420302:"茅箭区",420303:"张湾区",420321:"郧县",420322:"郧西县",420323:"竹山县",420324:"竹溪县",420325:"房县",420381:"丹江口市",420383:"其它区",420500:"宜昌市",420502:"西陵区",420503:"伍家岗区",420504:"点军区",420505:"猇亭区",420506:"夷陵区",420525:"远安县",420526:"兴山县",420527:"秭归县",420528:"长阳土家族自治县",420529:"五峰土家族自治县",420581:"宜都市",420582:"当阳市",420583:"枝江市",420584:"其它区",420600:"襄阳市",420602:"襄城区",420606:"樊城区",420607:"襄州区",420624:"南漳县",420625:"谷城县",420626:"保康县",420682:"老河口市",420683:"枣阳市",420684:"宜城市",420685:"其它区",420700:"鄂州市",420702:"梁子湖区",420703:"华容区",420704:"鄂城区",420705:"其它区",420800:"荆门市",420802:"东宝区",420804:"掇刀区",420821:"京山县",420822:"沙洋县",420881:"钟祥市",420882:"其它区",420900:"孝感市",420902:"孝南区",420921:"孝昌县",420922:"大悟县",420923:"云梦县",420981:"应城市",420982:"安陆市",420984:"汉川市",420985:"其它区",421e3:"荆州市",421002:"沙市区",421003:"荆州区",421022:"公安县",421023:"监利县",421024:"江陵县",421081:"石首市",421083:"洪湖市",421087:"松滋市",421088:"其它区",421100:"黄冈市",421102:"黄州区",421121:"团风县",421122:"红安县",421123:"罗田县",421124:"英山县",421125:"浠水县",421126:"蕲春县",421127:"黄梅县",421181:"麻城市",421182:"武穴市",421183:"其它区",421200:"咸宁市",421202:"咸安区",421221:"嘉鱼县",421222:"通城县",421223:"崇阳县",421224:"通山县",421281:"赤壁市",421283:"其它区",421300:"随州市",421302:"曾都区",421321:"随县",421381:"广水市",421382:"其它区",422800:"恩施土家族苗族自治州",422801:"恩施市",422802:"利川市",422822:"建始县",422823:"巴东县",422825:"宣恩县",422826:"咸丰县",422827:"来凤县",422828:"鹤峰县",422829:"其它区",429004:"仙桃市",429005:"潜江市",429006:"天门市",429021:"神农架林区",43e4:"湖南省",430100:"长沙市",430102:"芙蓉区",430103:"天心区",430104:"岳麓区",430105:"开福区",430111:"雨花区",430121:"长沙县",430122:"望城区",430124:"宁乡县",430181:"浏阳市",430182:"其它区",430200:"株洲市",430202:"荷塘区",430203:"芦淞区",430204:"石峰区",430211:"天元区",430221:"株洲县",430223:"攸县",430224:"茶陵县",430225:"炎陵县",430281:"醴陵市",430282:"其它区",430300:"湘潭市",430302:"雨湖区",430304:"岳塘区",430321:"湘潭县",430381:"湘乡市",430382:"韶山市",430383:"其它区",430400:"衡阳市",430405:"珠晖区",430406:"雁峰区",430407:"石鼓区",430408:"蒸湘区",430412:"南岳区",430421:"衡阳县",430422:"衡南县",430423:"衡山县",430424:"衡东县",430426:"祁东县",430481:"耒阳市",430482:"常宁市",430483:"其它区",430500:"邵阳市",430502:"双清区",430503:"大祥区",430511:"北塔区",430521:"邵东县",430522:"新邵县",430523:"邵阳县",430524:"隆回县",430525:"洞口县",430527:"绥宁县",430528:"新宁县",430529:"城步苗族自治县",430581:"武冈市",430582:"其它区",430600:"岳阳市",430602:"岳阳楼区",430603:"云溪区",430611:"君山区",430621:"岳阳县",430623:"华容县",430624:"湘阴县",430626:"平江县",430681:"汨罗市",430682:"临湘市",430683:"其它区",430700:"常德市",430702:"武陵区",430703:"鼎城区",430721:"安乡县",430722:"汉寿县",430723:"澧县",430724:"临澧县",430725:"桃源县",430726:"石门县",430781:"津市市",430782:"其它区",430800:"张家界市",430802:"永定区",430811:"武陵源区",430821:"慈利县",430822:"桑植县",430823:"其它区",430900:"益阳市",430902:"资阳区",430903:"赫山区",430921:"南县",430922:"桃江县",430923:"安化县",430981:"沅江市",430982:"其它区",431e3:"郴州市",431002:"北湖区",431003:"苏仙区",431021:"桂阳县",431022:"宜章县",431023:"永兴县",431024:"嘉禾县",431025:"临武县",431026:"汝城县",431027:"桂东县",431028:"安仁县",431081:"资兴市",431082:"其它区",431100:"永州市",431102:"零陵区",431103:"冷水滩区",431121:"祁阳县",431122:"东安县",431123:"双牌县",431124:"道县",431125:"江永县",431126:"宁远县",431127:"蓝山县",431128:"新田县",431129:"江华瑶族自治县",431130:"其它区",431200:"怀化市",431202:"鹤城区",431221:"中方县",431222:"沅陵县",431223:"辰溪县",431224:"溆浦县",431225:"会同县",431226:"麻阳苗族自治县",431227:"新晃侗族自治县",431228:"芷江侗族自治县",431229:"靖州苗族侗族自治县",431230:"通道侗族自治县",431281:"洪江市",431282:"其它区",431300:"娄底市",431302:"娄星区",431321:"双峰县",431322:"新化县",431381:"冷水江市",431382:"涟源市",431383:"其它区",433100:"湘西土家族苗族自治州",433101:"吉首市",433122:"泸溪县",433123:"凤凰县",433124:"花垣县",433125:"保靖县",433126:"古丈县",433127:"永顺县",433130:"龙山县",433131:"其它区",44e4:"广东省",440100:"广州市",440103:"荔湾区",440104:"越秀区",440105:"海珠区",440106:"天河区",440111:"白云区",440112:"黄埔区",440113:"番禺区",440114:"花都区",440115:"南沙区",440116:"萝岗区",440183:"增城市",440184:"从化市",440189:"其它区",440200:"韶关市",440203:"武江区",440204:"浈江区",440205:"曲江区",440222:"始兴县",440224:"仁化县",440229:"翁源县",440232:"乳源瑶族自治县",440233:"新丰县",440281:"乐昌市",440282:"南雄市",440283:"其它区",440300:"深圳市",440303:"罗湖区",440304:"福田区",440305:"南山区",440306:"宝安区",440307:"龙岗区",440308:"盐田区",440309:"其它区",440320:"光明新区",440321:"坪山新区",440322:"大鹏新区",440323:"龙华新区",440400:"珠海市",440402:"香洲区",440403:"斗门区",440404:"金湾区",440488:"其它区",440500:"汕头市",440507:"龙湖区",440511:"金平区",440512:"濠江区",440513:"潮阳区",440514:"潮南区",440515:"澄海区",440523:"南澳县",440524:"其它区",440600:"佛山市",440604:"禅城区",440605:"南海区",440606:"顺德区",440607:"三水区",440608:"高明区",440609:"其它区",440700:"江门市",440703:"蓬江区",440704:"江海区",440705:"新会区",440781:"台山市",440783:"开平市",440784:"鹤山市",440785:"恩平市",440786:"其它区",440800:"湛江市",440802:"赤坎区",440803:"霞山区",440804:"坡头区",440811:"麻章区",440823:"遂溪县",440825:"徐闻县",440881:"廉江市",440882:"雷州市",440883:"吴川市",440884:"其它区",440900:"茂名市",440902:"茂南区",440903:"茂港区",440923:"电白县",440981:"高州市",440982:"化州市",440983:"信宜市",440984:"其它区",441200:"肇庆市",441202:"端州区",441203:"鼎湖区",441223:"广宁县",441224:"怀集县",441225:"封开县",441226:"德庆县",441283:"高要市",441284:"四会市",441285:"其它区",441300:"惠州市",441302:"惠城区",441303:"惠阳区",441322:"博罗县",441323:"惠东县",441324:"龙门县",441325:"其它区",441400:"梅州市",441402:"梅江区",441421:"梅县",441422:"大埔县",441423:"丰顺县",441424:"五华县",441426:"平远县",441427:"蕉岭县",441481:"兴宁市",441482:"其它区",441500:"汕尾市",441502:"城区",441521:"海丰县",441523:"陆河县",441581:"陆丰市",441582:"其它区",441600:"河源市",441602:"源城区",441621:"紫金县",441622:"龙川县",441623:"连平县",441624:"和平县",441625:"东源县",441626:"其它区",441700:"阳江市",441702:"江城区",441721:"阳西县",441723:"阳东县",441781:"阳春市",441782:"其它区",441800:"清远市",441802:"清城区",441821:"佛冈县",441823:"阳山县",441825:"连山壮族瑶族自治县",441826:"连南瑶族自治县",441827:"清新区",441881:"英德市",441882:"连州市",441883:"其它区",441900:"东莞市",442e3:"中山市",442101:"东沙群岛",445100:"潮州市",445102:"湘桥区",445121:"潮安区",445122:"饶平县",445186:"其它区",445200:"揭阳市",445202:"榕城区",445221:"揭东区",445222:"揭西县",445224:"惠来县",445281:"普宁市",445285:"其它区",445300:"云浮市",445302:"云城区",445321:"新兴县",445322:"郁南县",445323:"云安县",445381:"罗定市",445382:"其它区",45e4:"广西壮族自治区",450100:"南宁市",450102:"兴宁区",450103:"青秀区",450105:"江南区",450107:"西乡塘区",450108:"良庆区",450109:"邕宁区",450122:"武鸣县",450123:"隆安县",450124:"马山县",450125:"上林县",450126:"宾阳县",450127:"横县",450128:"其它区",450200:"柳州市",450202:"城中区",450203:"鱼峰区",450204:"柳南区",450205:"柳北区",450221:"柳江县",450222:"柳城县",450223:"鹿寨县",450224:"融安县",450225:"融水苗族自治县",450226:"三江侗族自治县",450227:"其它区",450300:"桂林市",450302:"秀峰区",450303:"叠彩区",450304:"象山区",450305:"七星区",450311:"雁山区",450321:"阳朔县",450322:"临桂区",450323:"灵川县",450324:"全州县",450325:"兴安县",450326:"永福县",450327:"灌阳县",450328:"龙胜各族自治县",450329:"资源县",450330:"平乐县",450331:"荔浦县",450332:"恭城瑶族自治县",450333:"其它区",450400:"梧州市",450403:"万秀区",450405:"长洲区",450406:"龙圩区",450421:"苍梧县",450422:"藤县",450423:"蒙山县",450481:"岑溪市",450482:"其它区",450500:"北海市",450502:"海城区",450503:"银海区",450512:"铁山港区",450521:"合浦县",450522:"其它区",450600:"防城港市",450602:"港口区",450603:"防城区",450621:"上思县",450681:"东兴市",450682:"其它区",450700:"钦州市",450702:"钦南区",450703:"钦北区",450721:"灵山县",450722:"浦北县",450723:"其它区",450800:"贵港市",450802:"港北区",450803:"港南区",450804:"覃塘区",450821:"平南县",450881:"桂平市",450882:"其它区",450900:"玉林市",450902:"玉州区",450903:"福绵区",450921:"容县",450922:"陆川县",450923:"博白县",450924:"兴业县",450981:"北流市",450982:"其它区",451e3:"百色市",451002:"右江区",451021:"田阳县",451022:"田东县",451023:"平果县",451024:"德保县",451025:"靖西县",451026:"那坡县",451027:"凌云县",451028:"乐业县",451029:"田林县",451030:"西林县",451031:"隆林各族自治县",451032:"其它区",451100:"贺州市",451102:"八步区",451119:"平桂管理区",451121:"昭平县",451122:"钟山县",451123:"富川瑶族自治县",451124:"其它区",451200:"河池市",451202:"金城江区",451221:"南丹县",451222:"天峨县",451223:"凤山县",451224:"东兰县",451225:"罗城仫佬族自治县",451226:"环江毛南族自治县",451227:"巴马瑶族自治县",451228:"都安瑶族自治县",451229:"大化瑶族自治县",451281:"宜州市",451282:"其它区",451300:"来宾市",451302:"兴宾区",451321:"忻城县",451322:"象州县",451323:"武宣县",451324:"金秀瑶族自治县",451381:"合山市",451382:"其它区",451400:"崇左市",451402:"江州区",451421:"扶绥县",451422:"宁明县",451423:"龙州县",451424:"大新县",451425:"天等县",451481:"凭祥市",451482:"其它区",46e4:"海南省",460100:"海口市",460105:"秀英区",460106:"龙华区",460107:"琼山区",460108:"美兰区",460109:"其它区",460200:"三亚市",460300:"三沙市",460321:"西沙群岛",460322:"南沙群岛",460323:"中沙群岛的岛礁及其海域",469001:"五指山市",469002:"琼海市",469003:"儋州市",469005:"文昌市",469006:"万宁市",469007:"东方市",469025:"定安县",469026:"屯昌县",469027:"澄迈县",469028:"临高县",469030:"白沙黎族自治县",469031:"昌江黎族自治县",469033:"乐东黎族自治县",469034:"陵水黎族自治县",469035:"保亭黎族苗族自治县",469036:"琼中黎族苗族自治县",471005:"其它区",5e5:"重庆",500100:"重庆市",500101:"万州区",500102:"涪陵区",500103:"渝中区",500104:"大渡口区",500105:"江北区",500106:"沙坪坝区",500107:"九龙坡区",500108:"南岸区",500109:"北碚区",500110:"万盛区",500111:"双桥区",500112:"渝北区",500113:"巴南区",500114:"黔江区",500115:"长寿区",500222:"綦江区",500223:"潼南县",500224:"铜梁县",500225:"大足区",500226:"荣昌县",500227:"璧山县",500228:"梁平县",500229:"城口县",500230:"丰都县",500231:"垫江县",500232:"武隆县",500233:"忠县",500234:"开县",500235:"云阳县",500236:"奉节县",500237:"巫山县",500238:"巫溪县",500240:"石柱土家族自治县",500241:"秀山土家族苗族自治县",500242:"酉阳土家族苗族自治县",500243:"彭水苗族土家族自治县",500381:"江津区",500382:"合川区",500383:"永川区",500384:"南川区",500385:"其它区",51e4:"四川省",510100:"成都市",510104:"锦江区",510105:"青羊区",510106:"金牛区",510107:"武侯区",510108:"成华区",510112:"龙泉驿区",510113:"青白江区",510114:"新都区",510115:"温江区",510121:"金堂县",510122:"双流县",510124:"郫县",510129:"大邑县",510131:"蒲江县",510132:"新津县",510181:"都江堰市",510182:"彭州市",510183:"邛崃市",510184:"崇州市",510185:"其它区",510300:"自贡市",510302:"自流井区",510303:"贡井区",510304:"大安区",510311:"沿滩区",510321:"荣县",510322:"富顺县",510323:"其它区",510400:"攀枝花市",510402:"东区",510403:"西区",510411:"仁和区",510421:"米易县",510422:"盐边县",510423:"其它区",510500:"泸州市",510502:"江阳区",510503:"纳溪区",510504:"龙马潭区",510521:"泸县",510522:"合江县",510524:"叙永县",510525:"古蔺县",510526:"其它区",510600:"德阳市",510603:"旌阳区",510623:"中江县",510626:"罗江县",510681:"广汉市",510682:"什邡市",510683:"绵竹市",510684:"其它区",510700:"绵阳市",510703:"涪城区",510704:"游仙区",510722:"三台县",510723:"盐亭县",510724:"安县",510725:"梓潼县",510726:"北川羌族自治县",510727:"平武县",510781:"江油市",510782:"其它区",510800:"广元市",510802:"利州区",510811:"昭化区",510812:"朝天区",510821:"旺苍县",510822:"青川县",510823:"剑阁县",510824:"苍溪县",510825:"其它区",510900:"遂宁市",510903:"船山区",510904:"安居区",510921:"蓬溪县",510922:"射洪县",510923:"大英县",510924:"其它区",511e3:"内江市",511002:"市中区",511011:"东兴区",511024:"威远县",511025:"资中县",511028:"隆昌县",511029:"其它区",511100:"乐山市",511102:"市中区",511111:"沙湾区",511112:"五通桥区",511113:"金口河区",511123:"犍为县",511124:"井研县",511126:"夹江县",511129:"沐川县",511132:"峨边彝族自治县",511133:"马边彝族自治县",511181:"峨眉山市",511182:"其它区",511300:"南充市",511302:"顺庆区",511303:"高坪区",511304:"嘉陵区",511321:"南部县",511322:"营山县",511323:"蓬安县",511324:"仪陇县",511325:"西充县",511381:"阆中市",511382:"其它区",511400:"眉山市",511402:"东坡区",511421:"仁寿县",511422:"彭山县",511423:"洪雅县",511424:"丹棱县",511425:"青神县",511426:"其它区",511500:"宜宾市",511502:"翠屏区",511521:"宜宾县",511522:"南溪区",511523:"江安县",511524:"长宁县",511525:"高县",511526:"珙县",511527:"筠连县",511528:"兴文县",511529:"屏山县",511530:"其它区",511600:"广安市",511602:"广安区",511603:"前锋区",511621:"岳池县",511622:"武胜县",511623:"邻水县",511681:"华蓥市",511683:"其它区",511700:"达州市",511702:"通川区",511721:"达川区",511722:"宣汉县",511723:"开江县",511724:"大竹县",511725:"渠县",511781:"万源市",511782:"其它区",511800:"雅安市",511802:"雨城区",511821:"名山区",511822:"荥经县",511823:"汉源县",511824:"石棉县",511825:"天全县",511826:"芦山县",511827:"宝兴县",511828:"其它区",511900:"巴中市",511902:"巴州区",511903:"恩阳区",511921:"通江县",511922:"南江县",511923:"平昌县",511924:"其它区",512e3:"资阳市",512002:"雁江区",512021:"安岳县",512022:"乐至县",512081:"简阳市",512082:"其它区",513200:"阿坝藏族羌族自治州",513221:"汶川县",513222:"理县",513223:"茂县",513224:"松潘县",513225:"九寨沟县",513226:"金川县",513227:"小金县",513228:"黑水县",513229:"马尔康县",513230:"壤塘县",513231:"阿坝县",513232:"若尔盖县",513233:"红原县",513234:"其它区",513300:"甘孜藏族自治州",513321:"康定县",513322:"泸定县",513323:"丹巴县",513324:"九龙县",513325:"雅江县",513326:"道孚县",513327:"炉霍县",513328:"甘孜县",513329:"新龙县",513330:"德格县",513331:"白玉县",513332:"石渠县",513333:"色达县",513334:"理塘县",513335:"巴塘县",513336:"乡城县",513337:"稻城县",513338:"得荣县",513339:"其它区",513400:"凉山彝族自治州",513401:"西昌市",513422:"木里藏族自治县",513423:"盐源县",513424:"德昌县",513425:"会理县",513426:"会东县",513427:"宁南县",513428:"普格县",513429:"布拖县",513430:"金阳县",513431:"昭觉县",513432:"喜德县",513433:"冕宁县",513434:"越西县",513435:"甘洛县",513436:"美姑县",513437:"雷波县",513438:"其它区",52e4:"贵州省",520100:"贵阳市",520102:"南明区",520103:"云岩区",520111:"花溪区",520112:"乌当区",520113:"白云区",520121:"开阳县",520122:"息烽县",520123:"修文县",520151:"观山湖区",520181:"清镇市",520182:"其它区",520200:"六盘水市",520201:"钟山区",520203:"六枝特区",520221:"水城县",520222:"盘县",520223:"其它区",520300:"遵义市",520302:"红花岗区",520303:"汇川区",520321:"遵义县",520322:"桐梓县",520323:"绥阳县",520324:"正安县",520325:"道真仡佬族苗族自治县",520326:"务川仡佬族苗族自治县",520327:"凤冈县",520328:"湄潭县",520329:"余庆县",520330:"习水县",520381:"赤水市",520382:"仁怀市",520383:"其它区",520400:"安顺市",520402:"西秀区",520421:"平坝县",520422:"普定县",520423:"镇宁布依族苗族自治县",520424:"关岭布依族苗族自治县",520425:"紫云苗族布依族自治县",520426:"其它区",522200:"铜仁市",522201:"碧江区",522222:"江口县",522223:"玉屏侗族自治县",522224:"石阡县",522225:"思南县",522226:"印江土家族苗族自治县",522227:"德江县",522228:"沿河土家族自治县",522229:"松桃苗族自治县",522230:"万山区",522231:"其它区",522300:"黔西南布依族苗族自治州",522301:"兴义市",522322:"兴仁县",522323:"普安县",522324:"晴隆县",522325:"贞丰县",522326:"望谟县",522327:"册亨县",522328:"安龙县",522329:"其它区",522400:"毕节市",522401:"七星关区",522422:"大方县",522423:"黔西县",522424:"金沙县",522425:"织金县",522426:"纳雍县",522427:"威宁彝族回族苗族自治县",522428:"赫章县",522429:"其它区",522600:"黔东南苗族侗族自治州",522601:"凯里市",522622:"黄平县",522623:"施秉县",522624:"三穗县",522625:"镇远县",522626:"岑巩县",522627:"天柱县",522628:"锦屏县",522629:"剑河县",522630:"台江县",522631:"黎平县",522632:"榕江县",522633:"从江县",522634:"雷山县",522635:"麻江县",522636:"丹寨县",522637:"其它区",522700:"黔南布依族苗族自治州",522701:"都匀市",522702:"福泉市",522722:"荔波县",522723:"贵定县",522725:"瓮安县",522726:"独山县",522727:"平塘县",522728:"罗甸县",522729:"长顺县",522730:"龙里县",522731:"惠水县",522732:"三都水族自治县",522733:"其它区",53e4:"云南省",530100:"昆明市",530102:"五华区",530103:"盘龙区",530111:"官渡区",530112:"西山区",530113:"东川区",530121:"呈贡区",530122:"晋宁县",530124:"富民县",530125:"宜良县",530126:"石林彝族自治县",530127:"嵩明县",530128:"禄劝彝族苗族自治县",530129:"寻甸回族彝族自治县",530181:"安宁市",530182:"其它区",530300:"曲靖市",530302:"麒麟区",530321:"马龙县",530322:"陆良县",530323:"师宗县",530324:"罗平县",530325:"富源县",530326:"会泽县",530328:"沾益县",530381:"宣威市",530382:"其它区",530400:"玉溪市",530402:"红塔区",530421:"江川县",530422:"澄江县",530423:"通海县",530424:"华宁县",530425:"易门县",530426:"峨山彝族自治县",530427:"新平彝族傣族自治县",530428:"元江哈尼族彝族傣族自治县",530429:"其它区",530500:"保山市",530502:"隆阳区",530521:"施甸县",530522:"腾冲县",530523:"龙陵县",530524:"昌宁县",530525:"其它区",530600:"昭通市",530602:"昭阳区",530621:"鲁甸县",530622:"巧家县",530623:"盐津县",530624:"大关县",530625:"永善县",530626:"绥江县",530627:"镇雄县",530628:"彝良县",530629:"威信县",530630:"水富县",530631:"其它区",530700:"丽江市",530702:"古城区",530721:"玉龙纳西族自治县",530722:"永胜县",530723:"华坪县",530724:"宁蒗彝族自治县",530725:"其它区",530800:"普洱市",530802:"思茅区",530821:"宁洱哈尼族彝族自治县",530822:"墨江哈尼族自治县",530823:"景东彝族自治县",530824:"景谷傣族彝族自治县",530825:"镇沅彝族哈尼族拉祜族自治县",530826:"江城哈尼族彝族自治县",530827:"孟连傣族拉祜族佤族自治县",530828:"澜沧拉祜族自治县",530829:"西盟佤族自治县",530830:"其它区",530900:"临沧市",530902:"临翔区",530921:"凤庆县",530922:"云县",530923:"永德县",530924:"镇康县",530925:"双江拉祜族佤族布朗族傣族自治县",530926:"耿马傣族佤族自治县",530927:"沧源佤族自治县",530928:"其它区",532300:"楚雄彝族自治州",532301:"楚雄市",532322:"双柏县",532323:"牟定县",532324:"南华县",532325:"姚安县",532326:"大姚县",532327:"永仁县",532328:"元谋县",532329:"武定县",532331:"禄丰县",532332:"其它区",532500:"红河哈尼族彝族自治州",532501:"个旧市",532502:"开远市",532522:"蒙自市",532523:"屏边苗族自治县",532524:"建水县",532525:"石屏县",532526:"弥勒市",532527:"泸西县",532528:"元阳县",532529:"红河县",532530:"金平苗族瑶族傣族自治县",532531:"绿春县",532532:"河口瑶族自治县",532533:"其它区",532600:"文山壮族苗族自治州",532621:"文山市",532622:"砚山县",532623:"西畴县",532624:"麻栗坡县",532625:"马关县",532626:"丘北县",532627:"广南县",532628:"富宁县",532629:"其它区",532800:"西双版纳傣族自治州",532801:"景洪市",532822:"勐海县",532823:"勐腊县",532824:"其它区",532900:"大理白族自治州",532901:"大理市",532922:"漾濞彝族自治县",532923:"祥云县",532924:"宾川县",532925:"弥渡县",532926:"南涧彝族自治县",532927:"巍山彝族回族自治县",532928:"永平县",532929:"云龙县",532930:"洱源县",532931:"剑川县",532932:"鹤庆县",532933:"其它区",533100:"德宏傣族景颇族自治州",533102:"瑞丽市",533103:"芒市",533122:"梁河县",533123:"盈江县",533124:"陇川县",533125:"其它区",533300:"怒江傈僳族自治州",533321:"泸水县",533323:"福贡县",533324:"贡山独龙族怒族自治县",533325:"兰坪白族普米族自治县",533326:"其它区",533400:"迪庆藏族自治州",533421:"香格里拉县",533422:"德钦县",533423:"维西傈僳族自治县",533424:"其它区",54e4:"西藏自治区",540100:"拉萨市",540102:"城关区",540121:"林周县",540122:"当雄县",540123:"尼木县",540124:"曲水县",540125:"堆龙德庆县",540126:"达孜县",540127:"墨竹工卡县",540128:"其它区",542100:"昌都地区",542121:"昌都县",542122:"江达县",542123:"贡觉县",542124:"类乌齐县",542125:"丁青县",542126:"察雅县",542127:"八宿县",542128:"左贡县",542129:"芒康县",542132:"洛隆县",542133:"边坝县",542134:"其它区",542200:"山南地区",542221:"乃东县",542222:"扎囊县",542223:"贡嘎县",542224:"桑日县",542225:"琼结县",542226:"曲松县",542227:"措美县",542228:"洛扎县",542229:"加查县",542231:"隆子县",542232:"错那县",542233:"浪卡子县",542234:"其它区",542300:"日喀则地区",542301:"日喀则市",542322:"南木林县",542323:"江孜县",542324:"定日县",542325:"萨迦县",542326:"拉孜县",542327:"昂仁县",542328:"谢通门县",542329:"白朗县",542330:"仁布县",542331:"康马县",542332:"定结县",542333:"仲巴县",542334:"亚东县",542335:"吉隆县",542336:"聂拉木县",542337:"萨嘎县",542338:"岗巴县",542339:"其它区",542400:"那曲地区",542421:"那曲县",542422:"嘉黎县",542423:"比如县",542424:"聂荣县",542425:"安多县",542426:"申扎县",542427:"索县",542428:"班戈县",542429:"巴青县",542430:"尼玛县",542431:"其它区",542432:"双湖县",542500:"阿里地区",542521:"普兰县",542522:"札达县",542523:"噶尔县",542524:"日土县",542525:"革吉县",542526:"改则县",542527:"措勤县",542528:"其它区",542600:"林芝地区",542621:"林芝县",542622:"工布江达县",542623:"米林县",542624:"墨脱县",542625:"波密县",542626:"察隅县",542627:"朗县",542628:"其它区",61e4:"陕西省",610100:"西安市",610102:"新城区",610103:"碑林区",610104:"莲湖区",610111:"灞桥区",610112:"未央区",610113:"雁塔区",610114:"阎良区",610115:"临潼区",610116:"长安区",610122:"蓝田县",610124:"周至县",610125:"户县",610126:"高陵县",610127:"其它区",610200:"铜川市",610202:"王益区",610203:"印台区",610204:"耀州区",610222:"宜君县",610223:"其它区",610300:"宝鸡市",610302:"渭滨区",610303:"金台区",610304:"陈仓区",610322:"凤翔县",610323:"岐山县",610324:"扶风县",610326:"眉县",610327:"陇县",610328:"千阳县",610329:"麟游县",610330:"凤县",610331:"太白县",610332:"其它区",610400:"咸阳市",610402:"秦都区",610403:"杨陵区",610404:"渭城区",610422:"三原县",610423:"泾阳县",610424:"乾县",610425:"礼泉县",610426:"永寿县",610427:"彬县",610428:"长武县",610429:"旬邑县",610430:"淳化县",610431:"武功县",610481:"兴平市",610482:"其它区",610500:"渭南市",610502:"临渭区",610521:"华县",610522:"潼关县",610523:"大荔县",610524:"合阳县",610525:"澄城县",610526:"蒲城县",610527:"白水县",610528:"富平县",610581:"韩城市",610582:"华阴市",610583:"其它区",610600:"延安市",610602:"宝塔区",610621:"延长县",610622:"延川县",610623:"子长县",610624:"安塞县",610625:"志丹县",610626:"吴起县",610627:"甘泉县",610628:"富县",610629:"洛川县",610630:"宜川县",610631:"黄龙县",610632:"黄陵县",610633:"其它区",610700:"汉中市",610702:"汉台区",610721:"南郑县",610722:"城固县",610723:"洋县",610724:"西乡县",610725:"勉县",610726:"宁强县",610727:"略阳县",610728:"镇巴县",610729:"留坝县",610730:"佛坪县",610731:"其它区",610800:"榆林市",610802:"榆阳区",610821:"神木县",610822:"府谷县",610823:"横山县",610824:"靖边县",610825:"定边县",610826:"绥德县",610827:"米脂县",610828:"佳县",610829:"吴堡县",610830:"清涧县",610831:"子洲县",610832:"其它区",610900:"安康市",610902:"汉滨区",610921:"汉阴县",610922:"石泉县",610923:"宁陕县",610924:"紫阳县",610925:"岚皋县",610926:"平利县",610927:"镇坪县",610928:"旬阳县",610929:"白河县",610930:"其它区",611e3:"商洛市",611002:"商州区",611021:"洛南县",611022:"丹凤县",611023:"商南县",611024:"山阳县",611025:"镇安县",611026:"柞水县",611027:"其它区",62e4:"甘肃省",620100:"兰州市",620102:"城关区",620103:"七里河区",620104:"西固区",620105:"安宁区",620111:"红古区",620121:"永登县",620122:"皋兰县",620123:"榆中县",620124:"其它区",620200:"嘉峪关市",620300:"金昌市",620302:"金川区",620321:"永昌县",620322:"其它区",620400:"白银市",620402:"白银区",620403:"平川区",620421:"靖远县",620422:"会宁县",620423:"景泰县",620424:"其它区",620500:"天水市",620502:"秦州区",620503:"麦积区",620521:"清水县",620522:"秦安县",620523:"甘谷县",620524:"武山县",620525:"张家川回族自治县",620526:"其它区",620600:"武威市",620602:"凉州区",620621:"民勤县",620622:"古浪县",620623:"天祝藏族自治县",620624:"其它区",620700:"张掖市",620702:"甘州区",620721:"肃南裕固族自治县",620722:"民乐县",620723:"临泽县",620724:"高台县",620725:"山丹县",620726:"其它区",620800:"平凉市",620802:"崆峒区",620821:"泾川县",620822:"灵台县",620823:"崇信县",620824:"华亭县",620825:"庄浪县",620826:"静宁县",620827:"其它区",620900:"酒泉市",620902:"肃州区",620921:"金塔县",620922:"瓜州县",620923:"肃北蒙古族自治县",620924:"阿克塞哈萨克族自治县",620981:"玉门市",620982:"敦煌市",620983:"其它区",621e3:"庆阳市",621002:"西峰区",621021:"庆城县",621022:"环县",621023:"华池县",621024:"合水县",621025:"正宁县",621026:"宁县",621027:"镇原县",621028:"其它区",621100:"定西市",621102:"安定区",621121:"通渭县",621122:"陇西县",621123:"渭源县",621124:"临洮县",621125:"漳县",621126:"岷县",621127:"其它区",621200:"陇南市",621202:"武都区",621221:"成县",621222:"文县",621223:"宕昌县",621224:"康县",621225:"西和县",621226:"礼县",621227:"徽县",621228:"两当县",621229:"其它区",622900:"临夏回族自治州",622901:"临夏市",622921:"临夏县",622922:"康乐县",622923:"永靖县",622924:"广河县",622925:"和政县",622926:"东乡族自治县",622927:"积石山保安族东乡族撒拉族自治县",622928:"其它区",623e3:"甘南藏族自治州",623001:"合作市",623021:"临潭县",623022:"卓尼县",623023:"舟曲县",623024:"迭部县",623025:"玛曲县",623026:"碌曲县",623027:"夏河县",623028:"其它区",63e4:"青海省",630100:"西宁市",630102:"城东区",630103:"城中区",630104:"城西区",630105:"城北区",630121:"大通回族土族自治县",630122:"湟中县",630123:"湟源县",630124:"其它区",632100:"海东市",632121:"平安县",632122:"民和回族土族自治县",632123:"乐都区",632126:"互助土族自治县",632127:"化隆回族自治县",632128:"循化撒拉族自治县",632129:"其它区",632200:"海北藏族自治州",632221:"门源回族自治县",632222:"祁连县",632223:"海晏县",632224:"刚察县",632225:"其它区",632300:"黄南藏族自治州",632321:"同仁县",632322:"尖扎县",632323:"泽库县",632324:"河南蒙古族自治县",632325:"其它区",632500:"海南藏族自治州",632521:"共和县",632522:"同德县",632523:"贵德县",632524:"兴海县",632525:"贵南县",632526:"其它区",632600:"果洛藏族自治州",632621:"玛沁县",632622:"班玛县",632623:"甘德县",632624:"达日县",632625:"久治县",632626:"玛多县",632627:"其它区",632700:"玉树藏族自治州",632721:"玉树市",632722:"杂多县",632723:"称多县",632724:"治多县",632725:"囊谦县",632726:"曲麻莱县",632727:"其它区",632800:"海西蒙古族藏族自治州",632801:"格尔木市",632802:"德令哈市",632821:"乌兰县",632822:"都兰县",632823:"天峻县",632824:"其它区",64e4:"宁夏回族自治区",640100:"银川市",640104:"兴庆区",640105:"西夏区",640106:"金凤区",640121:"永宁县",640122:"贺兰县",640181:"灵武市",640182:"其它区",640200:"石嘴山市",640202:"大武口区",640205:"惠农区",640221:"平罗县",640222:"其它区",640300:"吴忠市",640302:"利通区",640303:"红寺堡区",640323:"盐池县",640324:"同心县",640381:"青铜峡市",640382:"其它区",640400:"固原市",640402:"原州区",640422:"西吉县",640423:"隆德县",640424:"泾源县",640425:"彭阳县",640426:"其它区",640500:"中卫市",640502:"沙坡头区",640521:"中宁县",640522:"海原县",640523:"其它区",65e4:"新疆维吾尔自治区",650100:"乌鲁木齐市",650102:"天山区",650103:"沙依巴克区",650104:"新市区",650105:"水磨沟区",650106:"头屯河区",650107:"达坂城区",650109:"米东区",650121:"乌鲁木齐县",650122:"其它区",650200:"克拉玛依市",650202:"独山子区",650203:"克拉玛依区",650204:"白碱滩区",650205:"乌尔禾区",650206:"其它区",652100:"吐鲁番地区",652101:"吐鲁番市",652122:"鄯善县",652123:"托克逊县",652124:"其它区",652200:"哈密地区",652201:"哈密市",652222:"巴里坤哈萨克自治县",652223:"伊吾县",652224:"其它区",652300:"昌吉回族自治州",652301:"昌吉市",652302:"阜康市",652323:"呼图壁县",652324:"玛纳斯县",652325:"奇台县",652327:"吉木萨尔县",652328:"木垒哈萨克自治县",652329:"其它区",652700:"博尔塔拉蒙古自治州",652701:"博乐市",652702:"阿拉山口市",652722:"精河县",652723:"温泉县",652724:"其它区",652800:"巴音郭楞蒙古自治州",652801:"库尔勒市",652822:"轮台县",652823:"尉犁县",652824:"若羌县",652825:"且末县",652826:"焉耆回族自治县",652827:"和静县",652828:"和硕县",652829:"博湖县",652830:"其它区",652900:"阿克苏地区",652901:"阿克苏市",652922:"温宿县",652923:"库车县",652924:"沙雅县",652925:"新和县",652926:"拜城县",652927:"乌什县",652928:"阿瓦提县",652929:"柯坪县",652930:"其它区",653e3:"克孜勒苏柯尔克孜自治州",653001:"阿图什市",653022:"阿克陶县",653023:"阿合奇县",653024:"乌恰县",653025:"其它区",653100:"喀什地区",653101:"喀什市",653121:"疏附县",653122:"疏勒县",653123:"英吉沙县",653124:"泽普县",653125:"莎车县",653126:"叶城县",653127:"麦盖提县",653128:"岳普湖县",653129:"伽师县",653130:"巴楚县",653131:"塔什库尔干塔吉克自治县",653132:"其它区",653200:"和田地区",653201:"和田市",653221:"和田县",653222:"墨玉县",653223:"皮山县",653224:"洛浦县",653225:"策勒县",653226:"于田县",653227:"民丰县",653228:"其它区",654e3:"伊犁哈萨克自治州",654002:"伊宁市",654003:"奎屯市",654021:"伊宁县",654022:"察布查尔锡伯自治县",654023:"霍城县",654024:"巩留县",654025:"新源县",654026:"昭苏县",654027:"特克斯县",654028:"尼勒克县",654029:"其它区",654200:"塔城地区",654201:"塔城市",654202:"乌苏市",654221:"额敏县",654223:"沙湾县",654224:"托里县",654225:"裕民县",654226:"和布克赛尔蒙古自治县",654227:"其它区",654300:"阿勒泰地区",654301:"阿勒泰市",654321:"布尔津县",654322:"富蕴县",654323:"福海县",654324:"哈巴河县",654325:"青河县",654326:"吉木乃县",654327:"其它区",659001:"石河子市",659002:"阿拉尔市",659003:"图木舒克市",659004:"五家渠市",71e4:"台湾",710100:"台北市",710101:"中正区",710102:"大同区",710103:"中山区",710104:"松山区",710105:"大安区",710106:"万华区",710107:"信义区",710108:"士林区",710109:"北投区",710110:"内湖区",710111:"南港区",710112:"文山区",710113:"其它区",710200:"高雄市",710201:"新兴区",710202:"前金区",710203:"芩雅区",710204:"盐埕区",710205:"鼓山区",710206:"旗津区",710207:"前镇区",710208:"三民区",710209:"左营区",710210:"楠梓区",710211:"小港区",710212:"其它区",710241:"苓雅区",710242:"仁武区",710243:"大社区",710244:"冈山区",710245:"路竹区",710246:"阿莲区",710247:"田寮区",710248:"燕巢区",710249:"桥头区",710250:"梓官区",710251:"弥陀区",710252:"永安区",710253:"湖内区",710254:"凤山区",710255:"大寮区",710256:"林园区",710257:"鸟松区",710258:"大树区",710259:"旗山区",710260:"美浓区",710261:"六龟区",710262:"内门区",710263:"杉林区",710264:"甲仙区",710265:"桃源区",710266:"那玛夏区",710267:"茂林区",710268:"茄萣区",710300:"台南市",710301:"中西区",710302:"东区",710303:"南区",710304:"北区",710305:"安平区",710306:"安南区",710307:"其它区",710339:"永康区",710340:"归仁区",710341:"新化区",710342:"左镇区",710343:"玉井区",710344:"楠西区",710345:"南化区",710346:"仁德区",710347:"关庙区",710348:"龙崎区",710349:"官田区",710350:"麻豆区",710351:"佳里区",710352:"西港区",710353:"七股区",710354:"将军区",710355:"学甲区",710356:"北门区",710357:"新营区",710358:"后壁区",710359:"白河区",710360:"东山区",710361:"六甲区",710362:"下营区",710363:"柳营区",710364:"盐水区",710365:"善化区",710366:"大内区",710367:"山上区",710368:"新市区",710369:"安定区",710400:"台中市",710401:"中区",710402:"东区",710403:"南区",710404:"西区",710405:"北区",710406:"北屯区",710407:"西屯区",710408:"南屯区",710409:"其它区",710431:"太平区",710432:"大里区",710433:"雾峰区",710434:"乌日区",710435:"丰原区",710436:"后里区",710437:"石冈区",710438:"东势区",710439:"和平区",710440:"新社区",710441:"潭子区",710442:"大雅区",710443:"神冈区",710444:"大肚区",710445:"沙鹿区",710446:"龙井区",710447:"梧栖区",710448:"清水区",710449:"大甲区",710450:"外埔区",710451:"大安区",710500:"金门县",710507:"金沙镇",710508:"金湖镇",710509:"金宁乡",710510:"金城镇",710511:"烈屿乡",710512:"乌坵乡",710600:"南投县",710614:"南投市",710615:"中寮乡",710616:"草屯镇",710617:"国姓乡",710618:"埔里镇",710619:"仁爱乡",710620:"名间乡",710621:"集集镇",710622:"水里乡",710623:"鱼池乡",710624:"信义乡",710625:"竹山镇",710626:"鹿谷乡",710700:"基隆市",710701:"仁爱区",710702:"信义区",710703:"中正区",710704:"中山区",710705:"安乐区",710706:"暖暖区",710707:"七堵区",710708:"其它区",710800:"新竹市",710801:"东区",710802:"北区",710803:"香山区",710804:"其它区",710900:"嘉义市",710901:"东区",710902:"西区",710903:"其它区",711100:"新北市",711130:"万里区",711131:"金山区",711132:"板桥区",711133:"汐止区",711134:"深坑区",711135:"石碇区",711136:"瑞芳区",711137:"平溪区",711138:"双溪区",711139:"贡寮区",711140:"新店区",711141:"坪林区",711142:"乌来区",711143:"永和区",711144:"中和区",711145:"土城区",711146:"三峡区",711147:"树林区",711148:"莺歌区",711149:"三重区",711150:"新庄区",711151:"泰山区",711152:"林口区",711153:"芦洲区",711154:"五股区",711155:"八里区",711156:"淡水区",711157:"三芝区",711158:"石门区",711200:"宜兰县",711214:"宜兰市",711215:"头城镇",711216:"礁溪乡",711217:"壮围乡",711218:"员山乡",711219:"罗东镇",711220:"三星乡",711221:"大同乡",711222:"五结乡",711223:"冬山乡",711224:"苏澳镇",711225:"南澳乡",711226:"钓鱼台",711300:"新竹县",711314:"竹北市",711315:"湖口乡",711316:"新丰乡",711317:"新埔镇",711318:"关西镇",711319:"芎林乡",711320:"宝山乡",711321:"竹东镇",711322:"五峰乡",711323:"横山乡",711324:"尖石乡",711325:"北埔乡",711326:"峨眉乡",711400:"桃园县",711414:"中坜市",711415:"平镇市",711416:"龙潭乡",711417:"杨梅市",711418:"新屋乡",711419:"观音乡",711420:"桃园市",711421:"龟山乡",711422:"八德市",711423:"大溪镇",711424:"复兴乡",711425:"大园乡",711426:"芦竹乡",711500:"苗栗县",711519:"竹南镇",711520:"头份镇",711521:"三湾乡",711522:"南庄乡",711523:"狮潭乡",711524:"后龙镇",711525:"通霄镇",711526:"苑里镇",711527:"苗栗市",711528:"造桥乡",711529:"头屋乡",711530:"公馆乡",711531:"大湖乡",711532:"泰安乡",711533:"铜锣乡",711534:"三义乡",711535:"西湖乡",711536:"卓兰镇",711700:"彰化县",711727:"彰化市",711728:"芬园乡",711729:"花坛乡",711730:"秀水乡",711731:"鹿港镇",711732:"福兴乡",711733:"线西乡",711734:"和美镇",711735:"伸港乡",711736:"员林镇",711737:"社头乡",711738:"永靖乡",711739:"埔心乡",711740:"溪湖镇",711741:"大村乡",711742:"埔盐乡",711743:"田中镇",711744:"北斗镇",711745:"田尾乡",711746:"埤头乡",711747:"溪州乡",711748:"竹塘乡",711749:"二林镇",711750:"大城乡",711751:"芳苑乡",711752:"二水乡",711900:"嘉义县",711919:"番路乡",711920:"梅山乡",711921:"竹崎乡",711922:"阿里山乡",711923:"中埔乡",711924:"大埔乡",711925:"水上乡",711926:"鹿草乡",711927:"太保市",711928:"朴子市",711929:"东石乡",711930:"六脚乡",711931:"新港乡",711932:"民雄乡",711933:"大林镇",711934:"溪口乡",711935:"义竹乡",711936:"布袋镇",712100:"云林县",712121:"斗南镇",712122:"大埤乡",712123:"虎尾镇",712124:"土库镇",712125:"褒忠乡",712126:"东势乡",712127:"台西乡",712128:"仑背乡",712129:"麦寮乡",712130:"斗六市",712131:"林内乡",712132:"古坑乡",712133:"莿桐乡",712134:"西螺镇",712135:"二仑乡",712136:"北港镇",712137:"水林乡",712138:"口湖乡",712139:"四湖乡",712140:"元长乡",712400:"屏东县",712434:"屏东市",712435:"三地门乡",712436:"雾台乡",712437:"玛家乡",712438:"九如乡",712439:"里港乡",712440:"高树乡",712441:"盐埔乡",712442:"长治乡",712443:"麟洛乡",712444:"竹田乡",712445:"内埔乡",712446:"万丹乡",712447:"潮州镇",712448:"泰武乡",712449:"来义乡",712450:"万峦乡",712451:"崁顶乡",712452:"新埤乡",712453:"南州乡",712454:"林边乡",712455:"东港镇",712456:"琉球乡",712457:"佳冬乡",712458:"新园乡",712459:"枋寮乡",712460:"枋山乡",712461:"春日乡",712462:"狮子乡",712463:"车城乡",712464:"牡丹乡",712465:"恒春镇",712466:"满州乡",712500:"台东县",712517:"台东市",712518:"绿岛乡",712519:"兰屿乡",712520:"延平乡",712521:"卑南乡",712522:"鹿野乡",712523:"关山镇",712524:"海端乡",712525:"池上乡",712526:"东河乡",712527:"成功镇",712528:"长滨乡",712529:"金峰乡",712530:"大武乡",712531:"达仁乡",712532:"太麻里乡",712600:"花莲县",712615:"花莲市",712616:"新城乡",712617:"太鲁阁",712618:"秀林乡",712619:"吉安乡",712620:"寿丰乡",712621:"凤林镇",712622:"光复乡",712623:"丰滨乡",712624:"瑞穗乡",712625:"万荣乡",712626:"玉里镇",712627:"卓溪乡",712628:"富里乡",712700:"澎湖县",712707:"马公市",712708:"西屿乡",712709:"望安乡",712710:"七美乡",712711:"白沙乡",712712:"湖西乡",712800:"连江县",712805:"南竿乡",712806:"北竿乡",712807:"莒光乡",712808:"东引乡",81e4:"香港特别行政区",810100:"香港岛",810101:"中西区",810102:"湾仔",810103:"东区",810104:"南区",810200:"九龙",810201:"九龙城区",810202:"油尖旺区",810203:"深水埗区",810204:"黄大仙区",810205:"观塘区",810300:"新界",810301:"北区",810302:"大埔区",810303:"沙田区",810304:"西贡区",810305:"元朗区",810306:"屯门区",810307:"荃湾区",810308:"葵青区",810309:"离岛区",82e4:"澳门特别行政区",820100:"澳门半岛",820200:"离岛",99e4:"海外",990100:"海外"},a=function(){var e=[];for(var t in n){var a="0000"===t.slice(2,6)?void 0:"00"==t.slice(4,6)?t.slice(0,2)+"0000":t.slice(0,4)+"00";e.push({id:t,pid:a,name:n[t]})}return function(e){for(var t,n={},a=0;a<e.length;a++)(t=e[a])&&t.id&&(n[t.id]=t);for(var r=[],o=0;o<e.length;o++)if(t=e[o])if(null!=t.pid||null!=t.parentId){var l=n[t.pid]||n[t.parentId];l&&(l.children||(l.children=[]),l.children.push(t))}else r.push(t);return r}(e)}();e.exports=a},function(e,t,n){var a,r=n(18);e.exports={d4:function(){return this.natural(1,4)},d6:function(){return this.natural(1,6)},d8:function(){return this.natural(1,8)},d12:function(){return this.natural(1,12)},d20:function(){return this.natural(1,20)},d100:function(){return this.natural(1,100)},guid:function(){var e="abcdefABCDEF1234567890";return this.string(e,8)+"-"+this.string(e,4)+"-"+this.string(e,4)+"-"+this.string(e,4)+"-"+this.string(e,12)},uuid:function(){return this.guid()},id:function(){var e,t=0,n=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"];e=this.pick(r).id+this.date("yyyyMMdd")+this.string("number",3);for(var a=0;a<e.length;a++)t+=e[a]*n[a];return e+=["1","0","X","9","8","7","6","5","4","3","2"][t%11]},increment:(a=0,function(e){return a+=+e||1}),inc:function(e){return this.increment(e)}}},function(e,t,n){var a=n(21),r=n(22);e.exports={Parser:a,Handler:r}},function(e,t){function n(e){this.type=e,this.offset=n.offset(),this.text=n.text()}function a(e,t){n.call(this,"alternate"),this.left=e,this.right=t}function r(e){n.call(this,"match"),this.body=e.filter(Boolean)}function o(e,t){n.call(this,e),this.body=t}function l(e){o.call(this,"capture-group"),this.index=_[this.offset]||(_[this.offset]=y++),this.body=e}function i(e,t){n.call(this,"quantified"),this.body=e,this.quantifier=t}function s(e,t){n.call(this,"quantifier"),this.min=e,this.max=t,this.greedy=!0}function u(e,t){n.call(this,"charset"),this.invert=e,this.body=t}function c(e,t){n.call(this,"range"),this.start=e,this.end=t}function d(e){n.call(this,"literal"),this.body=e,this.escaped=this.body!=this.text}function p(e){n.call(this,"unicode"),this.code=e.toUpperCase()}function m(e){n.call(this,"hex"),this.code=e.toUpperCase()}function f(e){n.call(this,"octal"),this.code=e.toUpperCase()}function h(e){n.call(this,"back-reference"),this.code=e.toUpperCase()}function g(e){n.call(this,"control-character"),this.code=e.toUpperCase()}var v=function(){function e(e,t,n,a,r){this.expected=e,this.found=t,this.offset=n,this.line=a,this.column=r,this.name="SyntaxError",this.message=function(e,t){var n;switch(e.length){case 0:n="end of input";break;case 1:n=e[0];break;default:n=e.slice(0,-1).join(", ")+" or "+e[e.length-1]}return"Expected "+n+" but "+(t?'"'+function(e){function t(e){return e.charCodeAt(0).toString(16).toUpperCase()}return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\x08/g,"\\b").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\f/g,"\\f").replace(/\r/g,"\\r").replace(/[\x00-\x07\x0B\x0E\x0F]/g,(function(e){return"\\x0"+t(e)})).replace(/[\x10-\x1F\x80-\xFF]/g,(function(e){return"\\x"+t(e)})).replace(/[\u0180-\u0FFF]/g,(function(e){return"\\u0"+t(e)})).replace(/[\u1080-\uFFFF]/g,(function(e){return"\\u"+t(e)}))}(t)+'"':"end of input")+" found."}(e,t)}return function(e,t){function n(){this.constructor=e}n.prototype=t.prototype,e.prototype=new n}(e,Error),{SyntaxError:e,parse:function(t){function v(){return t.substring(Zn,Xn)}function y(){return Zn}function _(e){return ea!==e&&(ea>e&&(ea=0,ta={line:1,column:1,seenCR:!1}),function(e,n,a){var r,o;for(r=n;a>r;r++)"\n"===(o=t.charAt(r))?(e.seenCR||e.line++,e.column=1,e.seenCR=!1):"\r"===o||"\u2028"===o||"\u2029"===o?(e.line++,e.column=1,e.seenCR=!0):(e.column++,e.seenCR=!1)}(ta,ea,e),ea=e),ta}function b(e){na>Xn||(Xn>na&&(na=Xn,aa=[]),aa.push(e))}function k(e){var t=0;for(e.sort();t<e.length;)e[t-1]===e[t]?e.splice(t,1):t++}function x(){var e,n,a,r,o;return e=Xn,null!==(n=C())?(a=Xn,124===t.charCodeAt(Xn)?(r=Ee,Xn++):(r=null,0===ra&&b(we)),null!==r&&null!==(o=x())?a=r=[r,o]:(Xn=a,a=Ce),null===a&&(a=Pe),null!==a?(Zn=e,null===(n=Se(n,a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function C(){var e,t,n,a,r;if(e=Xn,null===(t=E())&&(t=Pe),null!==t)if(n=Xn,ra++,a=B(),ra--,null===a?n=Pe:(Xn=n,n=Ce),null!==n){for(a=[],null===(r=S())&&(r=P());null!==r;)a.push(r),null===(r=S())&&(r=P());null!==a?(null===(r=w())&&(r=Pe),null!==r?(Zn=e,null===(t=Be(t,a,r))?(Xn=e,e=t):e=t):(Xn=e,e=Ce)):(Xn=e,e=Ce)}else Xn=e,e=Ce;else Xn=e,e=Ce;return e}function P(){var e;return null===(e=M())&&null===(e=K())&&(e=J()),e}function E(){var e,n;return e=Xn,94===t.charCodeAt(Xn)?(n=Oe,Xn++):(n=null,0===ra&&b(Te)),null!==n&&(Zn=e,n=De()),null===n?(Xn=e,e=n):e=n,e}function w(){var e,n;return e=Xn,36===t.charCodeAt(Xn)?(n=Ne,Xn++):(n=null,0===ra&&b($e)),null!==n&&(Zn=e,n=Ae()),null===n?(Xn=e,e=n):e=n,e}function S(){var e,t,n;return e=Xn,null!==(t=P())&&null!==(n=B())?(Zn=e,null===(t=Re(t,n))?(Xn=e,e=t):e=t):(Xn=e,e=Ce),e}function B(){var e,t,n;return ra++,e=Xn,null!==(t=O())?(null===(n=V())&&(n=Pe),null!==n?(Zn=e,null===(t=Ie(t,n))?(Xn=e,e=t):e=t):(Xn=e,e=Ce)):(Xn=e,e=Ce),ra--,null===e&&(t=null,0===ra&&b(Ve)),e}function O(){var e;return null===(e=T())&&null===(e=D())&&null===(e=N())&&null===(e=$())&&null===(e=A())&&(e=R()),e}function T(){var e,n,a,r,o,l;return e=Xn,123===t.charCodeAt(Xn)?(n=Me,Xn++):(n=null,0===ra&&b(je)),null!==n&&null!==(a=I())?(44===t.charCodeAt(Xn)?(r=He,Xn++):(r=null,0===ra&&b(Le)),null!==r&&null!==(o=I())?(125===t.charCodeAt(Xn)?(l=qe,Xn++):(l=null,0===ra&&b(Ke)),null!==l?(Zn=e,null===(n=Fe(a,o))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function D(){var e,n,a,r;return e=Xn,123===t.charCodeAt(Xn)?(n=Me,Xn++):(n=null,0===ra&&b(je)),null!==n&&null!==(a=I())?(t.substr(Xn,2)===ze?(r=ze,Xn+=2):(r=null,0===ra&&b(Ue)),null!==r?(Zn=e,null===(n=Ge(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function N(){var e,n,a,r;return e=Xn,123===t.charCodeAt(Xn)?(n=Me,Xn++):(n=null,0===ra&&b(je)),null!==n&&null!==(a=I())?(125===t.charCodeAt(Xn)?(r=qe,Xn++):(r=null,0===ra&&b(Ke)),null!==r?(Zn=e,null===(n=Je(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function $(){var e,n;return e=Xn,43===t.charCodeAt(Xn)?(n=We,Xn++):(n=null,0===ra&&b(Ye)),null!==n&&(Zn=e,n=Qe()),null===n?(Xn=e,e=n):e=n,e}function A(){var e,n;return e=Xn,42===t.charCodeAt(Xn)?(n=Xe,Xn++):(n=null,0===ra&&b(Ze)),null!==n&&(Zn=e,n=et()),null===n?(Xn=e,e=n):e=n,e}function R(){var e,n;return e=Xn,63===t.charCodeAt(Xn)?(n=tt,Xn++):(n=null,0===ra&&b(nt)),null!==n&&(Zn=e,n=at()),null===n?(Xn=e,e=n):e=n,e}function V(){var e;return 63===t.charCodeAt(Xn)?(e=tt,Xn++):(e=null,0===ra&&b(nt)),e}function I(){var e,n,a;if(e=Xn,n=[],rt.test(t.charAt(Xn))?(a=t.charAt(Xn),Xn++):(a=null,0===ra&&b(ot)),null!==a)for(;null!==a;)n.push(a),rt.test(t.charAt(Xn))?(a=t.charAt(Xn),Xn++):(a=null,0===ra&&b(ot));else n=Ce;return null!==n&&(Zn=e,n=lt(n)),null===n?(Xn=e,e=n):e=n,e}function M(){var e,n,a,r;return e=Xn,40===t.charCodeAt(Xn)?(n=it,Xn++):(n=null,0===ra&&b(st)),null!==n?(null===(a=L())&&null===(a=q())&&null===(a=H())&&(a=j()),null!==a?(41===t.charCodeAt(Xn)?(r=ut,Xn++):(r=null,0===ra&&b(ct)),null!==r?(Zn=e,null===(n=dt(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function j(){var e,t;return e=Xn,null!==(t=x())&&(Zn=e,t=pt(t)),null===t?(Xn=e,e=t):e=t,e}function H(){var e,n,a;return e=Xn,t.substr(Xn,2)===mt?(n=mt,Xn+=2):(n=null,0===ra&&b(ft)),null!==n&&null!==(a=x())?(Zn=e,null===(n=ht(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce),e}function L(){var e,n,a;return e=Xn,t.substr(Xn,2)===gt?(n=gt,Xn+=2):(n=null,0===ra&&b(vt)),null!==n&&null!==(a=x())?(Zn=e,null===(n=yt(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce),e}function q(){var e,n,a;return e=Xn,t.substr(Xn,2)===_t?(n=_t,Xn+=2):(n=null,0===ra&&b(bt)),null!==n&&null!==(a=x())?(Zn=e,null===(n=kt(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce),e}function K(){var e,n,a,r,o;if(ra++,e=Xn,91===t.charCodeAt(Xn)?(n=Ct,Xn++):(n=null,0===ra&&b(Pt)),null!==n)if(94===t.charCodeAt(Xn)?(a=Oe,Xn++):(a=null,0===ra&&b(Te)),null===a&&(a=Pe),null!==a){for(r=[],null===(o=F())&&(o=z());null!==o;)r.push(o),null===(o=F())&&(o=z());null!==r?(93===t.charCodeAt(Xn)?(o=Et,Xn++):(o=null,0===ra&&b(wt)),null!==o?(Zn=e,null===(n=St(a,r))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce)}else Xn=e,e=Ce;else Xn=e,e=Ce;return ra--,null===e&&(n=null,0===ra&&b(xt)),e}function F(){var e,n,a,r;return ra++,e=Xn,null!==(n=z())?(45===t.charCodeAt(Xn)?(a=Ot,Xn++):(a=null,0===ra&&b(Tt)),null!==a&&null!==(r=z())?(Zn=e,null===(n=Dt(n,r))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),ra--,null===e&&(n=null,0===ra&&b(Bt)),e}function z(){var e;return ra++,null===(e=G())&&(e=U()),ra--,null===e&&0===ra&&b(Nt),e}function U(){var e,n;return e=Xn,$t.test(t.charAt(Xn))?(n=t.charAt(Xn),Xn++):(n=null,0===ra&&b(At)),null!==n&&(Zn=e,n=Rt(n)),null===n?(Xn=e,e=n):e=n,e}function G(){var e;return null===(e=X())&&null===(e=pe())&&null===(e=te())&&null===(e=ne())&&null===(e=ae())&&null===(e=re())&&null===(e=oe())&&null===(e=le())&&null===(e=ie())&&null===(e=se())&&null===(e=ue())&&null===(e=ce())&&null===(e=de())&&null===(e=fe())&&null===(e=he())&&null===(e=ge())&&null===(e=ve())&&(e=ye()),e}function J(){var e;return null===(e=W())&&null===(e=Q())&&(e=Y()),e}function W(){var e,n;return e=Xn,46===t.charCodeAt(Xn)?(n=Vt,Xn++):(n=null,0===ra&&b(It)),null!==n&&(Zn=e,n=Mt()),null===n?(Xn=e,e=n):e=n,e}function Y(){var e,n;return ra++,e=Xn,Ht.test(t.charAt(Xn))?(n=t.charAt(Xn),Xn++):(n=null,0===ra&&b(Lt)),null!==n&&(Zn=e,n=Rt(n)),null===n?(Xn=e,e=n):e=n,ra--,null===e&&(n=null,0===ra&&b(jt)),e}function Q(){var e;return null===(e=Z())&&null===(e=ee())&&null===(e=pe())&&null===(e=te())&&null===(e=ne())&&null===(e=ae())&&null===(e=re())&&null===(e=oe())&&null===(e=le())&&null===(e=ie())&&null===(e=se())&&null===(e=ue())&&null===(e=ce())&&null===(e=de())&&null===(e=me())&&null===(e=fe())&&null===(e=he())&&null===(e=ge())&&null===(e=ve())&&(e=ye()),e}function X(){var e,n;return e=Xn,t.substr(Xn,2)===qt?(n=qt,Xn+=2):(n=null,0===ra&&b(Kt)),null!==n&&(Zn=e,n=Ft()),null===n?(Xn=e,e=n):e=n,e}function Z(){var e,n;return e=Xn,t.substr(Xn,2)===qt?(n=qt,Xn+=2):(n=null,0===ra&&b(Kt)),null!==n&&(Zn=e,n=zt()),null===n?(Xn=e,e=n):e=n,e}function ee(){var e,n;return e=Xn,t.substr(Xn,2)===Ut?(n=Ut,Xn+=2):(n=null,0===ra&&b(Gt)),null!==n&&(Zn=e,n=Jt()),null===n?(Xn=e,e=n):e=n,e}function te(){var e,n;return e=Xn,t.substr(Xn,2)===Wt?(n=Wt,Xn+=2):(n=null,0===ra&&b(Yt)),null!==n&&(Zn=e,n=Qt()),null===n?(Xn=e,e=n):e=n,e}function ne(){var e,n;return e=Xn,t.substr(Xn,2)===Xt?(n=Xt,Xn+=2):(n=null,0===ra&&b(Zt)),null!==n&&(Zn=e,n=en()),null===n?(Xn=e,e=n):e=n,e}function ae(){var e,n;return e=Xn,t.substr(Xn,2)===tn?(n=tn,Xn+=2):(n=null,0===ra&&b(nn)),null!==n&&(Zn=e,n=an()),null===n?(Xn=e,e=n):e=n,e}function re(){var e,n;return e=Xn,t.substr(Xn,2)===rn?(n=rn,Xn+=2):(n=null,0===ra&&b(on)),null!==n&&(Zn=e,n=ln()),null===n?(Xn=e,e=n):e=n,e}function oe(){var e,n;return e=Xn,t.substr(Xn,2)===sn?(n=sn,Xn+=2):(n=null,0===ra&&b(un)),null!==n&&(Zn=e,n=cn()),null===n?(Xn=e,e=n):e=n,e}function le(){var e,n;return e=Xn,t.substr(Xn,2)===dn?(n=dn,Xn+=2):(n=null,0===ra&&b(pn)),null!==n&&(Zn=e,n=mn()),null===n?(Xn=e,e=n):e=n,e}function ie(){var e,n;return e=Xn,t.substr(Xn,2)===fn?(n=fn,Xn+=2):(n=null,0===ra&&b(hn)),null!==n&&(Zn=e,n=gn()),null===n?(Xn=e,e=n):e=n,e}function se(){var e,n;return e=Xn,t.substr(Xn,2)===vn?(n=vn,Xn+=2):(n=null,0===ra&&b(yn)),null!==n&&(Zn=e,n=_n()),null===n?(Xn=e,e=n):e=n,e}function ue(){var e,n;return e=Xn,t.substr(Xn,2)===bn?(n=bn,Xn+=2):(n=null,0===ra&&b(kn)),null!==n&&(Zn=e,n=xn()),null===n?(Xn=e,e=n):e=n,e}function ce(){var e,n;return e=Xn,t.substr(Xn,2)===Cn?(n=Cn,Xn+=2):(n=null,0===ra&&b(Pn)),null!==n&&(Zn=e,n=En()),null===n?(Xn=e,e=n):e=n,e}function de(){var e,n;return e=Xn,t.substr(Xn,2)===wn?(n=wn,Xn+=2):(n=null,0===ra&&b(Sn)),null!==n&&(Zn=e,n=Bn()),null===n?(Xn=e,e=n):e=n,e}function pe(){var e,n,a;return e=Xn,t.substr(Xn,2)===On?(n=On,Xn+=2):(n=null,0===ra&&b(Tn)),null!==n?(t.length>Xn?(a=t.charAt(Xn),Xn++):(a=null,0===ra&&b(Dn)),null!==a?(Zn=e,null===(n=Nn(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function me(){var e,n,a;return e=Xn,92===t.charCodeAt(Xn)?(n=$n,Xn++):(n=null,0===ra&&b(An)),null!==n?(Rn.test(t.charAt(Xn))?(a=t.charAt(Xn),Xn++):(a=null,0===ra&&b(Vn)),null!==a?(Zn=e,null===(n=In(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}function fe(){var e,n,a,r;if(e=Xn,t.substr(Xn,2)===Mn?(n=Mn,Xn+=2):(n=null,0===ra&&b(jn)),null!==n){if(a=[],Hn.test(t.charAt(Xn))?(r=t.charAt(Xn),Xn++):(r=null,0===ra&&b(Ln)),null!==r)for(;null!==r;)a.push(r),Hn.test(t.charAt(Xn))?(r=t.charAt(Xn),Xn++):(r=null,0===ra&&b(Ln));else a=Ce;null!==a?(Zn=e,null===(n=qn(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)}else Xn=e,e=Ce;return e}function he(){var e,n,a,r;if(e=Xn,t.substr(Xn,2)===Kn?(n=Kn,Xn+=2):(n=null,0===ra&&b(Fn)),null!==n){if(a=[],zn.test(t.charAt(Xn))?(r=t.charAt(Xn),Xn++):(r=null,0===ra&&b(Un)),null!==r)for(;null!==r;)a.push(r),zn.test(t.charAt(Xn))?(r=t.charAt(Xn),Xn++):(r=null,0===ra&&b(Un));else a=Ce;null!==a?(Zn=e,null===(n=Gn(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)}else Xn=e,e=Ce;return e}function ge(){var e,n,a,r;if(e=Xn,t.substr(Xn,2)===Jn?(n=Jn,Xn+=2):(n=null,0===ra&&b(Wn)),null!==n){if(a=[],zn.test(t.charAt(Xn))?(r=t.charAt(Xn),Xn++):(r=null,0===ra&&b(Un)),null!==r)for(;null!==r;)a.push(r),zn.test(t.charAt(Xn))?(r=t.charAt(Xn),Xn++):(r=null,0===ra&&b(Un));else a=Ce;null!==a?(Zn=e,null===(n=Yn(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)}else Xn=e,e=Ce;return e}function ve(){var e,n;return e=Xn,t.substr(Xn,2)===Mn?(n=Mn,Xn+=2):(n=null,0===ra&&b(jn)),null!==n&&(Zn=e,n=Qn()),null===n?(Xn=e,e=n):e=n,e}function ye(){var e,n,a;return e=Xn,92===t.charCodeAt(Xn)?(n=$n,Xn++):(n=null,0===ra&&b(An)),null!==n?(t.length>Xn?(a=t.charAt(Xn),Xn++):(a=null,0===ra&&b(Dn)),null!==a?(Zn=e,null===(n=Rt(a))?(Xn=e,e=n):e=n):(Xn=e,e=Ce)):(Xn=e,e=Ce),e}var _e,be=arguments.length>1?arguments[1]:{},ke={regexp:x},xe=x,Ce=null,Pe="",Ee="|",we='"|"',Se=function(e,t){return t?new a(e,t[1]):e},Be=function(e,t,n){return new r([e].concat(t).concat([n]))},Oe="^",Te='"^"',De=function(){return new n("start")},Ne="$",$e='"$"',Ae=function(){return new n("end")},Re=function(e,t){return new i(e,t)},Ve="Quantifier",Ie=function(e,t){return t&&(e.greedy=!1),e},Me="{",je='"{"',He=",",Le='","',qe="}",Ke='"}"',Fe=function(e,t){return new s(e,t)},ze=",}",Ue='",}"',Ge=function(e){return new s(e,1/0)},Je=function(e){return new s(e,e)},We="+",Ye='"+"',Qe=function(){return new s(1,1/0)},Xe="*",Ze='"*"',et=function(){return new s(0,1/0)},tt="?",nt='"?"',at=function(){return new s(0,1)},rt=/^[0-9]/,ot="[0-9]",lt=function(e){return+e.join("")},it="(",st='"("',ut=")",ct='")"',dt=function(e){return e},pt=function(e){return new l(e)},mt="?:",ft='"?:"',ht=function(e){return new o("non-capture-group",e)},gt="?=",vt='"?="',yt=function(e){return new o("positive-lookahead",e)},_t="?!",bt='"?!"',kt=function(e){return new o("negative-lookahead",e)},xt="CharacterSet",Ct="[",Pt='"["',Et="]",wt='"]"',St=function(e,t){return new u(!!e,t)},Bt="CharacterRange",Ot="-",Tt='"-"',Dt=function(e,t){return new c(e,t)},Nt="Character",$t=/^[^\\\]]/,At="[^\\\\\\]]",Rt=function(e){return new d(e)},Vt=".",It='"."',Mt=function(){return new n("any-character")},jt="Literal",Ht=/^[^|\\\/.[()?+*$\^]/,Lt="[^|\\\\\\/.[()?+*$\\^]",qt="\\b",Kt='"\\\\b"',Ft=function(){return new n("backspace")},zt=function(){return new n("word-boundary")},Ut="\\B",Gt='"\\\\B"',Jt=function(){return new n("non-word-boundary")},Wt="\\d",Yt='"\\\\d"',Qt=function(){return new n("digit")},Xt="\\D",Zt='"\\\\D"',en=function(){return new n("non-digit")},tn="\\f",nn='"\\\\f"',an=function(){return new n("form-feed")},rn="\\n",on='"\\\\n"',ln=function(){return new n("line-feed")},sn="\\r",un='"\\\\r"',cn=function(){return new n("carriage-return")},dn="\\s",pn='"\\\\s"',mn=function(){return new n("white-space")},fn="\\S",hn='"\\\\S"',gn=function(){return new n("non-white-space")},vn="\\t",yn='"\\\\t"',_n=function(){return new n("tab")},bn="\\v",kn='"\\\\v"',xn=function(){return new n("vertical-tab")},Cn="\\w",Pn='"\\\\w"',En=function(){return new n("word")},wn="\\W",Sn='"\\\\W"',Bn=function(){return new n("non-word")},On="\\c",Tn='"\\\\c"',Dn="any character",Nn=function(e){return new g(e)},$n="\\",An='"\\\\"',Rn=/^[1-9]/,Vn="[1-9]",In=function(e){return new h(e)},Mn="\\0",jn='"\\\\0"',Hn=/^[0-7]/,Ln="[0-7]",qn=function(e){return new f(e.join(""))},Kn="\\x",Fn='"\\\\x"',zn=/^[0-9a-fA-F]/,Un="[0-9a-fA-F]",Gn=function(e){return new m(e.join(""))},Jn="\\u",Wn='"\\\\u"',Yn=function(e){return new p(e.join(""))},Qn=function(){return new n("null-character")},Xn=0,Zn=0,ea=0,ta={line:1,column:1,seenCR:!1},na=0,aa=[],ra=0;if("startRule"in be){if(!(be.startRule in ke))throw new Error("Can't start parsing from rule \""+be.startRule+'".');xe=ke[be.startRule]}if(n.offset=y,n.text=v,null!==(_e=xe())&&Xn===t.length)return _e;throw k(aa),Zn=Math.max(Xn,na),new e(aa,Zn<t.length?t.charAt(Zn):null,Zn,_(Zn).line,_(Zn).column)}}}(),y=1,_={};e.exports=v},function(e,t,n){var a=n(3),r=n(5),o={extend:a.extend},l=m(97,122),i=m(65,90),s=m(48,57),u=m(32,47)+m(58,64)+m(91,96)+m(123,126),c=m(32,126),d=" \f\n\r\t\v \u2028\u2029",p={"\\w":l+i+s+"_","\\W":u.replace("_",""),"\\s":d,"\\S":function(){for(var e=c,t=0;t<d.length;t++)e=e.replace(d[t],"");return e}(),"\\d":s,"\\D":l+i+u};function m(e,t){for(var n="",a=e;a<=t;a++)n+=String.fromCharCode(a);return n}o.gen=function(e,t,n){return n=n||{guid:1},o[e.type]?o[e.type](e,t,n):o.token(e,t,n)},o.extend({token:function(e,t,n){switch(e.type){case"start":case"end":case"backspace":case"word-boundary":return"";case"any-character":return r.character();case"non-word-boundary":case"form-feed":case"carriage-return":case"tab":case"vertical-tab":break;case"digit":return r.pick(s.split(""));case"non-digit":return r.pick((l+i+u).split(""));case"line-feed":return e.body||e.text;case"white-space":return r.pick(d.split(""));case"non-white-space":case"word":return r.pick((l+i+s).split(""));case"non-word":return r.pick(u.replace("_","").split(""))}return e.body||e.text},alternate:function(e,t,n){return this.gen(r.boolean()?e.left:e.right,t,n)},match:function(e,t,n){t="";for(var a=0;a<e.body.length;a++)t+=this.gen(e.body[a],t,n);return t},"capture-group":function(e,t,n){return t=this.gen(e.body,t,n),n[n.guid++]=t,t},"non-capture-group":function(e,t,n){return this.gen(e.body,t,n)},"positive-lookahead":function(e,t,n){return this.gen(e.body,t,n)},"negative-lookahead":function(e,t,n){return""},quantified:function(e,t,n){t="";for(var a=this.quantifier(e.quantifier),r=0;r<a;r++)t+=this.gen(e.body,t,n);return t},quantifier:function(e,t,n){var a=Math.max(e.min,0),o=isFinite(e.max)?e.max:a+r.integer(3,7);return r.integer(a,o)},charset:function(e,t,n){if(e.invert)return this["invert-charset"](e,t,n);var a=r.pick(e.body);return this.gen(a,t,n)},"invert-charset":function(e,t,n){for(var a,o=c,l=0;l<e.body.length;l++)switch((a=e.body[l]).type){case"literal":o=o.replace(a.body,"");break;case"range":for(var i=this.gen(a.start,t,n).charCodeAt(),s=this.gen(a.end,t,n).charCodeAt(),u=i;u<=s;u++)o=o.replace(String.fromCharCode(u),"");default:var d=p[a.text];if(d)for(var m=0;m<=d.length;m++)o=o.replace(d[m],"")}return r.pick(o.split(""))},range:function(e,t,n){var a=this.gen(e.start,t,n).charCodeAt(),o=this.gen(e.end,t,n).charCodeAt();return String.fromCharCode(r.integer(a,o))},literal:function(e,t,n){return e.escaped?e.body:e.text},unicode:function(e,t,n){return String.fromCharCode(parseInt(e.code,16))},hex:function(e,t,n){return String.fromCharCode(parseInt(e.code,16))},octal:function(e,t,n){return String.fromCharCode(parseInt(e.code,8))},"back-reference":function(e,t,n){return n[e.code]||""},CONTROL_CHARACTER_MAP:function(){for(var e="@ A B C D E F G H I J K L M N O P Q R S T U V W X Y Z [ \\ ] ^ _".split(" "),t="\0        \b \t \n \v \f \r                  ".split(" "),n={},a=0;a<e.length;a++)n[e[a]]=t[a];return n}(),"control-character":function(e,t,n){return this.CONTROL_CHARACTER_MAP[e.code]}}),e.exports=o},function(e,t,n){e.exports=n(24)},function(e,t,n){var a=n(2),r=n(3),o=n(4);e.exports=function e(t,n,l){l=l||[];var i={name:"string"==typeof n?n.replace(a.RE_KEY,"$1"):n,template:t,type:r.type(t),rule:o.parse(n)};switch(i.path=l.slice(0),i.path.push(void 0===n?"ROOT":i.name),i.type){case"array":i.items=[],r.each(t,(function(t,n){i.items.push(e(t,n,i.path))}));break;case"object":i.properties=[],r.each(t,(function(t,n){i.properties.push(e(t,n,i.path))}))}return i}},function(e,t,n){e.exports=n(26)},function(e,t,n){var a=n(2),r=n(3),o=n(23);function l(e,t){for(var n=o(e),a=i.diff(n,t),r=0;r<a.length;r++);return a}var i={diff:function(e,t,n){var a=[];return this.name(e,t,n,a)&&this.type(e,t,n,a)&&(this.value(e,t,n,a),this.properties(e,t,n,a),this.items(e,t,n,a)),a},name:function(e,t,n,a){var r=a.length;return s.equal("name",e.path,n+"",e.name+"",a),a.length===r},type:function(e,t,n,o){var l=o.length;switch(e.type){case"string":if(e.template.match(a.RE_PLACEHOLDER))return!0;break;case"array":if(e.rule.parameters){if(void 0!==e.rule.min&&void 0===e.rule.max&&1===e.rule.count)return!0;if(e.rule.parameters[2])return!0}break;case"function":return!0}return s.equal("type",e.path,r.type(t),e.type,o),o.length===l},value:function(e,t,n,r){var o,l=r.length,i=e.rule,u=e.type;if("object"===u||"array"===u||"function"===u)return!0;if(!i.parameters){switch(u){case"regexp":return s.match("value",e.path,t,e.template,r),r.length===l;case"string":if(e.template.match(a.RE_PLACEHOLDER))return r.length===l}return s.equal("value",e.path,t,e.template,r),r.length===l}switch(u){case"number":var c=(t+"").split(".");c[0]=+c[0],void 0!==i.min&&void 0!==i.max&&(s.greaterThanOrEqualTo("value",e.path,c[0],Math.min(i.min,i.max),r),s.lessThanOrEqualTo("value",e.path,c[0],Math.max(i.min,i.max),r)),void 0!==i.min&&void 0===i.max&&s.equal("value",e.path,c[0],i.min,r,"[value] "+n),i.decimal&&(void 0!==i.dmin&&void 0!==i.dmax&&(s.greaterThanOrEqualTo("value",e.path,c[1].length,i.dmin,r),s.lessThanOrEqualTo("value",e.path,c[1].length,i.dmax,r)),void 0!==i.dmin&&void 0===i.dmax&&s.equal("value",e.path,c[1].length,i.dmin,r));break;case"boolean":break;case"string":o=(o=t.match(new RegExp(e.template,"g")))?o.length:0,void 0!==i.min&&void 0!==i.max&&(s.greaterThanOrEqualTo("repeat count",e.path,o,i.min,r),s.lessThanOrEqualTo("repeat count",e.path,o,i.max,r)),void 0!==i.min&&void 0===i.max&&s.equal("repeat count",e.path,o,i.min,r);break;case"regexp":o=(o=t.match(new RegExp(e.template.source.replace(/^\^|\$$/g,""),"g")))?o.length:0,void 0!==i.min&&void 0!==i.max&&(s.greaterThanOrEqualTo("repeat count",e.path,o,i.min,r),s.lessThanOrEqualTo("repeat count",e.path,o,i.max,r)),void 0!==i.min&&void 0===i.max&&s.equal("repeat count",e.path,o,i.min,r)}return r.length===l},properties:function(e,t,n,a){var o=a.length,l=e.rule,i=r.keys(t);if(e.properties){if(e.rule.parameters?(void 0!==l.min&&void 0!==l.max&&(s.greaterThanOrEqualTo("properties length",e.path,i.length,Math.min(l.min,l.max),a),s.lessThanOrEqualTo("properties length",e.path,i.length,Math.max(l.min,l.max),a)),void 0!==l.min&&void 0===l.max&&1!==l.count&&s.equal("properties length",e.path,i.length,l.min,a)):s.equal("properties length",e.path,i.length,e.properties.length,a),a.length!==o)return!1;for(var u=0;u<i.length;u++)a.push.apply(a,this.diff(function(){var t;return r.each(e.properties,(function(e){e.name===i[u]&&(t=e)})),t||e.properties[u]}(),t[i[u]],i[u]));return a.length===o}},items:function(e,t,n,a){var r=a.length;if(e.items){var o=e.rule;if(e.rule.parameters){if(void 0!==o.min&&void 0!==o.max&&(s.greaterThanOrEqualTo("items",e.path,t.length,Math.min(o.min,o.max)*e.items.length,a,"[{utype}] array is too short: {path} must have at least {expected} elements but instance has {actual} elements"),s.lessThanOrEqualTo("items",e.path,t.length,Math.max(o.min,o.max)*e.items.length,a,"[{utype}] array is too long: {path} must have at most {expected} elements but instance has {actual} elements")),void 0!==o.min&&void 0===o.max){if(1===o.count)return a.length===r;s.equal("items length",e.path,t.length,o.min*e.items.length,a)}if(o.parameters[2])return a.length===r}else s.equal("items length",e.path,t.length,e.items.length,a);if(a.length!==r)return!1;for(var l=0;l<t.length;l++)a.push.apply(a,this.diff(e.items[l%e.items.length],t[l],l%e.items.length));return a.length===r}}},s={message:function(e){return(e.message||"[{utype}] Expect {path}'{ltype} {action} {expected}, but is {actual}").replace("{utype}",e.type.toUpperCase()).replace("{ltype}",e.type.toLowerCase()).replace("{path}",r.isArray(e.path)&&e.path.join(".")||e.path).replace("{action}",e.action).replace("{expected}",e.expected).replace("{actual}",e.actual)},equal:function(e,t,n,a,r,o){if(n===a)return!0;if("type"===e&&"regexp"===a&&"string"===n)return!0;var l={path:t,type:e,actual:n,expected:a,action:"is equal to",message:o};return l.message=s.message(l),r.push(l),!1},match:function(e,t,n,a,r,o){if(a.test(n))return!0;var l={path:t,type:e,actual:n,expected:a,action:"matches",message:o};return l.message=s.message(l),r.push(l),!1},notEqual:function(e,t,n,a,r,o){if(n!==a)return!0;var l={path:t,type:e,actual:n,expected:a,action:"is not equal to",message:o};return l.message=s.message(l),r.push(l),!1},greaterThan:function(e,t,n,a,r,o){if(n>a)return!0;var l={path:t,type:e,actual:n,expected:a,action:"is greater than",message:o};return l.message=s.message(l),r.push(l),!1},lessThan:function(e,t,n,a,r,o){if(n<a)return!0;var l={path:t,type:e,actual:n,expected:a,action:"is less to",message:o};return l.message=s.message(l),r.push(l),!1},greaterThanOrEqualTo:function(e,t,n,a,r,o){if(n>=a)return!0;var l={path:t,type:e,actual:n,expected:a,action:"is greater than or equal to",message:o};return l.message=s.message(l),r.push(l),!1},lessThanOrEqualTo:function(e,t,n,a,r,o){if(n<=a)return!0;var l={path:t,type:e,actual:n,expected:a,action:"is less than or equal to",message:o};return l.message=s.message(l),r.push(l),!1}};l.Diff=i,l.Assert=s,e.exports=l},function(e,t,n){e.exports=n(28)},function(e,t,n){var a=n(3);window._XMLHttpRequest=window.XMLHttpRequest,window._ActiveXObject=window.ActiveXObject;try{new window.Event("custom")}catch(c){window.Event=function(e,t,n,a){var r=document.createEvent("CustomEvent");return r.initCustomEvent(e,t,n,a),r}}var r={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},o="readystatechange loadstart progress abort error load timeout loadend".split(" "),l="timeout withCredentials".split(" "),i="readyState responseURL status statusText responseType response responseText responseXML".split(" "),s="OK";function u(){this.custom={events:{},requestHeaders:{},responseHeaders:{}}}u._settings={timeout:"10-100"},u.setup=function(e){return a.extend(u._settings,e),u._settings},a.extend(u,r),a.extend(u.prototype,r),u.prototype.mock=!0,u.prototype.match=!1,a.extend(u.prototype,{open:function(e,t,n,r,s){var c=this;a.extend(this.custom,{method:e,url:t,async:"boolean"!=typeof n||n,username:r,password:s,options:{url:t,type:e}}),this.custom.timeout=function(e){if("number"==typeof e)return e;if("string"==typeof e&&!~e.indexOf("-"))return parseInt(e,10);if("string"==typeof e&&~e.indexOf("-")){var t=e.split("-"),n=parseInt(t[0],10),a=parseInt(t[1],10);return Math.round(Math.random()*(a-n))+n}}(u._settings.timeout);var d=function(e){for(var t in u.Mock._mocked){var n=u.Mock._mocked[t];if((!n.rurl||r(n.rurl,e.url))&&(!n.rtype||r(n.rtype,e.type.toLowerCase())))return n}function r(e,t){return"string"===a.type(e)?e===t:"regexp"===a.type(e)?e.test(t):void 0}}(this.custom.options);function p(e){for(var t=0;t<i.length;t++)try{c[i[t]]=m[i[t]]}catch(n){}c.dispatchEvent(new Event(e.type))}if(d)this.match=!0,this.custom.template=d,this.readyState=u.OPENED,this.dispatchEvent(new Event("readystatechange"));else{var m=function(){var e,t,n,a,r=(e=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,t=/^([\w.+-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,n=location.href,a=t.exec(n.toLowerCase())||[],e.test(a[1]));return window.ActiveXObject?!r&&o()||l():o();function o(){try{return new window._XMLHttpRequest}catch(e){}}function l(){try{return new window._ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}}();this.custom.xhr=m;for(var f=0;f<o.length;f++)m.addEventListener(o[f],p);r?m.open(e,t,n,r,s):m.open(e,t,n);for(var h=0;h<l.length;h++)try{m[l[h]]=c[l[h]]}catch(g){}}},setRequestHeader:function(e,t){if(this.match){var n=this.custom.requestHeaders;n[e]?n[e]+=","+t:n[e]=t}else this.custom.xhr.setRequestHeader(e,t)},timeout:0,withCredentials:!1,upload:{},send:function(e){var t=this;function n(){var e,n;t.readyState=u.HEADERS_RECEIVED,t.dispatchEvent(new Event("readystatechange")),t.readyState=u.LOADING,t.dispatchEvent(new Event("readystatechange")),t.status=200,t.statusText=s,t.response=t.responseText=JSON.stringify((e=t.custom.template,n=t.custom.options,a.isFunction(e.template)?e.template(n):u.Mock.mock(e.template)),null,4),t.readyState=u.DONE,t.dispatchEvent(new Event("readystatechange")),t.dispatchEvent(new Event("load")),t.dispatchEvent(new Event("loadend"))}this.custom.options.body=e,this.match?(this.setRequestHeader("X-Requested-With","MockXMLHttpRequest"),this.dispatchEvent(new Event("loadstart")),this.custom.async?setTimeout(n,this.custom.timeout):n()):this.custom.xhr.send(e)},abort:function(){this.match?(this.readyState=u.UNSENT,this.dispatchEvent(new Event("abort",!1,!1,this)),this.dispatchEvent(new Event("error",!1,!1,this))):this.custom.xhr.abort()}}),a.extend(u.prototype,{responseURL:"",status:u.UNSENT,statusText:"",getResponseHeader:function(e){return this.match?this.custom.responseHeaders[e.toLowerCase()]:this.custom.xhr.getResponseHeader(e)},getAllResponseHeaders:function(){if(!this.match)return this.custom.xhr.getAllResponseHeaders();var e=this.custom.responseHeaders,t="";for(var n in e)e.hasOwnProperty(n)&&(t+=n+": "+e[n]+"\r\n");return t},overrideMimeType:function(){},responseType:"",response:null,responseText:"",responseXML:null}),a.extend(u.prototype,{addEventListener:function(e,t){var n=this.custom.events;n[e]||(n[e]=[]),n[e].push(t)},removeEventListener:function(e,t){for(var n=this.custom.events[e]||[],a=0;a<n.length;a++)n[a]===t&&n.splice(a--,1)},dispatchEvent:function(e){for(var t=this.custom.events[e.type]||[],n=0;n<t.length;n++)t[n].call(this,e);var a="on"+e.type;this[a]&&this[a](e)}}),e.exports=u}])},module.exports=factory()})(mock);var Mock=mock.exports;const config=apidocFeConfig,mockExtends=config.MOCK_EXTENDS?config.MOCK_EXTENDS:{};function renderCodeJsonByParams(e,t,n,a){const r={};return e&&e.length&&e.forEach((e=>{var o,l;let i=e.default;if(t&&e.mock&&(i=Mock.mock(e.mock)),null==i)switch(e.type){case"int":i=null;break;case"boolean":i=!1}"array"!==e.type||"array"!==i&&i||(i=[]);let s=["int","float","boolean","array","object"].includes(e.type)?i:`${trim(i)}`;if("object"==e.type&&e.children&&e.children.length)s=renderCodeJsonByParams(e.children,t);else if("array"==e.type&&e.children&&e.children.length){const n=renderCodeJsonByParams(e.children,t),a=[];for(const e in n)a.push(n[e]);s="array"==e.childrenType?[a]:"string"==e.childrenType||"int"==e.childrenType?a.length?a:null:[n]}else if("tree"==e.type&&e.children&&e.children.length){s=[renderCodeJsonByParams(e.children,t)]}if(n&&n.body){const t=null==(o=n.body)?void 0:o.find((t=>t.name===e.name&&"all"==t.appKey)),r=null==(l=n.body)?void 0:l.find((t=>t.name===e.name&&t.appKey==a)),i=r||t;i&&i.value&&(s=i.value)}r[e.name]=s})),r}function formatJsonCode(e){return JSON.stringify(e,null,"\t")}function renderHoverTipsContent(e,t){const n=e.desc?`，${t("apiPage.common.desc")}：${e.desc}`:"";return`${t("apiPage.common.type")}：${e.type}${n}`}function handleHoverTipsParams(e,t=3,n=2){const a={},{t:r}=useI18n(),o=useAppStore();let l=n;for(let i=0;i<e.length;i++){const n=e[i];let s=l+i;"array"==n.childrenType&&(s+=1);const u=`${n.name}_${s}_${t}`;let c="";if(c=o.feConfig.CUSTOM_METHODS&&o.feConfig.CUSTOM_METHODS.RENDER_HOVER_TIPS_CONTENT?o.feConfig.CUSTOM_METHODS.RENDER_HOVER_TIPS_CONTENT(n,r):renderHoverTipsContent(n,r),a[u]=[{value:c}],n.children&&n.children.length){const e=n.type&&["array","tree"].includes(n.type)?2:1,r=handleHoverTipsParams(n.children,t+e,s+e);for(const t in r){const e=r[t];a[t]=e}l=l+Object.keys(r).length+e+1}}return a}Mock.Random.extend(__spreadValues({phone:function(){return this.pick(["130","131","132","133","135","137","138","152","155","157","159","170","177","180","182","183","185","187","188","189","191"])+Mock.mock(/\d{8}/)},idcard:function(){return createIdcard()},regexp:function(e,n){let key="regexp";n&&(key=key+"|"+n);const res=Mock.mock({[key]:eval(e)});return res.regexp}},mockExtends));const _hoisted_1$7={key:0},_hoisted_2$5={key:1},_hoisted_3$5={key:2},_hoisted_4$3={key:3},_sfc_main$c=defineComponent({__name:"Index",props:{detail:null},setup(e){const t=e,{t:n}=useI18n(),a=reactive({headerCode:"",queryCode:"",paramCode:"",responsesCode:"",routeParamCode:""});return watchEffect((()=>{t.detail.param&&(a.paramCode=formatJsonCode(renderCodeJsonByParams(t.detail.param)),a.paramTipsParams=handleHoverTipsParams(t.detail.param)),t.detail.query&&(a.queryCode=formatJsonCode(renderCodeJsonByParams(t.detail.query)),a.queryTipsParams=handleHoverTipsParams(t.detail.query)),t.detail.responseSuccess&&(a.responsesCode=formatJsonCode(renderCodeJsonByParams(t.detail.responseSuccess)),a.responsesTipsParams=handleHoverTipsParams(t.detail.responseSuccess)),t.detail.header&&(a.headerCode=formatJsonCode(renderCodeJsonByParams(t.detail.header)),a.headerTipsParams=handleHoverTipsParams(t.detail.header)),t.detail.routeParam&&(a.routeParamCode=formatJsonCode(renderCodeJsonByParams(t.detail.routeParam)),a.routeTipsParams=handleHoverTipsParams(t.detail.routeParam))})),(e,t)=>{const r=__unplugin_components_0;return openBlock(),createElementBlock("div",null,[a.headerCode?(openBlock(),createElementBlock("div",_hoisted_1$7,[createVNode(r,{title:unref(n)("apiPage.header.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.header.title"),code:a.headerCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.headerCode),hoverTipsParams:a.headerTipsParams},null,8,["title","code","height","hoverTipsParams"])])):createCommentVNode("",!0),a.routeParamCode?(openBlock(),createElementBlock("div",_hoisted_2$5,[createVNode(r,{title:unref(n)("apiPage.routeParam.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.routeParam.title"),code:a.routeParamCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.routeParamCode),hoverTipsParams:a.routeTipsParams},null,8,["title","code","height","hoverTipsParams"])])):createCommentVNode("",!0),a.queryCode?(openBlock(),createElementBlock("div",_hoisted_3$5,[createVNode(r,{title:unref(n)("apiPage.query.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.query.title"),code:a.queryCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.queryCode),hoverTipsParams:a.queryTipsParams},null,8,["title","code","height","hoverTipsParams"])])):createCommentVNode("",!0),a.paramCode?(openBlock(),createElementBlock("div",_hoisted_4$3,[createVNode(r,{title:unref(n)("apiPage.body.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.body.title"),code:a.paramCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.paramCode),hoverTipsParams:a.paramTipsParams},null,8,["title","code","height","hoverTipsParams"])])):createCommentVNode("",!0),createVNode(r,{title:unref(n)("apiPage.responses")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.responses"),code:a.responsesCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.responsesCode),hoverTipsParams:a.responsesTipsParams},null,8,["title","code","height","hoverTipsParams"])])}}});function getPrefix(e,t=!1){return`${t?"export ":""}interface ${e}{\n`}function toCamelCase(e=""){return e.replace(/(-|_)([a-z])/g,(function(e){return e[1].toUpperCase()}))}function upperFirstLetter(e=""){return e.charAt(0).toUpperCase()+e.slice(1)}function pad(e=2){return" ".repeat(e)}const defaultConfig={outputIndent:2,rootName:"RootName",exportRoot:!0};function transformTsByParams(e,t){let n="",a="";const r=pad(t.outputIndent);return e&&e.length&&e.forEach((e=>{const o=e.require?"":"?";if(e.type&&"int"==e.type||e.type&&e.type.indexOf("int(")>-1)n+=`${r}${e.name}${o}: number;\n`;else if(e.type&&"boolean"==e.type)n+=`${r}${e.name}${o}: boolean;\n`;else if(e.type&&"array"==e.type||"array"==t.childrenType)if(e.children&&e.children.length){const l=`${upperFirstLetter(toCamelCase(e.name))}Item`;n+=`${r}${e.name}${o}: ${l}[];\n`;const i=transformTypeScript(e.children,__spreadProps(__spreadValues({},t),{rootName:l,childrenType:e.childrenType}));a+=`${i}`}else{let t="any";e.childrenType&&(t=e.childrenType),n+=`${r}${e.name}${o}: ${t}[];\n`}else if(e.type&&"object"==e.type)if(e.children&&e.children.length){const l=`${upperFirstLetter(toCamelCase(e.name))}`;n+=`${r}${e.name}${o}: ${l};\n`;const i=transformTypeScript(e.children,__spreadProps(__spreadValues({},t),{rootName:l,childrenType:e.childrenType}));a+=`${i}`}else n+=`${r}${e.name}${o}: any;\n`;else n+=`${r}${e.name}${o}: string;\n`})),[n,a]}function transformTypeScript(e,t={}){const n=__spreadValues(__spreadValues({},defaultConfig),t),a=getPrefix(n.rootName,n.exportRoot),[r,o]=transformTsByParams(e,n);return`${o}${a}${r}}\n\n`}const _hoisted_1$6={key:0},_hoisted_2$4={key:1},_hoisted_3$4={key:2},_sfc_main$b=defineComponent({__name:"Index",props:{detail:null},setup(e){const t=e,{t:n}=useI18n(),a=reactive({headerCode:"",queryCode:"",paramCode:"",responsesCode:"",routeParamCode:""});return watchEffect((()=>{t.detail.param&&(a.paramCode=transformTypeScript(t.detail.param)),t.detail.query&&(a.queryCode=transformTypeScript(t.detail.query)),t.detail.responseSuccess&&(a.responsesCode=transformTypeScript(t.detail.responseSuccess)),t.detail.routeParam&&(a.routeParamCode=transformTypeScript(t.detail.routeParam))})),(e,t)=>{const r=__unplugin_components_0;return openBlock(),createElementBlock("div",null,[a.routeParamCode?(openBlock(),createElementBlock("div",_hoisted_1$6,[createVNode(r,{title:unref(n)("apiPage.routeParam.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.routeParam.title"),code:a.routeParamCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.routeParamCode),language:"typescript"},null,8,["title","code","height"])])):createCommentVNode("",!0),a.queryCode?(openBlock(),createElementBlock("div",_hoisted_2$4,[createVNode(r,{title:unref(n)("apiPage.query.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.query.title"),code:a.queryCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.queryCode),language:"typescript"},null,8,["title","code","height"])])):createCommentVNode("",!0),a.paramCode?(openBlock(),createElementBlock("div",_hoisted_3$4,[createVNode(r,{title:unref(n)("apiPage.body.title")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.body.title"),code:a.paramCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.paramCode),language:"typescript"},null,8,["title","code","height"])])):createCommentVNode("",!0),createVNode(r,{title:unref(n)("apiPage.responses")},null,8,["title"]),createVNode(unref(CodeEditor),{title:unref(n)("apiPage.responses"),code:a.responsesCode,readOnly:!0,height:unref(getCodeEditorHeight)(a.responsesCode),language:"typescript"},null,8,["title","code","height"])])}}}),_sfc_main$a=defineComponent({__name:"HeaderParams",props:{data:null},setup(e){const t=e,{t:n}=useI18n(),a=[{title:n("apiPage.common.field"),dataIndex:"name",width:200,align:"left"},{title:n("apiPage.common.value"),dataIndex:"default",itemRender:{name:"input"}},{title:n("apiPage.common.desc"),dataIndex:"desc",width:150,align:"left"}];return(e,n)=>(openBlock(),createBlock(unref(EditTable),{columns:a,data:t.data,"is-add":!1,scroll:{x:"500px",y:"180px"}},null,8,["data"]))}}),_sfc_main$9=defineComponent({__name:"RouteParams",props:{data:null},setup(e){const t=e,{t:n}=useI18n(),a=[{title:n("apiPage.common.field"),dataIndex:"name",width:200,align:"left"},{title:n("apiPage.common.value"),dataIndex:"default",itemRender:{name:"input"}},{title:n("apiPage.common.desc"),dataIndex:"desc",width:150,align:"left"}];return(e,n)=>(openBlock(),createBlock(unref(EditTable),{columns:a,data:t.data,"is-add":!1,scroll:{x:"500px",y:"180px"}},null,8,["data"]))}}),_sfc_main$8=defineComponent({__name:"QueryParams",props:{data:null},emits:["addRow","deleteRow"],setup(e,{emit:t}){const n=e,{t:a}=useI18n(),r=[{title:a("apiPage.common.field"),dataIndex:"name",width:200,align:"left",itemRender:{name:"input"}},{title:a("apiPage.common.value"),dataIndex:"default",itemRender:{name:"input"}},{title:a("apiPage.common.require"),dataIndex:"require",width:100,align:"center",itemRender:{name:"check-status"}},{title:a("apiPage.common.desc"),dataIndex:"desc",width:150,align:"left"},{title:a("apiPage.common.action"),dataIndex:"id",width:70,align:"center",itemRender:{name:"delete-button"}}],o=()=>{t("addRow")},l=e=>{t("deleteRow",e)};return(e,t)=>(openBlock(),createBlock(unref(EditTable),{columns:r,data:n.data,"is-add":!0,onAddRow:o,onDeleteRow:l,scroll:{x:"700px",y:"170px"}},null,8,["data"]))}}),_sfc_main$7=defineComponent({__name:"BodyParams",props:{data:null,detailParams:null},emits:["change","addRow","deleteRow"],setup(e,{emit:t}){const n=e,{t:a}=useI18n(),r=ref({}),o=()=>{n.detailParams&&"string"==typeof n.data&&(r.value=handleHoverTipsParams(n.detailParams))};o(),watch((()=>n.detailParams),o);const l=[{title:a("apiPage.common.field"),dataIndex:"name",width:200,align:"left",itemRender:{name:"input"}},{title:a("apiPage.common.value"),dataIndex:"default",itemRender:{name:"auto"}},{title:a("apiPage.common.require"),dataIndex:"require",width:100,align:"center",itemRender:{name:"check-status"}},{title:a("apiPage.common.desc"),dataIndex:"desc",width:150,align:"left"},{title:a("apiPage.common.action"),dataIndex:"id",width:70,align:"center",itemRender:{name:"delete-button"}}],i=()=>{t("addRow")},s=e=>{t("deleteRow",e)},u=e=>{t("change",e)};return(e,t)=>(openBlock(),createElementBlock("div",null,["object"==typeof n.data?(openBlock(),createBlock(unref(EditTable),{key:0,columns:l,data:n.data,"is-add":!0,onAddRow:i,onDeleteRow:s,scroll:{x:"700px",y:"170px"}},null,8,["data"])):(openBlock(),createBlock(unref(CodeEditor),{key:1,title:unref(a)("apiPage.body.title"),code:n.data,readOnly:!1,hoverTipsParams:r.value,height:"247px",language:"json",onChange:u},null,8,["title","code","hoverTipsParams"]))]))}}),renderData=(e,t,n,a)=>{var r;const o=cloneDeep(e);return o&&o.length?n&&n[t]&&(null==(r=n[t])?void 0:r.length)?o.map((e=>{var r,o;const l=null==(r=n[t])?void 0:r.find((t=>t.name===e.name&&"all"==t.appKey)),i=null==(o=n[t])?void 0:o.find((t=>t.name===e.name&&t.appKey==a)),s=i||l;return s&&s.value?e.default=s.value:e.mock&&(e.default=Mock.mock(e.mock)),e.id=createRandKey(),e})):o.map((e=>(e.mock&&(e.default=Mock.mock(e.mock)),e.id=createRandKey(),e))):[]},handleQueryParams=(e,t="")=>{if(!e||!e.length)return e;let n=[];return e.forEach((e=>{if("object"==e.type&&e.children&&e.children.length){let a=e.name;t&&(a=`${t}[${e.name}]`);const r=handleQueryParams(e.children,a);n=n.concat(r)}else if("array"==e.type&&e.children&&e.children.length){const t=`${e.name}[0]`,a=handleQueryParams(e.children,t);n=n.concat(a)}else t&&(e.name=`${t}[${e.name}]`),e.mock&&(e.default=Mock.mock(e.mock)),n.push(e)})),n};var ParamTypeEnum=(e=>(e.HEADER="header",e.QUERY="query",e.BODY="body",e.ROUTEPARAM="routeParam",e))(ParamTypeEnum||{});const paramTypeKeys=["header","query","body"];(()=>{const e=new Map;e.set("header","headers"),e.set("query","params"),e.set("body","data")})();const mergeGlobalParams=(e,t,n,a)=>(paramTypeKeys.forEach((r=>{var o,l;t&&t[r]&&(null==(o=t[r])?void 0:o.length)&&(null==(l=t[r])||l.forEach((t=>{!t.name||!t.value||t.appKey&&"all"!==t.appKey&&t.appKey!==a||e[r][t.name]||0==e[r][t.name]||"boolean"==typeof e[r][t.name]||(e[r][t.name]=!1!==n.HTTP.ENCODEURICOMPONENT&&r!=ParamTypeEnum.BODY?encodeURIComponent(t.value):t.value)})))})),e),formatTime=(e=new Date,t="hh:mm:ss")=>{const n={y:e.getFullYear(),M:e.getMonth()+1,d:e.getDate(),h:e.getHours(),m:e.getMinutes(),s:e.getSeconds()};return t.replace(/(y+|M+|d+|h+|m+|s+)/g,(function(e){return((e.length>1?"0":"")+n[e.slice(-1)]).slice(-(e.length>2?e.length:2))}))},setGolbalParam=(e,t,n,a,r)=>{var o,l,i;r||(r="all");const s=useApidocOutsideStore(),u=useAppOutsideStore(),c=s.globalParams;if(c&&c[e]&&(null==(o=c[e])?void 0:o.length)){const o=null==(l=c[e])?void 0:l.findIndex((e=>e.name===t&&(e.appKey==r||"all"==e.appKey||e.appKey==u.appKey))),s=c[e];"number"==typeof o&&o>-1&&s?s[o].value=n:null==(i=c[e])||i.push({id:createRandKey(),name:t,value:n,desc:a,appKey:r})}else c[e]=[{id:createRandKey(),name:t,value:n,desc:a,appKey:r}];s.setGlobalParams(__spreadValues({},c))},removeGolbalParam=(e,t)=>{var n,a;const r=useApidocOutsideStore(),o=r.globalParams;o&&o[e]&&(null==(n=o[e])?void 0:n.length)&&(o[e]=null==(a=o[e])?void 0:a.filter((e=>e.name!==t))),r.setGlobalParams(o)},feConfig=apidocFeConfig,customEvents=feConfig.DEBUG_EVENTS?feConfig.DEBUG_EVENTS:{},events=__spreadValues({setHeader:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key&&void 0!==n){t.headers[e.key]=!1!==(null==feConfig?void 0:feConfig.HTTP.ENCODEURICOMPONENT)?encodeURIComponent(n):n;a({event:e,config:t,value:n})}else r()})),setQuery:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key&&void 0!==n){t.params[e.key]=n;a({event:e,config:t,value:n})}else r()})),setBody:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key&&void 0!==n){t.data[e.key]=n;a({event:e,config:t,value:n})}else r()})),clearHeader:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key){delete t.headers[e.key];a({event:e,config:t,value:n})}else r()})),clearQuery:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key){delete t.params[e.key];a({event:e,config:t,value:n})}else r()})),clearBody:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key){delete t.data[e.key];a({event:e,config:t,value:n})}else r()})),setGlobalHeader:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key&&void 0!==n){setGolbalParam("header",e.key,n,e.desc,e.appKey);a({event:e,config:t,value:n})}else r()})),setGlobalQuery:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key&&void 0!==n){setGolbalParam("query",e.key,n,e.desc,e.appKey);a({event:e,config:t,value:n})}else r()})),setGlobalBody:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key&&void 0!==n){setGolbalParam("body",e.key,n,e.desc,e.appKey);a({event:e,config:t,value:n})}else r()})),clearGlobalHeader:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key){removeGolbalParam("header",e.key);a({event:e,config:t,value:n})}else r()})),clearGlobalQuery:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key){removeGolbalParam("query",e.key);a({event:e,config:t,value:n})}else r()})),clearGlobalBody:({event:e,config:t,value:n})=>new Promise(((a,r)=>{if(e.key){removeGolbalParam("body",e.key);a({event:e,config:t,value:n})}else r()})),ajax({event:e,config:t}){const n=t.params,a=t.data;return new Promise(((r,o)=>{var l,i,s,u;if(e.url){const c={method:e.method?e.method.toLowerCase():t.method,headers:{},url:e.url};e.contentType&&(c.headers["content-type"]=e.contentType);let d={},p={};if(e.value){let t={},r={};(null==(l=e.value)?void 0:l.indexOf("query."))>-1?t=getObjectValueByKey(e.value,{query:n}):(null==(i=e.value)?void 0:i.indexOf("body."))>-1?r=getObjectValueByKey(e.value,{body:a}):"GET"==c.method?t=e.value:r=e.value,e.key?(t&&Object.keys(t).length&&(d[e.key]=t),r&&Object.keys(r).length&&(p[e.key]=r)):(d=t,p=r)}if(e.before&&isArray(e.before))for(let t=0;t<e.before.length;t++){const r=e.before[t];let o=r.value;r.value&&(null==(s=r.value)?void 0:s.indexOf("query."))>-1?o=getObjectValueByKey(r.value,{query:n}):r.value&&(null==(u=r.value)?void 0:u.indexOf("body."))>-1&&(o=getObjectValueByKey(r.value,{body:a})),"setQuery"===r.event?r.key?d[r.key]=o:d=o:"setBody"===r.event?r.key?p[r.key]=o:p=o:"setHeader"===r.event&&(r.key?c.headers[r.key]=encodeURIComponent(o):isObject(o)&&(c.headers=o))}c.params=d,c.data=p,request(c).then((n=>{var a;if(e.after&&isArray(e.after))for(let r=0;r<e.after.length;r++){const o=e.after[r];let l=o.value;o.value&&(null==(a=o.value)?void 0:a.indexOf("res."))>-1&&(l=getObjectValueByKey(o.value,{res:n})),o.key&&"setQuery"===o.event?t.params[o.key]=l:o.key&&"setBody"===o.event?t.data[o.key]=l:o.key&&"setHeader"===o.event&&(t.headers[o.key]=!1!==feConfig.HTTP.ENCODEURICOMPONENT?encodeURIComponent(l):l)}r({event:e,result:n,config:t})})).catch((e=>{o(e)}))}}))}},customEvents);function handleRequestEvent(e,t,n){return new Promise((a=>{var r,o,l;const i={status:"default",list:[],config:{}};e&&e.length||a(i);const s=[];for(let u=0;u<e.length;u++){const a=e[u];let i=a.value;a.value&&(null==(r=a.value)?void 0:r.indexOf("params."))>-1?i=getObjectValueByKey(a.value,{params:cloneDeep(t.params)}):a.value&&(null==(o=a.value)?void 0:o.indexOf("body."))>-1?i=getObjectValueByKey(a.value,{body:cloneDeep(t.data)}):a.value&&(null==(l=a.value)?void 0:l.indexOf("res."))>-1&&(i=getObjectValueByKey(a.value,{res:n})),events[a.event]&&s.push(events[a.event]({event:a,config:t,value:i,result:n}))}Promise.allSettled(s).then((t=>{let n={};i.status="success";for(let a=0;a<t.length;a++){const r=t[a];if("fulfilled"===r.status){const e=r.value;n=__spreadValues(__spreadValues({},n),e.config),i.list.push(__spreadProps(__spreadValues({},e.event),{status:"success",realValue:e.value,message:e.message}))}else{i.status="error";const t=e[a];i.list.push({status:"error",event:t.event,message:r.reason})}}i.config=n,a(i)})).catch((()=>{a({status:"error",list:[],config:{}})}))}))}var useDebugApi=()=>{const apidocStore=useApidocStore(),appStore=useAppStore(),{t:t}=useI18n();function handleApiUrl(e,t){let n=e;if(t&&t.length)for(let a=0;a<t.length;a++){const e=t[a],r=[`/:${e.name}`,`/<${e.name}>`,`/<${e.name}?>`,`/[:${e.name}]`,`/{${e.name}}`,`/{${e.name}?}`];for(let t=0;t<r.length;t++){const a=r[t];if(n.indexOf(a)>-1){const t=e.default?`/${e.default}`:"";n=n.replace(a,t)}}}return n}function renderHeaderData(e){return e.header&&e.header.length?renderData(e.header,"header",apidocStore.globalParams,e.appKey):[]}function renderRouteData(e){return e.routeParam&&e.routeParam.length?renderData(e.routeParam,"routeParam",{},e.appKey):[]}function renderQueryData(e){if(e.query&&e.query.length){const t=renderData(e.query,"query",apidocStore.globalParams,e.appKey);return handleQueryParams(t)}return[]}function renderBodyData(e,t=""){const n=null==e?void 0:e.param;return"formdata"===(null==e?void 0:e.paramType)||"array"===t?renderData(n,"body",apidocStore.globalParams,e.appKey):formatJsonCode(renderCodeJsonByParams(n,!0,apidocStore.globalParams,e.appKey))}function renderApiParams(e,t={}){const n={};return t.header&&t.header.length?n.header=t.header:e.header&&e.header.length&&(n.header=renderHeaderData(e)),t.query&&t.query.length?n.query=t.query:e.query&&e.query.length&&(n.query=renderQueryData(e)),t.body&&t.body.length?n.body=t.body:e.param&&e.param.length&&(n.body=renderBodyData(e)),t.routeParam&&t.routeParam.length?n.routeParam=t.routeParam:e.routeParam&&e.routeParam.length&&(n.routeParam=renderRouteData(e)),n}function handleRequestParams(e,t){return __async(this,arguments,(function*(e,t,n={}){return new Promise(((a,r)=>{const o=mergeGlobalParams(t,apidocStore.globalParams,appStore.feConfig,e.appKey);e.contentType?o.header["content-type"]=e.contentType:"formdata"===e.paramType&&(o.header["content-type"]="application/x-www-form-urlencoded");const l=appStore.appObject[e.appKey],i=handleApiUrl(e.url,t.routeParam);let s="GET";e.method&&"string"==typeof e.method?s=e.method.toLowerCase():e.method&&"object"==typeof e.method&&e.method.length&&(s=e.method[0]);const u=__spreadValues({url:i,method:s,headers:o.header,params:o.query,data:o.body,baseURL:l&&l.host?l.host:void 0},n);e.before&&e.before.length?handleRequestEvent(e.before,u).then((t=>{"error"!=t.status?sendRequest(e,t.config).then((e=>{e.beforeEvents=t,a(e)})).catch((e=>{e.beforeEvents=t,r(e)})):r({beforeEvents:t})})).catch((e=>{r(e)})):sendRequest(e,u).then((e=>{a(e)})).catch((e=>{r(e)}))}))}))}function sendRequest(e,t){return new Promise(((n,a)=>{const r=Date.now();request(t).then((a=>{a.responseItme=formatTime(),a.timer=Date.now()-r,e.after&&e.after.length?handleRequestEvent(e.after,t,a).then((e=>{a.afterEvents=e,n(a)})):n(a)})).catch((e=>{e.responseItme=formatTime(),e.timer=Date.now()-r,a(e)}))}))}function excuteDebug(apiDetail,paramsData,options={}){return new Promise(((resolve,reject)=>{const json=renderApiParams(apiDetail,paramsData);if(json.header=renderCodeJsonByParams(paramsData.header),json.query=renderCodeJsonByParams(paramsData.query),"formdata"==apiDetail.paramType){const e=new FormData;json.body.forEach((t=>{if("file"===t.type){const n=t.default;n&&n.length&&e.append(t.name,n[0])}else if("files"===t.type){const n=t.default;if(n&&n.length)for(let a=0;a<n.length;a++){const r=n[a];e.append(`${t.name}[]`,r)}}else if(t.default||0==t.default||"boolean"==typeof t.default){const n=t.default;e.append(t.name,n)}})),handleRequestParams(apiDetail,__spreadProps(__spreadValues({},json),{body:e}),options).then((e=>{resolve(e)})).catch((e=>{reject(e)}))}else if(paramsData.body)try{const paramJson=eval("("+paramsData.body+")");handleRequestParams(apiDetail,__spreadProps(__spreadValues({},json),{body:paramJson}),options).then((e=>{resolve(e)})).catch((e=>{reject(e)}))}catch(error){reject(t("apiPage.json.formatError"))}}))}return{renderApiParams:renderApiParams,excuteDebug:excuteDebug,renderHeaderData:renderHeaderData,renderQueryData:renderQueryData,renderBodyData:renderBodyData,renderRouteData:renderRouteData}};const _hoisted_1$5={style:{height:"290px"}},_sfc_main$6=defineComponent({__name:"Index",props:{detail:null},setup(e,{expose:t}){const n=e,a=useApidocStore(),{t:r}=useI18n(),{renderHeaderData:o,renderQueryData:l,renderBodyData:i,renderRouteData:s}=useDebugApi(),u=ref(),c=reactive({activeTab:ParamTypeEnum.BODY,headerData:[],queryData:[],bodyData:"",disabledTabs:[],routeData:[]}),d=()=>{c.queryData.push({id:createRandKey(),name:"",value:"",desc:"",require:!1,type:"string"})},p=e=>{c.queryData=c.queryData.filter((t=>t.id!=e.id))},m=()=>{"string"!=typeof c.bodyData&&c.bodyData.push({id:createRandKey(),name:"",value:"",desc:"",require:!1,type:"string"})},f=e=>{"string"!=typeof c.bodyData&&(c.bodyData=c.bodyData.filter((t=>t.id!=e.id)))},h=()=>{c.headerData=o(n.detail),c.queryData=l(n.detail),c.bodyData=i(n.detail),c.routeData=s(n.detail)};h(),n.detail.routeParam&&n.detail.routeParam.length?c.activeTab=ParamTypeEnum.ROUTEPARAM:"GET"===n.detail.method&&"string"==typeof c.bodyData&&(c.activeTab=ParamTypeEnum.QUERY);const g=e=>{c.bodyData=e},v=()=>({header:c.headerData,query:c.queryData,body:c.bodyData,routeParam:c.routeData});return watch((()=>a.globalParams),(()=>{(e=>{const t=v();Object.keys(t).forEach((a=>{const r=t[a],o=e[a];if(r&&o&&o.length){const e={};for(let t=0;t<o.length;t++){const n=o[t];e[`${n.appKey}_${n.name}`]=n}if(isArray(r)&&r.length){const t=r.map((t=>{const a=`${n.detail.appKey}_${t.name}`,r=`all_${t.name}`;return e[a]?t.default=e[a].value:e[r]&&(t.default=e[r].value),t}));c[`${"routeParam"==a?"route":a}Data`]=t}else if("string"==typeof r){let t=JSON.parse(r);for(const a in t){const r=`${n.detail.appKey}_${a}`,o=`all_${a}`;e[r]?t[a]=e[r].value:e[o]&&(t[a]=e[o].value)}c.bodyData=formatJsonCode(t)}}}))})(a.globalParams)})),t({getData:v,onReloadAllParams:h}),(e,t)=>{const a=__unplugin_components_4,o=Button,l=__unplugin_components_2$2,i=Tabs;return openBlock(),createElementBlock("div",_hoisted_1$5,[createVNode(i,{activeKey:c.activeTab,"onUpdate:activeKey":t[0]||(t[0]=e=>c.activeTab=e),type:"card",size:"small"},{rightExtra:withCtx((()=>[createVNode(l,null,{default:withCtx((()=>[createVNode(o,{onClick:h},{default:withCtx((()=>[createVNode(unref(ReloadOutlined$1)),createTextVNode(toDisplayString(unref(r)("apiPage.debug.param.reload")),1)])),_:1})])),_:1})])),default:withCtx((()=>[(openBlock(),createBlock(a,{key:unref(ParamTypeEnum).HEADER,tab:unref(r)("apiPage.debug.header")},{default:withCtx((()=>[createVNode(_sfc_main$a,{ref:"headerRef",data:c.headerData},null,8,["data"])])),_:1},8,["tab"])),c.routeData&&c.routeData.length?(openBlock(),createBlock(a,{key:unref(ParamTypeEnum).ROUTEPARAM,tab:unref(r)("apiPage.debug.routeParam")},{default:withCtx((()=>[createVNode(_sfc_main$9,{ref:"routeRef",data:c.routeData},null,8,["data"])])),_:1},8,["tab"])):createCommentVNode("",!0),(openBlock(),createBlock(a,{key:unref(ParamTypeEnum).QUERY,tab:unref(r)("apiPage.debug.query")},{default:withCtx((()=>[createVNode(_sfc_main$8,{ref:"queryRef",data:c.queryData,onAddRow:d,onDeleteRow:p},null,8,["data"])])),_:1},8,["tab"])),(openBlock(),createBlock(a,{key:unref(ParamTypeEnum).BODY,disabled:c.disabledTabs.includes(unref(ParamTypeEnum).BODY),tab:unref(r)("apiPage.debug.body")},{default:withCtx((()=>[createVNode(_sfc_main$7,{ref_key:"bodyRef",ref:u,data:c.bodyData,detailParams:n.detail.param,onChange:g,onAddRow:m,onDeleteRow:f},null,8,["data","detailParams"])])),_:1},8,["disabled","tab"]))])),_:1},8,["activeKey"])])}}});var index$1="";const _hoisted_1$4={class:"event-list"},_hoisted_2$3={key:0},_hoisted_3$3={key:1},_hoisted_4$2={key:2},_hoisted_5$2={key:3},_hoisted_6$2={key:0,style:{"padding-left":"84px"}},_hoisted_7$1=createTextVNode("Before"),_hoisted_8$1={key:0},_hoisted_9={key:1},_hoisted_10={key:1,style:{"padding-left":"84px"}},_hoisted_11=createTextVNode("After"),_hoisted_12={key:0},_hoisted_13={key:1},_hoisted_14={key:4},_hoisted_15=["innerHTML"],_sfc_main$5=defineComponent({__name:"EventTab",props:{eventList:null},setup(e){const t=e,{t:n}=useI18n();return(e,a)=>{const r=__unplugin_components_1$1,o=Badge,l=__unplugin_components_2,i=__unplugin_components_3;return openBlock(),createElementBlock("div",_hoisted_1$4,[createVNode(i,{size:"small",bordered:"","data-source":t.eventList},{renderItem:withCtx((({item:e})=>[createVNode(l,null,{default:withCtx((()=>[createVNode(o,{status:e.status?e.status:"default"},{text:withCtx((()=>[e.name?(openBlock(),createElementBlock("span",_hoisted_2$3,toDisplayString(e.name)+"：",1)):(openBlock(),createElementBlock("span",_hoisted_3$3,toDisplayString(unref(n)(`debug.event.${e.event}`,unref(n)("debug.event.custom")+e.event))+"：",1)),e.handleValue?(openBlock(),createElementBlock("span",_hoisted_4$2,toDisplayString(e.key)+" = "+toDisplayString(e.handleValue)+"("+toDisplayString(e.value)+")",1)):"ajax"==e.event?(openBlock(),createElementBlock("span",_hoisted_5$2,[createBaseVNode("span",null,'ajax("'+toDisplayString(e.url)+'")',1),e.before&&"object"==typeof e.before&&e.before.length?(openBlock(),createElementBlock("div",_hoisted_6$2,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.before,((t,a)=>(openBlock(),createElementBlock("div",{key:a,class:"mb-xs"},[createBaseVNode("span",null,[createVNode(r,{style:{width:"50px"}},{default:withCtx((()=>[_hoisted_7$1])),_:1}),createTextVNode(toDisplayString(unref(n)(`debug.event.${t.event}`))+"：",1)]),e.handleValue?(openBlock(),createElementBlock("span",_hoisted_8$1,toDisplayString(t.key)+" = "+toDisplayString(t.handleValue)+"("+toDisplayString(t.value)+")",1)):(openBlock(),createElementBlock("span",_hoisted_9,toDisplayString(t.key)+" "+toDisplayString(t.value?"=":"")+" "+toDisplayString(t.value),1))])))),128))])):createCommentVNode("",!0),e.after&&"object"==typeof e.after&&e.after.length?(openBlock(),createElementBlock("div",_hoisted_10,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.after,((t,a)=>(openBlock(),createElementBlock("div",{key:a,class:"mb-xs"},[createBaseVNode("span",null,[createVNode(r,{style:{width:"50px"}},{default:withCtx((()=>[_hoisted_11])),_:1}),createTextVNode(toDisplayString(unref(n)(`debug.event.${t.event}`))+"：",1)]),e.handleValue?(openBlock(),createElementBlock("span",_hoisted_12,toDisplayString(t.key)+" = "+toDisplayString(t.handleValue)+"("+toDisplayString(t.value)+")",1)):(openBlock(),createElementBlock("span",_hoisted_13,toDisplayString(t.key)+" "+toDisplayString(t.value?"=":"")+" "+toDisplayString(t.value),1))])))),128))])):createCommentVNode("",!0)])):(openBlock(),createElementBlock("span",_hoisted_14,toDisplayString(e.key)+" "+toDisplayString(e.value||e.realValue?"=":"")+" "+toDisplayString(e.realValue?e.realValue:e.value),1)),e.message?(openBlock(),createElementBlock("span",{key:5,innerHTML:e.message},null,8,_hoisted_15)):createCommentVNode("",!0)])),_:2},1032,["status"])])),_:2},1024)])),_:1},8,["data-source"])])}}});var NumberBadge_vue_vue_type_style_index_0_scoped_true_lang="";const _sfc_main$4=defineComponent({__name:"NumberBadge",props:{count:{default:0},type:{default:"default"}},setup(e){const t=e;return(n,a)=>(openBlock(),createElementBlock("div",{class:normalizeClass(["number-badge",e.type])},toDisplayString(t.count),3))}});var NumberBadge=_export_sfc(_sfc_main$4,[["__scopeId","data-v-2227d6b0"]]);const _hoisted_1$3={key:0,class:"mr-xs"},_hoisted_2$2={key:1,class:"mr-xs"},_hoisted_3$2={key:2,class:"mr"},_hoisted_4$1={class:"mr"},_hoisted_5$1={key:3,class:"mr"},_hoisted_6$1={key:4},_sfc_main$3=defineComponent({__name:"ResultStatus",props:{result:null,resultStatus:null},setup(e){const t=e,{t:n}=useI18n();return(e,a)=>{const r=__unplugin_components_0$1;return t.result?(openBlock(),createBlock(r,{key:0,type:t.resultStatus,"show-icon":"",banner:""},{message:withCtx((()=>[createBaseVNode("div",null,[t.result.status&&t.result.status?(openBlock(),createElementBlock("b",_hoisted_1$3,toDisplayString(t.result.status),1)):t.result.response&&t.result.response.status?(openBlock(),createElementBlock("b",_hoisted_2$2,toDisplayString(t.result.response.status),1)):createCommentVNode("",!0),"info"==t.resultStatus?(openBlock(),createElementBlock("span",_hoisted_3$2,toDisplayString(unref(n)("apiPage.debug.notExcute")),1)):createCommentVNode("",!0),createBaseVNode("span",_hoisted_4$1,toDisplayString(t.result.message?t.result.message:t.result.statusText),1),t.result.responseItme?(openBlock(),createElementBlock("span",_hoisted_5$1,[createVNode(unref(ClockCircleOutlined$1),{class:"mr-xs"}),createBaseVNode("span",null,toDisplayString(t.result.responseItme),1)])):createCommentVNode("",!0),t.result.timer?(openBlock(),createElementBlock("span",_hoisted_6$1,[createVNode(unref(HourglassOutlined$1),{class:"mr-xs"}),createBaseVNode("span",null,toDisplayString(t.result.timer)+toDisplayString(unref(n)("common.ms")),1)])):createCommentVNode("",!0)])])),_:1},8,["type"])):createCommentVNode("",!0)}}});var Index_vue_vue_type_style_index_0_scoped_true_lang$1="";const _hoisted_1$2={key:0,class:"api-param-code"},_hoisted_2$1=["innerHTML"],_hoisted_3$1={key:1,class:"api-param-empty"},_sfc_main$2=defineComponent({__name:"Index",props:{detail:null,result:null},setup(e){const t=e,n=useAppStore(),{t:a}=useI18n(),r=reactive({events:{before:[],after:[]},activeTab:"responses",responsesCode:"",responsesHtml:"",showCodeEditor:!0,resultStatus:"info",responsesTipsParams:{}});t.detail.before&&(r.events.before=t.detail.before),t.detail.after&&(r.events.after=t.detail.after),watchEffect((()=>{t.detail.responseSuccess&&(r.responsesTipsParams=handleHoverTipsParams(t.detail.responseSuccess))}));const o=e=>{"responses"===e&&(r.showCodeEditor=!1,setTimeout((()=>{r.showCodeEditor=!0}),10))};return watchEffect((()=>{var e;const a=t.result;if(a.beforeEvents&&a.beforeEvents.list&&a.beforeEvents.list.length&&(r.events.before=a.beforeEvents.list),a.afterEvents&&a.afterEvents.list&&a.afterEvents.list.length&&(r.events.after=a.afterEvents.list),a.data)if(null==(e=n.feConfig.CUSTOM_METHODS)?void 0:e.RESPONSES_VIEW){const e=n.feConfig.CUSTOM_METHODS.RESPONSES_VIEW({detail:t.detail,result:t.result});"string"==typeof e?(r.responsesHtml=e,r.responsesCode=""):(e.html&&(r.responsesHtml=e.html),e.code&&("string"==typeof e.code?r.responsesHtml+=e.code:r.responsesCode=formatJsonCode(e.code)))}else"string"==typeof a.data?(r.responsesHtml=a.data,r.responsesCode=""):(r.responsesHtml="",r.responsesCode=formatJsonCode(a.data));a.status||a.response?a.status>=200&&a.status<300?r.resultStatus="success":(r.resultStatus="error",a.response&&a.response.data?r.responsesCode=formatJsonCode(a.response.data):r.responsesCode=formatJsonCode(a)):r.resultStatus="info"})),(l,i)=>{const s=__unplugin_components_0$2,u=__unplugin_components_4,c=Tabs;return openBlock(),createElementBlock("div",null,[createVNode(c,{visible:r.activeTab,"onUpdate:visible":i[0]||(i[0]=e=>r.activeTab=e),type:"card",size:"small",class:normalizeClass([unref(n).device]),onChange:o},{rightExtra:withCtx((()=>[createVNode(_sfc_main$3,{result:t.result,resultStatus:r.resultStatus},null,8,["result","resultStatus"])])),default:withCtx((()=>[createVNode(u,{key:"responses",tab:unref(a)("apiPage.responses")},{default:withCtx((()=>[r.responsesHtml||r.responsesCode?(openBlock(),createElementBlock("div",_hoisted_1$2,[r.responsesHtml?(openBlock(),createElementBlock("div",{key:0,class:"string-code",innerHTML:r.responsesHtml},null,8,_hoisted_2$1)):createCommentVNode("",!0),r.showCodeEditor&&r.responsesCode?(openBlock(),createBlock(unref(CodeEditor),{key:1,code:r.responsesCode,readOnly:!0,title:unref(a)("apiPage.responses"),height:"300px",hoverTipsParams:r.responsesTipsParams},null,8,["code","title","hoverTipsParams"])):createCommentVNode("",!0)])):(openBlock(),createElementBlock("div",_hoisted_3$1,[createVNode(s,{description:!1})]))])),_:1},8,["tab"]),t.detail.before?(openBlock(),createBlock(u,{key:"eventBefore"},{tab:withCtx((()=>[createTextVNode(toDisplayString(unref(a)("debug.event.before"))+" ",1),createVNode(unref(NumberBadge),{count:t.detail.before.length,type:e.result.beforeEvents&&e.result.beforeEvents.status?e.result.beforeEvents.status:"default"},null,8,["count","type"])])),default:withCtx((()=>[createVNode(_sfc_main$5,{eventList:r.events.before},null,8,["eventList"])])),_:1})):createCommentVNode("",!0),createVNode(u,{key:"eventAfter"},{tab:withCtx((()=>{var n;return[createTextVNode(toDisplayString(unref(a)("debug.event.after"))+" ",1),createVNode(unref(NumberBadge),{count:null==(n=t.detail.after)?void 0:n.length,type:e.result.afterEvents&&e.result.afterEvents.status?e.result.afterEvents.status:"default"},null,8,["count","type"])]})),default:withCtx((()=>[createVNode(_sfc_main$5,{eventList:r.events.after},null,8,["eventList"])])),_:1})])),_:1},8,["visible","class"])])}}});var Responses=_export_sfc(_sfc_main$2,[["__scopeId","data-v-25564980"]]),Index_vue_vue_type_style_index_0_scoped_true_lang="";const _hoisted_1$1={class:"excute-buttons"},_sfc_main$1=defineComponent({__name:"Index",props:{detail:null,currentMethod:null},setup(e){const t=e,{t:n}=useI18n(),{excuteDebug:a}=useDebugApi(),r=ref(),o=reactive({loading:!1,resultData:{}}),l=()=>{const e=r.value.getData();a(t.detail,e,{method:t.currentMethod}).then((e=>{o.resultData=e})).catch((e=>{o.resultData=e}))},i=()=>{r.value.onReloadAllParams(),l()};return(e,a)=>{const s=Button;return openBlock(),createElementBlock("div",null,[createVNode(_sfc_main$6,{ref_key:"paramsRef",ref:r,detail:t.detail},null,8,["detail"]),createBaseVNode("div",_hoisted_1$1,[createVNode(s,{type:"primary",loading:o.loading,block:"",onClick:l},{default:withCtx((()=>[createTextVNode(toDisplayString(unref(n)("apiPage.debug.excute")),1)])),_:1},8,["loading"]),createVNode(s,{type:"primary",loading:o.loading,block:"",onClick:i},{default:withCtx((()=>[createTextVNode(toDisplayString(unref(n)("apiPage.debug.reloadParamsAndExcute")),1)])),_:1},8,["loading"])]),createVNode(Responses,{detail:t.detail,result:o.resultData},null,8,["detail","result"])])}}});var DebugTab=_export_sfc(_sfc_main$1,[["__scopeId","data-v-1c30a695"]]),index_vue_vue_type_style_index_0_scoped_true_lang="";const _hoisted_1={key:2},_hoisted_2={key:0},_hoisted_3={class:"api-url"},_hoisted_4=["title"],_hoisted_5={class:normalizeClass(["api-url-input"])},_hoisted_6={class:"api-tabs"},_hoisted_7={key:1,class:"iconfont icon-json"},_hoisted_8={key:2,class:"iconfont icon-typescript-def"},__default__={name:"ApiDetail"},_sfc_main=defineComponent(__spreadProps(__spreadValues({},__default__),{setup(e){const t=useRoute(),n=useAppStore(),{t:a}=useI18n(),r=n.feConfig.API_DETAIL_TABS?n.feConfig.API_DETAIL_TABS:["table","json","ts","debug"],o=reactive({detail:{title:"",name:"",menuKey:"",header:[]},loading:!1,currentMethod:"",activeTab:r[0],error:{config:{},isAxiosError:!1,toJSON:()=>({}),name:"",message:""},tabs:[],spinning:!1}),l=computed((()=>{let e="";return o.detail.method&&"string"==typeof o.detail.method&&n.feConfig.METHOD_COLOR&&n.feConfig.METHOD_COLOR[o.detail.method]&&(e=n.feConfig.METHOD_COLOR[o.detail.method]),e})),i=()=>{copyTextToClipboard(o.detail.url),message.success(a("common.copySuccess"))},s=e=>{o.currentMethod=e},u=(e="loading")=>{const a=t.query;o[e]=!0,globalApi.getApiDetail({appKey:a.appKey?a.appKey:n.appKey,path:a.key,lang:a.lang?a.lang:n.lang,shareKey:a.shareKey?a.shareKey:n.shareKey}).then((t=>{let n=t.data;n.query&&(n.query=handleTableDataRowKey(n.query)),n.param&&(n.param=handleTableDataRowKey(n.param)),n.responseSuccess&&(n.responseSuccess=handleTableDataRowKey(n.responseSuccess)),o.detail=n,o.detail.notDebug?o.tabs=r.filter((e=>"debug"!=e)):o.tabs=r,o[e]=!1;const a=o.detail.method;a&&"string"==typeof a?o.currentMethod=a:a&&"object"==typeof a&&a.length&&(o.currentMethod=a[0])})).catch((t=>{if(o[e]=!1,200==t.status&&t.response&&t.response.data&&0!=t.response.data.code)return o.error=t,void(o[e]=!1);handleApidocHttpError(t).then((e=>{!1===e&&(o.error=t)}))}))};return u(),(e,t)=>{const r=Button,c=__unplugin_components_1$1,d=SelectOption,p=__unplugin_components_3$1,m=__unplugin_components_4,f=Tabs,h=Spin;return openBlock(),createElementBlock("div",{class:normalizeClass(["api-detail",unref(n).device])},[o.loading?(openBlock(),createBlock(Skeleton,{key:0})):!o.loading&&(o.error.response&&200!=o.error.response.status||!o.error.response&&o.error.message)?(openBlock(),createBlock(unref(Error$1),{key:1,error:o.error},null,8,["error"])):(openBlock(),createElementBlock("div",_hoisted_1,[createVNode(h,{spinning:o.spinning},{default:withCtx((()=>[createVNode(r,{class:"button-reload",type:"primary",ghost:"",onClick:t[0]||(t[0]=e=>u("spinning"))},{icon:withCtx((()=>[createVNode(unref(RedoOutlined))])),default:withCtx((()=>[unref(n).device!=unref(DeviceEnum).MOBILE?(openBlock(),createElementBlock("span",_hoisted_2,toDisplayString(unref(a)("apiPage.reload.button")),1)):createCommentVNode("",!0)])),_:1}),createBaseVNode("h1",null,toDisplayString(o.detail.title),1),createVNode(unref(TextGrid),{labelWidth:"auto",class:"mb-sm"},{default:withCtx((()=>[o.detail.author?(openBlock(),createBlock(unref(TextItem),{key:0,title:unref(a)("common.author"),value:o.detail.author},null,8,["title","value"])):createCommentVNode("",!0),o.detail.tag?(openBlock(),createBlock(unref(TextItem),{key:1,title:unref(a)("common.tag")},{default:withCtx((()=>[o.detail.tag&&"string"==typeof o.detail.tag?(openBlock(),createBlock(c,{key:0},{default:withCtx((()=>[createTextVNode(toDisplayString(o.detail.tag),1)])),_:1})):(openBlock(!0),createElementBlock(Fragment,{key:1},renderList(o.detail.tag,((e,t)=>(openBlock(),createBlock(c,{key:t},{default:withCtx((()=>[createTextVNode(toDisplayString(e),1)])),_:2},1024)))),128))])),_:1},8,["title"])):createCommentVNode("",!0)])),_:1}),createBaseVNode("div",_hoisted_3,[o.detail.method&&"string"==typeof o.detail.method?(openBlock(),createElementBlock("div",{key:0,class:normalizeClass(["api-url-method",o.currentMethod]),style:normalizeStyle({backgroundColor:unref(l)})},toDisplayString(o.detail.method),7)):o.detail.method&&o.detail.method.length>0?(openBlock(),createElementBlock("div",{key:1,class:normalizeClass(["api-method-select"]),title:o.currentMethod},[createVNode(p,{value:o.currentMethod,style:{width:"100%"},onChange:s},{default:withCtx((()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(o.detail.method,(e=>(openBlock(),createBlock(d,{key:e,value:e},{default:withCtx((()=>[createTextVNode(toDisplayString(e),1)])),_:2},1032,["value"])))),128))])),_:1},8,["value"])],8,_hoisted_4)):createCommentVNode("",!0),createBaseVNode("div",_hoisted_5,[withDirectives(createBaseVNode("input",{"onUpdate:modelValue":t[1]||(t[1]=e=>o.detail.url=e),readonly:""},null,512),[[vModelText,o.detail.url]])]),createBaseVNode("div",{class:"api-url-copy",onClick:i},[createVNode(unref(CopyOutlined))])]),createBaseVNode("div",_hoisted_6,[createVNode(f,{activeKey:o.activeTab,"onUpdate:activeKey":t[2]||(t[2]=e=>o.activeTab=e)},{default:withCtx((()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(o.tabs,(e=>(openBlock(),createBlock(m,{key:e},{tab:withCtx((()=>[createBaseVNode("span",null,["table"==e?(openBlock(),createBlock(unref(BookOutlined$1),{key:0})):createCommentVNode("",!0),"json"==e?(openBlock(),createElementBlock("i",_hoisted_7)):createCommentVNode("",!0),"ts"==e?(openBlock(),createElementBlock("i",_hoisted_8)):createCommentVNode("",!0),"debug"==e?(openBlock(),createBlock(unref(BugOutlined$1),{key:3})):createCommentVNode("",!0),createTextVNode(" "+toDisplayString(unref(a)(`apiPage.tabs.${e}`)),1)])])),default:withCtx((()=>["table"==e?(openBlock(),createBlock(_sfc_main$d,{key:0,detail:o.detail},null,8,["detail"])):"json"==e?(openBlock(),createBlock(_sfc_main$c,{key:1,detail:o.detail},null,8,["detail"])):"ts"==e?(openBlock(),createBlock(_sfc_main$b,{key:2,detail:o.detail},null,8,["detail"])):"debug"==e?(openBlock(),createBlock(DebugTab,{key:3,detail:o.detail,currentMethod:o.currentMethod},null,8,["detail","currentMethod"])):createCommentVNode("",!0)])),_:2},1024)))),128))])),_:1},8,["activeKey"])])])),_:1},8,["spinning"])]))],2)}}}));var index=_export_sfc(_sfc_main,[["__scopeId","data-v-f3ab641c"]]);export{index as default};
