var e=Object.defineProperty,t=Object.defineProperties,n=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,a=(t,n,i)=>n in t?e(t,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[n]=i;import{aY as o,a6 as l,aZ as c,a_ as u,w as h,d as g,K as d,n as p,j as f,S as b,V as m,a$ as x,b0 as k,u as w,a9 as _,_ as v,b1 as y,c as E,Y as S,X as A,W as O,b2 as I,ac as T,a8 as R,U as $,b3 as C,b4 as N,b5 as M,ab as z,h as L,P as B,p as D,k as j,q as P,s as U,aw as Z,H,z as F,D as K,F as q,y as G,l as Q,A as W,B as X,x as J,v as V,t as Y,aQ as ee,aF as te,b6 as ne,b7 as ie,b8 as re,b9 as se}from"./index.d0b2fdb6.js";function ae(e){var t,n=function(n){return function(){t=null,e.apply(void 0,l(n))}},i=function(){if(null==t){for(var e=arguments.length,i=new Array(e),r=0;r<e;r++)i[r]=arguments[r];t=o(n(i))}};return i.cancel=function(){return o.cancel(t)},i}function oe(e){return e!==window?e.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function le(e,t,n){if(void 0!==n&&t.top>e.top-n)return"".concat(n+t.top,"px")}function ce(e,t,n){if(void 0!==n&&t.bottom<e.bottom+n){var i=window.innerHeight-t.bottom;return"".concat(n+i,"px")}}var ue,he,ge=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"],de=[];function pe(e,t){if(e){var n=de.find((function(t){return t.target===e}));n?n.affixList.push(t):(n={target:e,affixList:[t],eventHandlers:{}},de.push(n),ge.forEach((function(t){n.eventHandlers[t]=c(e,t,(function(){n.affixList.forEach((function(e){(0,e.exposed.lazyUpdatePosition)()}),!("touchstart"!==t&&"touchmove"!==t||!u)&&{passive:!0})}))})))}}function fe(e){var t=de.find((function(t){var n=t.affixList.some((function(t){return t===e}));return n&&(t.affixList=t.affixList.filter((function(t){return t!==e}))),n}));t&&0===t.affixList.length&&(de=de.filter((function(e){return e!==t})),ge.forEach((function(e){var n=t.eventHandlers[e];n&&n.remove&&n.remove()})))}function be(){return"undefined"!=typeof window?window:null}(he=ue||(ue={}))[he.None=0]="None",he[he.Prepare=1]="Prepare";var me=h(g({name:"AAffix",props:{offsetTop:Number,offsetBottom:Number,target:{type:Function,default:be},prefixCls:String,onChange:Function,onTestUpdatePosition:Function},setup:function(e,t){var n=t.slots,i=t.emit,r=t.expose,s=d(),a=d(),o=p({affixStyle:void 0,placeholderStyle:void 0,status:ue.None,lastAffix:!1,prevTarget:null,timeout:null}),l=I(),c=f((function(){return void 0===e.offsetBottom&&void 0===e.offsetTop?0:e.offsetTop})),u=f((function(){return e.offsetBottom})),h=function(){O(o,{status:ue.Prepare,affixStyle:void 0,placeholderStyle:void 0}),l.update()},g=ae((function(){h()})),T=ae((function(){var t=e.target,n=o.affixStyle;if(t&&n){var i=t();if(i&&s.value){var r=oe(i),a=oe(s.value),l=le(a,r,c.value),g=ce(a,r,u.value);if(void 0!==l&&n.top===l||void 0!==g&&n.bottom===g)return}}h()}));r({updatePosition:g,lazyUpdatePosition:T}),b((function(){return e.target}),(function(e){var t=(null==e?void 0:e())||null;o.prevTarget!==t&&(fe(l),t&&(pe(t,l),g()),o.prevTarget=t)})),b((function(){return[e.offsetTop,e.offsetBottom]}),g),m((function(){var t=e.target;t&&(o.timeout=setTimeout((function(){pe(t(),l),g()})))})),x((function(){!function(){var t=o.status,n=o.lastAffix,r=e.target;if(t===ue.Prepare&&a.value&&s.value&&r){var l=r();if(l){var h={status:ue.None},g=oe(l),d=oe(s.value),p=le(d,g,c.value),f=ce(d,g,u.value);void 0!==p?(h.affixStyle={position:"fixed",top:p,width:d.width+"px",height:d.height+"px"},h.placeholderStyle={width:d.width+"px",height:d.height+"px"}):void 0!==f&&(h.affixStyle={position:"fixed",bottom:f,width:d.width+"px",height:d.height+"px"},h.placeholderStyle={width:d.width+"px",height:d.height+"px"}),h.lastAffix=!!h.affixStyle,n!==h.lastAffix&&i("change",h.lastAffix),O(o,h)}}}()})),k((function(){clearTimeout(o.timeout),fe(l),g.cancel(),T.cancel()}));var R=w("affix",e).prefixCls;return function(){var t,i=o.affixStyle,r=o.placeholderStyle,l=_(v({},R.value,i)),c=y(e,["prefixCls","offsetTop","offsetBottom","target","onChange","onTestUpdatePosition"]);return E(A,{onResize:g},{default:function(){return[E("div",S(S({},c),{},{style:r,ref:s}),[E("div",{class:l,ref:a,style:i},[null===(t=n.default)||void 0===t?void 0:t.call(n)])])]}})}}}));function xe(){}var ke=Symbol("anchorContextKey"),we=function(e){T(ke,e)};function _e(){return window}function ve(e,t){if(!e.getClientRects().length)return 0;var n=e.getBoundingClientRect();return n.width||n.height?t===window?(t=e.ownerDocument.documentElement,n.top-t.clientTop):n.top-t.getBoundingClientRect().top:n.top}var ye=/#([\S ]+)$/,Ee=g({name:"AAnchor",inheritAttrs:!1,props:{prefixCls:String,offsetTop:Number,bounds:Number,affix:{type:Boolean,default:!0},showInkInFixed:{type:Boolean,default:!1},getContainer:Function,wrapperClass:String,wrapperStyle:{type:Object,default:void 0},getCurrentAnchor:Function,targetOffset:Number,onChange:Function,onClick:Function},setup:function(e,t){var n=t.emit,i=t.attrs,r=t.slots,s=t.expose,a=w("anchor",e),o=a.prefixCls,l=a.getTargetContainer,u=a.direction,h=d(),g=d(),b=p({links:[],scrollContainer:null,scrollEvent:null,animating:!1}),k=d(null),y=f((function(){return e.getContainer||l.value||_e})),A=function(t){var i=e.getCurrentAnchor;k.value!==t&&(k.value="function"==typeof i?i():t,n("change",t))},I=function(t){var n=e.offsetTop,i=e.targetOffset;A(t);var r=y.value(),s=N(r,!0),a=ye.exec(t);if(a){var o=document.getElementById(a[1]);if(o){var l=s+ve(o,r);l-=void 0!==i?i:n||0,b.animating=!0,M(l,{callback:function(){b.animating=!1},getContainer:y.value})}}};s({scrollTo:I});var T=function(){if(!b.animating){var t=e.offsetTop,n=e.bounds,i=e.targetOffset,r=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,n=[],i=y.value();if(b.links.forEach((function(r){var s=ye.exec(r.toString());if(s){var a=document.getElementById(s[1]);if(a){var o=ve(a,i);o<e+t&&n.push({link:r,top:o})}}})),n.length){return n.reduce((function(e,t){return t.top>e.top?t:e})).link}return""}(void 0!==i?i:t||0,n);A(r)}};return we({registerLink:function(e){b.links.includes(e)||b.links.push(e)},unregisterLink:function(e){var t=b.links.indexOf(e);-1!==t&&b.links.splice(t,1)},activeLink:k,scrollTo:I,handleClick:function(e,t){n("click",e,t)}}),m((function(){$((function(){var e=y.value();b.scrollContainer=e,b.scrollEvent=c(b.scrollContainer,"scroll",T),T()}))})),C((function(){b.scrollEvent&&b.scrollEvent.remove()})),x((function(){if(b.scrollEvent){var e=y.value();b.scrollContainer!==e&&(b.scrollContainer=e,b.scrollEvent.remove(),b.scrollEvent=c(b.scrollContainer,"scroll",T),T())}var t;(t=g.value.getElementsByClassName("".concat(o.value,"-link-title-active"))[0])&&(h.value.style.top="".concat(t.offsetTop+t.clientHeight/2-4.5,"px"))})),function(){var t,n=e.offsetTop,s=e.affix,a=e.showInkInFixed,l=o.value,c=_("".concat(l,"-ink-ball"),{visible:k.value}),d=_(e.wrapperClass,"".concat(l,"-wrapper"),v({},"".concat(l,"-rtl"),"rtl"===u.value)),p=_(l,v({},"".concat(l,"-fixed"),!s&&!a)),f=O({maxHeight:n?"calc(100vh - ".concat(n,"px)"):"100vh"},e.wrapperStyle),b=E("div",{class:d,style:f,ref:g},[E("div",{class:p},[E("div",{class:"".concat(l,"-ink")},[E("span",{class:c,ref:h},null)]),null===(t=r.default)||void 0===t?void 0:t.call(r)])]);return s?E(me,S(S({},i),{},{offsetTop:n,target:y.value}),{default:function(){return[b]}}):b}}}),Se=g({name:"AAnchorLink",props:z({prefixCls:String,href:String,title:B.any,target:String},{href:"#"}),slots:["title"],setup:function(e,t){var n=t.slots,i=null,r=R(ke,{registerLink:xe,unregisterLink:xe,scrollTo:xe,activeLink:f((function(){return""})),handleClick:xe}),s=r.handleClick,a=r.scrollTo,o=r.unregisterLink,l=r.registerLink,c=r.activeLink,u=w("anchor",e).prefixCls,h=function(t){var n=e.href;s(t,{title:i,href:n}),a(n)};return b((function(){return e.href}),(function(e,t){$((function(){o(t),l(e)}))})),m((function(){l(e.href)})),C((function(){o(e.href)})),function(){var t,r=e.href,s=e.target,a=u.value,o=L(n,e,"title");i=o;var l=c.value===r,g=_("".concat(a,"-link"),v({},"".concat(a,"-link-active"),l)),d=_("".concat(a,"-link-title"),v({},"".concat(a,"-link-title-active"),l));return E("div",{class:g},[E("a",{class:d,href:r,title:"string"==typeof o?o:"",target:s,onClick:h},[o]),null===(t=n.default)||void 0===t?void 0:t.call(n)])}}});Ee.Link=Se,Ee.install=function(e){return e.component(Ee.name,Ee),e.component(Ee.Link.name,Ee.Link),e};function Ae(){return{baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1}}let Oe={baseUrl:null,breaks:!1,extensions:null,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:null,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tokenizer:null,walkTokens:null,xhtml:!1};const Ie=/[&<>"']/,Te=/[&<>"']/g,Re=/[<>"']|&(?!#?\w+;)/,$e=/[<>"']|&(?!#?\w+;)/g,Ce={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Ne=e=>Ce[e];function Me(e,t){if(t){if(Ie.test(e))return e.replace(Te,Ne)}else if(Re.test(e))return e.replace($e,Ne);return e}const ze=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Le(e){return e.replace(ze,((e,t)=>"colon"===(t=t.toLowerCase())?":":"#"===t.charAt(0)?"x"===t.charAt(1)?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):""))}const Be=/(^|[^\[])\^/g;function De(e,t){e="string"==typeof e?e:e.source,t=t||"";const n={replace:(t,i)=>(i=(i=i.source||i).replace(Be,"$1"),e=e.replace(t,i),n),getRegex:()=>new RegExp(e,t)};return n}const je=/[^\w:]/g,Pe=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function Ue(e,t,n){if(e){let e;try{e=decodeURIComponent(Le(n)).replace(je,"").toLowerCase()}catch(i){return null}if(0===e.indexOf("javascript:")||0===e.indexOf("vbscript:")||0===e.indexOf("data:"))return null}t&&!Pe.test(n)&&(n=function(e,t){Ze[" "+e]||(He.test(e)?Ze[" "+e]=e+"/":Ze[" "+e]=We(e,"/",!0));const n=-1===(e=Ze[" "+e]).indexOf(":");return"//"===t.substring(0,2)?n?t:e.replace(Fe,"$1")+t:"/"===t.charAt(0)?n?t:e.replace(Ke,"$1")+t:e+t}(t,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(i){return null}return n}const Ze={},He=/^[^:]+:\/*[^/]*$/,Fe=/^([^:]+:)[\s\S]*$/,Ke=/^([^:]+:\/*[^/]*)[\s\S]*$/;const qe={exec:function(){}};function Ge(e){let t,n,i=1;for(;i<arguments.length;i++)for(n in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}function Qe(e,t){const n=e.replace(/\|/g,((e,t,n)=>{let i=!1,r=t;for(;--r>=0&&"\\"===n[r];)i=!i;return i?"|":" |"})).split(/ \|/);let i=0;if(n[0].trim()||n.shift(),n.length>0&&!n[n.length-1].trim()&&n.pop(),n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;i<n.length;i++)n[i]=n[i].trim().replace(/\\\|/g,"|");return n}function We(e,t,n){const i=e.length;if(0===i)return"";let r=0;for(;r<i;){const s=e.charAt(i-r-1);if(s!==t||n){if(s===t||!n)break;r++}else r++}return e.slice(0,i-r)}function Xe(e){e&&e.sanitize&&e.silent}function Je(e,t){if(t<1)return"";let n="";for(;t>1;)1&t&&(n+=e),t>>=1,e+=e;return n+e}function Ve(e,t,n,i){const r=t.href,s=t.title?Me(t.title):null,a=e[1].replace(/\\([\[\]])/g,"$1");if("!"!==e[0].charAt(0)){i.state.inLink=!0;const e={type:"link",raw:n,href:r,title:s,text:a,tokens:i.inlineTokens(a,[])};return i.state.inLink=!1,e}return{type:"image",raw:n,href:r,title:s,text:Me(a)}}class Ye{constructor(e){this.options=e||Oe}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const e=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:We(e,"\n")}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const e=t[0],n=function(e,t){const n=e.match(/^(\s+)(?:```)/);if(null===n)return t;const i=n[1];return t.split("\n").map((e=>{const t=e.match(/^\s+/);if(null===t)return e;const[n]=t;return n.length>=i.length?e.slice(i.length):e})).join("\n")}(e,t[3]||"");return{type:"code",raw:e,lang:t[2]?t[2].trim():t[2],text:n}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(/#$/.test(e)){const t=We(e,"#");this.options.pedantic?e=t.trim():t&&!/ $/.test(t)||(e=t.trim())}const n={type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:[]};return this.lexer.inline(n.text,n.tokens),n}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const e=t[0].replace(/^ *>[ \t]?/gm,"");return{type:"blockquote",raw:t[0],tokens:this.lexer.blockTokens(e,[]),text:e}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n,i,r,s,a,o,l,c,u,h,g,d,p=t[1].trim();const f=p.length>1,b={type:"list",raw:"",ordered:f,start:f?+p.slice(0,-1):"",loose:!1,items:[]};p=f?`\\d{1,9}\\${p.slice(-1)}`:`\\${p}`,this.options.pedantic&&(p=f?p:"[*+-]");const m=new RegExp(`^( {0,3}${p})((?:[\t ][^\\n]*)?(?:\\n|$))`);for(;e&&(d=!1,t=m.exec(e))&&!this.rules.block.hr.test(e);){if(n=t[0],e=e.substring(n.length),c=t[2].split("\n",1)[0],u=e.split("\n",1)[0],this.options.pedantic?(s=2,g=c.trimLeft()):(s=t[2].search(/[^ ]/),s=s>4?1:s,g=c.slice(s),s+=t[1].length),o=!1,!c&&/^ *$/.test(u)&&(n+=u+"\n",e=e.substring(u.length+1),d=!0),!d){const t=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:[*+-]|\\d{1,9}[.)])((?: [^\\n]*)?(?:\\n|$))`),i=new RegExp(`^ {0,${Math.min(3,s-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),r=new RegExp(`^ {0,${Math.min(3,s-1)}}(?:\`\`\`|~~~)`),a=new RegExp(`^ {0,${Math.min(3,s-1)}}#`);for(;e&&(h=e.split("\n",1)[0],c=h,this.options.pedantic&&(c=c.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),!r.test(c))&&!a.test(c)&&!t.test(c)&&!i.test(e);){if(c.search(/[^ ]/)>=s||!c.trim())g+="\n"+c.slice(s);else{if(o)break;g+="\n"+c}o||c.trim()||(o=!0),n+=h+"\n",e=e.substring(h.length+1)}}b.loose||(l?b.loose=!0:/\n *\n *$/.test(n)&&(l=!0)),this.options.gfm&&(i=/^\[[ xX]\] /.exec(g),i&&(r="[ ] "!==i[0],g=g.replace(/^\[[ xX]\] +/,""))),b.items.push({type:"list_item",raw:n,task:!!i,checked:r,loose:!1,text:g}),b.raw+=n}b.items[b.items.length-1].raw=n.trimRight(),b.items[b.items.length-1].text=g.trimRight(),b.raw=b.raw.trimRight();const x=b.items.length;for(a=0;a<x;a++){this.lexer.state.top=!1,b.items[a].tokens=this.lexer.blockTokens(b.items[a].text,[]);const e=b.items[a].tokens.filter((e=>"space"===e.type)),t=e.every((e=>{const t=e.raw.split("");let n=0;for(const i of t)if("\n"===i&&(n+=1),n>1)return!0;return!1}));!b.loose&&e.length&&t&&(b.loose=!0,b.items[a].loose=!0)}return b}}html(e){const t=this.rules.block.html.exec(e);if(t){const e={type:"html",raw:t[0],pre:!this.options.sanitizer&&("pre"===t[1]||"script"===t[1]||"style"===t[1]),text:t[0]};return this.options.sanitize&&(e.type="paragraph",e.text=this.options.sanitizer?this.options.sanitizer(t[0]):Me(t[0]),e.tokens=[],this.lexer.inline(e.text,e.tokens)),e}}def(e){const t=this.rules.block.def.exec(e);if(t){t[3]&&(t[3]=t[3].substring(1,t[3].length-1));return{type:"def",tag:t[1].toLowerCase().replace(/\s+/g," "),raw:t[0],href:t[2],title:t[3]}}}table(e){const t=this.rules.block.table.exec(e);if(t){const e={type:"table",header:Qe(t[1]).map((e=>({text:e}))),align:t[2].replace(/^ *|\| *$/g,"").split(/ *\| */),rows:t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split("\n"):[]};if(e.header.length===e.align.length){e.raw=t[0];let n,i,r,s,a=e.align.length;for(n=0;n<a;n++)/^ *-+: *$/.test(e.align[n])?e.align[n]="right":/^ *:-+: *$/.test(e.align[n])?e.align[n]="center":/^ *:-+ *$/.test(e.align[n])?e.align[n]="left":e.align[n]=null;for(a=e.rows.length,n=0;n<a;n++)e.rows[n]=Qe(e.rows[n],e.header.length).map((e=>({text:e})));for(a=e.header.length,i=0;i<a;i++)e.header[i].tokens=[],this.lexer.inline(e.header[i].text,e.header[i].tokens);for(a=e.rows.length,i=0;i<a;i++)for(s=e.rows[i],r=0;r<s.length;r++)s[r].tokens=[],this.lexer.inline(s[r].text,s[r].tokens);return e}}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t){const e={type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:[]};return this.lexer.inline(e.text,e.tokens),e}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const e={type:"paragraph",raw:t[0],text:"\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1],tokens:[]};return this.lexer.inline(e.text,e.tokens),e}}text(e){const t=this.rules.block.text.exec(e);if(t){const e={type:"text",raw:t[0],text:t[0],tokens:[]};return this.lexer.inline(e.text,e.tokens),e}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:Me(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:this.options.sanitize?"text":"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,text:this.options.sanitize?this.options.sanitizer?this.options.sanitizer(t[0]):Me(t[0]):t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const e=t[2].trim();if(!this.options.pedantic&&/^</.test(e)){if(!/>$/.test(e))return;const t=We(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{const e=function(e,t){if(-1===e.indexOf(t[1]))return-1;const n=e.length;let i=0,r=0;for(;r<n;r++)if("\\"===e[r])r++;else if(e[r]===t[0])i++;else if(e[r]===t[1]&&(i--,i<0))return r;return-1}(t[2],"()");if(e>-1){const n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],i="";if(this.options.pedantic){const e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);e&&(n=e[1],i=e[3])}else i=t[3]?t[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(e)?n.slice(1):n.slice(1,-1)),Ve(t,{href:n?n.replace(this.rules.inline._escapes,"$1"):n,title:i?i.replace(this.rules.inline._escapes,"$1"):i},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=(n[2]||n[1]).replace(/\s+/g," ");if(e=t[e.toLowerCase()],!e||!e.href){const e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return Ve(n,e,n[0],this.lexer)}}emStrong(e,t,n=""){let i=this.rules.inline.emStrong.lDelim.exec(e);if(!i)return;if(i[3]&&n.match(/[\p{L}\p{N}]/u))return;const r=i[1]||i[2]||"";if(!r||r&&(""===n||this.rules.inline.punctuation.exec(n))){const n=i[0].length-1;let r,s,a=n,o=0;const l="*"===i[0][0]?this.rules.inline.emStrong.rDelimAst:this.rules.inline.emStrong.rDelimUnd;for(l.lastIndex=0,t=t.slice(-1*e.length+n);null!=(i=l.exec(t));){if(r=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!r)continue;if(s=r.length,i[3]||i[4]){a+=s;continue}if((i[5]||i[6])&&n%3&&!((n+s)%3)){o+=s;continue}if(a-=s,a>0)continue;if(s=Math.min(s,s+a+o),Math.min(n,s)%2){const t=e.slice(1,n+i.index+s);return{type:"em",raw:e.slice(0,n+i.index+s+1),text:t,tokens:this.lexer.inlineTokens(t,[])}}const t=e.slice(2,n+i.index+s-1);return{type:"strong",raw:e.slice(0,n+i.index+s+1),text:t,tokens:this.lexer.inlineTokens(t,[])}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(/\n/g," ");const n=/[^ ]/.test(e),i=/^ /.test(e)&&/ $/.test(e);return n&&i&&(e=e.substring(1,e.length-1)),e=Me(e,!0),{type:"codespan",raw:t[0],text:e}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2],[])}}autolink(e,t){const n=this.rules.inline.autolink.exec(e);if(n){let e,i;return"@"===n[2]?(e=Me(this.options.mangle?t(n[1]):n[1]),i="mailto:"+e):(e=Me(n[1]),i=e),{type:"link",raw:n[0],text:e,href:i,tokens:[{type:"text",raw:e,text:e}]}}}url(e,t){let n;if(n=this.rules.inline.url.exec(e)){let e,i;if("@"===n[2])e=Me(this.options.mangle?t(n[0]):n[0]),i="mailto:"+e;else{let t;do{t=n[0],n[0]=this.rules.inline._backpedal.exec(n[0])[0]}while(t!==n[0]);e=Me(n[0]),i="www."===n[1]?"http://"+e:e}return{type:"link",raw:n[0],text:e,href:i,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e,t){const n=this.rules.inline.text.exec(e);if(n){let e;return e=this.lexer.state.inRawBlock?this.options.sanitize?this.options.sanitizer?this.options.sanitizer(n[0]):Me(n[0]):n[0]:Me(this.options.smartypants?t(n[0]):n[0]),{type:"text",raw:n[0],text:e}}}}const et={newline:/^(?: *(?:\n|$))+/,code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,fences:/^ {0,3}(`{3,}(?=[^`\n]*\n)|~{3,})([^\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?=\n|$)|$)/,hr:/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/,html:"^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))",def:/^ {0,3}\[(label)\]: *(?:\n *)?<?([^\s>]+)>?(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/,table:qe,lheading:/^([^\n]+)\n {0,3}(=+|-+) *(?:\n+|$)/,_paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,text:/^[^\n]+/,_label:/(?!\s*\])(?:\\.|[^\[\]\\])+/,_title:/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/};et.def=De(et.def).replace("label",et._label).replace("title",et._title).getRegex(),et.bullet=/(?:[*+-]|\d{1,9}[.)])/,et.listItemStart=De(/^( *)(bull) */).replace("bull",et.bullet).getRegex(),et.list=De(et.list).replace(/bull/g,et.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+et.def.source+")").getRegex(),et._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",et._comment=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,et.html=De(et.html,"i").replace("comment",et._comment).replace("tag",et._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),et.paragraph=De(et._paragraph).replace("hr",et.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",et._tag).getRegex(),et.blockquote=De(et.blockquote).replace("paragraph",et.paragraph).getRegex(),et.normal=Ge({},et),et.gfm=Ge({},et.normal,{table:"^ *([^\\n ].*\\|.*)\\n {0,3}(?:\\| *)?(:?-+:? *(?:\\| *:?-+:? *)*)(?:\\| *)?(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)"}),et.gfm.table=De(et.gfm.table).replace("hr",et.hr).replace("heading"," {0,3}#{1,6} ").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",et._tag).getRegex(),et.gfm.paragraph=De(et._paragraph).replace("hr",et.hr).replace("heading"," {0,3}#{1,6} ").replace("|lheading","").replace("table",et.gfm.table).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",et._tag).getRegex(),et.pedantic=Ge({},et.normal,{html:De("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",et._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:qe,paragraph:De(et.normal._paragraph).replace("hr",et.hr).replace("heading"," *#{1,6} *[^\n]").replace("lheading",et.lheading).replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").getRegex()});const tt={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:qe,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(ref)\]/,nolink:/^!?\[(ref)\](?:\[\])?/,reflinkSearch:"reflink|nolink(?!\\()",emStrong:{lDelim:/^(?:\*+(?:([punct_])|[^\s*]))|^_+(?:([punct*])|([^\s_]))/,rDelimAst:/^[^_*]*?\_\_[^_*]*?\*[^_*]*?(?=\_\_)|[^*]+(?=[^*])|[punct_](\*+)(?=[\s]|$)|[^punct*_\s](\*+)(?=[punct_\s]|$)|[punct_\s](\*+)(?=[^punct*_\s])|[\s](\*+)(?=[punct_])|[punct_](\*+)(?=[punct_])|[^punct*_\s](\*+)(?=[^punct*_\s])/,rDelimUnd:/^[^_*]*?\*\*[^_*]*?\_[^_*]*?(?=\*\*)|[^_]+(?=[^_])|[punct*](\_+)(?=[\s]|$)|[^punct*_\s](\_+)(?=[punct*\s]|$)|[punct*\s](\_+)(?=[^punct*_\s])|[\s](\_+)(?=[punct*])|[punct*](\_+)(?=[punct*])/},code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:qe,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,punctuation:/^([\spunctuation])/};function nt(e){return e.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…")}function it(e){let t,n,i="";const r=e.length;for(t=0;t<r;t++)n=e.charCodeAt(t),Math.random()>.5&&(n="x"+n.toString(16)),i+="&#"+n+";";return i}tt._punctuation="!\"#$%&'()+\\-.,/:;<=>?@\\[\\]`^{|}~",tt.punctuation=De(tt.punctuation).replace(/punctuation/g,tt._punctuation).getRegex(),tt.blockSkip=/\[[^\]]*?\]\([^\)]*?\)|`[^`]*?`|<[^>]*?>/g,tt.escapedEmSt=/\\\*|\\_/g,tt._comment=De(et._comment).replace("(?:--\x3e|$)","--\x3e").getRegex(),tt.emStrong.lDelim=De(tt.emStrong.lDelim).replace(/punct/g,tt._punctuation).getRegex(),tt.emStrong.rDelimAst=De(tt.emStrong.rDelimAst,"g").replace(/punct/g,tt._punctuation).getRegex(),tt.emStrong.rDelimUnd=De(tt.emStrong.rDelimUnd,"g").replace(/punct/g,tt._punctuation).getRegex(),tt._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,tt._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,tt._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,tt.autolink=De(tt.autolink).replace("scheme",tt._scheme).replace("email",tt._email).getRegex(),tt._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,tt.tag=De(tt.tag).replace("comment",tt._comment).replace("attribute",tt._attribute).getRegex(),tt._label=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,tt._href=/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/,tt._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,tt.link=De(tt.link).replace("label",tt._label).replace("href",tt._href).replace("title",tt._title).getRegex(),tt.reflink=De(tt.reflink).replace("label",tt._label).replace("ref",et._label).getRegex(),tt.nolink=De(tt.nolink).replace("ref",et._label).getRegex(),tt.reflinkSearch=De(tt.reflinkSearch,"g").replace("reflink",tt.reflink).replace("nolink",tt.nolink).getRegex(),tt.normal=Ge({},tt),tt.pedantic=Ge({},tt.normal,{strong:{start:/^__|\*\*/,middle:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,endAst:/\*\*(?!\*)/g,endUnd:/__(?!_)/g},em:{start:/^_|\*/,middle:/^()\*(?=\S)([\s\S]*?\S)\*(?!\*)|^_(?=\S)([\s\S]*?\S)_(?!_)/,endAst:/\*(?!\*)/g,endUnd:/_(?!_)/g},link:De(/^!?\[(label)\]\((.*?)\)/).replace("label",tt._label).getRegex(),reflink:De(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",tt._label).getRegex()}),tt.gfm=Ge({},tt.normal,{escape:De(tt.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/}),tt.gfm.url=De(tt.gfm.url,"i").replace("email",tt.gfm._extended_email).getRegex(),tt.breaks=Ge({},tt.gfm,{br:De(tt.br).replace("{2,}","*").getRegex(),text:De(tt.gfm.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()});class rt{constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Oe,this.options.tokenizer=this.options.tokenizer||new Ye,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:et.normal,inline:tt.normal};this.options.pedantic?(t.block=et.pedantic,t.inline=tt.pedantic):this.options.gfm&&(t.block=et.gfm,this.options.breaks?t.inline=tt.breaks:t.inline=tt.gfm),this.tokenizer.rules=t}static get rules(){return{block:et,inline:tt}}static lex(e,t){return new rt(t).lex(e)}static lexInline(e,t){return new rt(t).inlineTokens(e)}lex(e){let t;for(e=e.replace(/\r\n|\r/g,"\n"),this.blockTokens(e,this.tokens);t=this.inlineQueue.shift();)this.inlineTokens(t.src,t.tokens);return this.tokens}blockTokens(e,t=[]){let n,i,r,s;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,((e,t,n)=>t+"    ".repeat(n.length)));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some((i=>!!(n=i.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),1===n.raw.length&&t.length>0?t[t.length-1].raw+="\n":t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),i=t[t.length-1],!i||"paragraph"!==i.type&&"text"!==i.type?t.push(n):(i.raw+="\n"+n.raw,i.text+="\n"+n.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),i=t[t.length-1],!i||"paragraph"!==i.type&&"text"!==i.type?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(i.raw+="\n"+n.raw,i.text+="\n"+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(r=e,this.options.extensions&&this.options.extensions.startBlock){let t=Infinity;const n=e.slice(1);let i;this.options.extensions.startBlock.forEach((function(e){i=e.call({lexer:this},n),"number"==typeof i&&i>=0&&(t=Math.min(t,i))})),t<Infinity&&t>=0&&(r=e.substring(0,t+1))}if(this.state.top&&(n=this.tokenizer.paragraph(r)))i=t[t.length-1],s&&"paragraph"===i.type?(i.raw+="\n"+n.raw,i.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n),s=r.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),i=t[t.length-1],i&&"text"===i.type?(i.raw+="\n"+n.raw,i.text+="\n"+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):t.push(n);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent)break;throw new Error(t)}}return this.state.top=!0,t}inline(e,t){this.inlineQueue.push({src:e,tokens:t})}inlineTokens(e,t=[]){let n,i,r,s,a,o,l=e;if(this.tokens.links){const e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(s=this.tokenizer.rules.inline.reflinkSearch.exec(l));)e.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(l=l.slice(0,s.index)+"["+Je("a",s[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(s=this.tokenizer.rules.inline.blockSkip.exec(l));)l=l.slice(0,s.index)+"["+Je("a",s[0].length-2)+"]"+l.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;null!=(s=this.tokenizer.rules.inline.escapedEmSt.exec(l));)l=l.slice(0,s.index)+"++"+l.slice(this.tokenizer.rules.inline.escapedEmSt.lastIndex);for(;e;)if(a||(o=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some((i=>!!(n=i.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0)))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),i=t[t.length-1],i&&"text"===n.type&&"text"===i.type?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),i=t[t.length-1],i&&"text"===n.type&&"text"===i.type?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,l,o))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e,it))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e,it))){if(r=e,this.options.extensions&&this.options.extensions.startInline){let t=Infinity;const n=e.slice(1);let i;this.options.extensions.startInline.forEach((function(e){i=e.call({lexer:this},n),"number"==typeof i&&i>=0&&(t=Math.min(t,i))})),t<Infinity&&t>=0&&(r=e.substring(0,t+1))}if(n=this.tokenizer.inlineText(r,nt))e=e.substring(n.raw.length),"_"!==n.raw.slice(-1)&&(o=n.raw.slice(-1)),a=!0,i=t[t.length-1],i&&"text"===i.type?(i.raw+=n.raw,i.text+=n.text):t.push(n);else if(e){const t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent)break;throw new Error(t)}}else e=e.substring(n.raw.length),t.push(n);return t}}class st{constructor(e){this.options=e||Oe}code(e,t,n){const i=(t||"").match(/\S*/)[0];if(this.options.highlight){const t=this.options.highlight(e,i);null!=t&&t!==e&&(n=!0,e=t)}return e=e.replace(/\n$/,"")+"\n",i?'<pre><code class="'+this.options.langPrefix+Me(i,!0)+'">'+(n?e:Me(e,!0))+"</code></pre>\n":"<pre><code>"+(n?e:Me(e,!0))+"</code></pre>\n"}blockquote(e){return`<blockquote>\n${e}</blockquote>\n`}html(e){return e}heading(e,t,n,i){if(this.options.headerIds){return`<h${t} id="${this.options.headerPrefix+i.slug(n)}">${e}</h${t}>\n`}return`<h${t}>${e}</h${t}>\n`}hr(){return this.options.xhtml?"<hr/>\n":"<hr>\n"}list(e,t,n){const i=t?"ol":"ul";return"<"+i+(t&&1!==n?' start="'+n+'"':"")+">\n"+e+"</"+i+">\n"}listitem(e){return`<li>${e}</li>\n`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "}paragraph(e){return`<p>${e}</p>\n`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),"<table>\n<thead>\n"+e+"</thead>\n"+t+"</table>\n"}tablerow(e){return`<tr>\n${e}</tr>\n`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>\n`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return this.options.xhtml?"<br/>":"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){if(null===(e=Ue(this.options.sanitize,this.options.baseUrl,e)))return n;let i='<a href="'+Me(e)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>",i}image(e,t,n){if(null===(e=Ue(this.options.sanitize,this.options.baseUrl,e)))return n;let i=`<img src="${e}" alt="${n}"`;return t&&(i+=` title="${t}"`),i+=this.options.xhtml?"/>":">",i}text(e){return e}}class at{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class ot{constructor(){this.seen={}}serialize(e){return e.toLowerCase().trim().replace(/<[!\/a-z].*?>/gi,"").replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-")}getNextSafeSlug(e,t){let n=e,i=0;if(this.seen.hasOwnProperty(n)){i=this.seen[e];do{i++,n=e+"-"+i}while(this.seen.hasOwnProperty(n))}return t||(this.seen[e]=i,this.seen[n]=0),n}slug(e,t={}){const n=this.serialize(e);return this.getNextSafeSlug(n,t.dryrun)}}class lt{constructor(e){this.options=e||Oe,this.options.renderer=this.options.renderer||new st,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new at,this.slugger=new ot}static parse(e,t){return new lt(t).parse(e)}static parseInline(e,t){return new lt(t).parseInline(e)}parse(e,t=!0){let n,i,r,s,a,o,l,c,u,h,g,d,p,f,b,m,x,k,w,_="";const v=e.length;for(n=0;n<v;n++)if(h=e[n],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[h.type]&&(w=this.options.extensions.renderers[h.type].call({parser:this},h),!1!==w||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(h.type)))_+=w||"";else switch(h.type){case"space":continue;case"hr":_+=this.renderer.hr();continue;case"heading":_+=this.renderer.heading(this.parseInline(h.tokens),h.depth,Le(this.parseInline(h.tokens,this.textRenderer)),this.slugger);continue;case"code":_+=this.renderer.code(h.text,h.lang,h.escaped);continue;case"table":for(c="",l="",s=h.header.length,i=0;i<s;i++)l+=this.renderer.tablecell(this.parseInline(h.header[i].tokens),{header:!0,align:h.align[i]});for(c+=this.renderer.tablerow(l),u="",s=h.rows.length,i=0;i<s;i++){for(o=h.rows[i],l="",a=o.length,r=0;r<a;r++)l+=this.renderer.tablecell(this.parseInline(o[r].tokens),{header:!1,align:h.align[r]});u+=this.renderer.tablerow(l)}_+=this.renderer.table(c,u);continue;case"blockquote":u=this.parse(h.tokens),_+=this.renderer.blockquote(u);continue;case"list":for(g=h.ordered,d=h.start,p=h.loose,s=h.items.length,u="",i=0;i<s;i++)b=h.items[i],m=b.checked,x=b.task,f="",b.task&&(k=this.renderer.checkbox(m),p?b.tokens.length>0&&"paragraph"===b.tokens[0].type?(b.tokens[0].text=k+" "+b.tokens[0].text,b.tokens[0].tokens&&b.tokens[0].tokens.length>0&&"text"===b.tokens[0].tokens[0].type&&(b.tokens[0].tokens[0].text=k+" "+b.tokens[0].tokens[0].text)):b.tokens.unshift({type:"text",text:k}):f+=k),f+=this.parse(b.tokens,p),u+=this.renderer.listitem(f,x,m);_+=this.renderer.list(u,g,d);continue;case"html":_+=this.renderer.html(h.text);continue;case"paragraph":_+=this.renderer.paragraph(this.parseInline(h.tokens));continue;case"text":for(u=h.tokens?this.parseInline(h.tokens):h.text;n+1<v&&"text"===e[n+1].type;)h=e[++n],u+="\n"+(h.tokens?this.parseInline(h.tokens):h.text);_+=t?this.renderer.paragraph(u):u;continue;default:{const e='Token with "'+h.type+'" type was not found.';if(this.options.silent)return;throw new Error(e)}}return _}parseInline(e,t){t=t||this.renderer;let n,i,r,s="";const a=e.length;for(n=0;n<a;n++)if(i=e[n],this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]&&(r=this.options.extensions.renderers[i.type].call({parser:this},i),!1!==r||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)))s+=r||"";else switch(i.type){case"escape":case"text":s+=t.text(i.text);break;case"html":s+=t.html(i.text);break;case"link":s+=t.link(i.href,i.title,this.parseInline(i.tokens,t));break;case"image":s+=t.image(i.href,i.title,i.text);break;case"strong":s+=t.strong(this.parseInline(i.tokens,t));break;case"em":s+=t.em(this.parseInline(i.tokens,t));break;case"codespan":s+=t.codespan(i.text);break;case"br":s+=t.br();break;case"del":s+=t.del(this.parseInline(i.tokens,t));break;default:{const e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return;throw new Error(e)}}return s}}function ct(e,t,n){if(null==e)throw new Error("marked(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");if("function"==typeof t&&(n=t,t=null),Xe(t=Ge({},ct.defaults,t||{})),n){const r=t.highlight;let s;try{s=rt.lex(e,t)}catch(i){return n(i)}const a=function(e){let a;if(!e)try{t.walkTokens&&ct.walkTokens(s,t.walkTokens),a=lt.parse(s,t)}catch(i){e=i}return t.highlight=r,e?n(e):n(null,a)};if(!r||r.length<3)return a();if(delete t.highlight,!s.length)return a();let o=0;return ct.walkTokens(s,(function(e){"code"===e.type&&(o++,setTimeout((()=>{r(e.text,e.lang,(function(t,n){if(t)return a(t);null!=n&&n!==e.text&&(e.text=n,e.escaped=!0),o--,0===o&&a()}))}),0))})),void(0===o&&a())}try{const n=rt.lex(e,t);return t.walkTokens&&ct.walkTokens(n,t.walkTokens),lt.parse(n,t)}catch(i){if(i.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+Me(i.message+"",!0)+"</pre>";throw i}}ct.options=ct.setOptions=function(e){var t;return Ge(ct.defaults,e),t=ct.defaults,Oe=t,ct},ct.getDefaults=Ae,ct.defaults=Oe,ct.use=function(...e){const t=Ge({},...e),n=ct.defaults.extensions||{renderers:{},childTokens:{}};let i;e.forEach((e=>{if(e.extensions&&(i=!0,e.extensions.forEach((e=>{if(!e.name)throw new Error("extension name required");if(e.renderer){const t=n.renderers?n.renderers[e.name]:null;n.renderers[e.name]=t?function(...n){let i=e.renderer.apply(this,n);return!1===i&&(i=t.apply(this,n)),i}:e.renderer}if(e.tokenizer){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw new Error("extension level must be 'block' or 'inline'");n[e.level]?n[e.level].unshift(e.tokenizer):n[e.level]=[e.tokenizer],e.start&&("block"===e.level?n.startBlock?n.startBlock.push(e.start):n.startBlock=[e.start]:"inline"===e.level&&(n.startInline?n.startInline.push(e.start):n.startInline=[e.start]))}e.childTokens&&(n.childTokens[e.name]=e.childTokens)}))),e.renderer){const n=ct.defaults.renderer||new st;for(const t in e.renderer){const i=n[t];n[t]=(...r)=>{let s=e.renderer[t].apply(n,r);return!1===s&&(s=i.apply(n,r)),s}}t.renderer=n}if(e.tokenizer){const n=ct.defaults.tokenizer||new Ye;for(const t in e.tokenizer){const i=n[t];n[t]=(...r)=>{let s=e.tokenizer[t].apply(n,r);return!1===s&&(s=i.apply(n,r)),s}}t.tokenizer=n}if(e.walkTokens){const n=ct.defaults.walkTokens;t.walkTokens=function(t){e.walkTokens.call(this,t),n&&n.call(this,t)}}i&&(t.extensions=n),ct.setOptions(t)}))},ct.walkTokens=function(e,t){for(const n of e)switch(t.call(ct,n),n.type){case"table":for(const e of n.header)ct.walkTokens(e.tokens,t);for(const e of n.rows)for(const n of e)ct.walkTokens(n.tokens,t);break;case"list":ct.walkTokens(n.items,t);break;default:ct.defaults.extensions&&ct.defaults.extensions.childTokens&&ct.defaults.extensions.childTokens[n.type]?ct.defaults.extensions.childTokens[n.type].forEach((function(e){ct.walkTokens(n[e],t)})):n.tokens&&ct.walkTokens(n.tokens,t)}},ct.parseInline=function(e,t){if(null==e)throw new Error("marked.parseInline(): input parameter is undefined or null");if("string"!=typeof e)throw new Error("marked.parseInline(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected");Xe(t=Ge({},ct.defaults,t||{}));try{const n=rt.lexInline(e,t);return t.walkTokens&&ct.walkTokens(n,t.walkTokens),lt.parseInline(n,t)}catch(n){if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",t.silent)return"<p>An error occurred:</p><pre>"+Me(n.message+"",!0)+"</pre>";throw n}},ct.Parser=lt,ct.parser=lt.parse,ct.Renderer=st,ct.TextRenderer=at,ct.Lexer=rt,ct.lexer=rt.lex,ct.Tokenizer=Ye,ct.Slugger=ot,ct.parse=ct,ct.options,ct.setOptions,ct.use,ct.walkTokens,ct.parseInline,lt.parse,rt.lex;var ut={exports:{}};function ht(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach((function(t){var n=e[t];"object"!=typeof n||Object.isFrozen(n)||ht(n)})),e}ut.exports=ht,ut.exports.default=ht;class gt{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function dt(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function pt(e,...t){const n=Object.create(null);for(const i in e)n[i]=e[i];return t.forEach((function(e){for(const t in e)n[t]=e[t]})),n}const ft=e=>!!e.scope||e.sublanguage&&e.language;class bt{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=dt(e)}openNode(e){if(!ft(e))return;let t="";t=e.sublanguage?`language-${e.language}`:((e,{prefix:t})=>{if(e.includes(".")){const n=e.split(".");return[`${t}${n.shift()}`,...n.map(((e,t)=>`${e}${"_".repeat(t+1)}`))].join(" ")}return`${t}${e}`})(e.scope,{prefix:this.classPrefix}),this.span(t)}closeNode(e){ft(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}const mt=(e={})=>{const t={children:[]};return Object.assign(t,e),t};class xt{constructor(){this.rootNode=mt(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){const t=mt({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach((t=>this._walk(e,t))),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every((e=>"string"==typeof e))?e.children=[e.children.join("")]:e.children.forEach((e=>{xt._collapse(e)})))}}class kt extends xt{constructor(e){super(),this.options=e}addKeyword(e,t){""!==e&&(this.openNode(t),this.addText(e),this.closeNode())}addText(e){""!==e&&this.add(e)}addSublanguage(e,t){const n=e.root;n.sublanguage=!0,n.language=t,this.add(n)}toHTML(){return new bt(this,this.options).value()}finalize(){return!0}}function wt(e){return e?"string"==typeof e?e:e.source:null}function _t(e){return Et("(?=",e,")")}function vt(e){return Et("(?:",e,")*")}function yt(e){return Et("(?:",e,")?")}function Et(...e){return e.map((e=>wt(e))).join("")}function St(...e){const t=function(e){const t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e);return"("+(t.capture?"":"?:")+e.map((e=>wt(e))).join("|")+")"}function At(e){return new RegExp(e.toString()+"|").exec("").length-1}const Ot=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function It(e,{joinWith:t}){let n=0;return e.map((e=>{n+=1;const t=n;let i=wt(e),r="";for(;i.length>0;){const e=Ot.exec(i);if(!e){r+=i;break}r+=i.substring(0,e.index),i=i.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?r+="\\"+String(Number(e[1])+t):(r+=e[0],"("===e[0]&&n++)}return r})).map((e=>`(${e})`)).join(t)}const Tt="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Rt={begin:"\\\\[\\s\\S]",relevance:0},$t={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Rt]},Ct={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Rt]},Nt=function(e,t,n={}){const i=pt({scope:"comment",begin:e,end:t,contains:[]},n);i.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const r=St("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return i.contains.push({begin:Et(/[ ]+/,"(",r,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),i},Mt=Nt("//","$"),zt=Nt("/\\*","\\*/"),Lt=Nt("#","$"),Bt={scope:"number",begin:"\\b\\d+(\\.\\d+)?",relevance:0},Dt={scope:"number",begin:Tt,relevance:0},jt={scope:"number",begin:"\\b(0b[01]+)",relevance:0},Pt={begin:/(?=\/[^/\n]*\/)/,contains:[{scope:"regexp",begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[Rt,{begin:/\[/,end:/\]/,relevance:0,contains:[Rt]}]}]},Ut={scope:"title",begin:"[a-zA-Z]\\w*",relevance:0},Zt={scope:"title",begin:"[a-zA-Z_]\\w*",relevance:0},Ht={begin:"\\.\\s*[a-zA-Z_]\\w*",relevance:0};var Ft=Object.freeze({__proto__:null,MATCH_NOTHING_RE:/\b\B/,IDENT_RE:"[a-zA-Z]\\w*",UNDERSCORE_IDENT_RE:"[a-zA-Z_]\\w*",NUMBER_RE:"\\b\\d+(\\.\\d+)?",C_NUMBER_RE:Tt,BINARY_NUMBER_RE:"\\b(0b[01]+)",RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=Et(t,/.*\b/,e.binary,/\b.*/)),pt({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},BACKSLASH_ESCAPE:Rt,APOS_STRING_MODE:$t,QUOTE_STRING_MODE:Ct,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},COMMENT:Nt,C_LINE_COMMENT_MODE:Mt,C_BLOCK_COMMENT_MODE:zt,HASH_COMMENT_MODE:Lt,NUMBER_MODE:Bt,C_NUMBER_MODE:Dt,BINARY_NUMBER_MODE:jt,REGEXP_MODE:Pt,TITLE_MODE:Ut,UNDERSCORE_TITLE_MODE:Zt,METHOD_GUARD:Ht,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})}});function Kt(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function qt(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function Gt(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=Kt,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function Qt(e,t){Array.isArray(e.illegal)&&(e.illegal=St(...e.illegal))}function Wt(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function Xt(e,t){void 0===e.relevance&&(e.relevance=1)}const Jt=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const n=Object.assign({},e);Object.keys(e).forEach((t=>{delete e[t]})),e.keywords=n.keywords,e.begin=Et(n.beforeMatch,_t(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},Vt=["of","and","for","in","not","or","if","then","parent","list","value"];function Yt(e,t,n="keyword"){const i=Object.create(null);return"string"==typeof e?r(n,e.split(" ")):Array.isArray(e)?r(n,e):Object.keys(e).forEach((function(n){Object.assign(i,Yt(e[n],t,n))})),i;function r(e,n){t&&(n=n.map((e=>e.toLowerCase()))),n.forEach((function(t){const n=t.split("|");i[n[0]]=[e,en(n[0],n[1])]}))}}function en(e,t){return t?Number(t):function(e){return Vt.includes(e.toLowerCase())}(e)?0:1}const tn={},nn=(e,t)=>{tn[`${e}/${t}`]||(tn[`${e}/${t}`]=!0)},rn=new Error;function sn(e,t,{key:n}){let i=0;const r=e[n],s={},a={};for(let o=1;o<=t.length;o++)a[o+i]=r[o],s[o+i]=!0,i+=At(t[o-1]);e[n]=a,e[n]._emit=s,e[n]._multi=!0}function an(e){!function(e){e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope)}(e),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),function(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw rn;if("object"!=typeof e.beginScope||null===e.beginScope)throw rn;sn(e,e.begin,{key:"beginScope"}),e.begin=It(e.begin,{joinWith:""})}}(e),function(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw rn;if("object"!=typeof e.endScope||null===e.endScope)throw rn;sn(e,e.end,{key:"endScope"}),e.end=It(e.end,{joinWith:""})}}(e)}function on(e){function t(t,n){return new RegExp(wt(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=At(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);const e=this.regexes.map((e=>e[1]));this.matcherRe=t(It(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;const t=this.matcherRe.exec(e);if(!t)return null;const n=t.findIndex(((e,t)=>t>0&&void 0!==e)),i=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,i)}}class i{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];const t=new n;return this.rules.slice(e).forEach((([e,n])=>t.addRule(e,n))),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){const t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{const t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=pt(e.classNameAliases||{}),function n(r,s){const a=r;if(r.isCompiled)return a;[qt,Wt,an,Jt].forEach((e=>e(r,s))),e.compilerExtensions.forEach((e=>e(r,s))),r.__beforeBegin=null,[Gt,Qt,Xt].forEach((e=>e(r,s))),r.isCompiled=!0;let o=null;return"object"==typeof r.keywords&&r.keywords.$pattern&&(r.keywords=Object.assign({},r.keywords),o=r.keywords.$pattern,delete r.keywords.$pattern),o=o||/\w+/,r.keywords&&(r.keywords=Yt(r.keywords,e.case_insensitive)),a.keywordPatternRe=t(o,!0),s&&(r.begin||(r.begin=/\B|\b/),a.beginRe=t(a.begin),r.end||r.endsWithParent||(r.end=/\B|\b/),r.end&&(a.endRe=t(a.end)),a.terminatorEnd=wt(a.end)||"",r.endsWithParent&&s.terminatorEnd&&(a.terminatorEnd+=(r.end?"|":"")+s.terminatorEnd)),r.illegal&&(a.illegalRe=t(r.illegal)),r.contains||(r.contains=[]),r.contains=[].concat(...r.contains.map((function(e){return function(e){e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map((function(t){return pt(e,{variants:null},t)})));if(e.cachedVariants)return e.cachedVariants;if(ln(e))return pt(e,{starts:e.starts?pt(e.starts):null});if(Object.isFrozen(e))return pt(e);return e}("self"===e?r:e)}))),r.contains.forEach((function(e){n(e,a)})),r.starts&&n(r.starts,s),a.matcher=function(e){const t=new i;return e.contains.forEach((e=>t.addRule(e.begin,{rule:e,type:"begin"}))),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(a),a}(e)}function ln(e){return!!e&&(e.endsWithParent||ln(e.starts))}class cn extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}const un=dt,hn=pt,gn=Symbol("nomatch");var dn=function(e){const t=Object.create(null),n=Object.create(null),i=[];let r=!0;const s="Could not find the language '{}', did you forget to load/include a language module?",a={disableAutodetect:!0,name:"Plain text",contains:[]};let o={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:kt};function l(e){return o.noHighlightRe.test(e)}function c(e,t,n){let i="",r="";"object"==typeof t?(i=e,n=t.ignoreIllegals,r=t.language):(nn("10.7.0","highlight(lang, code, ...args) has been deprecated."),nn("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),r=e,i=t),void 0===n&&(n=!0);const s={code:i,language:r};x("before:highlight",s);const a=s.result?s.result:u(s.language,s.code,n);return a.code=s.code,x("after:highlight",a),a}function u(e,n,i,a){const l=Object.create(null);function c(){if(!E.keywords)return void A.addText(O);let e=0;E.keywordPatternRe.lastIndex=0;let t=E.keywordPatternRe.exec(O),n="";for(;t;){n+=O.substring(e,t.index);const r=_.case_insensitive?t[0].toLowerCase():t[0],s=(i=r,E.keywords[i]);if(s){const[e,i]=s;if(A.addText(n),n="",l[r]=(l[r]||0)+1,l[r]<=7&&(I+=i),e.startsWith("_"))n+=t[0];else{const n=_.classNameAliases[e]||e;A.addKeyword(t[0],n)}}else n+=t[0];e=E.keywordPatternRe.lastIndex,t=E.keywordPatternRe.exec(O)}var i;n+=O.substring(e),A.addText(n)}function g(){null!=E.subLanguage?function(){if(""===O)return;let e=null;if("string"==typeof E.subLanguage){if(!t[E.subLanguage])return void A.addText(O);e=u(E.subLanguage,O,!0,S[E.subLanguage]),S[E.subLanguage]=e._top}else e=h(O,E.subLanguage.length?E.subLanguage:null);E.relevance>0&&(I+=e.relevance),A.addSublanguage(e._emitter,e.language)}():c(),O=""}function d(e,t){let n=1;const i=t.length-1;for(;n<=i;){if(!e._emit[n]){n++;continue}const i=_.classNameAliases[e[n]]||e[n],r=t[n];i?A.addKeyword(r,i):(O=r,c(),O=""),n++}}function p(e,t){return e.scope&&"string"==typeof e.scope&&A.openNode(_.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(A.addKeyword(O,_.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),O=""):e.beginScope._multi&&(d(e.beginScope,t),O="")),E=Object.create(e,{parent:{value:E}}),E}function b(e,t,n){let i=function(e,t){const n=e&&e.exec(t);return n&&0===n.index}(e.endRe,n);if(i){if(e["on:end"]){const n=new gt(e);e["on:end"](t,n),n.isMatchIgnored&&(i=!1)}if(i){for(;e.endsParent&&e.parent;)e=e.parent;return e}}if(e.endsWithParent)return b(e.parent,t,n)}function m(e){return 0===E.matcher.regexIndex?(O+=e[0],1):($=!0,0)}function x(e){const t=e[0],i=n.substring(e.index),r=b(E,e,i);if(!r)return gn;const s=E;E.endScope&&E.endScope._wrap?(g(),A.addKeyword(t,E.endScope._wrap)):E.endScope&&E.endScope._multi?(g(),d(E.endScope,e)):s.skip?O+=t:(s.returnEnd||s.excludeEnd||(O+=t),g(),s.excludeEnd&&(O=t));do{E.scope&&A.closeNode(),E.skip||E.subLanguage||(I+=E.relevance),E=E.parent}while(E!==r.parent);return r.starts&&p(r.starts,e),s.returnEnd?0:t.length}let k={};function w(t,s){const a=s&&s[0];if(O+=t,null==a)return g(),0;if("begin"===k.type&&"end"===s.type&&k.index===s.index&&""===a){if(O+=n.slice(s.index,s.index+1),!r){const t=new Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=k.rule,t}return 1}if(k=s,"begin"===s.type)return function(e){const t=e[0],n=e.rule,i=new gt(n),r=[n.__beforeBegin,n["on:begin"]];for(const s of r)if(s&&(s(e,i),i.isMatchIgnored))return m(t);return n.skip?O+=t:(n.excludeBegin&&(O+=t),g(),n.returnBegin||n.excludeBegin||(O=t)),p(n,e),n.returnBegin?0:t.length}(s);if("illegal"===s.type&&!i){const e=new Error('Illegal lexeme "'+a+'" for mode "'+(E.scope||"<unnamed>")+'"');throw e.mode=E,e}if("end"===s.type){const e=x(s);if(e!==gn)return e}if("illegal"===s.type&&""===a)return 1;if(R>1e5&&R>3*s.index){throw new Error("potential infinite loop, way more iterations than matches")}return O+=a,a.length}const _=f(e);if(!_)throw s.replace("{}",e),new Error('Unknown language: "'+e+'"');const v=on(_);let y="",E=a||v;const S={},A=new o.__emitter(o);!function(){const e=[];for(let t=E;t!==_;t=t.parent)t.scope&&e.unshift(t.scope);e.forEach((e=>A.openNode(e)))}();let O="",I=0,T=0,R=0,$=!1;try{for(E.matcher.considerAll();;){R++,$?$=!1:E.matcher.considerAll(),E.matcher.lastIndex=T;const e=E.matcher.exec(n);if(!e)break;const t=w(n.substring(T,e.index),e);T=e.index+t}return w(n.substring(T)),A.closeAllNodes(),A.finalize(),y=A.toHTML(),{language:e,value:y,relevance:I,illegal:!1,_emitter:A,_top:E}}catch(C){if(C.message&&C.message.includes("Illegal"))return{language:e,value:un(n),illegal:!0,relevance:0,_illegalBy:{message:C.message,index:T,context:n.slice(T-100,T+100),mode:C.mode,resultSoFar:y},_emitter:A};if(r)return{language:e,value:un(n),illegal:!1,relevance:0,errorRaised:C,_emitter:A,_top:E};throw C}}function h(e,n){n=n||o.languages||Object.keys(t);const i=function(e){const t={value:un(e),illegal:!1,relevance:0,_top:a,_emitter:new o.__emitter(o)};return t._emitter.addText(e),t}(e),r=n.filter(f).filter(m).map((t=>u(t,e,!1)));r.unshift(i);const s=r.sort(((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(f(e.language).supersetOf===t.language)return 1;if(f(t.language).supersetOf===e.language)return-1}return 0})),[l,c]=s,h=l;return h.secondBest=c,h}function g(e){let t=null;const i=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";const n=o.languageDetectRe.exec(t);if(n){const e=f(n[1]);return e||s.replace("{}",n[1]),e?n[1]:"no-highlight"}return t.split(/\s+/).find((e=>l(e)||f(e)))}(e);if(l(i))return;if(x("before:highlightElement",{el:e,language:i}),e.children.length>0&&(o.ignoreUnescapedHTML,o.throwUnescapedHTML)){throw new cn("One of your code blocks includes unescaped HTML.",e.innerHTML)}t=e;const r=t.textContent,a=i?c(r,{language:i,ignoreIllegals:!0}):h(r);e.innerHTML=a.value,function(e,t,i){const r=t&&n[t]||i;e.classList.add("hljs"),e.classList.add(`language-${r}`)}(e,i,a.language),e.result={language:a.language,re:a.relevance,relevance:a.relevance},a.secondBest&&(e.secondBest={language:a.secondBest.language,relevance:a.secondBest.relevance}),x("after:highlightElement",{el:e,result:a,text:r})}let d=!1;function p(){if("loading"===document.readyState)return void(d=!0);document.querySelectorAll(o.cssSelector).forEach(g)}function f(e){return e=(e||"").toLowerCase(),t[e]||t[n[e]]}function b(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach((e=>{n[e.toLowerCase()]=t}))}function m(e){const t=f(e);return t&&!t.disableAutodetect}function x(e,t){const n=e;i.forEach((function(e){e[n]&&e[n](t)}))}"undefined"!=typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",(function(){d&&p()}),!1),Object.assign(e,{highlight:c,highlightAuto:h,highlightAll:p,highlightElement:g,highlightBlock:function(e){return nn("10.7.0","highlightBlock will be removed entirely in v12.0"),nn("10.7.0","Please use highlightElement now."),g(e)},configure:function(e){o=hn(o,e)},initHighlighting:()=>{p(),nn("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){p(),nn("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(n,i){let s=null;try{s=i(e)}catch(o){if("Language definition for '{}' could not be registered.".replace("{}",n),!r)throw o;s=a}s.name||(s.name=n),t[n]=s,s.rawDefinition=i.bind(null,e),s.aliases&&b(s.aliases,{languageName:n})},unregisterLanguage:function(e){delete t[e];for(const t of Object.keys(n))n[t]===e&&delete n[t]},listLanguages:function(){return Object.keys(t)},getLanguage:f,registerAliases:b,autoDetection:m,inherit:hn,addPlugin:function(e){!function(e){e["before:highlightBlock"]&&!e["before:highlightElement"]&&(e["before:highlightElement"]=t=>{e["before:highlightBlock"](Object.assign({block:t.el},t))}),e["after:highlightBlock"]&&!e["after:highlightElement"]&&(e["after:highlightElement"]=t=>{e["after:highlightBlock"](Object.assign({block:t.el},t))})}(e),i.push(e)}}),e.debugMode=function(){r=!1},e.safeMode=function(){r=!0},e.versionString="11.6.0",e.regex={concat:Et,lookahead:_t,either:St,optional:yt,anyNumberOfTimes:vt};for(const k in Ft)"object"==typeof Ft[k]&&ut.exports(Ft[k]);return Object.assign(e,Ft),e}({}),pn=dn;dn.HighlightJS=dn,dn.default=dn;var fn=pn;const bn=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends"],mn=["true","false","null","undefined","NaN","Infinity"],xn=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],kn=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],wn=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],_n=["arguments","this","super","console","window","document","localStorage","module","global"],vn=[].concat(wn,xn,kn);fn.registerLanguage("json",(function(e){const t=["true","false","null"],n={scope:"literal",beginKeywords:t.join(" ")};return{name:"JSON",keywords:{literal:t},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},e.QUOTE_STRING_MODE,n,e.C_NUMBER_MODE,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE],illegal:"\\S"}})),fn.registerLanguage("php",(function(e){const t=e.regex,n=/(?![A-Za-z0-9])(?![$])/,i=t.concat(/[a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*/,n),r=t.concat(/(\\?[A-Z][a-z0-9_\x7f-\xff]+|\\?[A-Z]+(?=[A-Z][a-z0-9_\x7f-\xff])){1,}/,n),s={scope:"variable",match:"\\$+"+i},a={scope:"subst",variants:[{begin:/\$\w+/},{begin:/\{\$/,end:/\}/}]},o=e.inherit(e.APOS_STRING_MODE,{illegal:null}),l="[ \t\n]",c={scope:"string",variants:[e.inherit(e.QUOTE_STRING_MODE,{illegal:null,contains:e.QUOTE_STRING_MODE.contains.concat(a)}),o,e.END_SAME_AS_BEGIN({begin:/<<<[ \t]*(\w+)\n/,end:/[ \t]*(\w+)\b/,contains:e.QUOTE_STRING_MODE.contains.concat(a)})]},u={scope:"number",variants:[{begin:"\\b0[bB][01]+(?:_[01]+)*\\b"},{begin:"\\b0[oO][0-7]+(?:_[0-7]+)*\\b"},{begin:"\\b0[xX][\\da-fA-F]+(?:_[\\da-fA-F]+)*\\b"},{begin:"(?:\\b\\d+(?:_\\d+)*(\\.(?:\\d+(?:_\\d+)*))?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?"}],relevance:0},h=["false","null","true"],g=["__CLASS__","__DIR__","__FILE__","__FUNCTION__","__COMPILER_HALT_OFFSET__","__LINE__","__METHOD__","__NAMESPACE__","__TRAIT__","die","echo","exit","include","include_once","print","require","require_once","array","abstract","and","as","binary","bool","boolean","break","callable","case","catch","class","clone","const","continue","declare","default","do","double","else","elseif","empty","enddeclare","endfor","endforeach","endif","endswitch","endwhile","enum","eval","extends","final","finally","float","for","foreach","from","global","goto","if","implements","instanceof","insteadof","int","integer","interface","isset","iterable","list","match|0","mixed","new","never","object","or","private","protected","public","readonly","real","return","string","switch","throw","trait","try","unset","use","var","void","while","xor","yield"],d=["Error|0","AppendIterator","ArgumentCountError","ArithmeticError","ArrayIterator","ArrayObject","AssertionError","BadFunctionCallException","BadMethodCallException","CachingIterator","CallbackFilterIterator","CompileError","Countable","DirectoryIterator","DivisionByZeroError","DomainException","EmptyIterator","ErrorException","Exception","FilesystemIterator","FilterIterator","GlobIterator","InfiniteIterator","InvalidArgumentException","IteratorIterator","LengthException","LimitIterator","LogicException","MultipleIterator","NoRewindIterator","OutOfBoundsException","OutOfRangeException","OuterIterator","OverflowException","ParentIterator","ParseError","RangeException","RecursiveArrayIterator","RecursiveCachingIterator","RecursiveCallbackFilterIterator","RecursiveDirectoryIterator","RecursiveFilterIterator","RecursiveIterator","RecursiveIteratorIterator","RecursiveRegexIterator","RecursiveTreeIterator","RegexIterator","RuntimeException","SeekableIterator","SplDoublyLinkedList","SplFileInfo","SplFileObject","SplFixedArray","SplHeap","SplMaxHeap","SplMinHeap","SplObjectStorage","SplObserver","SplPriorityQueue","SplQueue","SplStack","SplSubject","SplTempFileObject","TypeError","UnderflowException","UnexpectedValueException","UnhandledMatchError","ArrayAccess","BackedEnum","Closure","Fiber","Generator","Iterator","IteratorAggregate","Serializable","Stringable","Throwable","Traversable","UnitEnum","WeakReference","WeakMap","Directory","__PHP_Incomplete_Class","parent","php_user_filter","self","static","stdClass"],p={keyword:g,literal:(e=>{const t=[];return e.forEach((e=>{t.push(e),e.toLowerCase()===e?t.push(e.toUpperCase()):t.push(e.toLowerCase())})),t})(h),built_in:d},f=e=>e.map((e=>e.replace(/\|\d+$/,""))),b={variants:[{match:[/new/,t.concat(l,"+"),t.concat("(?!",f(d).join("\\b|"),"\\b)"),r],scope:{1:"keyword",4:"title.class"}}]},m=t.concat(i,"\\b(?!\\()"),x={variants:[{match:[t.concat(/::/,t.lookahead(/(?!class\b)/)),m],scope:{2:"variable.constant"}},{match:[/::/,/class/],scope:{2:"variable.language"}},{match:[r,t.concat(/::/,t.lookahead(/(?!class\b)/)),m],scope:{1:"title.class",3:"variable.constant"}},{match:[r,t.concat("::",t.lookahead(/(?!class\b)/))],scope:{1:"title.class"}},{match:[r,/::/,/class/],scope:{1:"title.class",3:"variable.language"}}]},k={scope:"attr",match:t.concat(i,t.lookahead(":"),t.lookahead(/(?!::)/))},w={relevance:0,begin:/\(/,end:/\)/,keywords:p,contains:[k,s,x,e.C_BLOCK_COMMENT_MODE,c,u,b]},_={relevance:0,match:[/\b/,t.concat("(?!fn\\b|function\\b|",f(g).join("\\b|"),"|",f(d).join("\\b|"),"\\b)"),i,t.concat(l,"*"),t.lookahead(/(?=\()/)],scope:{3:"title.function.invoke"},contains:[w]};w.contains.push(_);const v=[k,x,e.C_BLOCK_COMMENT_MODE,c,u,b];return{case_insensitive:!1,keywords:p,contains:[{begin:t.concat(/#\[\s*/,r),beginScope:"meta",end:/]/,endScope:"meta",keywords:{literal:h,keyword:["new","array"]},contains:[{begin:/\[/,end:/]/,keywords:{literal:h,keyword:["new","array"]},contains:["self",...v]},...v,{scope:"meta",match:r}]},e.HASH_COMMENT_MODE,e.COMMENT("//","$"),e.COMMENT("/\\*","\\*/",{contains:[{scope:"doctag",match:"@[A-Za-z]+"}]}),{match:/__halt_compiler\(\);/,keywords:"__halt_compiler",starts:{scope:"comment",end:e.MATCH_NOTHING_RE,contains:[{match:/\?>/,scope:"meta",endsParent:!0}]}},{scope:"meta",variants:[{begin:/<\?php/,relevance:10},{begin:/<\?=/},{begin:/<\?/,relevance:.1},{begin:/\?>/}]},{scope:"variable.language",match:/\$this\b/},s,_,x,{match:[/const/,/\s/,i],scope:{1:"keyword",3:"variable.constant"}},b,{scope:"function",relevance:0,beginKeywords:"fn function",end:/[;{]/,excludeEnd:!0,illegal:"[$%\\[]",contains:[{beginKeywords:"use"},e.UNDERSCORE_TITLE_MODE,{begin:"=>",endsParent:!0},{scope:"params",begin:"\\(",end:"\\)",excludeBegin:!0,excludeEnd:!0,keywords:p,contains:["self",s,x,e.C_BLOCK_COMMENT_MODE,c,u]}]},{scope:"class",variants:[{beginKeywords:"enum",illegal:/[($"]/},{beginKeywords:"class interface trait",illegal:/[:($"]/}],relevance:0,end:/\{/,excludeEnd:!0,contains:[{beginKeywords:"extends implements"},e.UNDERSCORE_TITLE_MODE]},{beginKeywords:"namespace",relevance:0,end:";",illegal:/[.']/,contains:[e.inherit(e.UNDERSCORE_TITLE_MODE,{scope:"title.class"})]},{beginKeywords:"use",relevance:0,end:";",contains:[{match:/\b(as|const|function)\b/,scope:"keyword"},e.UNDERSCORE_TITLE_MODE]},c,u]}})),fn.registerLanguage("javascript",(function(e){const t=e.regex,n="[A-Za-z$_][0-9A-Za-z$_]*",i="<>",r="</>",s={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/,isTrulyOpeningTag:(e,t)=>{const n=e[0].length+e.index,i=e.input[n];if("<"===i||","===i)return void t.ignoreMatch();let r;">"===i&&(((e,{after:t})=>{const n="</"+e[0].slice(1);return-1!==e.input.indexOf(n,t)})(e,{after:n})||t.ignoreMatch());(r=e.input.substring(n).match(/^\s+extends\s+/))&&0===r.index&&t.ignoreMatch()}},a={$pattern:"[A-Za-z$_][0-9A-Za-z$_]*",keyword:bn,literal:mn,built_in:vn,"variable.language":_n},o="\\.([0-9](_?[0-9])*)",l="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",c={className:"number",variants:[{begin:`(\\b(${l})((${o})|\\.)?|(${o}))[eE][+-]?([0-9](_?[0-9])*)\\b`},{begin:`\\b(${l})\\b((${o})\\b|\\.)?|(${o})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},u={className:"subst",begin:"\\$\\{",end:"\\}",keywords:a,contains:[]},h={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,u],subLanguage:"xml"}},g={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,u],subLanguage:"css"}},d={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,u]},p={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:n+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},f=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,h,g,d,c];u.contains=f.concat({begin:/\{/,end:/\}/,keywords:a,contains:["self"].concat(f)});const b=[].concat(p,u.contains),m=b.concat([{begin:/\(/,end:/\)/,keywords:a,contains:["self"].concat(b)}]),x={className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:a,contains:m},k={variants:[{match:[/class/,/\s+/,n,/\s+/,/extends/,/\s+/,t.concat(n,"(",t.concat(/\./,n),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,n],scope:{1:"keyword",3:"title.class"}}]},w={relevance:0,match:t.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...xn,...kn]}},_={variants:[{match:[/function/,/\s+/,n,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[x],illegal:/%/},v={match:t.concat(/\b/,(y=[...wn,"super"],t.concat("(?!",y.join("|"),")")),n,t.lookahead(/\(/)),className:"title.function",relevance:0};var y;const E={begin:t.concat(/\./,t.lookahead(t.concat(n,/(?![0-9A-Za-z$_(])/))),end:n,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},S={match:[/get|set/,/\s+/,n,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},x]},A="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",O={match:[/const|var|let/,/\s+/,n,/\s*/,/=\s*/,/(async\s*)?/,t.lookahead(A)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[x]};return{name:"Javascript",aliases:["js","jsx","mjs","cjs"],keywords:a,exports:{PARAMS_CONTAINS:m,CLASS_REFERENCE:w},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,h,g,d,p,c,w,{className:"attr",begin:n+t.lookahead(":"),relevance:0},O,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[p,e.REGEXP_MODE,{className:"function",begin:A,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:a,contains:m}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:i,end:r},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:s.begin,"on:begin":s.isTrulyOpeningTag,end:s.end}],subLanguage:"xml",contains:[{begin:s.begin,end:s.end,skip:!0,contains:["self"]}]}]},_,{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[x,e.inherit(e.TITLE_MODE,{begin:n,className:"title.function"})]},{match:/\.\.\./,relevance:0},E,{match:"\\$"+n,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[x]},v,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},k,S,{match:/\$[(.]/}]}})),ct.setOptions({highlight:function(e){return fn.highlightAuto(e).value}});const yn=g({props:{md:{type:String,default:""}},setup(e){const t=d("");return D((()=>{t.value=ct(e.md)})),{mdHtml:t}}}),En=["innerHTML"];var Sn=j(yn,[["render",function(e,t,n,i,r,s){return P(),U("div",{class:"markdown",innerHTML:e.mdHtml},null,8,En)}]]);function An(e){const t=function(e){let t=[];const n=[];e.replace(/(#+)[^#][^\n]*?(?:\n)/g,(function(e,t){let i=e.replace("\n","");const r=t.length;return i=i.replace(/^#+/,"").replace(/\([^)]*?\)/,""),n.push({title:Z(i),level:r,children:[]}),""})),t=n.filter((e=>2===e.level||3===e.level));let i=0;return t.map((e=>(e.index=i++,e)))}(e);let n,i=[];return[2,3].forEach((e=>{var r;r={level:e},n=t.filter((e=>{for(const t in r)if(r.hasOwnProperty(t)&&r[t]!==e[t])return!1;return!0})),0===i.length?i=i.concat(n):n.forEach((e=>{e=Object.assign(e);const n=function(e,t){for(let n=t-1;n>=0;n--)if(e[t].level>e[n].level)return e[n].index}(t,e.index);return function(e,t,n){const i=function(e,t){let n=-1;return e.forEach(((e,i)=>{for(const n in t)if(t.hasOwnProperty(n)&&t[n]!==e[n])return!1;n=i})),n}(e,{index:t});e[i].children=e[i].children.concat(n)}(i,n,e)}))})),i}var On=j(g({components:{},props:{md:{type:String,default:""}},setup(e){const t=d(),n=d();D((()=>{n.value=An(e.md)}));return{navs:n,pageContainer:t,handleClick:e=>{e.preventDefault()}}}}),[["render",function(e,t,n,i,r,s){const a=Se,o=Ee;return P(),H(o,{getContainer:e.pageContainer,offsetTop:95,"target-offset":50,onClick:e.handleClick},{default:F((()=>[(P(!0),U(q,null,K(e.navs,((e,t)=>(P(),H(a,{key:t,href:`#${e.title}`,title:e.title},{default:F((()=>[e.children&&e.children.length?(P(!0),U(q,{key:0},K(e.children,((e,t)=>(P(),H(a,{key:t,href:`#${e.title}`,title:e.title},null,8,["href","title"])))),128)):G("",!0)])),_:2},1032,["href","title"])))),128))])),_:1},8,["getContainer","onClick"])}]]);const In=Y("div",null,null,-1),Tn=g({__name:"Modal",props:{onCancel:{type:Function,default:()=>{}},title:{type:String,default:""},md:{type:String,default:""}},setup(e){const t=e,n=Q(),{t:i}=W(),r=p({visible:!1,title:"",md:""});m((()=>{r.visible=!0}));const s=()=>{t.onCancel&&t.onCancel(),r.visible=!1};return(e,a)=>{const o=Sn,l=te,c=ne;return P(),U(q,null,[E(c,{visible:r.visible,width:V(n).device==V(ee).MOBILE?"90%":900,maskClosable:!1,title:t.title,destroyOnClose:"",onCancel:s},{footer:F((()=>[E(l,{onClick:s},{default:F((()=>[X(J(V(i)("common.close")),1)])),_:1})])),default:F((()=>[Y("div",null,[E(o,{md:t.md},null,8,["md"])])])),_:1},8,["visible","width","title"]),In],64)}}}),Rn=On,$n=e=>{const o=ie(Tn,(l=((e,t)=>{for(var n in t||(t={}))r.call(t,n)&&a(e,n,t[n]);if(i)for(var n of i(t))s.call(t,n)&&a(e,n,t[n]);return e})({},e),c={onCancel:()=>{e.onCancel&&e.onCancel(),u()}},t(l,n(c)))).use(re);var l,c;const u=()=>{o.unmount(),document.body.removeChild(h)},h=document.createElement("div");return document.body.appendChild(h),o.mount(h),o};const Cn={},Nn={class:"skeleton"},Mn=[se('<ul data-v-326733d8><li class="skeleton-title skeleton-sm" data-v-326733d8></li><li class="skeleton-xs" data-v-326733d8></li><li class="skeleton-inline" data-v-326733d8><div class="skeleton-xs" data-v-326733d8></div><div class="skeleton-sm" data-v-326733d8></div></li><li class="skeleton-title" data-v-326733d8></li><li class="skeleton-inline" data-v-326733d8><div class="skeleton-xs" data-v-326733d8></div><div class="skeleton-xs" data-v-326733d8></div><div class="skeleton-xs" data-v-326733d8></div><div class="skeleton-xs" data-v-326733d8></div></li><li class="skeleton-sm" data-v-326733d8></li><li data-v-326733d8></li><li data-v-326733d8></li><li data-v-326733d8></li><li class="skeleton-sm" data-v-326733d8></li><li data-v-326733d8></li><li data-v-326733d8></li><li data-v-326733d8></li><li class="skeleton-sm" data-v-326733d8></li><li data-v-326733d8></li><li data-v-326733d8></li><li data-v-326733d8></li></ul>',1)];var zn=j(Cn,[["render",function(e,t){return P(),U("div",Nn,Mn)}],["__scopeId","data-v-326733d8"]]);export{Sn as M,zn as S,Rn as a,$n as m};
