var e=Object.defineProperty,a=Object.defineProperties,r=Object.getOwnPropertyDescriptors,t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,n=(a,r,t)=>r in a?e(a,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[r]=t;import{k as l,d,aK as i,l as p,n as c,q as m,s as y,H as g,v as b,aL as f,c as u,aQ as O,y as v,aI as j,aM as k,aO as h}from"./index.d0b2fdb6.js";import{M as w,a as I,S as K}from"./Skeleton.aa5f4cc6.js";const P={class:j(["md-detail"])},D={key:2,class:"md-content-wraper"},M={key:3,class:"md-anchor-wraper"},S=d((x=((e,a)=>{for(var r in a||(a={}))o.call(a,r)&&n(e,r,a[r]);if(t)for(var r of t(a))s.call(a,r)&&n(e,r,a[r]);return e})({},{name:"MdDetail"}),a(x,r({setup(e){const a=i(),r=p(),t=c({detail:"",title:"",loading:!1,error:{config:{},isAxiosError:!1,toJSON:()=>({}),name:"",message:""}});return(()=>{const e=a.query;t.loading=!0,k.getDocDetail({appKey:e.appKey?e.appKey:r.appKey,path:e.key,lang:e.lang?e.lang:r.lang}).then((a=>{t.title=decodeURIComponent(e.title),t.detail=a.data.content,t.loading=!1})).catch((e=>{t.loading=!1,h(e).then((a=>{!1===a&&(t.error=e)}))}))})(),(e,a)=>(m(),y("div",P,[t.loading?(m(),g(K,{key:0})):!t.loading&&(t.error.response&&200!=t.error.response.status||!t.error.response&&t.error.message)?(m(),g(b(f),{key:1,error:t.error},null,8,["error"])):(m(),y("div",D,[u(b(w),{md:t.detail},null,8,["md"])])),b(r).device==b(O).MOBILE||t.loading?v("",!0):(m(),y("div",M,[u(b(I),{md:t.detail},null,8,["md"])]))]))}}))));var x,E=l(S,[["__scopeId","data-v-8aa3270a"]]);export{E as default};
