# UnifiedPackageOrderService 表结构迁移总结

## 修改概述

已成功将 `UnifiedPackageOrderService.php` 中的表名从旧版系统迁移到新版系统：

- `food_order` → `order_food`
- `food_order_item` → `order_food_item`

## 主要修改内容

### 1. 表名更新
- ✅ 所有 `food_order` 表名改为 `order_food`
- ✅ 所有 `food_order_item` 表名改为 `order_food_item`

### 2. 字段名适配

#### order_food 表字段变化：
- 主键：`order_id` → `order_food_id`
- 金额字段：`total_amount` → `sum_amount`
- 时间字段：`created_at/updated_at` → `create_time/update_time`
- 新增字段：
  - `delivery_num` - 配送数量
  - `cust_delivery_fee` - 客户支付配送费
  - `gov_fee` - 政府补贴金额
  - `discounts_text` - 折扣名称
  - `discounts_price` - 折扣金额
  - `user_longitude/user_latitude` - 用户经纬度

#### order_food_item 表字段变化：
- 主键：`item_id` → `order_food_item_id`
- 时间字段：`created_at/updated_at` → `create_time/update_time`

### 3. 数据结构适配

#### 订单ID处理：
- `order_food` 表使用自增的 `order_food_id` 作为主键
- `order_food` 表的 `order_id` 字段存储字符串格式的订单号
- `order_food_item` 表通过字符串 `order_id` 关联到 `order_food`

#### 生成的订单号格式：
```php
'order_id' => 'WX' . $orderInfo['order_id'] . '_' . $deliveryDate
```

### 4. 关键代码修改点

#### 创建 order_food 记录：
```php
$foodOrderData = [
    'order_id' => 'WX' . $orderInfo['order_id'] . '_' . $deliveryDate,
    'cust_uid' => $orderInfo['uid'],
    'employee_uid' => $isEmployee ? $orderInfo['uid'] : null,
    'delivery_time' => $deliveryDate,
    'delivery_num' => $totalNum,
    'package_type' => $isEmployee ? '员工餐' : '老年餐',
    'total_num' => $totalNum,
    'pay_price' => $totalAmount,
    'sum_amount' => $totalAmount,
    'order_status' => 0,
    'discounts_text' => $isEmployee ? '员工优惠' : null,
    'discounts_price' => $isEmployee ? ($orderInfoData['employee_discount'] ?? 0) : 0,
    // ... 其他字段
];
```

#### 创建 order_food_item 记录：
```php
$foodOrderItemData = [
    'order_id' => $foodOrderData['order_id'], // 使用字符串订单号
    'cust_uid' => $orderInfo['uid'],
    'employee_uid' => $isEmployee ? $orderInfo['uid'] : null,
    'package_type' => $isEmployee ? '员工餐' : '老年餐',
    'package_name' => $package['package_name'],
    // ... 其他字段
];
```

#### 关联关系更新：
```php
// 存储 order_food_id（数字）到 wx_store_order_package
'food_order_id' => $foodOrderInfo['order_food_id'],
'food_order_item_id' => $foodOrderItem['order_food_item_id']
```

### 5. 退款处理适配

在退款处理中，需要通过 `order_food_id` 查找对应的字符串 `order_id`：

```php
// 获取order_food的order_id（字符串）
$orderFood = $database->get('order_food', [
    'order_id'
], [
    'order_food_id' => $foodOrderId
]);

// 使用字符串order_id查询order_food_item
$totalAmount = $database->sum('order_food_item', 'pay_price', [
    'order_id' => $orderIdString
]);
```

## 测试建议

1. ✅ 验证套餐订单创建功能
2. ✅ 验证 order_food 和 order_food_item 记录正确生成
3. ✅ 验证关联关系正确建立
4. ✅ 验证退款功能正常工作
5. ✅ 验证员工订单和普通订单的区别处理

## 注意事项

1. **订单号格式**：新系统使用 `WX{order_id}_{date}` 格式生成唯一订单号
2. **主键关系**：`wx_store_order_package` 表存储的是 `order_food_id`（数字）
3. **查询关系**：`order_food_item` 查询需要使用字符串 `order_id`
4. **时间字段**：新系统使用 `create_time/update_time` 而不是 `created_at/updated_at`
5. **金额字段**：新系统使用 `sum_amount` 和 `pay_price` 两个字段存储金额信息

修改完成后，系统应该能够正确处理套餐订单的创建、同步和退款功能。
