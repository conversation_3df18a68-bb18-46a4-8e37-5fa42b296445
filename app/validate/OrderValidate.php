<?php

namespace app\validate;

use think\Validate;

class OrderValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'order_food_item_id' => 'require|integer|max:30',
        'num' => 'require|integer|max:30',

        'order_id' => 'require|integer|max:30',
        'cust_uid' =>  'integer|max:11|min:1',
        'user_longitude' => 'max:30',
        'user_latitude' => 'max:30',
        'user_address' => 'max:800',

        'delivery_option' =>  'chsDash|max:200|min:1',
        'delivery_site' =>  'chsDash|max:200|min:1',
        'delivery_fee' => 'require|array',
        'package_data' =>  'require',

        'package_type' => 'require|max:800',
        'employee_uid' =>  'integer|max:11|min:1',
        'remark' =>  'max:255|min:1',

        'page|页码' => 'require|integer',
        'limit|页数' => 'require|integer',
        'start_time' => 'require|date',
        'end_time' => 'require|date',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'order_id' => '订单号必须填写',
        'cust_uid' => '客户必须填写',
        'user_longitude' => '老人地址必须填写',
        'user_latitude' => '老人地址必须填写',
        'user_address' => '老人地址必须填写',

        'delivery_option' => '配送方式必须填写',
        'delivery_site' => '配送站点必须填写',
        'delivery_fee' => '送餐费必须填写',
        'employee_uid' => '请选择员工',

        'package_data' => '请选择套餐',
        'package_type' => '请选择套餐类型',
        'remark' => '备注太长了',

        'start_time' => '请输入正确的开始时间',
        'end_time' => '请输入正确的结束时间',

        'page' => '请输入正确的页码',
        'limit' => '请输入正确的页数',
    ];

    //定义场景
    protected $scene = [
        'createOrder'  =>  [
            'cust_uid',
            'user_longitude',
            'user_latitude',
            'user_address',

            'delivery_option',
            'delivery_site',
            'delivery_fee',
            'employee_uid',


            'remark',
            'package_data',
            'package_type',
        ],
        'getFoodOrderList' => [
            'page',
            'limit',
            'cust_uid',
            'delivery_site',
            'delivery_option',
            'employee_uid',
            'start_time',
            'end_time'
        ],
        'updateFoodOrderConfig' => [
            'order_id',
            'user_longitude',
            'user_latitude',
            'user_address',
            'delivery_option',
            'delivery_site',
            'employee_uid',
            'remark',
            'package_type',
        ],
        'updateFoodOrder' => [
            'order_id',
            'delivery_option',
            'delivery_site',
            'employee_uid',
        ],
        'updateFoodOrderNum' => [
            'order_food_item_id',
            'num',
        ],
        'getOrderInfo' => [
            'order_id',
        ],
    ];
}