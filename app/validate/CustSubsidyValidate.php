<?php

namespace app\validate;

use think\Validate;

class CustSubsidyValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'limit|分页条数' => 'require|integer',
        'page|分页' => 'require|integer',
        'month|月份' => 'require',

        'name|姓名或手机号'  =>  'max:80|min:1',
        'balance_id|账号id' => 'require|integer',
        'cust_uid|用户id' => 'require|integer',
        'payment_id|缴费记录id' => 'require|integer',

        'paymentVoucherNumber|缴费凭证单号' => 'chsAlphaNum|max:40',
        'paymentVoucherImage|缴费凭证图片' => 'url',
        'amountDue|应缴费金额' => 'require|float|max:10',
        'amountPaid|实际缴费金额' => 'require|float|max:10',
        'paymentMethod|缴费方式' => 'max:99',
        'paymentTime|缴费时间' => 'require|date',
        'paymentStatus|缴费状态' => 'chsAlphaNum|max:40',
        'adminRemark|审核员备注' => 'max:800',
        'serviceType|服务分类' => 'max:255',
        'serviceRemark|服务备注' => 'max:800',

        'title|内容' => 'require|max:255',
        'category|类型' => 'require|max:255',
        'money|金额' => 'require|float|max:10',
        'usage_date|使用日期' => 'require|date',
    ];


    //定义场景
    protected $scene = [
        'list'  =>  ['limit','page'],
        'queryAccount'  =>  ['name','limit','page'],
        'queryBalanceRecord' => ['balance_id','limit','page','name','paymentStatus','paymentMethod'],
        'AddPaymentRecord' => [
            'balance_id',
            'uid',
            'paymentVoucherNumber',
            'paymentVoucherImage',
            'amountDue',
            'amountPaid',
            'paymentMethod',
            'paymentTime',
            'paymentStatus',
            'serviceType',
            'serviceRemark'
        ],
        'queryPaymentInfo' => ['payment_id'],
        'editPaymentRecord' => [
            'payment_id',
            'paymentVoucherNumber',
            'paymentVoucherImage',
            'amountDue',
            'amountPaid',
            'paymentMethod',
            'paymentTime',
            'paymentStatus',
            'serviceType',
            'serviceRemark',
        ],
        'getInfo' => [
            'limit',
            'page',
            'month',
            'uid'
        ],
        'createBill' => [
            'uid',
            'title',
            'category',
            'money',
            'usage_date',
        ]
    ];
}