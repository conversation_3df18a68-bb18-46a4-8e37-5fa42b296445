<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\validate;

use think\Validate;

/**
 * 套餐包副表-套餐菜品表验证器
 */
class FoodPackageItemValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'food_id' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'food_id' => '菜品编号必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'food_id',
        ],
        'update' => [
            'food_id',
        ],
    ];

}
