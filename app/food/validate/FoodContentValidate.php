<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\validate;

use think\Validate;

/**
 * 菜品管理验证器
 */
class FoodContentValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'name' => 'require',
        'cate_name' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'name' => '菜品名称必须填写',
        'cate_name' => '分类名称必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'name',
            'cate_name',
        ],
        'update' => [
            'name',
            'cate_name',
        ],
    ];

}
