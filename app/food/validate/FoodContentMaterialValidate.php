<?php
// +----------------------------------------------------------------------
// | 科苑养老管理系统
// +----------------------------------------------------------------------
// | Author: Yang
// +----------------------------------------------------------------------
namespace app\food\validate;

use think\Validate;

/**
 * 菜品关联原材料表验证器
 */
class FoodContentMaterialValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'content_id' => 'require',
        'name' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'content_id' => '菜品编号必须填写',
        'name' => '原材料名称必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'content_id',
            'name',
        ],
        'update' => [
            'content_id',
            'name',
        ],
    ];

}
