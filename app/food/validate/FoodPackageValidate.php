<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\validate;

use think\Validate;

/**
 * 周套餐设置验证器
 */
class FoodPackageValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'name' => 'require',
        'start_time'  =>  'require|date',
        'end_time' =>  'require|date',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'name' => '套餐名称必须填写',
        'start_time' => '请输入开始时间',
        'end_time' => '请输入结束时间',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'name',
        ],
        'update' => [
            'name',
        ],
        'init' => [
            'start_time',
            'end_time'
        ],
    ];

}
