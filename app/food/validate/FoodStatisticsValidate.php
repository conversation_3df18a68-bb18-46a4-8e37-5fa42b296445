<?php

namespace app\food\validate;


use think\Validate;


class FoodStatisticsValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'delivery_site|驿站名称' => 'chsAlphaNum|max:40',
        'id|id' => 'require|integer',
        'page|分页条数' => 'require|integer',
        'limit|分页' => 'require|integer',
        'delivery_site|站点' => 'chsAlphaNum|max:40',
        'delivery_option|取餐方式' => 'chsAlphaNum|max:40',
        'time|统计时间' => 'require',
        'employee_uid' =>  'require|integer|max:11|min:1',
        'start_time|开始时间' =>  'require|date',
        'end_time|结束时间' =>  'require|date',
    ];

    // 定义信息
    protected $message  =   [

    ];

    //定义场景
    protected $scene = [
        'getOrderList'=>['delivery_site','time','page','limit','delivery_option'],
        'exportOrderList'=>['delivery_site','time','delivery_option'],
        'getDeliveryList'=>['employee_uid','page','limit','time'],
        'exportDeliveryList'=>['time'],
        'hotel'=>['time'],
        'getBalanceList'=>['start_time','end_time'],
        'getStaffTable'=>['start_time','end_time'],
        'getOrderPersonList'=>[
            'start_time',
            'end_time',
            'page',
            'limit',
        ],
        'exportOrderPersonList'=>[
            'start_time',
            'end_time',
        ],
        'getSiteList' => [
            'delivery_site',
            'start_time',
            'end_time',
        ]
    ];
}