<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\validate;

use think\Validate;

/**
 * 套餐紧急订单表验证器
 */
class FoodEmergencyValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'label' => 'require',
        'type' => 'require',
        'id' => 'require|integer|max:30',
        'auditor_status' =>  'chsDash|max:40|min:1',

        'delivery_option' =>  'require|chsDash|max:200|min:1',
        'order_food_item_id' =>  'require|integer|max:11|min:1',
        'num' =>  'require|integer|max:11|min:0',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'label' => '标签必须填写',
        'type' => '(1:添加、-1:退餐)必须填写',
        'id' => 'id必须填写',
        'auditor_status' => '状态必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'label',
            'type',
        ],
        'update' => [
            'label',
            'type',
        ],
        'changeStatus' => [
            'id',
            'auditor_status'
        ],
        'editItemOrder'  =>  ['order_food_item_id','num'],
    ];

}
