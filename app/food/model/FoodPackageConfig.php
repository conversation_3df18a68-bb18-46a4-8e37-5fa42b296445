<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 套餐模板模型
 */
class FoodPackageConfig extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'config_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'food_package_config';

    
    /**
     * 名称 搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }


}
