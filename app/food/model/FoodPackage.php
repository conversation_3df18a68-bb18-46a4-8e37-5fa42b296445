<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\model;

use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasMany;
use think\model\relation\HasOne;

use app\food\model\FoodPackageItem;

/**
 * 周套餐设置模型
 */
class FoodPackage extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'package_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'food_package';

    
    /**
     * 套餐名称 搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }
    
    /**
     * 有效日期 搜索
     */
    public function searchDayTimeAttr($query, $value)
    {
        $query->whereTime('day_time', 'between', $value);
    }

    /**
     * 一对多关联套餐子订单带食品元素
     */
    public function foods(): HasMany
    {
        return $this->hasMany(FoodPackageItem::class,'package_id','package_id')
            ->field('id,package_id,package_id,food_id,food_name,food_cate,num');
    }

}
