<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\model;

use plugin\saiadmin\basic\BaseModel;

/**
 *  原料管理模型
 */
class FoodMaterial extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'material_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'food_material';

    
    /**
     * 原材料名称 搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }


}
