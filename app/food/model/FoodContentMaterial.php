<?php
// +----------------------------------------------------------------------
// | 科苑养老管理系统
// +----------------------------------------------------------------------
// | Author: Yang
// +----------------------------------------------------------------------
namespace app\food\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 菜品关联原材料表模型
 */
class FoodContentMaterial extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'food_content_material';

    
    /**
     * 原材料名称 搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }


    /**
     * 关联模型material
     */
    public function material()
    {
        return $this->hasOne(FoodMaterial::class, 'material_id', 'material_id');
    }


}
