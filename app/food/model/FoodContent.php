<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 菜品管理模型
 */
class FoodContent extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'food_content';

    
    /**
     * 菜品名称 搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }
    
    /**
     * 分类名称 搜索
     */
    public function searchCateNameAttr($query, $value)
    {
        $query->where('cate_name', 'like', '%'.$value.'%');
    }


    /**
     * 关联模型content
     */
    public function content()
    {
        return $this->hasMany(FoodContentMaterial::class, 'content_id', 'id');
    }


}
