<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\model;

use plugin\saiadmin\app\model\system\SystemUser;
use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasOne;

/**
 * 套餐紧急订单表模型
 */
class FoodEmergency extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'food_emergency';

    /**
     * 设置json类型字段
     */
    protected $json = ['content','delivery_fee'];


    /**
     * 一对一关联 创建人信息
     * @return HasOne
     */
    public function auditInfo(): HasOne
    {
        return $this->hasOne(SystemUser::class,'id','auditor_uid')->field('id,nickname');
    }

}
