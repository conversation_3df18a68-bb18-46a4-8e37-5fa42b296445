<?php
// +----------------------------------------------------------------------
// | 科苑养老管理系统
// +----------------------------------------------------------------------
// | Author: Yang
// +----------------------------------------------------------------------
namespace app\food\controller;

use app\controller\BaseController;
use app\domain\model\OrderFoodItem;
use app\utils\Excel;
use support\Request;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use <PERSON><PERSON>\ExceptionHandler\Exception\BadRequestHttpException;
use Exception;
use app\domain\service\FoodStatisticsService;

/**
 * 菜品统计控制器
 * @Apidoc\Title("菜品统计")
 */
class FoodStatisticsController extends BaseController
{
    /**
     * 菜品统计
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws \think\db\exception\DbException
     */
    public function getOrderList(Request $request): Response
    {
        try {
            validate( \app\food\validate\FoodStatisticsValidate::class)
                ->scene('getOrderList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'delivery_site',
            'delivery_option',
            'time',
            'page',
            'limit',
        ]);
        $data = FoodStatisticsService::getOrderList($params);
        return $this->success($data);
    }

    /**
     * 导出订餐名单
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws \think\db\exception\DbException
     */
    public function exportOrderList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('exportOrderList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'delivery_site',
            'time',
            'delivery_option'
        ]);
        $data = FoodStatisticsService::getOrderList($params);
        // 创建导出表
        $title = (!empty($params['delivery_site']) ? $params['delivery_site'] : '全部站点') . '订餐名单_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '供餐日期',
            '供餐站点',
            '姓名',
            '联系方式',
            '地址',
            '取餐方式',
        ];
        $headers = array_merge($headers,$data['packageName']);
        $headers[] = '金额';
        $headers[] = '备注';
        // 向导出表尾部追加总额
        $popArray = [
            '总计',
            '',
            '',
            '',
            '',
            '',
            '',
        ];
        $popArray = array_merge($popArray,$data['totalAmount']);
        $data['row'][] = $popArray;
        $url = Excel::export($title,$headers,$data['row']);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 获取送餐名单-天
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDeliveryList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getDeliveryList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'employee_uid',
            'time',
            'page',
            'limit',
        ]);
        $data = FoodStatisticsService::getDeliveryList($params);
        return $this->success($data);
    }

    /**
     * 导出送餐名单-天
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function exportDeliveryList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('exportDeliveryList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'employee_uid',
            'time',
        ]);
        $data = FoodStatisticsService::getDeliveryList($params);
        // 创建导出表
        $title = '送餐员名单(天)_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '送餐时间',
            '送餐员',
            '姓名',
            '联系方式',
            '地址',
        ];
        $headers = array_merge($headers,$data['packageName']);
        $headers[] = '送餐费用';
        $headers[] = '订餐人';
        // 向导出表尾部追加总额
        $popArray = [
            '合计',
            '',
            '',
            '',
            '',
            '',
        ];
        $popArray = array_merge($popArray,$data['totalAmount']);
        $data['row'][] = $popArray;
        $url = Excel::export($title,$headers,$data['row']);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 获取送餐名单-月
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDeliveryMonthList(Request $request): Response
    {
        $params = $request->only([
            'employee_uid',
            'time',
            'page',
            'limit',
        ]);
        $data = FoodStatisticsService::getDeliveryMonthList($params);
        return $this->success($data);
    }

    /**
     * 导出送餐名单 月
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function exportDeliveryMonthList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('exportDeliveryList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'employee_uid',
            'time',
        ]);
        $data = FoodStatisticsService::getDeliveryMonthList($params);
        // 创建导出表
        $title = '送餐员名单_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '送餐时间',
            '服务站点',
            '老人姓名',
            '老人地址',
            '送餐员',
            '送餐份数',
            '送餐费用',
            '备注',
            '订餐人'
        ];
        // 向导出表尾部追加总额
        $data['row'][] = [
            '合计',
            '',
            '',
            '',
            '',
            '',
            '',
            $data['totalAmount']
        ];
        $url = Excel::export($title,$headers,$data['row']);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 获取酒店报表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getHotelList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getBalanceList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'start_time',
            'end_time'
        ]);
        // 获取送餐统计总表
        $data['staffData'] = FoodStatisticsService::getTotalDelivery($params);
        // 获取统计总表
        $data['siteData'] = FoodStatisticsService::getSiteStatistics($params);
        return $this->success($data);
    }

    /**
     * 导出酒店报表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function exportHotelList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getBalanceList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'start_time',
            'end_time'
        ]);
        $data = FoodStatisticsService::exportHotel($params);
        return $this->success([
            'url' => $data,
        ]);
    }

    /**
     * 订餐人数据统计接口
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function getOrderPersonList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getOrderPersonList')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'id',
            'start_time',
            'end_time',
            'page',
            'limit',
        ]);
        $data = FoodStatisticsService::getOrderPersonList($params);
        return $this->success($data);
    }

    /**
     * 导出订餐人数据统计接口
     * @param Request $request
     * @return Response
     * @throws Exception
     */
    public function exportOrderPersonList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('exportOrderPersonList')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'id',
            'start_time',
            'end_time',
        ]);
        $data = FoodStatisticsService::getOrderPersonList($params);
        if(empty($data)) {
            throw new BadRequestHttpException('暂无数据');
        }
        // 创建导出表
        $title = '订餐人统计_'.date('YmdHis').'.xlsx';
        $headers = array_keys($data[0]);
        $url = Excel::export($title,$headers,$data);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 订餐人统计明细
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function getOrderPersonDetail(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getOrderPersonList')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'id',
            'start_time',
            'end_time',
            'page',
            'limit',
        ]);
        $data = FoodStatisticsService::getOrderPersonDetail($params);
        return $this->success($data);
    }

    /**
     * 获取财务报表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getBalanceList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getBalanceList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'start_time',
            'end_time'
        ]);
        $data = FoodStatisticsService::getTable($params);
        return $this->success($data);
    }

    /**
     * 导出财务报表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function exportBalanceList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getBalanceList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'start_time',
            'end_time'
        ]);
        $data = FoodStatisticsService::exportBalance($params);
        return $this->success($data);
    }

    /**
     * 订餐人统计明细导出
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function exportOrderPersonDetail(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('exportOrderPersonList')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'id',
            'start_time',
            'end_time',
        ]);
        $data = FoodStatisticsService::getOrderPersonDetail($params);
        if(empty($data)) {
            throw new BadRequestHttpException('暂无数据');
        }
        // 创建导出表
        $title = '订餐人统计明细_'.date('YmdHis').'.xlsx';
        $headers = [
            '年月',
            '老人姓名',
            '评估等级',
            '订餐份数',
            '取餐方式',
            '收费金额',
            '订餐人',
        ];
        $url = Excel::export($title,$headers,$data);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 导出酒店数据表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getHotelV2(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getBalanceList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        // 查询当天套餐以及关联菜品信息
        $params = $request->only([
            'start_time',
            'end_time'
        ]);
        $data = FoodStatisticsService::getHotelMaterial($params);
        return $this->success($data);
    }

    /**
     * 获取送餐员数据表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function getStaffTable(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getStaffTable')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'start_time',
            'end_time',
            'employee_uid'
        ]);
        $data = FoodStatisticsService::getStaffTable($params);
        return $this->success($data);
    }

       /**
     * 驿站数据统计接口
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function getSiteList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getSiteList')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'site_name',
            'start_time',
            'end_time',
            'delivery_option',
        ]);
        $data = FoodStatisticsService::getStationData($params);
        return $this->success($data);
    }

    /**
     * 导出驿站数据统计接口
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function exportSiteList(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getSiteList')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'site_name',
            'start_time',
            'end_time',
            'delivery_option',
        ]);
        $data = FoodStatisticsService::getStationData($params);
        if(empty($data)) {
            throw new BadRequestHttpException('暂无数据');
        }
        // 创建导出表
        $title = '驿站统计总表_'.date('YmdHis').'.xlsx';
        $headers = array_keys($data[0]);
        $url = Excel::export($title,$headers,$data);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 导出送餐员数据表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function exportStaffTable(Request $request): Response
    {
        try {
            validate(\app\food\validate\FoodStatisticsValidate::class)
                ->scene('getStaffTable')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'start_time',
            'end_time',
            'employee_uid'
        ]);
        $data = FoodStatisticsService::getStaffTable($params);
        if(empty($data)) {
            throw new BadRequestHttpException('暂无数据');
        }
        // 创建导出表
        $title = '送餐员统计_'.date('YmdHis').'.xlsx';
        $headers = array_keys($data[0]);
        $url = Excel::export($title,$headers,$data);
        return $this->success([
            'url' => $url,
        ]);
    }
}
