<?php
// +----------------------------------------------------------------------
// | 科苑养老管理系统
// +----------------------------------------------------------------------
// | Author: Yang
// +----------------------------------------------------------------------
namespace app\food\controller;

use plugin\saiadmin\basic\BaseController;
use app\food\logic\FoodCategoryLogic;
use app\food\validate\FoodCategoryValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;

/**
 * 菜品分类控制器
 * @Apidoc\Title("菜品分类")
 */
class FoodCategoryController extends BaseController
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new FoodCategoryLogic();
        $this->validate = new FoodCategoryValidate;
        parent::__construct();
    }

    /**
     * 数据列表
     * @Apidoc\Title("菜品分类列表查询")
     * @Apidoc\Url("/food/category/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("name", type="varchar",require=true, desc="分类名称")
     * @Apidoc\Returned("data", type="array", desc="列表")
     * @Apidoc\Returned("current_page", type="int", desc="当前页")
     * @Apidoc\Returned("data", type="array", desc="列表")
     * @Apidoc\Returned("last_page", type="int", desc="最后页")
     * @Apidoc\Returned("per_page", type="int", desc="每页数量")
     * @Apidoc\Returned("total", type="int", desc="总页数")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['name', ''],
        ]);
        $data = $this->logic->tree($where);
        return $this->success($data);
    }

    /**
     * 保存数据
     * @Apidoc\Title("菜品分类保存数据")
     * @Apidoc\Url("/food/category/save")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            $this->afterChange('save');
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * 修改数据
     * @Apidoc\Title("菜品分类修改数据")
     * @Apidoc\Url("/food/category/update/{id}")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int",require=true, desc="ID")
     * @param $id
     * @param Request $request
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            $this->afterChange('update');
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * 删除数据
     * @Apidoc\Title("菜品分类删除数据")
     * @Apidoc\Url("/food/category/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Query("ids", type="string",require=true, desc="ids,多个用逗号隔开")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            $this->afterChange('destroy');
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * 读取信息
     * @Apidoc\Title("菜品分类读取信息")
     * @Apidoc\Url("/food/category/read/{id}")
     * @Apidoc\Query("id", type="string",require=true, desc="ID")
     * @Apidoc\Method("GET")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $model = $this->logic->find($id);
        if ($model) {
            return $this->success($model->toArray());
        } else {
            return $this->fail('未查找到信息');
        }
    }

}
