<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\controller;

use app\domain\model\OrderFood;
use app\domain\service\FoodOrderService;
use app\food\model\FoodEmergency;
use DI\Attribute\Inject;
use Exception;
use plugin\saiadmin\basic\BaseController;
use app\food\logic\FoodEmergencyLogic;
use app\food\validate\FoodEmergencyValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use yzh52521\WebmanLock\Locker;

/**
 * @Apidoc\Title("套餐紧急订单表")
 */
class FoodEmergencyController extends BaseController
{
    protected string $pk = "id";

    #[Inject]
    private FoodOrderService $service;

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new FoodEmergencyLogic();
        $this->validate = new FoodEmergencyValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/food/FoodEmergency/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("auditor_status", type="varchar", require=false, desc="审核状态", default="")
     * @Apidoc\Query("created_by", type="int", require=false, desc="创建人", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['auditor_status', ''],
            ['created_by', ''],
        ]);
        $query = $this->logic->search($where)->with(['auditInfo']);
        $data = $this->logic->getList($query);

        $data1 =  $data['data'];
        if (!empty($data1)) {
            foreach ($data1 as  &$v) {
                $result1 = [];
                $content = $v['content'];
                if($v['type'] == 1){
                    foreach ($content as $item) {
                        // 检查 $item[0] 是否存在，并且 $item[0]['num'] 是否不为 0
                        if (isset($item[0]) && isset($item[0]['num']) && $item[0]['num'] != 0) {
                            $v1 = $item[0];
                            if(!empty($v1)){
                                $text1 = $v1['dayTime'] . '增加'.  $v1['num'] . '份' . $v1['name']  . '套餐';
                                $result1[] = [
                                    'name' => $v1['name'],// 套餐名称
                                    'title' =>'套餐增加',
                                    'num' => $v1['num'],// 套餐数量
                                    'time' =>$v1['dayTime'],// 日期
                                    'remark' => $text1,// 有效日期
                                ];
                            }
                        }
                    }
                }else{
                    // 强制转换为数组
                    $v1 = (array) $content;
                    if(!empty($v1)){
                        // stdClass 对象   $v1->time  获取值
                        $text1 = $v1['time'] . '减少' .  ($v1['diffNum']*-1) . '份' . $v1['packageName']  . '套餐';
                        $result1[] = [
                            'name' => $v1['packageName'],// 套餐名称
                            'title' => '套餐取消',
                            'num' => ($v1['diffNum']*-1),// 套餐数量
                            'time' =>$v1['time'],// 日期
                            'remark' => ($v1['title']??$text1),// 有效日期
                        ];
                    }
                }
                $v['content'] =  $result1;
                $v['type'] = $v['type'] == 1 ? '加餐' : ($v['type'] == -1 ? '退餐' : '');

                // 查询用户信息
                $custInfo = $this->dao->get('cust_user',[ 'cust_uid' => $v['cust_uid'], ], ['cust_uid', 'cust_name','cust_private_phone' ]);
                $v['cust_name']  = $custInfo["cust_name"];
                $v['cust_phone']  = $custInfo["cust_private_phone"];
            }
            unset($v); // 释放引用，避免后续影响
        }

        $data['data'] = $data1;
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/food/FoodEmergency/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("label", type="varchar", require=false, desc="标签", default="")
     * @Apidoc\Query("type", type="tinyint", require=false, desc="(1:添加、-1:退餐)", default="")
     * @Apidoc\Query("content", type="text", require=false, desc="内容", default="")
     * @Apidoc\Query("delivery_option", type="varchar", require=false, desc="配送方式", default="")
     * @Apidoc\Query("delivery_site", type="varchar", require=false, desc="配送站点", default="")
     * @Apidoc\Query("package_type", type="varchar", require=false, desc="套餐类型", default="")
     * @Apidoc\Query("delivery_type", type="varchar", require=false, desc="送餐费支付方式", default="")
     * @Apidoc\Query("delivery_fee", type="text", require=false, desc="配送费", default="")
     * @Apidoc\Query("delivery_start_date", type="date", require=false, desc="配送开始日期", default="")
     * @Apidoc\Query("user_address", type="varchar", require=false, desc="老人地址", default="")
     * @Apidoc\Query("cust_uid", type="int", require=false, desc="订餐老人", default="")
     * @Apidoc\Query("employee_uid", type="int", require=false, desc="送餐人员", default="")
     * @Apidoc\Query("auditor_status", type="varchar", require=false, desc="审核状态", default="")
     * @Apidoc\Query("payment_method", type="varchar", require=false, desc="支付方式", default="")
     * @Apidoc\Query("discount_amount", type="varchar", require=false, desc="补贴金额-当天", default="")
     * @Apidoc\Query("discounts_price", type="varchar", require=false, desc="优惠金额-当天", default="")
     * @Apidoc\Query("payment_amount", type="varchar", require=false, desc="支付金额-当天", default="")
     * @Apidoc\Query("discount_setting", type="varchar", require=false, desc="折扣设置", default="")
     * @Apidoc\Query("remark", type="varchar", require=false, desc="特殊要求", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/food/FoodEmergency/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("label", type="varchar", require=false, desc="标签", default="")
     * @Apidoc\Query("type", type="tinyint", require=false, desc="(1:添加、-1:退餐)", default="")
     * @Apidoc\Query("content", type="text", require=false, desc="内容", default="")
     * @Apidoc\Query("delivery_option", type="varchar", require=false, desc="配送方式", default="")
     * @Apidoc\Query("delivery_site", type="varchar", require=false, desc="配送站点", default="")
     * @Apidoc\Query("package_type", type="varchar", require=false, desc="套餐类型", default="")
     * @Apidoc\Query("delivery_type", type="varchar", require=false, desc="送餐费支付方式", default="")
     * @Apidoc\Query("delivery_fee", type="text", require=false, desc="配送费", default="")
     * @Apidoc\Query("delivery_start_date", type="date", require=false, desc="配送开始日期", default="")
     * @Apidoc\Query("user_address", type="varchar", require=false, desc="老人地址", default="")
     * @Apidoc\Query("cust_uid", type="int", require=false, desc="订餐老人", default="")
     * @Apidoc\Query("employee_uid", type="int", require=false, desc="送餐人员", default="")
     * @Apidoc\Query("auditor_status", type="varchar", require=false, desc="审核状态", default="")
     * @Apidoc\Query("payment_method", type="varchar", require=false, desc="支付方式", default="")
     * @Apidoc\Query("discount_amount", type="varchar", require=false, desc="补贴金额-当天", default="")
     * @Apidoc\Query("discounts_price", type="varchar", require=false, desc="优惠金额-当天", default="")
     * @Apidoc\Query("payment_amount", type="varchar", require=false, desc="支付金额-当天", default="")
     * @Apidoc\Query("discount_setting", type="varchar", require=false, desc="折扣设置", default="")
     * @Apidoc\Query("remark", type="varchar", require=false, desc="特殊要求", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("申请退餐")
     * @Apidoc\Url("/food/FoodEmergency/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("label", type="varchar", require=false, desc="标签", default="")
     * @Apidoc\Query("type", type="tinyint", require=false, desc="(1:添加、-1:退餐)", default="")
     * @Apidoc\Query("num", type="int", require=false, desc="修改后数量", default="")
     * @Apidoc\Query("item_id", type="int", require=false, desc="套餐订单附属表id", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function decrease(Request $request) : Response
    {
        $data = $request->post();

        $num = $data['num'];
        $item_id = $data['item_id'];
        // $order_id = $this->dao->get('order_food',[ 'order_food_id' => $data['item_id'] ],'order_id');

        // 获取对应套餐附属订单表
        $order_food = $this->dao->search('order_food',[
            'order_food_id' => $data['item_id'] , 
        ],[
            'order_id',// 主订单id
            'delivery_time',// 配送日期
            'num',// 数量
            'price',// 单价
            'package_name',// 套餐名称
            'cust_uid',// 订餐人
        ]);
        $content = '';
        $cust_uid = '';
        if(!empty($order_food)){
            $food = $order_food[0];
            
            $num1 = $num - $food['num'];
            $text1 = $food['delivery_time'] . ' 减少' .  (-1 * $num1) . '份 ' . $food['package_name']  . ' 套餐';
            
            // 创建一个对象
            $object = new \stdClass();
            $object->order_id = $food['order_id'];
            $object->item_id =$item_id;
            $object->num =   $num;
            $object->diffNum = $num1;
            $object->title = $text1;
            $object->category = "套餐取消";
            $object->diffMoney =  $num1 * (float)$food['price'];// 强制转换为浮点数
            $object->paymentStatus = "已支付";
            $object->time = $food['delivery_time'];
            $object->packageName = $food['package_name'];

            // var_dump('$object-=-=-=---------------',$object);

            // 将对象转换为 JSON 字符串
            $content = json_encode($object, JSON_PRETTY_PRINT);
            
            $cust_uid = $food['cust_uid']; // 订餐人
        }

        $data1 = [
            'type'=>-1,// 退餐
            'label'=> $data['label'],// 标签
            'cust_uid'=>$cust_uid,// 退餐人
            'content'=>$content,// 内容
        ];

        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data1)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data1);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/food/FoodEmergency/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/food/FoodEmergency/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("auditor_status", type="int", require=true, desc="审核状态", default="1")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function changeStatus(Request $request) : Response
    {
        try {
            validate(\app\food\validate\FoodEmergencyValidate::class)
                ->scene('changeStatus')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'admin_remark',
            'id',
            'auditor_status'
        ]);
        $orderModel = new OrderFood();
        $emergencyInfo = $this->dao->get('food_emergency',[
            'id' => $params['id'],
            'delete_time' => null
        ],[
            'id',
            'type',
            'is_wx_order',
            'content[JSON]',
            'cust_uid',
            'delivery_fee[JSON]',
            'auditor_status',
            'admin_remark',
            'delivery_option',
            'delivery_site',
            'employee_uid',
            'remark',
            'package_type',
            'created_by',
            'discounts_price',
        ]);
        if (empty($emergencyInfo)) {
            throw new BadRequestHttpException('记录不存在');
        }
        if ($emergencyInfo['auditor_status'] == '已通过') {
            throw new BadRequestHttpException('审核已通过');
        }
        if($emergencyInfo['type'] == 1 && empty($emergencyInfo['delivery_option'])) {
            throw new BadRequestHttpException('请选择配送方式');
        }
        // 添加防抖
        $lock = Locker::lock($request->adminId);
        if (!$lock->acquire()) {
            throw new BadRequestHttpException('操作太频繁，请稍后再试');
        }
        try {
            // 通过执行
            if ($params['auditor_status'] == '已通过') {
                // 退款通过
                if ($emergencyInfo['type'] == -1 && $emergencyInfo['is_wx_order'] == -1) {
                    $content = $emergencyInfo['content'];
                    // 订单更新
                    $itemInfo = $this->dao->get('order_food_item', [
                        'order_food_item_id' => $content['order_food_item_id']
                    ]);
                    if(empty($itemInfo)) {
                        throw new BadRequestHttpException('退餐关联订单不存在');
                    }
                    // 查询用户订单
                    $order = $orderModel->where([
                        'order_id' => $itemInfo['order_id'],
                        'order_status' => 1,
                    ])->findOrEmpty()->toArray();
                    if(!empty($order)) {
                        throw new BadRequestHttpException('订单已确认完成,无法加/退餐');
                    }
                    $payPrice = (float)$itemInfo['price'] * (int)$content['num'];
                    // 精度额小数点后两位
                    $payPrice = round($payPrice, 2);

                    $this->dao->update('order_food_item', [
                        'pay_price' => $payPrice,
                        'num' => (int)$content['num'],
                        'updated_by' => $request->adminId,
                        'update_time' => date('Y-m-d H:i:s')
                    ], [
                        'order_food_item_id' => $content['order_food_item_id']
                    ]);
                    // 计算送餐费
                    $data = $this->service->calculateDeliveryFee($itemInfo['order_id']);
                    // 更新订单信息
                    $orderModel->update($data, ['order_id' => $itemInfo['order_id']]);
                }
                // 加餐通过
                if ($emergencyInfo['type'] == 1 && $emergencyInfo['is_wx_order'] == -1) {
                    // 判断订单是否确认完成
                    foreach ($emergencyInfo['delivery_fee'] as $v) {
                        // 查询用户订单
                        $order = $orderModel->where([
                            'cust_uid' => $emergencyInfo['cust_uid'],
                            'delivery_time' => $v['time'],
                            'order_status' => 1,
                        ])->findOrEmpty()->toArray();
                        if(!empty($order)) {
                            throw new BadRequestHttpException($v['time'].'日订单已确认完成,无法加/退餐');
                        }
                    }
                    $content = $emergencyInfo['content'];
                    // 获取送餐费数组
                    $deliveryFeeValue = [];
                    if(!empty($emergencyInfo['delivery_fee'])) {
                        foreach ($emergencyInfo['delivery_fee'] as $item) {
                            $deliveryFeeValue[$item['time']] = $item;
                        }
                    }
                    // 获取子商品数量
                    $nowTime = date('Y-m-d H:i:s');
                    $countNum = count($content[0]);
                    $completeWeeklyOrderId = 0;
                    for ($i = 0; $i < $countNum; $i++) {
                        foreach ($content as $v) {
                            if ($v[$i]['num'] > 0) {
                                // 查询用户是否已经存在订单
                                $orderInfo = $orderModel
                                    ->where('cust_uid',$emergencyInfo['cust_uid'])
                                    ->where('delivery_time',$v[$i]['dayTime'])
                                    ->findOrEmpty()
                                    ->toArray();
                                if(empty($orderInfo)) {
                                    $orderId = getId();
                                    // 添加订单信息-天
                                    $oderInfo = [];
                                    $oderInfo['order_id'] = $orderId;
                                    $oderInfo['delivery_option'] = $deliveryFeeValue[$v[$i]['dayTime']]['delivery_option']; // 配送方式
                                    $oderInfo['discounts_price'] = $emergencyInfo['discounts_price'];
                                    $oderInfo['delivery_site'] = $deliveryFeeValue[$v[$i]['dayTime']]['delivery_site']; // 配送站点
                                    $oderInfo['delivery_num'] = $deliveryFeeValue[$v[$i]['dayTime']]['num']; // 配送数量
                                    $oderInfo['total_num'] = $deliveryFeeValue[$v[$i]['dayTime']]['num']; // 配送数量
                                    $oderInfo['cust_uid'] = $emergencyInfo['cust_uid'];
                                    $oderInfo['delivery_time'] = $v[$i]['dayTime'];
                                    $oderInfo['gov_fee'] = $deliveryFeeValue[$v[$i]['dayTime']]['discount'];
                                    $oderInfo['pay_price'] = $deliveryFeeValue[$v[$i]['dayTime']]['pay_price'];
                                    $oderInfo['cust_delivery_fee'] = $deliveryFeeValue[$v[$i]['dayTime']]['fee'];
                                    $oderInfo['employee_uid'] = $emergencyInfo['employee_uid'];
                                    $oderInfo['remark'] = $emergencyInfo['remark'];
                                    $oderInfo['package_type'] = $emergencyInfo['package_type'];
                                    $oderInfo['created_by'] = $emergencyInfo['created_by'];
                                    $oderInfo['create_time'] = $nowTime;
                                    $this->dao->insert('order_food',$oderInfo);
                                } else {
                                    $orderId = $orderInfo['order_id'];
                                }
                                // 订单差量计算
                                $itemInfo = $this->dao->get('order_food_item',[
                                    'order_id'=>$orderId,
                                    'package_id' => $v[$i]['packageId'],
                                    'delivery_time' => $v[$i]['dayTime'],
                                ]);
                                if(empty($itemInfo)) {
                                    // 添加订单套餐
                                    $orderInfoItem = [];
                                    $orderInfoItem['order_id'] = $orderId;
                                    $orderInfoItem['delivery_time'] = $v[$i]['dayTime'];
                                    $orderInfoItem['delivery_option'] = $deliveryFeeValue[$v[$i]['dayTime']]['delivery_option']; // 配送方式
                                    $orderInfoItem['delivery_site'] = $deliveryFeeValue[$v[$i]['dayTime']]['delivery_site']; // 配送站点
                                    $orderInfoItem['cust_uid'] = $emergencyInfo['cust_uid'];
                                    if (!empty($emergencyInfo['employee_uid'])) {
                                        $orderInfoItem['employee_uid'] = $emergencyInfo['employee_uid'];
                                    }
                                    $orderInfoItem['num'] = $v[$i]['num'];
                                    $orderInfoItem['price'] = $v[$i]['price'];
                                    $orderInfoItem['pay_price'] = $v[$i]['price'] * $v[$i]['num'];

                                    $orderInfoItem['package_id'] = $v[$i]['packageId'];
                                    $orderInfoItem['package_name'] = $v[$i]['name'];
                                    $orderInfoItem['created_by'] = $emergencyInfo['created_by'];
                                    $orderInfoItem['create_time'] = $nowTime;
                                    $this->dao->insert('order_food_item', $orderInfoItem);
                                } else {
                                    $total = $v[$i]['num'] + $itemInfo['num'];
                                    // 订单更新
                                    $payPrice = $itemInfo['price'] * $total;
                                    $this->dao->update('order_food_item', [
                                        'pay_price' => $payPrice,
                                        'num' => (int)$total,
                                        'updated_by' => $request->adminId,
                                        'update_time' => date('Y-m-d H:i:s')
                                    ], [
                                        'order_food_item_id' => $itemInfo['order_food_item_id']
                                    ]);
                                }
                                // 计算送餐费
                                $data = $this->service->calculateDeliveryFee($orderId);
                                // 更新订单信息
                                $orderModel->update($data, ['order_id' => $orderId]);
                                // 获取最后一个orderId用于执行补全
                                $completeWeeklyOrderId = $orderId;
                            }
                        }
                    }
                    // 执行补全
                    event('food.emergencyOrderPassAfter', [
                        'order_id' => $completeWeeklyOrderId
                    ]);
                }
            }
            $id = $request->input('id', '');
            $auditor_status = $request->input('auditor_status', '待审核');
            $result = $this->logic->where($this->pk, $id)->update([
                'auditor_status' => $auditor_status,
                'admin_remark' => $params['admin_remark'],
                'auditor_time' => date('Y-m-d H:i:s'),
                'auditor_uid' => $request->adminId,
            ]);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        } finally {
            $lock->release();
        }
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * 获取退款接口
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function decreaseFoodOrder(Request $request): Response
    {
        try {
            validate(FoodEmergencyValidate::class)
                ->scene('editItemOrder')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only(['order_food_item_id', 'num','label']);
        $itemInfo = $this->dao->get('order_food_item', [
            'order_food_item_id' => $params['order_food_item_id']
        ]);
        if (empty($itemInfo)) {
            throw new BadRequestHttpException('订单套餐未找到');
        }
        // 订单差量计算
        $orderInfo = $this->dao->get('order_food',[
            'order_id'=>$itemInfo['order_id']
        ]);
        if (empty($orderInfo)) {
            throw new BadRequestHttpException('订单未找到');
        }
        if($orderInfo['order_status'] == 1) {
            throw new BadRequestHttpException('订单已支付，不能退餐');
        }
        $diffNum = (int)$params['num'] - (int)$itemInfo['num'];
        $diffMoney = $diffNum * (int)$itemInfo['price'];
        if($diffNum > 0) {
            $title = $itemInfo['delivery_time'].'增加'.$diffNum.'份'.$itemInfo['package_name'].'套餐';
            $category = '套餐增加';
        } else {
            $title = $itemInfo['delivery_time'].'减少'. -$diffNum.'份'.$itemInfo['package_name'].'套餐';
            $category = '套餐取消';
        }
        // 订单修改记录
        $content = [
            'order_id' => $orderInfo['order_id'],
            'order_food_item_id' => $params['order_food_item_id'],
            'num' => $params['num'],
            'diffNum' => $diffNum,
            'title' => $title,
            'category' => $category,
            'diffMoney' => $diffMoney,
            'time' => $itemInfo['delivery_time'],
            'packageName' => $itemInfo['package_name'],
        ];
        // 提交信息
        $model = new FoodEmergency();
        // 查询订单是否存在
        if(!empty($model
            ->where('content', 'like', '%' . $params['order_food_item_id'] . '%')
            ->whereNotIn('auditor_status',[
            '已驳回',
            '已取消'
        ])->findOrEmpty()->toArray())) {
            throw new BadRequestHttpException('已提交取消信息');
        }
        $editData = [
            'content' => $content,
            'type' => -1,
            'cust_uid' => $orderInfo['cust_uid'],
            'created_by' => $request->adminId,
        ];
        if(!empty($params['label'])) {
            $editData['label'] = $params['label'];
        }
        $model::create($editData);
        return $this->success('操作成功');
    }


    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/food/FoodEmergency/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/food/FoodEmergency/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/food/FoodEmergency/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/food/FoodEmergency/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

}
