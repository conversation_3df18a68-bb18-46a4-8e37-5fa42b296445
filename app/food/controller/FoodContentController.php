<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\food\controller;

use app\food\model\FoodContentMaterial;
use app\utils\Dao;
use DI\Attribute\Inject;
use plugin\saiadmin\basic\BaseController;
use app\food\logic\FoodContentLogic;
use app\food\validate\FoodContentValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use think\db\exception\DbException;

/**
 * @Apidoc\Title("菜品管理")
 */
class FoodContentController extends BaseController
{
    protected string $pk = "id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new FoodContentLogic();
        $this->validate = new FoodContentValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/food/FoodContent/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("name", type="varchar", require=false, desc="菜品名称", default="")
     * @Apidoc\Query("cate_name", type="varchar", require=false, desc="分类名称", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['name', ''],
            ['cate_name', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/food/FoodContent/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("name", type="varchar", require=false, desc="菜品名称", default="")
     * @Apidoc\Query("cate_name", type="varchar", require=false, desc="分类名称", default="")
     * @Apidoc\Query("sort", type="varchar", require=false, desc="排序", default="")
     * @Apidoc\Query("status", type="varchar", require=false, desc="状态", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/food/FoodContent/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("name", type="varchar", require=false, desc="菜品名称", default="")
     * @Apidoc\Query("cate_name", type="varchar", require=false, desc="分类名称", default="")
     * @Apidoc\Query("sort", type="varchar", require=false, desc="排序", default="")
     * @Apidoc\Query("status", type="varchar", require=false, desc="状态", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/food/FoodContent/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            // 添加配菜
            $data['material_form'] = $this->dao->search('food_content_material',[
                'content_id' => $id,
            ],[
                'material_id',
                'weight',
            ]);
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/food/FoodContent/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/food/FoodContent/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/food/FoodContent/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/food/FoodContent/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/food/FoodContent/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("创建菜品新")
     * @Apidoc\Url("/food/FoodContent/createFoodContent")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function createFoodContent(Request $request) : Response
    {
        $params = $request->more([
            ['id', 0],
            ['name', ''],
            ['cate_name', ''],
            ['sort', 100],
            ['status', 1],
            ['material_form', []],
        ]);
        $foodData = $params;
        unset($foodData['material_form']);
        $contentMaterialModel = new FoodContentMaterial();
        if($params['id'] == 0) {
            // 新增
            $foodData['created_by'] = $request->adminId;
            $foodData['updated_by'] = $request->adminId;
            $foodData['create_time'] = date('Y-m-d H:i:s');
            $foodData['update_time'] = date('Y-m-d H:i:s');
            $result = $this->logic->insertGetId($foodData);
            $params['id'] = $result;
        } else {
            $info = $this->logic->find($params['id']);
            if (!$info) {
                return $this->fail('没有找到该数据');
            }
            $result = $this->logic->update($foodData, [$this->pk => $params['id']]);
            // 更新配菜
            $this->dao->del('food_content_material',[
                'content_id' => $params['id'],
            ]);
        }
        if ($result) {
            // 添加配菜
            if(!empty($params['material_form'])) {
                $materialArr = [];
                foreach ($params['material_form'] as $v) {
                    $value['content_id'] = $params['id'];
                    $value['material_id'] = $v['material_id'];
                    $value['name'] = $this->dao->get('food_material',[
                        'material_id' => $value['material_id'],
                    ],'name');
                    $value['weight'] = $v['weight'];
                    $value['created_by'] = $request->adminId;
                    $value['updated_by'] = $request->adminId;
                    $value['create_time'] = date('Y-m-d H:i:s');
                    $value['update_time'] = date('Y-m-d H:i:s');
                    $materialArr[] = $value;
                }
                $contentMaterialModel->insertAll($materialArr);
            }
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

}
