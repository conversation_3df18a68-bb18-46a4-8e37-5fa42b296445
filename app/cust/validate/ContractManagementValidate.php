<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 合同管理验证器
 */
class ContractManagementValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        // 'contract_number' => 'require',
        'cust_contract_classification' => 'require',
        'cust_uid' => 'require',
        'personnel_type' => 'require',
        'service_type' => 'require',
        'contract_period_start' => 'require',
        'contract_period_end' => 'require',
        'contract_status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'contract_number' => '合同编号必须填写',
        'cust_contract_classification' => '合同分类必须填写',
        'cust_uid' => '客户必须填写',
        'personnel_type' => '人员类型必须填写',
        'service_type' => '服务类型必须填写',
        'contract_period_start' => '合同开始时间必须填写',
        'contract_period_end' => '合同结束时间必须填写',
        'contract_status' => '合同状态必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            // 'contract_number',
            'cust_contract_classification',
            'cust_uid',
            'personnel_type',
            'service_type',
            'contract_period_start',
            'contract_period_end',
            'contract_status',
        ],
        'update' => [
            // 'contract_number',
            'cust_contract_classification',
            'cust_uid',
            'personnel_type',
            'service_type',
            'contract_period_start',
            'contract_period_end',
            'contract_status',
        ],
    ];

}
