<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 档案管理验证器
 */
class CustUserValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_name' => 'require',
        'cust_id_type' => 'require',
        'cust_marital_status' => 'require',
        'cust_live_status' => 'require',
        'cust_identity_type' => 'require',
        'cust_is_bed' => 'require',
        'cust_avatar' => 'require',
        'cust_belief' => 'require',
        'cust_disability_type' => 'require',
        'cust_disability_level' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_name' => '姓名必须填写',
        'cust_id_type' => '身份证类型必须填写',
        'cust_marital_status' => '婚姻状态必须填写',
        'cust_live_status' => '居住状况必须填写',
        'cust_identity_type' => '身份类别必须填写',
        'cust_is_bed' => '是否家床必须填写',
        'cust_avatar' => '头像必须填写',
        'cust_belief' => '宗教信仰必须填写',
        'cust_disability_type' => '残疾类别必须填写',
        'cust_disability_level' => '残疾等级必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'cust_name',
        ],
        'update' => [
            'cust_name',
        ],
    ];

}
