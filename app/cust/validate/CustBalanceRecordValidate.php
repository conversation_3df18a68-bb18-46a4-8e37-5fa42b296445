<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 客户余额记录表验证器
 */
class CustBalanceRecordValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'balance_id' => 'require',
        'cust_uid' => 'require',
        'pm' => 'require',
        'record_title' => 'require',
        'record_details_type' => 'require',
        'number' => 'require',
        'balance' => 'require',
        'record_status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'balance_id' => '客户余额id必须填写',
        'cust_uid' => '客户id必须填写',
        'pm' => '0 = 支出 1 = 获得必须填写',
        'record_title' => '记录标题必须填写',
        'record_details_type' => '明细类型0套餐消费、1每月账户结余转移、2缴费、3服务订单消费、4运营补贴、5缴费作废必须填写',
        'number' => '明细数字必须填写',
        'balance' => '修改后金额必须填写',
        'record_status' => '记录状态(0 = 待确定 1 = 有效 -1 = 无效)必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'balance_id',
            'cust_uid',
            'pm',
            'record_title',
            'record_details_type',
            'number',
            'balance',
            'record_status',
        ],
        'update' => [
            'balance_id',
            'cust_uid',
            'pm',
            'record_title',
            'record_details_type',
            'number',
            'balance',
            'record_status',
        ],
    ];

}
