<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 老人养老顾问变更记录表验证器
 */
class CustUserConsultantLogValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_uid' => 'require',
        'former_consultant_id' => 'require',
        'new_consultant_id' => 'require',
        'former_service_site' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_uid' => '变更客户id必须填写',
        'former_consultant_id' => '原养老顾问必须填写',
        'new_consultant_id' => '新养老顾问必须填写',
        'former_service_site' => '原服务机构必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'cust_uid',
            'former_consultant_id',
            'new_consultant_id',
            'former_service_site',
        ],
        'update' => [
            'cust_uid',
            'former_consultant_id',
            'new_consultant_id',
            'former_service_site',
        ],
    ];

}
