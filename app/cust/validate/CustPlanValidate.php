<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 服务规划验证器
 */
class CustPlanValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_plan_name' => 'require',
        'cust_plant_execution_status' => 'require',
        'cust_plant_review_status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_plan_name' => '规划名称必须填写',
        'cust_plant_execution_status' => '服务规划执行状态必须填写',
        'cust_plant_review_status' => '服务规划审核状态:必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'cust_plan_name',
            'cust_plant_execution_status',
            'cust_plant_review_status',
        ],
        'update' => [
            'cust_plan_name',
            'cust_plant_execution_status',
            'cust_plant_review_status',
        ],
    ];

}
