<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 评估记录验证器
 */
class CustSurveyAssessmentValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_survey_name' => 'require',
        'cust_uid' => 'require',
        // 'employee_uid' => 'require',
        'employee_name' => 'require',
        'company_cid' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_survey_name' => '评估名称必须填写',
        'cust_uid' => '老人编号必须填写',
        // 'employee_uid' => '调查人员必须填写',
        'employee_name' => '调查人员必须填写',
        'company_cid' => '机构编号必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'cust_survey_name',
            'cust_uid',
            // 'employee_uid',
            'employee_name',
            'company_cid',
        ],
        'update' => [
            'cust_survey_name',
            'cust_uid',
            // 'employee_uid',
            'employee_name',
            'company_cid',
        ],
    ];

}
