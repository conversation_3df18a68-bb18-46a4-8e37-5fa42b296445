<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 项目模板验证器
 */
class CustPlanItemTemplateValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_plan_item_template_name' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_plan_item_template_name' => '项目名称必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'cust_plan_item_template_name',
        ],
        'update' => [
            'cust_plan_item_template_name',
        ],
    ];

}
