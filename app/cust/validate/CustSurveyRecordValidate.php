<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\validate;

use think\Validate;

/**
 * 表单记录验证器
 */
class CustSurveyRecordValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_survey_name' => 'require',
        'cust_uid' => 'require',
        'employee_uid' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_survey_name' => '问卷名称必须填写',
        'cust_uid' => '客户必须填写',
        'employee_uid' => '调查人员必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'cust_survey_name',
            'cust_uid',
            'employee_uid',
        ],
        'update' => [
            'cust_survey_name',
            'cust_uid',
            'employee_uid',
        ],
    ];

}
