<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\logic;

use plugin\saiadmin\basic\BaseLogic;
use plugin\saiadmin\exception\ApiException;
use plugin\saiadmin\utils\Helper;
use app\cust\model\CustPlanItemTemplate;

/**
 * 项目模板逻辑层
 */
class CustPlanItemTemplateLogic extends BaseLogic
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CustPlanItemTemplate();
    }

    /**
     * 数据修改
     */
    public function update($data, $where)
    {
        if (!isset($data['cust_plan_item_template_pid_id'])) {
            $data['cust_plan_item_template_pid_id'] = 0;
        }
        if ($data['cust_plan_item_template_pid_id'] == $where['cust_plan_item_template_id']) {
            throw new ApiException('不能设置父级为自身');
        }
        return $this->model->update($data, $where);
    }

    /**
     * 数据删除
     */
    public function destroy($ids, $force = false)
    {
        $num = $this->model->where('cust_plan_item_template_pid_id', 'in', $ids)->count();
        if ($num > 0) {
            throw new ApiException('该分类下存在子分类，请先删除子分类');
        } else {
            return $this->model->destroy($ids, $force);
        }
    }

    /**
     * 树形数据
     */
    public function tree($where)
    {
        $query = $this->search($where);
        if (request()->input('tree', 'false') === 'true') {
            $query->field('cust_plan_item_template_id, cust_plan_item_template_id as value, cust_plan_item_template_name as label, cust_plan_item_template_pid_id');
        }
        $data = $this->getAll($query);
        return Helper::makeTree($data,'children','cust_plan_item_template_id','cust_plan_item_template_pid_id');
    }

}
