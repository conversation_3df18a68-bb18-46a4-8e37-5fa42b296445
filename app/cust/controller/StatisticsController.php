<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use app\controller\BaseController;
use app\utils\NocoDbApi;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use <PERSON><PERSON>\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * @Apidoc\Title("数据分析")
 */
class StatisticsController extends BaseController
{
    /**
     * @Apidoc\Title("数据分析")
     * @Apidoc\Url("/cust/Statistics/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function index(Request $request): Response
    {
        $params = $request->more([
            ['cust_survey_template_id', ''],
            ['time', ''],
            ['page',1],
            ['limit',10]
        ]);
        $templateInfo = $this->dao->get('cust_survey_template',[
            'cust_survey_template_id' => $params['cust_survey_template_id']
        ]);
        if(empty($templateInfo)) {
            throw new BadRequestHttpException('问卷不存在');
        }
        $where = null;
        if(!empty($params['time'])) {
            $where = "(调查时间,like,".$params['time'].")";
        }
        $offset = (int)($params['page'] - 1) * (int)$params['limit'];
        // 查询问卷信息-字段
        $fieldArr = NocoDbApi::send([
            'table_id' => $templateInfo['table_id'],
            'view_id' => $templateInfo['view_id'],
            'where' => $where,
            'offset' => 0,
            'limit' => 1
        ]);
        if(empty($fieldArr['list'])) {
            return $this->fail('数据为空');
        }
        // 获取字段
        $field = [];
        $notField = ['Id','老人编号','调查员编号','调查时间'];
        foreach ($fieldArr['list'][0] as $k=>$v) {
            if(!in_array($k,$notField)) {
                $field[] = $k;
            }
        }
        // 查询每个字段对应统计
        $data = [];
        if(!empty($field)) {
            // 查询统计
            foreach ($field as $v) {
                $chart = [];
                $list = [];
                $groupArr = NocoDbApi::groupSend([
                    'table_id' => $templateInfo['table_id'],
                    'view_id' => $templateInfo['view_id'],
                    'where' => $where,
                    'sort' => '+'.$v,
                    'column_name' => $v,
                    'offset' => 0,
                    'limit' => 10
                ]);
                $listArr = NocoDbApi::send([
                    'table_id' => $templateInfo['table_id'],
                    'view_id' => $templateInfo['view_id'],
                    'fields' => $v .','.'Id',
                    'where' => $where,
                    'offset' => $offset,
                    'limit' => $params['limit']
                ]);
                if(!empty($groupArr['list'])) {
                    $chart['name'] = $v;
                    $list['name'] = $v;

                    $chart['type'] = 'chart';
                    $list['type'] = 'table';

                    foreach ($groupArr['list'] as $vv) {
                        $chart['data'][] = [
                            'value' => $vv['count'],
                            'name' => $vv[$v],
                        ];
                    }

                    foreach ($listArr['list'] as $vv) {
                        $list['data'][] = [
                            'value' => $vv[$v],
                            'id' => $vv['Id'],
                        ];
                    }

                    $list['total'] = $listArr['pageInfo']['totalRows'];
                    $chart['total'] = $listArr['pageInfo']['totalRows'];
                    $data[] = [
                        'name' => $v,
                        'children' => [
                            $chart,
                            $list
                        ]
                    ];
                }
            }
        }
        return $this->success($data);
    }
}
