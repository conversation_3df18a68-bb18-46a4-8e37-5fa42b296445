<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use app\cust\logic\CustUserLogic;
use app\utils\Dao;
use app\utils\Excel;
use DI\Attribute\Inject;
use plugin\saiadmin\basic\BaseController;
use app\cust\logic\ContractManagementLogic;
use app\cust\validate\ContractManagementValidate;
use plugin\saiadmin\exception\ApiException;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * @Apidoc\Title("合同管理")
 */
class ContractManagementController extends BaseController
{
    protected string $pk = "contract_id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new ContractManagementLogic();
        $this->validate = new ContractManagementValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/cust/ContractManagement/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("contract_number", type="varchar", require=false, desc="合同编号", default="")
     * @Apidoc\Query("personnel_type", type="tinyint", require=false, desc="人员类型", default="")
     * @Apidoc\Query("service_type", type="varchar", require=false, desc="服务类型", default="")
     * @Apidoc\Query("service_address", type="varchar", require=false, desc="服务地址", default="")
     * @Apidoc\Query("contract_period_start", type="date", require=false, desc="合同开始时间", default="")
     * @Apidoc\Query("contract_period_end", type="date", require=false, desc="合同结束时间", default="")
     * @Apidoc\Query("contract_status", type="varchar", require=false, desc="合同状态", default="")
     * @Apidoc\Query("cust_contract_classification", type="varchar", require=false, desc="合同分类", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['contract_number', ''],
            ['personnel_type', ''],
            ['service_type', ''],
            ['service_address', ''],
            ['contract_period_start', ''],
            ['contract_period_end', ''],
            ['contract_status', ''],
            ['cust_uid', ''],
            ['cust_contract_classification', ''],
            ['contract_type', ''],
            ['employee_uid', ''],
            ['order_source', ''],
            ['sales_attribution_uid', ''],
            ['cust_contract_service_site', ''],
        ]);
        $query = $this->logic->search($where)->with(['custInfo','employeeInfo']);
        $data = $this->logic->getList($query);

         $data1 =  $data['data'];
        // var_dump('data1-=-=-=---------------',$data1);
        if (!empty($data1)) {
            // var_dump('data1-=-=-=---------------',$data1);
            foreach ($data1 as &$v) {
                $contract_documents2 = $v['contract_documents2'] ?? [];
                if(!empty($contract_documents2)){
                    $result = [];
                    foreach ($contract_documents2 as $key => $value) {
                        $result[] = $value;  // 将每个值添加到结果数组
                    }
                    $v['contract_documents2']  = $result;
                }
                // if($v['cust_contract_classification'] == '市场化合同'){
                //     // 修改市场化合同的合同编号
                //     $this->dao->update('contract_management', [
                //         'contract_number' => 'SCH-' . $v['cust_uid'],
                //         'update_time' => date('Y-m-d H:i:s'),
                //     ], [
                //         'cust_contract_classification' => '市场化合同',
                //         'contract_id' => $v['contract_id'],
                //     ]);
                // }
            }
            $data['data'] = $data1;
        }

        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/cust/ContractManagement/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("contract_number", type="varchar", require=false, desc="合同编号", default="")
     * @Apidoc\Query("cust_contract_classification", type="varchar", require=false, desc="合同分类", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户", default="")
     * @Apidoc\Query("personnel_type", type="tinyint", require=false, desc="人员类型", default="")
     * @Apidoc\Query("service_type", type="varchar", require=false, desc="服务类型", default="")
     * @Apidoc\Query("contract_type", type="varchar", require=false, desc="合同服务项目", default="")
     * @Apidoc\Query("service_address", type="varchar", require=false, desc="服务地址", default="")
     * @Apidoc\Query("contract_period_start", type="date", require=false, desc="合同开始时间", default="")
     * @Apidoc\Query("contract_period_end", type="date", require=false, desc="合同结束时间", default="")
     * @Apidoc\Query("contract_status", type="varchar", require=false, desc="合同状态", default="")
     * @Apidoc\Query("order_source", type="tinyint", require=false, desc="订单来源", default="")
     * @Apidoc\Query("sales_attribution_uid", type="bigint", require=false, desc="销售归属", default="")
     * @Apidoc\Query("after_sales_uid", type="bigint", require=false, desc="售后归属", default="")
     * @Apidoc\Query("cust_contract_service_site", type="varchar", require=false, desc="所属机构", default="")
     * @Apidoc\Query("contract_documents", type="varchar", require=false, desc="合同文件", default="")
     * @Apidoc\Query("contract_documents2", type="varchar", require=false, desc="合同文件(多图)", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        // 获取新建合同id
        $id = $this->logic->insertGetId($data);
        if (!empty($id)) {
            // var_dump('$id-=-=-=---------------',$id); 
            if (!empty($data['contract_type']) && in_array($data['contract_type'], [10, 65])) {
                // 居家照护合同添加合同换人记录
                $data1 = [];
                $data1['replace_classify'] = 'contract_management';// 换人分类
                $data1['link_id'] = $id;// 关联合同id
                // var_dump('data-=-=-=----------1111-----',$data1);
                $data1['employee_uid'] = $data['employee_uid'];// 服务员
                $data1['salary_payment_date'] = 25; // 工资发放日
                $data1['working_days'] = $data['working_days'];// 月工作天数
                if(!empty($data['working_hour']))$data1['working_hour'] = $data['working_hour'];// 日工作小时
                // var_dump('data-=-=-=----------2222-----',$data1);
                if(!empty($data['employee_service_fee']))$data1['employee_service_fee'] = $data['employee_service_fee'];// 小时工单价
                $data1['employee_salary'] = $data['employee_salary']; // 服务员工资
                $data1['working_start'] = $data['contract_period_start']; // 工作开始时间
                $data1['working_end'] = $data['contract_period_end']; // 工作结束时间
                $data1['created_by'] = $request->adminId; // 创建人
                $data1['create_time'] = date('Y-m-d H:i:s'); // 创建时间
                // var_dump('data1-=-=-=---------------',$data1);
                $this->dao->insert('employee_replace_record', $data1);
            }
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("保存服务员协议")
     * @Apidoc\Url("/cust/ContractManagement/save1")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("contract_number", type="varchar", require=false, desc="合同编号", default="")
     * @Apidoc\Query("cust_contract_classification", type="varchar", require=false, desc="合同分类", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="服务员", default="")
     * @Apidoc\Query("service_address", type="varchar", require=false, desc="服务地址", default="")
     * @Apidoc\Query("contract_period_start", type="date", require=false, desc="合同开始时间", default="")
     * @Apidoc\Query("contract_period_end", type="date", require=false, desc="合同结束时间", default="")
     * @Apidoc\Query("contract_status", type="varchar", require=false, desc="合同状态", default="")
     * @Apidoc\Query("order_source", type="tinyint", require=false, desc="订单来源", default="")
     * @Apidoc\Query("cust_contract_service_site", type="varchar", require=false, desc="所属机构", default="")
     * @param Request $request
     * @return Response
     */
    public function save1(Request $request) : Response
    {
        $data = $request->post();

        // 验证信息
        if (!empty($data)) {
            if (!empty($data['cust_contract_classification'])) {
                if($data['cust_contract_classification'] == '劳务协议'){
                    if(empty($data['contract_period_start'])){
                        return $this->fail('开始时间必须填写');
                    }
                    if(empty($data['contract_period_end'])){
                        return $this->fail('结束时间必须填写'); 
                    }
                }
            } else {
               return $this->fail('协议分类必须选择'); 
            }
            if (empty($data['employee_uid'])) {
                return $this->fail('服务员必须选择'); 
            }
        }
        // var_dump('$data-=-=-=---------------',$data); 
        $data1 = [
            'contract_number' => $data['contract_number'] ?? '',
            'cust_contract_classification' => $data['cust_contract_classification'] ?? '',
            'employee_uid' => $data['employee_uid'] ?? '',
            'service_address' => $data['employee_regist_address'] ?? '',
            'contract_status' => $data['contract_status'] ?? '已签约',
            'order_source' => $data['order_source'] ?? '',
            'cust_contract_service_site' => $data['cust_contract_service_site'],
            'created_by' => $request->adminId, // 创建人
            'create_time' => date('Y-m-d H:i:s'), // 创建时间
        ];
        if($data['cust_contract_classification'] == '劳务协议'){
            $data1['contract_period_start'] = $data['contract_period_start'];
            $data1['contract_period_end'] = $data['contract_period_end'];
        }
        // var_dump('$data1-=-=-=---------------',$data1); 

        // 获取新建合同id
        $id = $this->logic->insertGetId($data1);
        if (!empty($id)) {
            // var_dump('$id-=-=-=---------------',$id); 
            $src = $this->dao->get('employee_user',['employee_uid' => $data['employee_uid']], 'employee_signature_picture');
            $src1 = $data['employee_signature_picture'];

            // 服务员没有存签名 且 请求上传了签名
            if (empty($src) && !empty($src1)) {
                // 修改服务员签名
                $this->dao->update('employee_user', ['employee_signature_picture' => $src1 ] , [ 'employee_uid' => $data['employee_uid']]); 
            }
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/cust/ContractManagement/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("contract_number", type="varchar", require=false, desc="合同编号", default="")
     * @Apidoc\Query("cust_contract_classification", type="varchar", require=false, desc="合同分类", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户", default="")
     * @Apidoc\Query("personnel_type", type="tinyint", require=false, desc="人员类型", default="")
     * @Apidoc\Query("service_type", type="varchar", require=false, desc="服务类型", default="")
     * @Apidoc\Query("contract_type", type="varchar", require=false, desc="合同服务项目", default="")
     * @Apidoc\Query("service_address", type="varchar", require=false, desc="服务地址", default="")
     * @Apidoc\Query("contract_period_start", type="date", require=false, desc="合同开始时间", default="")
     * @Apidoc\Query("contract_period_end", type="date", require=false, desc="合同结束时间", default="")
     * @Apidoc\Query("contract_status", type="varchar", require=false, desc="合同状态", default="")
     * @Apidoc\Query("order_source", type="tinyint", require=false, desc="订单来源", default="")
     * @Apidoc\Query("sales_attribution_uid", type="bigint", require=false, desc="销售归属", default="")
     * @Apidoc\Query("after_sales_uid", type="bigint", require=false, desc="售后归属", default="")
     * @Apidoc\Query("cust_contract_service_site", type="varchar", require=false, desc="所属机构", default="")
     * @Apidoc\Query("contract_documents", type="varchar", require=false, desc="合同文件", default="")
     * @Apidoc\Query("contract_documents2", type="varchar", require=false, desc="合同文件(多图)", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        // var_dump('data=-=-=---------------',$data);
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/cust/ContractManagement/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/cust/ContractManagement/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改不续签原因")
     * @Apidoc\Url("/cust/ContractManagement/changeNotRenewingReason")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeNotRenewingReason(Request $request) : Response
    {
        $id = $request->input('id', '');
        $not_renewing_reason = $request->input('not_renewing_reason', '');
        $result = $this->logic->where($this->pk, $id)->update(['not_renewing_reason' => $not_renewing_reason]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("结束时间超时批量过期")
     * @Apidoc\Url("/cust/ContractManagement/changeStatusAll")
     * @Apidoc\Method("GET")
     * @return Response
     */
    public function changeStatusAll() : Response
    {
        $nowTime = date('Y-m-d H:i:s'); // 获取当前时间
        // 结束时间早于当前时间的合同状态改为'过期'
        $result = $this->dao->update('contract_management', [
            'contract_status' => '到期',
            'updated_by' => 1,
            'update_time' => date('Y-m-d H:i:s'),
        ], [
            // 'cust_uid' => 4733,
            'contract_status' => '进行中',
            'delete_time' => null,
            'contract_period_end[<=]' => $nowTime,
        ]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/cust/ContractManagement/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/cust/ContractManagement/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/cust/ContractManagement/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/cust/ContractManagement/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("导入数据")
     * @Apidoc\Url("/cust/ContractManagement/import")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function import(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '合同编号' => 'contract_number',
            '合同分类' => 'cust_contract_classification',
            '客户姓名' => 'cust_uid',
            '人员类型' => 'cust_is_bed',
            '服务类型' => 'service_type',
            '服务地址' => 'service_address',
            '合同开始时间' => 'contract_period_start',
            '合同结束时间' => 'contract_period_end',
            '合同金额' => 'cust_contract_amount',
            '合同状态' => 'contract_status',
            '订单来源' => 'order_source',
            '销售归属' => 'sales_attribution_uid',
            '售后归属' => 'after_sales_uid',
            '所属机构' => 'cust_contract_service_site',
        ];
        // 获取数据
        $data = Excel::import($header);
        $insertArr = [];// 修改后的数据
        $insertArr1 = [];// 已录入客户  避免重复录入客户
        if(!empty($data)) {
            foreach ($data as $value) {
                if(!empty($value['cust_uid'])) {
                    $cust_name = $value['cust_uid'];// 获取客户姓名
                    $cust_service_site = $this->dao->get('company_depart',[ 'company_name' =>  $value['cust_contract_service_site'] ],'company_cid');
                    // 人员类型
                    $cust_is_bed = match ($value['cust_is_bed'] ) {
                        '基本服务对象' => 3,
                        '基本对象' => 3,
                        '家庭床位' => 2,
                        '家床' => 2,
                        default => 1
                    };

                    $value['cust_uid'] = $this->dao->get('cust_user',[ 'cust_name' => $cust_name ],'cust_uid');
                    $value['personnel_type'] = $this->dao->get('cust_user',[ 'cust_name' => $cust_name ],'cust_is_bed');
                    //  未匹配到对应客户人员类型
                    if(empty($value['personnel_type'])) {
                        $value['personnel_type'] =  $cust_is_bed;
                    }
                    
                    // 修改客户档案的人员类型   && !empty($value['cust_uid'])
                    if($value['personnel_type'] != $cust_is_bed ){
                        $this->dao->update('cust_user',[
                            'cust_is_bed' => $cust_is_bed,// 人员类型
                            'cust_service_site' => $cust_service_site,// 服务机构
                        ],[
                            'cust_name' => $cust_name
                        ]);

                        $value['personnel_type'] =  $cust_is_bed;
                    }

                    // 没有匹配到对应客户
                    if(empty($value['cust_uid'])) {
                        // 不是已新增的客户
                        if(empty($insertArr1[$cust_name])){
                            $custUser = new CustUserLogic();

                            // 如果养老顾问是杨爱莲就换成左庆刚，不是根据姓名查找，查找不到就暂时归admin账号
                            $cust_consultant = $value['order_source']=='杨爱莲'?48:$this->dao->get('eb_system_user',[ 'nickname' =>  $value['order_source'] ],'id');

                            // 获取新建客户的cust_id
                            $cust_id = $custUser->insertGetId([
                                'cust_name' => $cust_name,// 客户姓名
                                'cust_evaluation_level' => 1,// 评估等级
                                'cust_is_bed' => $value['personnel_type'],// 人员类型
                                'cust_city' =>  "{\"province\":\"\",\"city\":\"\",\"area\":\"\",\"street\":\"\",\"community\":\"\"}",// 居住地址
                                'cust_consultant' => $cust_consultant||1,// 养老顾问
                                'cust_service_site' => $cust_service_site||1,// 服务机构
                            ]);
                            // 更改cust_uid
                            $value['cust_uid'] = $cust_id;

                            // 加入已添加客户
                            $insertArr1[$cust_name] = $cust_id;
                        }else{
                            $value['cust_uid'] = $insertArr1[$cust_name];
                        }
                    }

                    $value['order_id'] = 0;
                    if(!empty($value['contract_period_start'])) {
                        $value['contract_period_start'] = convertExcelDateToPHPDate($value['contract_period_start']);
                    }
                    if(!empty($value['contract_period_end'])) {
                        $value['contract_period_end'] = convertExcelDateToPHPDate($value['contract_period_end']);
                    }
                    if(empty($value['contract_number'])) {
                        $value['contract_number'] = 0;
                    }
                    $insertArr[] = $value;
                }
            }
            $this->logic->saveAll($insertArr);
        }
        return $this->success('导入成功');
    }

    /**
     * @Apidoc\Title("更新合同数据")
     * @Apidoc\Url("/cust/ContractManagement/import1")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function import1(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '合同分类' => 'cust_contract_classification',
            '养老顾问' => 'sales_attribution_uid',
            '老人姓名' => 'cust_uid',
            '服务类型' => 'service_type',
            '合同服务项目' => 'contract_type',
            '护理员姓名' => 'employee_uid',
            '工作天数' => 'working_days',
            '基本工资' => 'employee_salary',
            '增值服务费' => 'value_added_expenses',
            '工资发放日' => 'salary_payment_date',
            '日工作小时数' => 'working_hour',
            '小时单价' => 'employee_service_fee',
        ];
        // 获取数据
        $data = Excel::import($header);
        // var_dump('data=-=-=---------------',$data);
        
        $insertArr = [];// 未修改的数据
        if(!empty($data)) {
            foreach ($data as $v) {
                if(!empty($v['cust_uid'])) {
                    $cust_name = $v['cust_uid'];// 获取客户姓名
                    $user = $this->dao->get('cust_user',[ 'cust_name' => $cust_name, 'delete_time' => NULL  ],['cust_uid','cust_internal_number']);
                    $cust_uid = !empty($user)&&!empty($user['cust_uid'])?$user['cust_uid']:$cust_name;
                    // if(empty($user)) var_dump('user=-=-=---------------',$user,'---cust_name---',$cust_name);

                    $employee_name = $v['employee_uid'];// 获取服务员姓名
                    $employee_uid = $this->dao->get('employee_user',[ 'employee_name' =>  $employee_name, 'delete_time' => NULL  ],'employee_uid');

                    // 
                    $cm = $this->dao->get('contract_management',[ 'cust_uid' => $cust_uid, 'cust_contract_classification' => $v['cust_contract_classification'], 
                        'contract_status' => '进行中', 'delete_time' => NULL ], ['contract_id','contract_period_start','contract_period_end','contract_number']);
                    // if(empty($cm)) var_dump('cm=-=-=---------------',$cm);

                    if(!empty($user)&&!empty($cm)){
                        $data1 = [
                            'contract_number' =>  $user['cust_internal_number'] ?? $cm['contract_number'] ?? '',
                            'service_type' =>  $v['service_type'] ?? '按需上门服务',
                            'employee_uid' =>  $employee_uid ?? '',
                            'working_days' =>  $v['working_days'] ?? 0,
                            'employee_salary' =>  $v['employee_salary'] ?? 0.00,
                            'value_added_expenses' =>  $v['value_added_expenses'] ?? 0.00,
                            'salary_payment_date' =>  $v['salary_payment_date'] ?? 5,
                            'working_hour' =>  $v['working_hour'] ?? 0,
                            'employee_service_fee' =>  $v['employee_service_fee'] ?? 0.00,
                            'order_source' =>  $v['sales_attribution_uid'] ?? '',
                            'sales_attribution_uid' =>  $v['sales_attribution_uid'] ?? '',
                            'updated_by' => $request->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                        ];
                        
                        if(!empty($v['contract_type']))$data1['contract_type'] = $v['contract_type']=='小时保姆'?65:($v['contract_type']=='24H保姆'?10:null);// 小时工单价
                        // var_dump('$v=-=-=----!empty($cm)----$data1-------',$data1);

                        // 修改合同信息
                        $this->dao->update('contract_management', $data1 , [ 'contract_id' => $cm['contract_id'] ]);
                        // var_dump('$v=-=-=----!empty(contract_id)--1---------');

                        if(!empty($employee_uid)){

                            // 添加合同换人记录
                            $data1 = [];
                            $data1['replace_classify'] = 'contract_management';// 换人分类
                            $data1['link_id'] = $cm['contract_id']??'';// 关联合同id
                            // var_dump('data-=-=-=----------1111-----',$data1);
                            $data1['employee_uid'] = $employee_uid;// 服务员
                            $data1['salary_payment_date'] = $v['salary_payment_date'] ?? 5; // 工资发放日
                            $data1['working_days'] = $v['working_days'];// 月工作天数
                            if(!empty($v['working_hour']))$data1['working_hour'] = $v['working_hour'];// 日工作小时
                            // var_dump('data-=-=-=----------2222-----',$data1);
                            if(!empty($v['employee_service_fee']))$data1['employee_service_fee'] = $v['employee_service_fee'];// 小时工单价
                            $data1['employee_salary'] = $v['employee_salary']; // 服务员工资
                            $data1['working_start'] = $cm['contract_period_start']; // 工作开始时间
                            $data1['working_end'] = $cm['contract_period_end']; // 工作结束时间
                            $data1['created_by'] = $request->adminId; // 创建人
                            $data1['create_time'] = date('Y-m-d H:i:s'); // 创建时间
                            // var_dump('data1-=-=-=---------------',$data1);
                            $this->dao->insert('employee_replace_record', $data1);
                        }
                    } else {
                        $insertArr[] = $v;
                        // var_dump('insertArr=-=-=---------------',$insertArr);
                    }
                }
            }
        }
        // var_dump('insertArr-=-=-=---------------',$insertArr);
        return $this->success($insertArr);
    }

    /**
    * @Apidoc\Title("更新并新建家床合同")
    * @Apidoc\Url("/cust/ContractManagement/import2")
    * @Apidoc\Method("POST")
    * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
    * @param Request $request
    * @return Response
    * @throws BadRequestHttpException
    */
    public function import2(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '系统编号' => 'haidian_district_dystem_number',
            '老人姓名' => 'cust_name',
            '身份证' => 'cust_id_card',
            '服务类型' => 'service_type',
            '服务地址' => 'service_address',
            '联系电话' => 'customer_phone',
            '状态' => 'state',
            '法定代表人' => 'name1',
        ];
        // 获取数据
        $data = Excel::import($header);
        // var_dump('data=-=-=---------------',$data);
        
        $insertArr = [];// 未修改的数据
        if(!empty($data)) {
            foreach ($data as $v) {
                // var_dump('$v[cust_id_card]=-=-=---------------',$v);
                $user = $this->dao->get('cust_user',['cust_name' => $v['cust_name'],'cust_id_card' => ($v['cust_id_card'] ?? ''),'cust_is_bed' => 2, 'delete_time' => NULL],
                    ['cust_uid','cust_internal_number'] );

                if(!empty($user)){
                    $cust_uid =  $user['cust_uid'];
                    // var_dump('cm=-=-=-----------cust_uid----',$cust_uid,$user['cust_internal_number']);
                    $cm = $this->dao->search('contract_management',
                        [ 'cust_uid' => $cust_uid, 'cust_contract_classification' => '家庭床位合同', 'contract_status' => '进行中', 'delete_time' => NULL ]);
                    // if(empty($cm)) var_dump('cm=-=-=---------------',$v);

                    // 法定代表人id
                    $id = $this->dao->get('eb_system_user',[ 'nickname' =>  $v['name1'], 'delete_time' => NULL ],'id');
                    // 状态 是否需要新建合同
                    $state = in_array($v['state'], ['重度','中度','轻度','自理']);

                    $amount = 0;
                    if($v['service_type'] == '按需上门服务' && $state){
                        $amount = match ($v['state'] ) {
                            '重度' => 600,
                            '中度' => 1800,
                            '轻度' => 1000,
                            '自理' => 300,
                            default => 0
                        };
                    }
                    
                    if(!empty($cm)){
                        $data1 = []; // 要生成的新合同数据
                        foreach ($cm as $v1) {
                            $data2 = [
                                'contract_number' => $user['cust_internal_number'] ?? '',
                                'haidian_district_dystem_number' =>  $v['haidian_district_dystem_number'] ?? '',
                                'service_type' =>  $v['service_type'] ?? '按需上门服务',
                                'service_address' =>  $v['service_address'] ?? '',
                                'updated_by' => $request->adminId,
                                'update_time' => date('Y-m-d H:i:s'),
                            ];
                            if($v['service_type'] == '按需上门服务' && $state) $data2['cust_contract_amount'] = $amount;

                            // 合同是否过期 strtotime()将时间转换成时间戳
                            if(strtotime(date('Y-m-d')) > strtotime($v1['contract_period_end'])){
                                // 时间已到期
                                $data2['contract_status'] = '到期';

                                // 
                                if($state){
                                    // 复制数组
                                    $data1 = array_slice($v1,0);
                                    $data1['contract_number'] =  $user['cust_internal_number'] ?? '';
                                    $data1['haidian_district_dystem_number'] =  $v['haidian_district_dystem_number'] ?? '';
                                    $data1['contract_period_start'] = '2025-01-01';
                                    $data1['contract_period_end'] = '2028-12-31';
                                    $data1['service_type'] = $v['service_type'] ?? '按需上门服务';
                                    $data1['service_address'] = $v['service_address'] ?? '';
                                    $data1['customer_phone'] = $v['customer_phone'] ?? '';
                                    $data1['create_time'] = date('Y-m-d H:i:s');
                                    unset($data1['contract_id']);
                                    unset($data1['updated_by']);
                                    unset($data1['update_time']);
                                }
                            } else {
                                // 时间尚未到期
                                $data1 = []; // 清除数据
                            }  
                            // var_dump('$data2=-=-=---------------',$data2);
                            
                            // 修改合同
                            $this->dao->update('contract_management', $data2 , ['contract_id' => $v1['contract_id']]);
                        }

                        if(!empty($data1)){
                            // var_dump('$data1=-=-=--------$cm-------',$data1);
                            // var_dump('$data1=-=-=--------$cm-----v1--',$v1);
                            $this->dao->insert('contract_management', $data1);
                        }
                    } else {
                        if($state){
                            $data1 = [
                                'cust_contract_classification' =>  '家庭床位合同',
                                'contract_number' =>  $user['cust_internal_number'] ?? '',
                                'haidian_district_dystem_number' =>  $v['haidian_district_dystem_number'] ?? '',
                                'contract_period_start' =>  '2025-01-01',
                                'contract_period_end' =>  '2028-12-31',
                                'cust_uid' =>  $cust_uid ?? '',
                                'service_type' =>  $v['service_type'] ?? '按需上门服务',
                                'service_address' =>  $v['service_address'] ?? '',
                                'cust_contract_amount' => $v['service_type'] == '按需上门服务' ? $amount:5000.00,
                                'order_source' =>  $v['name1'] ?? '',
                                'sales_attribution_uid' =>  $v['name1'] ?? '',
                                'created_by' =>  $id ?? $request->adminId,
                                'create_time' => date('Y-m-d H:i:s'),
                            ];
                            // var_dump('$data1=-=-=---------------');
                            $this->dao->insert('contract_management', $data1);
                        } else {
                            // 没合同且未新生成
                            $insertArr[] = $v;
                        }
                    }
                }
            }
        }
        // var_dump('insertArr-=-=-=---------------',);
        return $this->success($insertArr);
    }

    /**
     * 导出数据
     * @Apidoc\Title("导出数据")
     * @Apidoc\Url("/cust/ContractManagement/export")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function export(Request $request) : Response
    {
        // 创建导出表
        $title = '合同_'.date('YmdHis').'.xlsx';
        $headers = [
            '合同编号',
            '合同分类',
            '客户姓名',
            '服务类型',
            '服务地址',
            '合同开始时间',
            '合同结束时间',
            '合同金额',
            '合同状态',
            '订单来源',
            '销售归属',
            '订单来源',
            '销售归属',
            '售后归属',
            '所属机构',
        ];
        $data = $this->logic->field([
            'contract_number',
            'cust_contract_classification',
            'cust_uid',
            'service_type',
            'service_address',
            'contract_period_start',
            'contract_period_end',
            'cust_contract_amount',
            'contract_status',
            'order_source',
            'sales_attribution_uid',
            'after_sales_uid',
            'cust_contract_service_site',
        ])->select()->toArray();
        if(!empty($data)) {
            foreach ($data as &$value) {
                $value['cust_uid'] = $this->dao->get('cust_user',[
                    'cust_uid' => $value['cust_uid']
                ],'cust_name');
            }
        }
        $url = Excel::export($title,$headers,$data);
        return response()->download($url, urlencode($title));
    }

    /**
     * @Apidoc\Title("下载导入模板")
     * @Apidoc\Url("/cust/ContractManagement/downloadTemplate")
     * @Apidoc\Method("GET")
     * @return Response
     */
    public function downloadTemplate() : Response
    {
        $name = '合同表导入模板';
        $dirName = public_path() . "/excel/$name.xlsx";
        if (file_exists($dirName)) {
            return response()->download($dirName, urlencode($name));
        } else {
            throw new ApiException('模板不存在');
        }
    }
}
