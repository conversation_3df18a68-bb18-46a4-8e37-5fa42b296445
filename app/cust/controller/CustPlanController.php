<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use app\cust\model\CustPlanItem;
use app\utils\Dao;
use DI\Attribute\Inject;
use plugin\saiadmin\basic\BaseController;
use app\cust\logic\CustPlanLogic;
use app\cust\validate\CustPlanValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;

/**
 * @Apidoc\Title("服务规划")
 */
class CustPlanController extends BaseController
{
    protected string $pk = "cust_plan_id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new CustPlanLogic();
        $this->validate = new CustPlanValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("创建规划项目新")
     * @Apidoc\Url("/cust/CustPlan/createPlanItemContent")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function createPlanItemContent(Request $request) : Response
    {
        $params = $request->all();
        $planData = $params;
        unset($planData['cust_plan_item']);
        $custPlanItemModel = new CustPlanItem();
        if($params['cust_plan_id'] == 0) {
            // 检查用户是否存在执行的规划
            if($this->dao->has('cust_plan',[
                'cust_uid' => $params['cust_uid'],
                'cust_plant_execution_status[!]' => 2,
                'delete_time' => null
            ])) {
                return $this->fail('该被照顾人存在未终止的规划');
            }
            // 新增
            $planData['created_by'] = $request->adminId;
            $planData['updated_by'] = $request->adminId;
            $planData['create_time'] = date('Y-m-d H:i:s');
            $planData['update_time'] = date('Y-m-d H:i:s');
            $result = $this->logic->insertGetId($planData);
            $params['cust_plan_id'] = $result;
        } else {
            $info = $this->logic->find($params['cust_plan_id']);
            if (!$info) {
                return $this->fail('没有找到该数据');
            }
            $result = $this->logic->update($planData, [$this->pk => $params['cust_plan_id']]);
            // 更新配菜
            $this->dao->del('cust_plan_item',[
                'cust_plan_id' => $params['cust_plan_id'],
            ]);
        }
        if ($result) {
            // 添加项目
            if(!empty($params['cust_plan_item'])) {
                $itemArr = [];
                foreach ($params['cust_plan_item'] as $v) {
                    // 验证
                    if($v['cust_plan_item_end_time'] < $v['cust_plan_item_start_time']) {
                        return $this->fail('服务结束时间必须大于服务开始时间');
                    }
                    $value['cust_plan_id'] = $params['cust_plan_id'];
                    $value['cust_plan_item_template_id'] = $v['cust_plan_item_template_id'];
                    $templateInfo = $this->dao->get('cust_plan_item_template',[
                        'cust_plan_item_template_id' => $value['cust_plan_item_template_id'],
                    ],['cust_plan_item_template_name','cust_plan_item_template_details']);
                    $value['cust_plan_item_name'] = $templateInfo['cust_plan_item_template_name'];
                    $value['cust_plan_item_details'] = $templateInfo['cust_plan_item_template_details'];
                    $value['cust_plan_item_frequency_cate'] = $v['cust_plan_item_frequency_cate'];
                    $value['cust_plan_item_frequency_num'] = $v['cust_plan_item_frequency_num'];
                    $value['cust_plan_item_start_time'] = $v['cust_plan_item_start_time'];
                    $value['cust_plan_item_end_time'] = $v['cust_plan_item_end_time'];
                    $value['created_by'] = $request->adminId;
                    $value['updated_by'] = $request->adminId;
                    $value['create_time'] = date('Y-m-d H:i:s');
                    $value['update_time'] = date('Y-m-d H:i:s');
                    $itemArr[] = $value;
                }
                $custPlanItemModel->insertAll($itemArr);
            }
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/cust/CustPlan/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("cust_plan_name", type="varchar", require=false, desc="规划名称", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['cust_uid', ''],
            ['cust_plan_name', ''],
            ['cust_plant_review_status', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/cust/CustPlan/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_plan_name", type="varchar", require=false, desc="规划名称", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="用户编号", default="")
     * @Apidoc\Query("creator_employee_uid", type="bigint", require=false, desc="制定人", default="")
     * @Apidoc\Query("reviewer_employee_uid", type="bigint", require=false, desc="审核人", default="")
     * @Apidoc\Query("reviewer_time", type="datetime", require=false, desc="审核时间", default="")
     * @Apidoc\Query("cust_service_requirement", type="varchar", require=false, desc="服务需求", default="")
     * @Apidoc\Query("cust_service_objective", type="varchar", require=false, desc="服务目标", default="")
     * @Apidoc\Query("cust_risk_assessment", type="varchar", require=false, desc="风险评估", default="")
     * @Apidoc\Query("cust_service_delivery_method", type="varchar", require=false, desc="服务提供方式", default="")
     * @Apidoc\Query("cust_risk_mitigation_measures", type="varchar", require=false, desc="风险防范措施", default="")
     * @Apidoc\Query("cust_plant_start_time", type="datetime", require=false, desc="规划开始时间", default="")
     * @Apidoc\Query("cust_plant_end_time", type="datetime", require=false, desc="规划结束时间", default="")
     * @Apidoc\Query("cust_plant_execution_status", type="tinyint", require=false, desc="服务规划执行状态", default="")
     * @Apidoc\Query("cust_plant_review_status", type="tinyint", require=false, desc="服务规划审核状态:", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/cust/CustPlan/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("cust_plan_name", type="varchar", require=false, desc="规划名称", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="用户编号", default="")
     * @Apidoc\Query("creator_employee_uid", type="bigint", require=false, desc="制定人", default="")
     * @Apidoc\Query("reviewer_employee_uid", type="bigint", require=false, desc="审核人", default="")
     * @Apidoc\Query("reviewer_time", type="datetime", require=false, desc="审核时间", default="")
     * @Apidoc\Query("cust_service_requirement", type="varchar", require=false, desc="服务需求", default="")
     * @Apidoc\Query("cust_service_objective", type="varchar", require=false, desc="服务目标", default="")
     * @Apidoc\Query("cust_risk_assessment", type="varchar", require=false, desc="风险评估", default="")
     * @Apidoc\Query("cust_service_delivery_method", type="varchar", require=false, desc="服务提供方式", default="")
     * @Apidoc\Query("cust_risk_mitigation_measures", type="varchar", require=false, desc="风险防范措施", default="")
     * @Apidoc\Query("cust_plant_start_time", type="datetime", require=false, desc="规划开始时间", default="")
     * @Apidoc\Query("cust_plant_end_time", type="datetime", require=false, desc="规划结束时间", default="")
     * @Apidoc\Query("cust_plant_execution_status", type="tinyint", require=false, desc="服务规划执行状态", default="")
     * @Apidoc\Query("cust_plant_review_status", type="tinyint", require=false, desc="服务规划审核状态:", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/cust/CustPlan/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            // 添加配菜
            $data['cust_plan_item'] = $this->dao->search('cust_plan_item',[
                'cust_plan_id' => $id,
            ]);
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/cust/CustPlan/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/cust/CustPlan/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/cust/CustPlan/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/cust/CustPlan/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/cust/CustPlan/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("获取选中数据")
     * @Apidoc\Url("/cust/CustPlan/getList")
     * @Apidoc\Method("get")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function getSelectedInfo(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        $type = $request->input('type', 'auditSet');
        if (!empty($ids)) {
            $data = $this->logic->whereIn($this->pk,$ids)->select()->toArray();
            if(empty($data)) {
                return $this->fail('数据无效');
            }
            // 进行按钮方法判断
            $noData = [];
            $selectData = [];
            if($type == 'auditSet') {
                // 检查是否为未审核状态
                foreach ($data as $v) {
                    if($v['cust_plant_review_status'] == 1) {
                        $selectData[] = $v;
                    } else {
                        $noData[] = $v;
                    }
                }
            }
            if($type == 'pauseSet') {
                // 检查是否为正常状态
                foreach ($data as $v) {
                    if($v['cust_plant_execution_status'] == 1) {
                        $selectData[] = $v;
                    } else {
                        $noData[] = $v;
                    }
                }
            }
            if($type == 'recoverSet') {
                // 检查是否为暂停、延期状态
                foreach ($data as $v) {
                    if(in_array($v['cust_plant_execution_status'],[3,4])) {
                        $selectData[] = $v;
                    } else {
                        $noData[] = $v;
                    }
                }
            }
            if($type == 'postponeSet') {
                // 检查是否为正常状态
                foreach ($data as $v) {
                    if(in_array($v['cust_plant_execution_status'],[1,3])) {
                        $selectData[] = $v;
                    } else {
                        $noData[] = $v;
                    }
                }
            }
            if($type == 'terminateSet') {
                // 检查是否为正常状态
                foreach ($data as $v) {
                    if(in_array($v['cust_plant_execution_status'],[1,3,4])) {
                        $selectData[] = $v;
                    } else {
                        $noData[] = $v;
                    }
                }
            }
            return $this->success([
                'selectData' => $selectData,
                'noData' => $noData
            ]);
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("设置选中数据值")
     * @Apidoc\Url("/cust/CustPlan/setSelectValue")
     * @Apidoc\Method("get")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function setSelectValue(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        $type = $request->input('type', 'auditSet');
        $agreeStatus = $request->input('agreeStatus', 1);
        if (!empty($ids)) {
            $getIdWhere = match ($type) {
                'auditSet' => [
                    'cust_plant_review_status' => 1
                ],
                'pauseSet' => [
                    'cust_plant_execution_status' => 1
                ],
                'recoverSet' => [
                    'cust_plant_execution_status' => [3,4]
                ],
                'postponeSet' => [
                    'cust_plant_execution_status' => [1, 3]
                ],
                'terminateSet' => [
                    'cust_plant_execution_status' => [1, 3, 4]
                ],
            };
            $data = $this->logic->where($getIdWhere)->whereIn($this->pk,$ids)->column('cust_plan_id');
            if(empty($data)) {
                return $this->fail('请选择有效数据');
            }
            // 进行按钮方法判断
            if($type == 'auditSet') {
                $updateData = [
                    'creator_employee_uid' => $request->adminId,
                    'reviewer_time' => date('Y-m-d H:i:s')
                ];
                // 审核通过状态
                if($agreeStatus == 1) {
                    $updateData['cust_plant_review_status'] = 2;
                    $updateData['reviewer_employee_uid'] = $request->adminId;
                }
                // 审核不通过
                if($agreeStatus == 2) {
                    $updateData['cust_plant_review_status'] = 3;
                }
                $result = $this->logic->where([
                    'cust_plant_review_status'=>1
                ])->whereIn($this->pk,$ids)->update($updateData);
                if($result) {
                    // 记录修改日志
                    foreach ($data as $v) {
                        setRecord('cust_plan',$v,$request->adminName .'修改服务规划审核状态为'. ($agreeStatus == 2 ? "审核通过" : "审核不通过"));
                    }
                    return $this->success('提交成功');
                }
            }
            if($type == 'pauseSet') {
                $updateData = [
                    'cust_plant_execution_status' => 3,
                ];
                $result = $this->logic->where([
                    'cust_plant_execution_status' => 1
                ])->whereIn($this->pk,$ids)->update($updateData);
                if($result) {
                    // 记录修改日志
                    foreach ($data as $v) {
                        setRecord('cust_plan',$v,$request->adminName .'修改服务规划执行状态为暂停');
                    }
                    return $this->success('提交成功');
                }
            }
            if($type == 'recoverSet') {
                $updateData = [
                    'cust_plant_execution_status' => 1,
                ];
                $result = $this->logic->where([
                    'cust_plant_execution_status' => [3,4]
                ])->whereIn($this->pk,$ids)->update($updateData);
                if($result) {
                    // 记录修改日志
                    foreach ($data as $v) {
                        setRecord('cust_plan',$v,$request->adminName .'修改服务规划执行状态为正常');
                    }
                    return $this->success('提交成功');
                }
            }
            if($type == 'postponeSet') {
                $updateData = [
                    'cust_plant_execution_status' => 4,
                ];
                $result = $this->logic->where([
                    'cust_plant_execution_status' => [1, 3]
                ])->whereIn($this->pk,$ids)->update($updateData);
                if($result) {
                    // 记录修改日志
                    foreach ($data as $v) {
                        setRecord('cust_plan',$v,$request->adminName .'修改服务规划执行状态为延期');
                    }
                    return $this->success('提交成功');
                }
            }
            if($type == 'terminateSet') {
                $updateData = [
                    'cust_plant_execution_status' => 2,
                ];
                $result = $this->logic->where([
                    'cust_plant_execution_status' => [1, 3, 4]
                ])->whereIn($this->pk,$ids)->update($updateData);
                if($result) {
                    // 记录修改日志
                    foreach ($data as $v) {
                        setRecord('cust_plan',$v,$request->adminName .'修改服务规划执行状态为停止');
                    }
                    return $this->success('提交成功');
                }
            }
            return $this->fail('提交失败，请重新提交');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

}
