<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use plugin\saiadmin\basic\BaseController;
use app\cust\logic\CustBalanceRecordLogic;
use app\cust\validate\CustBalanceRecordValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;

/**
 * @Apidoc\Title("客户余额记录表")
 */
class CustBalanceRecordController extends BaseController
{
    protected string $pk = "balance_record_id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new CustBalanceRecordLogic();
        $this->validate = new CustBalanceRecordValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/cust/CustBalanceRecord/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("balance_id", type="bigint", require=false, desc="客户余额id", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户id", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['balance_id', ''],
            ['cust_uid', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("balance_id", type="bigint", require=false, desc="客户余额id", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户id", default="")
     * @Apidoc\Query("pm", type="tinyint", require=false, desc="0 = 支出 1 = 获得", default="")
     * @Apidoc\Query("usage_date", type="date", require=false, desc="使用日期", default="")
     * @Apidoc\Query("record_title", type="varchar", require=false, desc="记录标题", default="")
     * @Apidoc\Query("record_details_category", type="varchar", require=false, desc="明细种类", default="")
     * @Apidoc\Query("record_details_type", type="varchar", require=false, desc="明细类型0套餐消费、1每月账户结余转移、2缴费、3服务订单消费、4运营补贴、5缴费作废", default="")
     * @Apidoc\Query("number", type="decimal", require=false, desc="明细数字", default="")
     * @Apidoc\Query("forward", type="decimal", require=false, desc="修改前金额", default="")
     * @Apidoc\Query("balance", type="decimal", require=false, desc="修改后金额", default="")
     * @Apidoc\Query("record_mark", type="varchar", require=false, desc="记录备注", default="")
     * @Apidoc\Query("record_status", type="tinyint", require=false, desc="记录状态(0 = 待确定 1 = 有效 -1 = 无效)", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("balance_id", type="bigint", require=false, desc="客户余额id", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户id", default="")
     * @Apidoc\Query("pm", type="tinyint", require=false, desc="0 = 支出 1 = 获得", default="")
     * @Apidoc\Query("usage_date", type="date", require=false, desc="使用日期", default="")
     * @Apidoc\Query("record_title", type="varchar", require=false, desc="记录标题", default="")
     * @Apidoc\Query("record_details_category", type="varchar", require=false, desc="明细种类", default="")
     * @Apidoc\Query("record_details_type", type="varchar", require=false, desc="明细类型0套餐消费、1每月账户结余转移、2缴费、3服务订单消费、4运营补贴、5缴费作废", default="")
     * @Apidoc\Query("number", type="decimal", require=false, desc="明细数字", default="")
     * @Apidoc\Query("forward", type="decimal", require=false, desc="修改前金额", default="")
     * @Apidoc\Query("balance", type="decimal", require=false, desc="修改后金额", default="")
     * @Apidoc\Query("record_mark", type="varchar", require=false, desc="记录备注", default="")
     * @Apidoc\Query("record_status", type="tinyint", require=false, desc="记录状态(0 = 待确定 1 = 有效 -1 = 无效)", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/cust/CustBalanceRecord/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/cust/CustBalanceRecord/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

}
