<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use DateTime;
use app\employee\logic\EmployeeUserLogic;
use app\cust\logic\CustUserLogic;
use app\cust\logic\RecordLogsLogic;
use app\utils\Excel;
use plugin\saiadmin\basic\BaseController;
use app\cust\logic\CustServiceRecordLogic;
use app\cust\validate\CustServiceRecordValidate;
use plugin\saiadmin\exception\ApiException;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * @Apidoc\Title("服务记录")
 */
class CustServiceRecordController extends BaseController
{
    protected string $pk = "id";

    protected array $translate = [
        'cust_uid' => [
            'name' => '姓名',
            'type' => 'table',
            'label' => 'cust_user',
            'linkId' => 'cust_uid',
            'linkValue' => 'cust_name',
        ],
        'cust_evaluation_level' => [
            'name' => '失能等级',
            'type' => 'dict',
            'label' => 'cust_evaluation_level',
        ],
        'cust_is_bed' => [
            'name' => '人员类型',
            'type' => 'dict',
            'label' => 'cust_is_bed',
        ],
        'service_items' => [
            'name' => '服务项目',
            'type' => 'table',
            'label' => 'cust_plan_item_template',
            'linkId' => 'cust_plan_item_template_id',
            'linkValue' => 'cust_plan_item_template_name',
        ],
        'dispatch_personnel' => [
            'name' => '派工人员',
            'type' => 'table',
            'label' => 'eb_system_user',
            'linkId' => 'id',
            'linkValue' => 'nickname',
        ],
        // 'service_record_personnel' => [
        //     'name' => '录单人员',
        //     'type' => 'table',
        //     'label' => 'eb_system_user',
        //     'linkId' => 'id',
        //     'linkValue' => 'nickname',
        // ],
        // 'elderly_care_consultant' => [
        //     'name' => '养老顾问',
        //     'type' => 'table',
        //     'label' => 'eb_system_user',
        //     'linkId' => 'id',
        //     'linkValue' => 'nickname',
        // ],
        'service_personnel' => [
            'name' => '服务人员',
            'type' => 'table',
            'label' => 'employee_user',
            'linkId' => 'employee_uid',
            'linkValue' => 'employee_name',
        ],
        'service_organization' => [
            'name' => '服务机构',
            'type' => 'table',
            'label' => 'company_depart',
            'linkId' => 'company_cid',
            'linkValue' => 'company_name',
        ],
    ];

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new CustServiceRecordLogic();
        $this->validate = new CustServiceRecordValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/cust/CustServiceRecord/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("cust_uid", type="varchar", require=false, desc="服务老人", default="")
     * @Apidoc\Query("service_items", type="varchar", require=false, desc="服务项目", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['cust_uid', ''],
            ['cust_evaluation_level', ''],
            ['cust_is_bed', ''],
            ['service_items', ''],
            ['service_date', ''],
            ['status', ''],
            ['service_record_personnel', ''],
            ['dispatch_personnel', ''],
            ['service_personnel', ''],
            ['elderly_care_consultant', ''],
            ['service_organization', ''],
            ['is_haidian', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);

        $data1 =  $data['data'];
        // var_dump('data1-=-=-=---------------',$data1);
        if (!empty($data1)) {
            // var_dump('data1-=-=-=---------------',$data1);
            foreach ($data1 as &$v) {

                $service_picture = $v['service_picture'] ?? [];
                
                $result = [];
                foreach ($service_picture as $key => $value) {
                    $result[] = $value;  // 将每个值添加到结果数组
                }
                $v['service_picture']  = $result;
                // var_dump('result-=-=-=---------------',$result);

                // 查询用户信息
                $custInfo = $this->dao->get('cust_user',[ 'cust_uid' => $v['cust_uid'], ], ['cust_uid', 'cust_name','cust_private_phone','cust_city','cust_live_address','cust_evaluation_level','cust_is_bed' ]);
                $v['cust_name']  = $custInfo["cust_name"];
                $v['custInfo']  = $custInfo;
            }
            $data['data'] = $data1;
        }

        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/cust/CustServiceRecord/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_uid", type="varchar", require=false, desc="服务老人", default="")
     * @Apidoc\Query("cust_disability_level", type="varchar", require=false, desc="失能等级", default="")
     * @Apidoc\Query("cust_is_bed", type="varchar", require=false, desc="人员类型", default="")
     * @Apidoc\Query("service_items", type="varchar", require=false, desc="服务项目", default="")
     * @Apidoc\Query("service_date", type="datetime", require=false, desc="服务日期", default="")
     * @Apidoc\Query("service_duration", type="varchar", require=false, desc="服务时长", default="")
     * @Apidoc\Query("service_amount", type="varchar", require=false, desc="服务金额", default="")
     * @Apidoc\Query("dispatch_personnel", type="varchar", require=false, desc="派工人员", default="")
     * @Apidoc\Query("service_personnel", type="varchar", require=false, desc="服务人员", default="")
     * @Apidoc\Query("elderly_care_consultant", type="varchar", require=false, desc="养老顾问", default="")
     * @Apidoc\Query("service_organization", type="varchar", require=false, desc="服务机构", default="")
     * @Apidoc\Query("status", type="tinyint", require=false, desc="服务状态", default="")
     * @Apidoc\Query("service_picture", type="varchar", require=false, desc="服务图片", default="")
     * @Apidoc\Query("cust_signature", type="varchar", require=false, desc="客户签名", default="")
     * @Apidoc\Query("service_remarks", type="varchar", require=false, desc="服务备注", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/cust/CustServiceRecord/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("cust_uid", type="varchar", require=false, desc="服务老人", default="")
     * @Apidoc\Query("cust_disability_level", type="varchar", require=false, desc="失能等级", default="")
     * @Apidoc\Query("cust_is_bed", type="varchar", require=false, desc="人员类型", default="")
     * @Apidoc\Query("service_items", type="varchar", require=false, desc="服务项目", default="")
     * @Apidoc\Query("service_date", type="datetime", require=false, desc="服务日期", default="")
     * @Apidoc\Query("service_duration", type="varchar", require=false, desc="服务时长", default="")
     * @Apidoc\Query("service_amount", type="varchar", require=false, desc="服务金额", default="")
     * @Apidoc\Query("dispatch_personnel", type="varchar", require=false, desc="派工人员", default="")
     * @Apidoc\Query("service_personnel", type="varchar", require=false, desc="服务人员", default="")
     * @Apidoc\Query("elderly_care_consultant", type="varchar", require=false, desc="养老顾问", default="")
     * @Apidoc\Query("service_organization", type="varchar", require=false, desc="服务机构", default="")
     * @Apidoc\Query("status", type="tinyint", require=false, desc="服务状态", default="")
     * @Apidoc\Query("service_picture", type="varchar", require=false, desc="服务图片", default="")
     * @Apidoc\Query("cust_signature", type="varchar", require=false, desc="客户签名", default="")
     * @Apidoc\Query("service_remarks", type="varchar", require=false, desc="服务备注", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->findOrEmpty($id)->toArray();
        if (empty($info)) {
            return $this->fail('没有找到该数据');
        }
        // 检测更改数据
        $updateInfo = compareArrayChanges($info,$data);
        if(!empty($updateInfo)) {
            $recordLogs = new RecordLogsLogic();
            foreach ($updateInfo as $v) {
                if(!empty($this->translate[$v['key']])) {
                    $content = $this->translate[$v['key']]['name'] . '由' . $this->convertTranslate($v['old_value'],$v['key'])
                        . '变更为' . $this->convertTranslate($v['new_value'],$v['key']);
                } else {
                    $content = $this->convertTranslate($v['old_value'],$v['key']) . '变更为' . $this->convertTranslate($v['new_value'],$v['key']);
                }
                $recordLogs->save([
                    'logs_classify' => 'cust_service_record',
                    'link_id' => $info['id'],
                    'logs_content' => $content,
                ]);
            }
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/cust/CustServiceRecord/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/cust/CustServiceRecord/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/cust/CustServiceRecord/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误,请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/cust/CustServiceRecord/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/cust/CustServiceRecord/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/cust/CustServiceRecord/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("导入数据")
     * @Apidoc\Url("/cust/CustServiceRecord/import")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function import(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '被照护人姓名'=>'cust_uid',
            '被照护人联系方式'=>'cust_phone',
            '实际服务'=>'service_items',
            '补贴类别'=>'haidian_service_type',
            '人员类型'=>'cust_is_bed',
            '失能等级'=>'cust_evaluation_level',
            '服务折合时间'=>'service_duration',
            '金额'=>'service_amount',
            '服务状态'=>'status',
            '录单人员'=>'service_record_personnel',
            '派工人员'=>'dispatch_personnel',
            '服务人员'=>'service_personnel',
            '养老顾问'=>'elderly_care_consultant',
            '服务机构'=>'service_organization',
            '服务项目'=>'haidian_service_items',
            '服务日期'=>'service_date',
            '服务开始时间'=>'service_start_date',
            '服务结束时间'=>'service_end_date',
            '登记时间'=>'haidian_date',
            '计价方式'=>'service_pricing_manner',
            '单价'=>'service_price',
            '数量'=>'service_number',
        ];
        // 获取数据
        $data = Excel::import($header);
        // var_dump('data-=-=-=--------',$data);
        if(!empty($data)) {
            $insertArr = [];
            // 导入时添加老人姓名
            foreach ($data as $v) {
                // var_dump('$v-=-=-=-------11-',$v);
                if(!empty($v['cust_uid'])){
                    $v['cust_name'] = $v['cust_uid'];
                    $v['service_personnel_name'] = $v['service_personnel'];
                    $v['cust_evaluation_level1'] = $v['cust_evaluation_level'];
                    $v['cust_is_bed1'] = $v['cust_is_bed'];
                    $insertArr[] = $v;
                }
            }

            $data = $this->convertTranslate($insertArr,'',false);
            // var_dump('data-=111-=-=---------------',$data);
            $insertArr = [];// 修改后的数据
            $insertArr1 = [];// 已录入客户  避免重复录入客户
            $insertArr2 = [];// 已录入服务人员  避免重复录入
            // 修改导入数据
            foreach ($data as $v) {
                $v['status'] = '已完成';
                // var_dump('$v[service_date]-=-=-=-------11-',$v['service_date'] );
                
                if(!empty($v['service_items'])) {
                    $v['is_haidian'] = 0;
                }else{
                    $v['is_haidian'] = 1;
                    // Excel 日期序列号
                    if(!str_contains($v['service_date'],'-')) {
                        $excel_date = $v['service_date'];
                        var_dump($excel_date);
                        // 转换为 Unix 时间戳
                        $unix_timestamp = ($excel_date - 25569) * 86400; // 25569 是 Excel 日期与 Unix 时间戳之间的差异（1970年1月1日）
                        // 创建 DateTime 对象
                        $date = new DateTime();
                        $date->setTimestamp($unix_timestamp);
                        $v['service_date']  = $date->format('Y-m-d');
                    }
                    // var_dump('date-=-=-=-------11-',$date );
                    // var_dump('haidian_service_items-=-=-=---------------',$v['haidian_service_items']);
                    $v['service_start_date']  = $v['service_date'] . ' ' . $v['service_start_date'];
                    $v['service_end_date']  = $v['service_date'] . ' ' . $v['service_end_date'];
                    // var_dump('service_date-=-=-=---------------',$v['service_date'] );
                    // var_dump('service_start_date-=-=-=---------------',$v['service_start_date'] );
                }

                if(empty($v['cust_evaluation_level'])) {
                    $v['cust_evaluation_level'] = match ($v['cust_evaluation_level1'] ) {
                        '重度失能' => 4,
                        '重度' => 4,
                        '中度失能' => 3,
                        '中度' => 3,
                        '轻度失能' => 2,
                        '轻度' => 2,
                        default => 1
                    };
                }

                if(empty($v['cust_is_bed'])) {
                    $v['cust_is_bed'] = match ($v['cust_is_bed1'] ) {
                        '基本服务对象' => 3,
                        '家庭床位' => 2,
                        '家床' => 2,
                        '加床' => 2,
                        default => 1
                    };
                }

                if(empty($v['service_organization'])) {
                    $v['service_organization'] = 3;
                }

                // 没有匹配到对应客户
                if(empty($v['cust_uid'])){
                    // 不是已新增的客户
                    if(empty($insertArr1[$v['cust_name']])){
                        $custUser = new CustUserLogic();

                        // 如果养老顾问是杨爱莲就换成左庆刚，不是根据姓名查找，查找不到就暂时归admin账号
                        if(isset($v['elderly_care_consultant'])){//  检查键是否存在
                            $cust_consultant = $v['elderly_care_consultant']=='杨爱莲'?48
                            :$this->dao->get('eb_system_user',[ 'nickname' =>  $v['elderly_care_consultant'] ],'id');
                        }else{
                            $cust_consultant = 1;
                        }

                        if(!isset($v['cust_evaluation_level'])){
                            $v['cust_evaluation_level'] = 1;
                        }

                        if(!isset($v['cust_is_bed'])){
                            $v['cust_is_bed'] = 2;
                        }

                        // 获取新建客户的cust_id
                        $cust_id = $custUser->insertGetId([
                            'cust_name' => $v['cust_name'],// 客户姓名
                            'cust_private_phone' => $v['cust_phone'],// 手机号
                            'cust_evaluation_level' => $v['cust_evaluation_level'],// 评估等级
                            'cust_is_bed' => $v['cust_is_bed'],// 人员类型
                            'cust_consultant' =>$cust_consultant,// 养老顾问
                            'cust_service_site' => $v['service_organization'],// 服务机构
                            'cust_city' =>  "{\"province\":\"\",\"city\":\"\",\"area\":\"\",\"street\":\"\",\"community\":\"\"}",// 居住地址
                        ]);
                        // 更改cust_uid
                        $v['cust_uid'] = $cust_id;

                        // 加入已添加客户
                        $insertArr1[$v['cust_name']] = $cust_id;
                    }else{
                        $v['cust_uid'] = $insertArr1[$v['cust_name']];
                    }
                }

                // 获取服务人员姓名并去除两端空格
                $personnel_name = trim($v['service_personnel_name']);
                // 没有匹配到对应服务人员  且  服务人员有名字
                if(empty($v['service_personnel']) && !empty($personnel_name)){
                    // 不是已新增的客户
                    if(empty($insertArr2[$personnel_name])){
                        $EmployeeUser = new EmployeeUserLogic();

                        $employee_service_site = $v['service_organization'];

                        // 获取新建客户的cust_id
                        $service_personnel = $EmployeeUser->insertGetId([
                            'employee_name' => $personnel_name,// 服务人员姓名
                            'employee_sex' => 0,// 性别
                            'employee_id_type' => 1,// 证件类型
                            'employee_nation' => '汉族',// 民族
                            'employee_marital_status' => 6,// 婚姻状态
                            'employee_education' => 1,// 学历
                            'employee_status' => 1,// 状态
                            'employee_service_site' => $employee_service_site,// 所属机构
                        ]);
                        // 更改service_personnel
                        $v['service_personnel'] = $service_personnel;

                        // 加入已添加客户
                        $insertArr2[$v['service_personnel_name']] = $service_personnel;
                    }else{
                        $v['service_personnel'] = $insertArr2[$v['service_personnel_name']];
                    }
                }
                // var_dump('$v-=-=-=---------------',$v);

                $insertArr[] = $v;
            }

            $this->logic->saveAll($insertArr);
        }
        return $this->success($data);
    }

    /**
     * 导出数据
     * @Apidoc\Title("导出数据")
     * @Apidoc\Url("/cust/CustServiceRecord/export")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function export(Request $request) : Response
    {
        // 创建导出表
        $title = '服务记录_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '姓名',
            '失能等级',
            '人员类型',
            '服务项目',
            '服务日期',
            '服务时长',
            '服务金额',
            '服务状态',
            '录单人员',
            '派工人员',
            '服务人员',
            '养老顾问',
            '服务机构',
        ];
        $data = $this->logic->field(
            'id,cust_uid,cust_evaluation_level,
            cust_is_bed,service_items,service_date,
            service_duration,service_amount,status,service_record_personnel,
            dispatch_personnel,service_personnel,elderly_care_consultant,service_organization',
        )->select()->toArray();
        if(!empty($data)) {
            $data = $this->convertTranslate($data);
            $data = json_encode($data);
            $data = json_decode($data,true);
        }
        $url = Excel::export($title,$headers,$data);
        return response()->download($url, urlencode($title));
    }

    /**
     * @Apidoc\Title("下载导入模板")
     * @Apidoc\Url("/cust/CustServiceRecord/downloadTemplate")
     * @Apidoc\Method("GET")
     * @return Response
     */
    public function downloadTemplate() : Response
    {
        $name = '服务记录表导入模板';
        $dirName = public_path() . "/excel/$name.xlsx";
        if (file_exists($dirName)) {
            return response()->download($dirName, urlencode($name));
        } else {
            throw new ApiException('模板不存在');
        }
    }

    /**
     * @Apidoc\Title("同步记录")
     * @Apidoc\Url("/cust/CustServiceRecord/syncRecord")
     * @Apidoc\Method("GET")
     * @return Response
     */
    public function syncRecord() : Response
    {
        // 查询未同步的记录 每次10条
        $data = $this->logic->where('status',1)->limit(10)->select()->toArray();
        if(!empty($data)) {
            foreach ($data as $v) {
                // 判断用户是否创建
                if(!$this->dao->has('cust_user',[
                    'cust_name' => trim($v['cust_name'])
                ])) {
                    // 创建用户信息
                    $this->dao->insert('cust_user',[
                        'cust_name' => $v['cust_name'],
                        'cust_is_bed' => $v['cust_is_bed'] == '家床' ? 2 : 1,
                        'cust_evaluation_level' => match ($v['cust_evaluation_level'] ) {
                            '重度' => 4,
                            '中度' => 3,
                            '轻度' => 2,
                            default => 1
                        },
                        'cust_private_phone' => $v['cust_phone'],
                        'cust_live_address' => $v['cust_service_address'],
                        'created_by' => $v['created_by'],
                        'updated_by' => $v['updated_by'],
                        'update_time' => $v['update_time'],
                    ]);
                }
                // 判断派工人员是否创建
                if(!$this->dao->has('eb_system_user',[
                    'dispatch_personnel' => trim($v['dispatch_personnel'])
                ])) {
                    // 创建用户信息
                    $this->dao->insert('eb_system_user',[
                        'username' => $v['dispatch_personnel'],
                        'password' => null,
                    ]);
                }
                // 判断服务人员是否创建
                if(!$this->dao->has('employee_user',[
                    'dispatch_personnel' => trim($v['dispatch_personnel'])
                ])) {
                    // 创建用户信息
                    $this->dao->insert('employee_user',[
                        'username' => $v['dispatch_personnel'],
                        'password' => null,
                    ]);
                }
                // 判断养老顾问是否创建

                // 判断服务机构是否创建

            }
        }
        return $this->success($data);
    }
}
