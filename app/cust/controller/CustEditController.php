<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use app\controller\BaseController;
use app\utils\Dao;
use app\utils\NocoDbApi;
use DI\Attribute\Inject;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use <PERSON><PERSON>\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * @Apidoc\Title("档案编辑")
 */
class CustEditController extends BaseController
{
    /**
     * @Apidoc\Title("客户表单信息")
     * @Apidoc\Url("/cust/CustEdit/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function index(Request $request): Response
    {
        $params = $request->more([
            ['cust_uid', ''],
            ['cust_survey_template_id', ''],
            ['page',1],
            ['limit',10]
        ]);
        // 查询客户信息
        $custInfo = $this->dao->get('cust_user',[
            'cust_uid' => $params['cust_uid'],
        ]);
        if(empty($custInfo)) {
            throw new BadRequestHttpException('档案信息不存在');
        }
        $templateInfo = $this->dao->get('cust_survey_template',[
            'cust_survey_template_id' => $params['cust_survey_template_id']
        ]);
        if(empty($templateInfo)) {
            throw new BadRequestHttpException('问卷不存在');
        }
        // 查询客户问卷信息
        $offset = (int)($params['page'] - 1) * (int)$params['limit'];
        $list = NocoDbApi::send([
            'table_id' => $templateInfo['table_id'],
            'view_id' => $templateInfo['view_id'],
            'where' => "(老人编号,eq,".$params['cust_uid'].")",
            'offset' => $offset,
            'limit' => $params['limit']
        ]);
        if (!empty($list['list'])) {
            foreach ($list['list'] as &$v) {
                // 查询调查员信息
                if(!empty($v['调查员编号'])) {
                    $v['调查员名称'] = $this->dao->get('eb_system_user',[
                        'id' => $v['调查员编号']
                    ],'nickname');
                }
            }
        } else {
            $list = [];
        }
        return $this->success($list);
    }

    /**
     * @Apidoc\Title("客户服务评估查询")
     * @Apidoc\Url("/cust/CustEdit/serviceEvaluation")
     * @Apidoc\Method("GET")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function serviceEvaluation(Request $request): Response
    {
//        $data = [
//            [
//                'name' => '日常生活能力评估量表(ADL)',
//                'value' => '',
//                'input' => [
//                    [
//                        'name' => '1.进食：用合适的餐具将食物由容器。送到口中，包括用筷子、勺子或叉子取食物、对碗/碟的把持、咀嚼、吞咽等过程',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全依赖他人',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：需要极大帮助',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：需部分帮助（前述某个步骤需要一定帮助）',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：可独立进食（在合理的时间内独立进食准备好的食物）',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '2.洗澡（主要参数）',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全依赖他人',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：在洗澡过程中需他人帮助',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：准备好洗澡水后，可自己独立完成',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：可独立完成',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '2.a修饰：（背景参数）洗脸、刷牙、梳头、刮脸',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'option' => [
//                            [
//                                'label' => '可自己独立完成',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '需他人帮助',
//                                'value' => '1',
//                            ]
//                        ],
//                    ],
//                    [
//                        'name' => '3.穿衣：包括穿/脱衣服、系扣子、拉拉链、穿/脱鞋袜、系鞋带等',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全依赖他人',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：需要极大帮助',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：需部分帮助（能自己穿或脱，但需他人帮助整理）',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：可独立完成',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '4.如厕：包括擦净、整理衣裤、冲水',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全依赖他人',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：需要极大帮助',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：需部分帮助（需他人搀扶、需他人帮忙冲水或整理衣裤等）',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：可独立完成',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '4.a. 大便控制（背景参数）',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'option' => [
//                            [
//                                'label' => '可控制大便',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '偶尔失控',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '完全失控',
//                                'value' => '2',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '4.b. 小便控制（背景参数）',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'option' => [
//                            [
//                                'label' => '可控制小便',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '偶尔失控',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '完全失控',
//                                'value' => '2',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '5. 移位转移(上下床)： （主要参数）',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全依赖他人',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：需要极大帮助（较大程度上依赖他人搀扶和帮助）',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：需部分帮助（需他人搀扶或使用拐杖）',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：可独立完成',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '6. 平地行走：（主要参数）',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全依赖他人',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：需要极大帮助（较大程度上需依赖他人搀扶，或坐在轮椅上自行在平地上移动）',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：需部分帮助（需他人搀扶,或使用拐杖、助行器等辅助器具）',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：可独立在平地上行走50 公尺',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '6.a. 上下楼梯：（背景参数）',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'option' => [
//                            [
//                                'label' => '可独立上下楼梯（连续上下10-15个台阶）',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '需部分帮助（需要他人搀扶，或使用拐杖）',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '需要极大帮助或完全依赖他人搀扶',
//                                'value' => '2',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '日常生活能力分级',
//                        'type' => 'textarea',
//                        'value' => '',
//                        'disable' => true,
//                        'isCalculate' => false,
//                        'option' => "1.重度受损 0-3分 <br> 2.中度受损：4-6分 <br> 3.轻度受损：7-12分 <br> 4.能力完好：13分或以上",
//                    ],
//                    [
//                        'name' => '程度等级',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'disable' => true,
//                        'option' => [
//                            [
//                                'label' => '正常',
//                                'value' => '0',
//                                'disable' => true,
//                            ],
//                            [
//                                'label' => '轻度',
//                                'value' => '1',
//                                'disable' => true,
//                            ],
//                            [
//                                'label' => '中度',
//                                'value' => '2',
//                                'disable' => true,
//                            ],
//                            [
//                                'label' => '重度',
//                                'value' => '3',
//                                'disable' => true,
//                            ],
//                        ],
//                    ],
//                ],
//            ],
//            [
//                'name' => '工具性日常生活活动能力评估量表（IADL）',
//                'value' => '',
//                'input' => [
//                    [
//                        'name' => '1.使用电话',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全不会使用电话',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：仅会接电话，不会拨打电话',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：仅可拨打熟悉的电话号码',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：独立使用电话，查电话号码、拨号等',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '2.上街购物',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全不会上街购物',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：每一次上街购物都需要有人陪',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：独立购买小的日常生活用品',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：独立完成所有购物',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '3.备餐',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：需要别人把饭菜煮好、摆好',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：会将已做好的饭菜加热',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：如果准备好一切烹调用的配料，会做一顿适当的饭菜',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：能独立计划、烹煮和摆设一顿适当的饭菜',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '4.处理家务',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：完全不会做家事',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '0分：所有的家事都需要别人协助',
//                                'value' => '0.1',
//                            ],
//                            [
//                                'label' => '1分：能做家事，但不能达到可被接受的整洁程度',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：能做较简单的家事，如洗碗、铺床、叠被',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：能做较繁重的家事或家事协助（重体力劳动，如洗地板、洗窗户）',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '5.服用药物',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => true,
//                        'option' => [
//                            [
//                                'label' => '0分：自己不会分配药物',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '1分：如果别人事先准备好服用的药物的独立包装，可自行服用',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '2分：需要提醒或少许协助',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '3分：能自己负责在正确的时间里用正确剂量的药物',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '工具性日常生活活动能力分级',
//                        'type' => 'textarea',
//                        'value' => '',
//                        'disable' => true,
//                        'isCalculate' => false,
//                        'option' => "1.重度受损 0-4分 <br> 2.中度受损：5-9分 <br> 3.轻度受损：10-13分 <br>4.能力完好：14分",
//                    ],
//                    [
//                        'name' => '程度等级',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'disable' => true,
//                        'option' => [
//                            [
//                                'label' => '正常',
//                                'value' => '0',
//                                'disable' => true,
//                            ],
//                            [
//                                'label' => '轻度',
//                                'value' => '1',
//                                'disable' => true,
//                            ],
//                            [
//                                'label' => '中度',
//                                'value' => '2',
//                                'disable' => true,
//                            ],
//                            [
//                                'label' => '重度',
//                                'value' => '3',
//                                'disable' => true,
//                            ],
//                        ],
//                    ],
//                ],
//            ],
//            [
//                'name' => '等级变化特殊情况',
//                'value' => '',
//                'input' => [
//                    [
//                        'name' => '是否昏迷',
//                        'type' => 'radio',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'option' => [
//                            [
//                                'label' => '是',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '否',
//                                'value' => '1',
//                            ],
//                        ],
//                    ],
//                    [
//                        'name' => '其他特殊情况：',
//                        'type' => 'checkbox',
//                        'value' => '',
//                        'isCalculate' => false,
//                        'option' => [
//                            [
//                                'label' => '经医生诊断，确诊为失智症、阿尔茨海默症和帕金森综合症;',
//                                'value' => '0',
//                            ],
//                            [
//                                'label' => '近30天内发生过2次及以上跌倒、噎食意外事件;',
//                                'value' => '1',
//                            ],
//                            [
//                                'label' => '近30天内发生过1次及以上自杀、走失意外事件;',
//                                'value' => '2',
//                            ],
//                            [
//                                'label' => '情绪与行为在过去三个月内，出现过该类行为（游走、日夜颠倒/作息混乱、语言攻击行为、肢体攻击行为、对物品的攻击行为、干扰行为、抗拒照护、妄想、幻觉、恐惧/焦虑、忧郁及负性症状、自伤行为及自杀、重复行为、其他不适当以及不洁行为）;',
//                                'value' => '3',
//                            ],
//                        ],
//                    ],
//                ],
//            ],
//        ];
//        $this->dao->update('cust_survey_template',[
//            'content' => json_encode($data)
//        ],[
//            'cust_survey_template_id'=>5,
//        ]);
        $data = $this->dao->get('cust_survey_template',[
            'cust_survey_template_id'=>5,
        ],'content');
        return $this->success([
            'form' => json_decode($data,true),
        ]);
    }

    /**
     * @Apidoc\Title("更新客户评估信息")
     * @Apidoc\Url("/cust/CustEdit/updateCustAssessment")
     * @Apidoc\Method("GET")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function updateCustAssessment(Request $request): Response
    {
        $params = $request->more([
            ['cust_uid', ''],
            ['employee_uid', ''],
            ['company_cid', ''],
            ['cust_survey_content', []],
            ['cust_survey_evaluation_time',date('Y-m-d H:i:s')],
            ['cust_sign_image',''],
            ['cust_sign_time',date('Y-m-d H:i:s')],
            ['cust_survey_assessment',''],
        ]);
        // 查询客户信息
        $custInfo = $this->dao->get('cust_uid',[
            'cust_uid' => $params['cust_uid'],
        ]);
        if(empty($custInfo)) {
            throw new BadRequestHttpException('档案信息不存在');
        }
        if(empty($params['cust_survey_content'])) {
            throw new BadRequestHttpException('请填写内容');
        }

        return $this->success($params);
    }
}
