<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\controller;

use plugin\saiadmin\basic\BaseController;
use app\cust\logic\CustUserLogic;
use app\cust\validate\CustUserValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Medoo\Medoo;
use app\utils\Excel;
use app\controller\CommonController;

/**
 * @Apidoc\Title("档案管理")
 */
class CustUserController extends BaseController
{
    protected string $pk = "cust_uid";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new CustUserLogic();
        $this->validate = new CustUserValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/cust/CustUser/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("cust_name", type="varchar", require=false, desc="姓名", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['cust_name', ''],
            ['cust_community', ''],
            ['cust_is_bed', ''],
            ['cust_evaluation_level', ''],
            ['cust_identity_type', ''],
            ['cust_area', ''],
            ['cust_service_site', ''],
            ['cust_work_unit', ''],
            ['cust_consultant', ''],
            ['cust_city', ''],
            ['cust_status', ''],
        ]);
        $query = $this->logic->search($where);
        if(request()->input('saiType', 'list') !== 'all'){
            $query = $query->with([ 'consultantLog' ]);
        }
        $data = $this->logic->getCustList($query);

        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/cust/CustUser/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_name", type="varchar", require=false, desc="姓名", default="")
     * @Apidoc\Query("cust_sex", type="tinyint", require=false, desc="性别", default="")
     * @Apidoc\Query("cust_id_card", type="varchar", require=false, desc="身份证号", default="")
     * @Apidoc\Query("cust_id_type", type="tinyint", require=false, desc="身份证类型", default="")
     * @Apidoc\Query("cust_birth", type="datetime", require=false, desc="出生年月", default="")
     * @Apidoc\Query("cust_nation", type="varchar", require=false, desc="民族", default="")
     * @Apidoc\Query("cust_private_phone", type="varchar", require=false, desc="私人电话", default="")
     * @Apidoc\Query("cust_home_phone", type="varchar", require=false, desc="家庭电话", default="")
     * @Apidoc\Query("cust_marital_status", type="tinyint", require=false, desc="婚姻状态", default="")
     * @Apidoc\Query("cust_live_status", type="tinyint", require=false, desc="居住状况", default="")
     * @Apidoc\Query("cust_identity_type", type="tinyint", require=false, desc="身份类别", default="")
     * @Apidoc\Query("cust_is_bed", type="tinyint", require=false, desc="是否家床", default="")
     * @Apidoc\Query("cust_avatar", type="varchar", require=false, desc="头像", default="")
     * @Apidoc\Query("cust_belief", type="tinyint", require=false, desc="宗教信仰", default="")
     * @Apidoc\Query("cust_regist_address", type="varchar", require=false, desc="户籍地址", default="")
     * @Apidoc\Query("cust_live_address", type="varchar", require=false, desc="居住详细地址", default="")
     * @Apidoc\Query("cust_longitude", type="decimal", require=false, desc="经度地址", default="")
     * @Apidoc\Query("cust_latitude", type="decimal", require=false, desc="纬度地址", default="")
     * @Apidoc\Query("cust_disability_type", type="tinyint", require=false, desc="残疾类别", default="")
     * @Apidoc\Query("cust_disability_level", type="tinyint", require=false, desc="残疾等级", default="")
     * @Apidoc\Query("cust_disability_card", type="varchar", require=false, desc="残疾证号", default="")
     * @Apidoc\Query("cust_area", type="varchar", require=false, desc="所属片区", default="")
     * @Apidoc\Query("cust_remark", type="varchar", require=false, desc="客户备注", default="")
     * @Apidoc\Query("cust_evaluation_level", type="tinyint", require=false, desc="评估等级", default="")
     * @Apidoc\Query("cust_work_unit", type="varchar", require=false, desc="原工作单位", default="")
     * @Apidoc\Query("cust_service_site", type="varchar", require=false, desc="服务机构", default="")
     * @Apidoc\Query("cust_guardian_name", type="varchar", require=false, desc="监护人姓名", default="")
     * @Apidoc\Query("cust_guardian_id_card", type="varchar", require=false, desc="监护人身份证", default="")
     * @Apidoc\Query("cust_guardian_phone", type="varchar", require=false, desc="监护人电话", default="")
     * @Apidoc\Query("cust_guardian_address", type="varchar", require=false, desc="监护人家庭住址", default="")
     * @Apidoc\Query("cust_signature", type="text", require=false, desc="本人签名", default="")
     * @Apidoc\Query("cust_guardian_signature", type="text", require=false, desc="监护人签名", default="")
     * @Apidoc\Query("cust_health_records", type="text", require=false, desc="健康档案", default="")
     * @Apidoc\Query("cust_health_records2", type="text", require=false, desc="医生入户健康档案", default="")
     * @Apidoc\Query("cust_content", type="text", require=false, desc="自定义内容", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        // 检查是否选择是否家床
        if(empty($data['cust_is_bed'])) {
            return $this->fail('请选择是否家床');
        }
        // 判断是否为家庭床位老人
        if ($data['cust_is_bed'] == 2) {
            // 家庭床位老人以身份证号为唯一
            if (!empty($data['cust_id_card']) && $this->logic->where(['cust_id_card' => $data['cust_id_card']])->findOrEmpty()->toArray()) {
                return $this->fail('身份证号已注册');
            }
        } else {
            // 其他类别老人以手机号为唯一
            if (!empty($data['cust_private_phone']) && $this->logic->where(['cust_private_phone' => $data['cust_private_phone']])->findOrEmpty()->toArray()) {
                return $this->fail('私人电话已注册');
            }
        }
        if(empty($data['create_time'])) {
            $data['create_time'] = date('Y-m-d H:i:s');
        }
        if(!empty($data['cust_city']) && !is_string($data['cust_city'])) {
            $data['cust_city'] = json_encode($data['cust_city'],JSON_UNESCAPED_UNICODE);
        }
        $data['created_by'] = $request->adminId;
        $cust_uid = $this->logic->insertGetId($data);
        if ($cust_uid) {
            // 档案用户注册后置事件
            event('cust.registerAfter', [
                'cust_uid' => $cust_uid,
            ]);
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/cust/CustUser/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("cust_name", type="varchar", require=false, desc="姓名", default="")
     * @Apidoc\Query("cust_sex", type="tinyint", require=false, desc="性别", default="")
     * @Apidoc\Query("cust_id_card", type="varchar", require=false, desc="身份证号", default="")
     * @Apidoc\Query("cust_id_type", type="tinyint", require=false, desc="身份证类型", default="")
     * @Apidoc\Query("cust_birth", type="datetime", require=false, desc="出生年月", default="")
     * @Apidoc\Query("cust_nation", type="varchar", require=false, desc="民族", default="")
     * @Apidoc\Query("cust_private_phone", type="varchar", require=false, desc="私人电话", default="")
     * @Apidoc\Query("cust_home_phone", type="varchar", require=false, desc="家庭电话", default="")
     * @Apidoc\Query("cust_marital_status", type="tinyint", require=false, desc="婚姻状态", default="")
     * @Apidoc\Query("cust_live_status", type="tinyint", require=false, desc="居住状况", default="")
     * @Apidoc\Query("cust_identity_type", type="tinyint", require=false, desc="身份类别", default="")
     * @Apidoc\Query("cust_is_bed", type="tinyint", require=false, desc="是否家床", default="")
     * @Apidoc\Query("cust_avatar", type="varchar", require=false, desc="头像", default="")
     * @Apidoc\Query("cust_belief", type="tinyint", require=false, desc="宗教信仰", default="")
     * @Apidoc\Query("cust_regist_address", type="varchar", require=false, desc="户籍地址", default="")
     * @Apidoc\Query("cust_live_address", type="varchar", require=false, desc="居住详细地址", default="")
     * @Apidoc\Query("cust_longitude", type="decimal", require=false, desc="经度地址", default="")
     * @Apidoc\Query("cust_latitude", type="decimal", require=false, desc="纬度地址", default="")
     * @Apidoc\Query("cust_disability_type", type="tinyint", require=false, desc="残疾类别", default="")
     * @Apidoc\Query("cust_disability_level", type="tinyint", require=false, desc="残疾等级", default="")
     * @Apidoc\Query("cust_disability_card", type="varchar", require=false, desc="残疾证号", default="")
     * @Apidoc\Query("cust_area", type="varchar", require=false, desc="所属片区", default="")
     * @Apidoc\Query("cust_remark", type="varchar", require=false, desc="客户备注", default="")
     * @Apidoc\Query("cust_evaluation_level", type="tinyint", require=false, desc="评估等级", default="")
     * @Apidoc\Query("cust_work_unit", type="varchar", require=false, desc="原工作单位", default="")
     * @Apidoc\Query("cust_service_site", type="varchar", require=false, desc="服务机构", default="")
     * @Apidoc\Query("cust_guardian_name", type="varchar", require=false, desc="监护人姓名", default="")
     * @Apidoc\Query("cust_guardian_id_card", type="varchar", require=false, desc="监护人身份证", default="")
     * @Apidoc\Query("cust_guardian_phone", type="varchar", require=false, desc="监护人电话", default="")
     * @Apidoc\Query("cust_guardian_address", type="varchar", require=false, desc="监护人家庭住址", default="")
     * @Apidoc\Query("cust_signature", type="text", require=false, desc="本人签名", default="")
     * @Apidoc\Query("cust_guardian_signature", type="text", require=false, desc="监护人签名", default="")
     * @Apidoc\Query("cust_health_records", type="text", require=false, desc="健康档案", default="")
     * @Apidoc\Query("cust_health_records2", type="text", require=false, desc="医生入户健康档案", default="")
     * @Apidoc\Query("cust_content", type="text", require=false, desc="自定义内容", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->all();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        // 检查是否选择是否家床
        if(empty($data['cust_is_bed'])) {
            return $this->fail('请选择是否家床');
        }
        // 判断是否为家庭床位老人
        if ($data['cust_is_bed'] == 2) {
            // 家庭床位老人以身份证号为唯一
            if (!empty($data['cust_id_card']) && $this->logic->where([
                    ['cust_id_card' ,'=', $data['cust_id_card']],
                    [$this->pk, '<>', $id]
                ])->findOrEmpty()->toArray()) {
                return $this->fail('身份证号已注册');
            }
        } else {
            // 其他类别老人以手机号为唯一
            if (!empty($data['cust_private_phone']) && $this->logic->where([
                    ['cust_private_phone' ,'=', $data['cust_private_phone']],
                    [$this->pk, '<>', $id]
                ])->findOrEmpty()->toArray()) {
                return $this->fail('私人电话已注册');
            }
        }

        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            // 档案用户修改后置事件
            event('cust.updateAfter', [
                'cust_uid' => $id,
            ]);
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/cust/CustUser/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("通过手机号读取数据")
     * @Apidoc\Url("/cust/CustUser/getPhone")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("phone", type="int", require=true, desc="手机号", default="")
     * @param Request $request
     * @return Response
     */
    public function getPhone(Request $request) : Response
    {
        $phone = $request->input('phone');
        $data = $this->logic->where([
            [
                'cust_private_phone','=',$phone
            ]
        ])->findOrEmpty()->toArray();
        if ($data) {
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改养老档案")
     * @Apidoc\Url("/cust/CustUser/updateHealth")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_uid", type="int", require=true, desc="用户Id", default="")
     * @Apidoc\Query("cust_health_records", type="string", require=true, desc="养老档案", default="")
     * @param Request $request
     * @return Response
     */
    public function updateHealth(Request $request) : Response
    {
        $id = $request->input('cust_uid');
        $health = $request->input('cust_health_records');
        $result = $this->logic->where($this->pk, $id)->update(['cust_health_records' => $health]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改巡诊档案")
     * @Apidoc\Url("/cust/CustUser/updateHealth2")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_uid", type="int", require=true, desc="用户Id", default="")
     * @Apidoc\Query("cust_health_records2", type="string", require=true, desc="巡诊档案", default="")
     * @param Request $request
     * @return Response
     */
    public function updateHealth2(Request $request) : Response
    {
        $id = $request->input('cust_uid');
        $health = $request->input('cust_health_records2');
        $this->logic->where($this->pk, $id)->update(['cust_health_records2' => $health]);
        return $this->success('操作成功');
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/cust/CustUser/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/cust/CustUser/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            // 添加档案用户删除后置事件
            event('cust.deleteAfter', [
                'cust_uid' => $ids
            ]);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/cust/CustUser/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/cust/CustUser/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/cust/CustUser/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }


    /**
     * @Apidoc\Title("查询巡诊档案列表")
     * @Apidoc\Url("/cust/CustUser/getHealthRecords")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("cust_name", type="varchar", require=false, desc="姓名", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function getHealthRecords(Request $request): Response
    {
        $params = $request->only(
            ['cust_name',],
        );
        $where = [
            'cust_name' => $params['cust_name'],
        ];

        $query = $this->logic->search($where)->where('cust_health_records2','>',0)
            // 1. 过滤掉包含 {"label": "姓名", "value": null} 的 JSON 字符串的记录
            ->whereRaw('NOT JSON_CONTAINS(cust_health_records2, \'{"label": "姓名","value": null}\')')
            // 2. 过滤掉没有选择性别的
            ->whereRaw('NOT JSON_CONTAINS(cust_health_records2, \'{"label": "性别","value": null}\')')
            
            // 服务记录中存在 service_items = 35 的记录   只展示有上门巡诊和电话巡诊的档案
            ->whereRaw('EXISTS (SELECT 1 FROM cust_service_record WHERE cust_service_record.cust_uid = cust_user.cust_uid AND (cust_service_record.service_items = 35 OR cust_service_record.service_items = 95))'); 
            // ->whereRaw('EXISTS (SELECT 1 FROM cust_service_record WHERE cust_service_record.cust_uid = cust_user.cust_uid AND cust_service_record.service_items = 35)'); 

        $data = $this->logic->getCustList($query);

        $data1 =  $data['data'];
        // var_dump('data1-=-=-=---------------',$data1);
        if (!empty($data1)) {
            // var_dump('data1-=-=-=---------------',$data1);
            foreach ($data1 as &$v) {
                $v['serviceNum'] = $this->dao->search('cust_service_record',[ 
                    'cust_uid' => $v['cust_uid'], 
                    'service_items' => [35, 95],
                    'GROUP' => 'service_items',
                ],[
                    'service_items',
                    'num' => Medoo::raw('COUNT(*)')
                ]);
            }
            $data['data'] = $data1;
        }

        return $this->success($data);
    }

    /**
     * 导出数据
     * @Apidoc\Title("导出数据")
     * @Apidoc\Url("/cust/CustUser/export")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function export(Request $request) : Response
    {
        $where = $request->more([
            ['cust_name', ''],
            ['cust_community', ''],
            ['cust_is_bed', ''],
            ['cust_evaluation_level', ''],
            ['cust_identity_type', ''],
            ['cust_area', ''],
            ['cust_service_site', ''],
            ['cust_work_unit', ''],
            ['cust_consultant', ''],
            ['cust_city', ''],
            ['cust_status', ''],
        ]);
        $query = $this->logic->search($where);

        $res = $this->logic->getAllWithAuth($query);
        // var_dump('$res-=-=-=1---------------',$res);
        if(empty($res)) {
            // var_dump('$res-=-=-=1---empty------------',$res);
            // throw new BadRequestHttpException('工资填报不存在!');
            return $this->fail('老人档案不存在,请修改导出条件!');
        }
        // var_dump('$res-=-=-=2---------------',$res);
        
        $i = 1;
        $data = [];
        foreach ($res as $v) {
            $value = [];
            // 序号
            $value['number'] = $i;
            $i++;
            $value['cust_internal_number'] = $v['cust_internal_number'];
            $value['cust_name'] = $v['cust_name'];
            $value['cust_status'] = $v['cust_status'];
            $value['cust_sex'] = $v['cust_sex'] == 2 ? '女' : '男';

            $value['cust_is_bed'] = $v['cust_is_bed'] == 4 ? "巡诊服务" : ($v['cust_is_bed'] == 3 ? "基本服务对象" :($v['cust_is_bed'] == 2 ? "家庭床位" : "市场化"));
            $value['level'] = $v['cust_evaluation_level'] == 4 ? "重度失能" : ($v['cust_evaluation_level'] == 3 ? "中度失能" :
                ($v['cust_evaluation_level'] == 2 ? "轻度失能" : "能力完好"));

            $value['cust_area'] = $v['cust_area'];// 所属片区
            // var_dump('$userInfo-=-=-=---------------',$value);
            // var_dump('cust_service_site-=-=-=---------------',$v['cust_service_site']);

            // 服务机构信息
            $siteInfo = $this->dao->get('company_depart',[ 'company_cid' => $v['cust_service_site'] ], ['company_cid','company_name']);
            // var_dump('$siteInfo-=-=-=---------------',$siteInfo);
            $value['cust_service_site'] = !empty($siteInfo['company_name']) ? $siteInfo['company_name'] : '';

            $value['cust_work_unit'] = $v['cust_work_unit'];// 原工作单位

            // var_dump('cust_consultant-=-=-=---------------',$v['cust_consultant']);
            // 养老顾问信息
            $userInfo = $this->dao->get('eb_system_user',[ 'id' => $v['cust_consultant'] ], ['id','nickname']);
            // var_dump('$userInfo-=-=-=---------------',$userInfo);
            $value['cust_consultant'] = !empty($userInfo['nickname']) ? $userInfo['nickname'] : '';

            // 居住地址
            $cust_city = !empty($v['cust_city']) ? json_decode($v['cust_city'], true) : [];
            // var_dump('$cust_city-=-=-=2---------------',$cust_city);
            // $value['cust_city'] =  (
            //     (!empty($cust_city['province'] ) ? $cust_city['province'] : '') .
            //     (!empty($cust_city['city'] ) ? $cust_city['city'] : '') .
            //     (!empty($cust_city['area'] ) ? $cust_city['area'] : '') .
            //     (!empty($cust_city['street'] ) ? $cust_city['street'] : '') .
            //     (!empty($cust_city['community'] ) ? $cust_city['community'] : '') 
            //     )  . ($v['cust_live_address'] ?? "");
            $value['cust_city'] = implode('', array_map(fn($key) =>  !empty($cust_city[$key]) ? $cust_city[$key] : '',  ['province', 'city', 'area', 'street', 'community'] )) 
                . ($v['cust_live_address'] ?? "");
            // var_dump('$value[cust_city]-=-=-=2---------------',$value['cust_city']);
            
            $data[] = $value;
        }
        // var_dump('$data-=-=-=2---------------',$data);
        // return '';

        // 创建导出表
        $title = '老人档案_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '内部编号',
            '老人姓名',
            '老人状态',
            '老人性别',
            '人员类型',
            '评估等级',
            '所属片区',
            '服务机构',
            '原工作单位',
            '养老顾问',
            '居住地址',
        ];

        // var_dump('$headers-=-=-=2---------------',$headers);
        $url = Excel::export($title,$headers,$data);
        // var_dump('$url-=-=-=2---------------',$url);
        return $this->success([ 'url' => $url  ]);
    }


    /**
    * @Apidoc\Title("更新用户数据包含图片")
    * @Apidoc\Url("/cust/ContractManagement/import1")
    * @Apidoc\Method("POST")
    * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
    * @param Request $request
    * @return Response
    * @throws BadRequestHttpException
    */
    public function import1(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '姓名' => 'cust_name',
            '性别' => 'cust_sex',
            '身份证号' => 'cust_id_card',
            '出生年月' => 'cust_birth',
            '民族' => 'cust_nation',
            '户籍住址' => 'cust_regist_address',
            '现住址' => 'cust_live_address',
            '年龄' => 'age',
            '评估等级' => 'cust_evaluation_level',
            '原工作单位' => 'cust_work_unit',
            '原工作单位名称' => 'cust_unit',
            '监护人姓名' => 'cust_guardian_name',
            '监护人手机号' => 'cust_guardian_phone',
            '与申请人关系' => 'cust_guardian_relationship',
            '本人签字' => 'cust_signature',
            '监护人签字' => 'cust_guardian_signature',
        ];
        // 获取数据
        $data = Excel::import($header);
        // var_dump('data=-=-=---------------',$data);
        
        $insertArr = [];// 未修改的数据
        if(!empty($data)) {
            foreach ($data as $v) {
                if(!empty($v['cust_name'])) {
                    $user = $this->dao->get('cust_user',[ 'cust_name' => $v['cust_name'],'cust_is_bed' => 2, 'delete_time' => NULL  ],
                        ['cust_uid','cust_name','cust_id_card','cust_nation','cust_guardian_name','cust_guardian_phone','cust_guardian_relationship']);
                    // if(empty($user)) var_dump('user=-=-=---------------',$user);

                    if(!empty($user)){
                        $data1 = [
                            'cust_sex' =>  $v['cust_sex'] == '女' ? 2 : 1,
                            'cust_id_card' =>  $v['cust_id_card'] ?? $user['cust_id_card'] ?? '',
                            'cust_nation' =>  !empty($v['cust_nation']) ? ($v['cust_nation'] . '族') : ($user['cust_nation'] ?? '汉族'),
                            'cust_regist_address' =>  $v['cust_regist_address'] ?? '',
                            'cust_work_unit' =>  $v['cust_work_unit'] ?? '非中科院',
                            'cust_unit' =>  $v['cust_unit'] ?? '',
                            'cust_guardian_name' =>  $v['cust_guardian_name'] ?? $user['cust_guardian_name'] ?? '',
                            'cust_guardian_phone' =>  $v['cust_guardian_phone'] ?? $user['cust_guardian_phone'] ?? '',
                            'cust_guardian_relationship' =>  $v['cust_guardian_relationship'] ?? $user['cust_guardian_relationship'] ?? '',
                            'cust_signature' =>  $v['cust_signature'] ?? $user['cust_signature'] ?? null,
                            'cust_guardian_signature' =>  $v['cust_guardian_signature'] ?? $user['cust_guardian_signature'] ?? null,
                            'updated_by' => $request->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                        ];

                        // 小时工单价
                        if(!empty($v['cust_evaluation_level'])){
                            $data1['cust_evaluation_level'] =  match ($v['cust_evaluation_level'] ) {
                                '重度失能' => 4,
                                '重度' => 4,
                                '中度失能' => 3,
                                '中度' => 3,
                                '轻度失能' => 2,
                                '轻度' => 2,
                                default => 1
                            };
                        }
                        if(!empty($v['cust_id_card'])){
                            $idCard = $v['cust_id_card'];
                            // 18位身份证号
                            if (strlen($idCard) == 18) {
                                // 截取出生年月（第7到第14位）
                                $birthDate = substr($idCard, 6, 8); // 截取从第7位到第14位（包含）
                                
                                // 格式化出生年月（例如：19900101 -> 1990-01-01）
                                $data1['cust_birth'] = substr($birthDate, 0, 4) . '-' . substr($birthDate, 4, 2) . '-' . substr($birthDate, 6, 2);
                            }

                            // 15位身份证号（升级到18位）
                            if (strlen($idCard) == 15) {
                                // 15位身份证的出生日期为第7到第12位
                                $birthDate = substr($idCard, 6, 6); // 截取第7到第12位
                                
                                // 格式化出生年月（例如：900101 -> 1990-01-01）
                                $data1['cust_birth'] = '19' . substr($birthDate, 0, 2) . '-' . substr($birthDate, 2, 2) . '-' . substr($birthDate, 4, 2);
                            }
                            // var_dump('$v=-=-=----$data1[cust_birth]-----------',$data1['cust_birth']);
                        }

                        if(!empty($v['cust_live_address'])){
                            $cust_live_address = $v['cust_live_address'];
                            // var_dump('$v=-=-=--------$cust_live_address-------',$cust_live_address);
                            $province = '北京市';$city = '市辖区';$area = '';$street = '';$community = '';$address = '';

                            $address = explode('海淀区',$cust_live_address, 2);
                            // var_dump('$v=-=-=--------$address-------',$address);
                            if(count($address)==2){
                                $area = '海淀区';
                                $cust_live_address = $address[1];
                            }

                            $address = explode('街道',$cust_live_address, 2);
                            // var_dump('$v=-=-=--------$address---街道----',$address);
                            if(count($address)==2){
                                $street = $address[0] . '街道';
                                $cust_live_address = $address[1];
                            }
                            $address = explode('居委会',$cust_live_address, 2);
                            // var_dump('$v=-=-=--------$address---居委会----',$address);
                            if(count($address)==2){
                                $community = $address[0] . '居委会';
                                $cust_live_address = $address[1];
                            }

                            // {"province":"北京市","city":"市辖区","area":"海淀区","street":"中关村街道","community":"科源社区居委会"}
                            $cust_city = [ 'province'=>'北京市',  'city'=>'市辖区',  'area'=>'海淀区', 'street'=>$street,'community'=> $community ];

                            $data1['cust_city'] = json_encode($cust_city,JSON_UNESCAPED_UNICODE);
                            if(!empty($street))$data1['cust_street'] = $street;
                            // var_dump('$v=-=-=--------$community-------',$community,strlen($community),strlen($community)-4);
                            // strlen() 函数计算的是字节数，而不是字符数。对于包含中文的字符串，每个中文字符通常占用 3 个字节（在 UTF-8 编码中），而一个英文字符或数字则占用 1 个字节。
                            // mb_strlen($string, 'UTF-8');  // 使用 mb_strlen 计算字符数
                            if(!empty($community))$data1['cust_community'] = substr($community, 0, (strlen($community)-9));
                            $data1['cust_live_address'] = $cust_live_address;
                        }
                        // var_dump('$v=-=-=--------$data1-------',$data1);

                        // 修改档案信息
                        $this->dao->update('cust_user', $data1 , [ 'cust_uid' => $user['cust_uid'] ]);
                        // var_dump('$v=-=-=----!empty(contract_id)--1---------');
                    } else {
                        $insertArr[] = $v;
                        // var_dump('insertArr=-=-=---------------',$insertArr);
                    }
                }
            }
        }
        // var_dump('insertArr-=-=-=---------------',$insertArr);
        return $this->success($insertArr);
    }
    
    /**
    * @Apidoc\Title("更新用户数据")
    * @Apidoc\Url("/cust/ContractManagement/import2")
    * @Apidoc\Method("POST")
    * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
    * @param Request $request
    * @return Response
    * @throws BadRequestHttpException
    */
    public function import2(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '老人姓名' => 'cust_name',
            '性别' => 'cust_sex',
            '身份证' => 'cust_id_card',
            '社区' => 'cust_community',
            '服务地址' => 'cust_live_address',
            '联系电话' => 'cust_private_phone',
            '原工作单位' => 'cust_work_unit',
            '状态' => 'state',
            '监护人' => 'cust_guardian_name',
            '监护人身份证' => 'cust_guardian_id_card',
            '监护人电话' => 'cust_guardian_phone',
            '监护人家庭住址' => 'cust_guardian_address',
            '法定代表人' => 'name1',
            '系统编号' => 'cust_haidian_number',
        ];
        // 获取数据
        $data = Excel::import($header);
        // var_dump('data=-=-=---------------',$data);
        
        $insertArr = [];// 未修改的数据
        if(!empty($data)) {
            foreach ($data as $v) {
                if(!empty($v['cust_name'])) {
                    $user = $this->dao->search('cust_user',[ 'cust_name' => $v['cust_name'],'cust_is_bed' => 2, 'delete_time' => NULL  ],
                        ['cust_uid','cust_name','cust_id_card','cust_private_phone','cust_guardian_name','cust_guardian_id_card','cust_guardian_phone','cust_guardian_address',
                        'cust_signature','cust_guardian_signature',]);
                        if(!empty($user)){
                            if(count($user)>1){
                            // 重名的档案查询一样的身份证号或一样的手机号 
                            $matched = array_filter($user, function($v1) use ($v) {
                                // var_dump(' $v[cust_id_card]=-=-=---------------',$v['cust_id_card']);
                                // var_dump(' $v1[cust_id_card]=-=-=---------------',$v1['cust_id_card']);
                                
                                return $v1['cust_id_card'] == $v['cust_id_card'] || $v1['cust_private_phone'] == $v['cust_private_phone'];
                            });
                            // var_dump('matched=-=-=---------------',$matched);
                            // var_dump('matched=-=-=------$v---------',$v);
                            
                            $matched = array_values($matched); // 重置索引
                            $user = !empty($matched) ? $matched[0] : [];
                        } else {
                            // 没有重名档案
                            $user = $user[0];
                        }
                    }
                    // if(empty($user)) var_dump('empty($user)=-=-=---------------',$v);

                    if(!empty($user)){
                        $data1 = [
                            'cust_id_type' =>  1,
                            'cust_id_card' =>  $v['cust_id_card'] ?? $user['cust_id_card'] ?? '',
                            'cust_private_phone' =>  $v['cust_private_phone'] ?? $user['cust_private_phone'] ?? '',
                            'cust_guardian_name' =>  $v['cust_guardian_name'] ?? $user['cust_guardian_name'] ?? null,
                            'cust_guardian_id_card' =>  $v['cust_guardian_id_card'] ?? $user['cust_guardian_id_card'] ?? null,
                            'cust_guardian_phone' =>  $v['cust_guardian_phone'] ?? $user['cust_guardian_phone'] ?? null,
                            'cust_guardian_address' =>  $v['cust_guardian_address'] ?? $user['cust_guardian_address'] ?? null,
                            // 'cust_signature' =>  $user['cust_guardian_signature'] ?? $user['cust_signature'] ?? null,
                            // 'cust_guardian_signature' =>  $user['cust_guardian_signature'] ?? $user['cust_guardian_signature'] ?? null,
                            'updated_by' => $request->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                        ];

                        if(empty($user['cust_signature']) && !empty($v['cust_name'])){
                            // 只保留中文汉字
                            $name = preg_replace('/[^\x{4e00}-\x{9fa5}]/u', '', $v['cust_name']);
                            // var_dump('cust_signature=-=-=----------$name-----',$name);

                            $CommonController = new CommonController();
                            $img = $CommonController->getSignImage1($name);
                            // var_dump('cust_guardian_signature=-=-=----------$img-----',$img);
                            if(!empty($img))$data1['cust_signature'] = $img;
                        }

                        if(empty($user['cust_guardian_signature']) && !empty($v['cust_guardian_name'])){
                            // 只保留中文汉字
                            $name = preg_replace('/[^\x{4e00}-\x{9fa5}]/u', '', $v['cust_guardian_name']);
                            // var_dump('cust_guardian_signature=-=-=----------$name-----',$name);
                            $CommonController = new CommonController();
                            // $request->merge(['name' => $name]) // merge 添加参数
                            // $request->replace(['name' => $name]) // replace 替换所有参数
                            // var_dump('request=-=-=----------$name-----',$request);
                            // $img = $CommonController->getSignImage($request);

                            // 传递的参数
                            // $request1 = Request::create('/some-uri', 'GET', ['name' => $name]);
                            // var_dump('cust_guardian_signature=-=-=----------$request1-----',$request1);
                            // $img = $CommonController->getSignImage($request1);
                            
                            // $img = $CommonController->getSignImage(new Request('/some-uri?name=21',['name' => $name]));
                            // $img = $CommonController->getSignImage(new Request('/some-uri','GET',['name' => $name]));

                            $img = $CommonController->getSignImage1($name);
                            // var_dump('cust_guardian_signature=-=-=----------$img-----',$img);
                            if(!empty($img))$data1['cust_guardian_signature'] = $img;
                        }
                        
                        // 养老顾问
                        $id = $this->dao->get('eb_system_user',[ 'nickname' =>  $v['name1'], 'delete_time' => NULL ],'id');
                        if(!empty($id)) $data1['cust_consultant'] =  $id;

                        // 海淀区编号
                        if(!empty($v['cust_haidian_number'])) $data1['cust_haidian_number'] =  $v['cust_haidian_number'];

                        // 状态
                        if(!empty($v['state'])){
                            $level = match ($v['state'] ) {
                                '重度' => 4,
                                '中度' => 3,
                                '轻度' => 2,
                                '自理' => 1,
                                default => 0
                            };
                            $cust_status = '服务中';
                            if($level == 0){
                                $cust_status = match ($v['state'] ) {
                                    '去世' => '终止(死亡)',
                                    '转出' => '终止(转出)',
                                    '不续签' => '终止(不续签)',
                                    default => '暂停'
                                };
                            }
                            $data1['cust_status'] =  $cust_status;
                            if($level!=0)$data1['cust_evaluation_level'] =  $level;
                        }

                        if(!empty($v['cust_id_card'])){
                            $idCard = $v['cust_id_card'];
                            // 18位身份证号
                            if (strlen($idCard) == 18) {
                                // 截取出生年月（第7到第14位）
                                $birthDate = substr($idCard, 6, 8); // 截取从第7位到第14位（包含）
                                
                                // 格式化出生年月（例如：19900101 -> 1990-01-01）
                                $data1['cust_birth'] = substr($birthDate, 0, 4) . '-' . substr($birthDate, 4, 2) . '-' . substr($birthDate, 6, 2);
                            }
                            // var_dump('$v=-=-=----$data1[cust_birth]-----------',$data1['cust_birth']);

                            
                            $cust_sex = substr($idCard, 16, 1); // 截取第17位，即倒数第二位  奇数是男性，偶数是女性
                            $data1['cust_sex'] = !empty($v['cust_sex']) ? ($v['cust_sex'] == '女' ? 2 : 1) : ($cust_sex % 2 == 1 ? 1 : 2);
                        }
                        
                        // 社区
                        if(!empty($v['cust_community']))$data1['cust_community'] = $v['cust_community'];

                        // 原工作单位
                        if(!empty($v['cust_work_unit'])){
                            $data1['cust_work_unit'] = $v['cust_work_unit'] != '非中科院' ? '中科院':$v['cust_work_unit'];
                            $data1['cust_unit'] = $v['cust_work_unit'] != '非中科院' ? $v['cust_work_unit'] : null;
                        }

                        if(!empty($v['cust_live_address'])){
                            $cust_live_address = $v['cust_live_address'];
                            // var_dump('$v=-=-=--------$cust_live_address-------',$cust_live_address);
                            $province = '北京市';$city = '市辖区';$area = '';$street = '';$community = '';$address = '';

                            $address = explode('海淀区',$cust_live_address, 2);
                            // var_dump('$v=-=-=--------$address-------',$address);
                            if(count($address)==2){
                                $area = '海淀区';
                                $cust_live_address = $address[1];
                            }

                            $address = explode('街道',$cust_live_address, 2);
                            // var_dump('$v=-=-=--------$address---街道----',$address);
                            if(count($address)==2){
                                $street = $address[0] . '街道';
                                $cust_live_address = $address[1];
                            }
                            $address = explode('居委会',$cust_live_address, 2);
                            // var_dump('$v=-=-=--------$address---居委会----',$address);
                            if(count($address)==2){
                                $community = $address[0] . '居委会';
                                $cust_live_address = $address[1];
                            }

                            // {"province":"北京市","city":"市辖区","area":"海淀区","street":"中关村街道","community":"科源社区居委会"}
                            $cust_city = [ 'province'=>'北京市',  'city'=>'市辖区',  'area'=>'海淀区', 'street'=>$street,'community'=> $community ];

                            $data1['cust_city'] = json_encode($cust_city,JSON_UNESCAPED_UNICODE);
                            if(!empty($street))$data1['cust_street'] = $street;
                            $data1['cust_live_address'] = $cust_live_address;
                        }
                        // var_dump('$v=-=-=--------$data1-------',$data1);

                        // 修改档案信息
                        $this->dao->update('cust_user', $data1 , [ 'cust_uid' => $user['cust_uid'] ]);
                        // var_dump('$v=-=-=----!empty(contract_id)--1---------',$v['cust_name']);
                    } else {
                        $insertArr[] = $v;
                        // var_dump('insertArr=-=-=---------------',$insertArr);
                    }
                }
            }
        }
        // var_dump('insertArr-=-=-=---------------',$insertArr);
        return $this->success($insertArr);
    }
}

