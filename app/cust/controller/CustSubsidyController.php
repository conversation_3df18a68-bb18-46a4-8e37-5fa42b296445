<?php
namespace app\cust\controller;

use app\cust\model\CustBalanceRecord;
use app\cust\model\CustSubsidy;
use app\cust\model\CustUser;
use app\utils\Excel;
use Exception;
use support\Request;
use support\Response;
use plugin\saiadmin\basic\BaseController;
use think\db\exception\DbException;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class CustSubsidyController extends BaseController
{
    /**
     * 查询列表
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws BadRequestHttpException
     * @throws DbException
     */
    public function list(Request $request, Response $response): Response
    {
        try {
            validate(\app\validate\CustSubsidyValidate::class)
                ->scene('list')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'cust_uid',
            'month',
            'page',
            'limit',
        ]);

        $userSubsidyModel = new CustSubsidy();
        $model = $userSubsidyModel
            ->with(['userInfo'])
            ->when(isset($params['month']) && $params['month'], function ($query) use ($params) {
                return $query->where('month', $params['month']);
            })->when(isset($params['cust_uid']) && $params['cust_uid'], function ($query) use ($params) {
                return $query->where('cust_uid', $params['cust_uid']);
            })->hidden([
                'delete_time',
                'created_by',
                'updated_by',
            ]);
        $data['total'] = $model->count();
        $data['data'] = $model->order(['update_time'=>'desc'])->page($params['page'], $params['limit'])
            ->select()
            ->toArray();
        return $this->success($data);
    }

    /**
     * 补助详情接口
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws BadRequestHttpException
     * @throws DbException
     */
    public function getInfo(Request $request, Response $response): Response
    {
        try {
            validate(\app\validate\CustSubsidyValidate::class)
                ->scene('getInfo')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'cust_uid',
            'month',
            'page',
            'limit',
        ]);

        $userSubsidyModel = new CustSubsidy();
        $userBillModel = new CustBalanceRecord();
        // 查询补贴信息
        $subsidyInfo = $userSubsidyModel->with(['userInfo'])
            ->when(isset($params['month']) && $params['month'], function ($query) use ($params) {
                return $query->where('month', $params['month']);
            })->when(isset($params['cust_uid']) && $params['cust_uid'], function ($query) use ($params) {
                return $query->where('cust_uid', $params['cust_uid']);
            })->where('type',1)->hidden([
                'delete_time',
                'created_by',
                'updated_by',
            ])->findOrEmpty()->toArray();
        if(!empty($subsidyInfo)) {
            // 查询消费信息
            $model = $userBillModel
                ->when(isset($params['month']) && $params['month'], function ($query) use ($params) {
                    return $query->where('usage_date', '>=',$params['month'].'-01')->where('usage_date', '<=',$params['month'].'-31');
                })->when(isset($params['cust_uid']) && $params['cust_uid'], function ($query) use ($params) {
                    return $query->where('cust_uid', $params['cust_uid']);
                })
                ->whereIn('record_details_type',[
                    3,4
                ])->hidden([
                    'delete_time',
                    'updated_by',
                    'forward',
                    'balance',
                    'balance_record_id',
                    'number'
                ]);
            $data['total'] = $model->count();
            $data['rows'] = $model->order(['update_time'=>'desc'])->page($params['page'], $params['limit'])
                ->select()
                ->toArray();
            return $this->success([
                'input_amount' => $subsidyInfo['input_amount'],
                'real_amount' => $subsidyInfo['real_amount'],
                'spending_amount' => $subsidyInfo['spending_amount'],
                'data' => $data,
            ]);
        } else {
            return $this->success([
                'input_amount' => 0,
                'real_amount' => 0,
                'spending_amount' => 0,
                'data' => [
                    'total' => 0,
                    'rows' => []
                ],
            ]);
        }
    }

    /**
     * 导入列表-海淀区家庭养老床位服务明细表（机构版）
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws BadRequestHttpException|DbException
     */
    public function importV1(Request $request, Response $response): Response
    {
        // 获取数据
        $data = Excel::readExcel([
            'sheet1',
        ]);
        // 获取数据
        $data = $data['sheet1'];
        if(empty($data)) {
            throw new BadRequestHttpException('导入数据为空');
        }
        // 生成数据
        $month = '';
        $lastDay = '';
        $insertData = [];
        $noData = [];
        $userModel = new CustUser();
        $userSubsidyModel = new CustSubsidy();
        foreach ($data as $k=>$v) {
            if($k == 0 || $k == 1 || $k == 3) {
                continue;
            }
            if($k == 2) {
                preg_match("/\d{4}-\d{2}/", $v[15], $matches);
                $month = $matches[0];
                $lastDay = date("Y-m-d", strtotime("$month +1 month -1 day"));
                continue;
            }
            if(empty($v[8])) {
                continue;
            }
            // 判断用户是否注册
            $userinfo = $userModel->where('cust_id_card',$v[8])->field('cust_uid,cust_name,cust_aging')->findOrEmpty()->toArray();
            if (!empty($userinfo)) {
                // 查询补贴是否创建
                $subsidyInfo = $userSubsidyModel
                    ->where('cust_uid',$userinfo['cust_uid'])
                    ->where('month',$month)
                    ->findOrEmpty()
                    ->toArray();
                // 创建补贴
                if (empty($subsidyInfo)) {
                    // 判断是否补贴类别
                    $v36 = match ($v[11]) {
                        '高龄自理' => 1,
                        '轻度失能/失智' => 2,
                        '中度失能/失智' => 3,
                        '重度失能/失智' => 4,
                        default => null,
                    };
                    if(!empty($v36)) {
                        $inputAmount = match ($v36) {
                            1 => 300,
                            2 => 500,
                            3 => 900,
                            4 => 600,
                            default => null,
                        };
                        $insertData[] = [
                            'cust_uid' => $userinfo['cust_uid'],
                            'month' => $month,
                            'input_amount' => $inputAmount,
                            'real_amount' => $v['18'],
                            'spending_amount' => 0,
                            'start_time' => $month.'-01',
                            'end_time' => $lastDay,
                            'status' => 1,
                            'type' => 1,
                            'created_by' => $request->adminId,
                            'create_time' => date('Y-m-d H:i:s'),
                        ];
                        // 判断是否添加运营补贴
                        if($v['17'] == '是' && $userinfo['cust_aging'] == '已改造') {
                            // 查询运营补贴是否创建
                            $billModel = new CustBalanceRecord();
                            $subsidyInfo = $billModel
                                ->where('cust_uid',$userinfo['cust_uid'])
                                ->where('usage_date','>=',$month.'-01')
                                ->where('usage_date','<=',$month.'-31')
                                ->where('record_details_type',4)
                                ->findOrEmpty()
                                ->toArray();
                            if(empty($subsidyInfo)) {
                                $billModel->insert([
                                    'cust_uid' => $userinfo['cust_uid'],
                                    'balance_id' => $userinfo['cust_uid'],
                                    'link_id'=>'',
                                    'usage_date' => $month.'-01',
                                    'pm'=>0,
                                    'record_title'=>'运营补贴',
                                    'record_details_category'=>'运营补贴',
                                    'record_details_type'=>4,
                                    'number'=>0,
                                    'subsidy_money'=>500,
                                    'subsidy_id'=>'',
                                    'create_time' => date('Y-m-d H:i:s'),
                                    'created_by' => $request->adminId,
                                ]);
                            }
                        }
                        // 修改老人信息
                        $userModel->where('cust_uid',$userinfo['cust_uid'])->update([
                            'cust_evaluation_level' => $v36,
                        ]);
                    }
                } else {
                    // 更新运营补贴
                    $userSubsidyModel
                        ->where('month', $month)
                        ->where('cust_uid', $userinfo['cust_uid'])
                        ->update([
                            'real_amount' => $v['18'],
                            'updated_by' => $request->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                        ]);
                }
            } else {
                if(!empty($v[5])) {
                    $noData[] = [
                        'name' => $v[5],
                        'idcard' => $v[8],
                    ];
                }
            }
        }
        if (!empty($insertData)) {
            $userSubsidyModel->insertAll($insertData);
        }
        return $this->success([
            'noData' => $noData,
        ]);
    }

    /**
     * 导入列表-海淀区家庭养老床位服务明细表（机构版）
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws BadRequestHttpException
     */
    public function importV2(Request $request, Response $response): Response
    {
        // 获取数据
        $data = Excel::readExcel([
            'sheet1',
        ]);
        // 获取数据
        $data = $data['sheet1'];
        if(empty($data)) {
            throw new BadRequestHttpException('导入数据为空');
        }
        // 生成数据
        $month = '';
        $lastDay = '';
        $insertData = [];
        $noData = [];
        $userModel = new CustUser();
        $userSubsidyModel = new CustSubsidy();
        foreach ($data as $k=>$v) {
            if($k == 0 || $k == 1 || $k == 3 || $k == 4) {
                continue;
            }
            if($k == 2) {
                preg_match("/\d{4}-\d{2}/", $v[14], $matches);
                $month = $matches[0];
                $lastDay = date("Y-m-d", strtotime("$month +1 month -1 day"));
                continue;
            }
            // 判断用户是否注册
            $userinfo = $userModel->where('cust_id_card',$v[8])->field('cust_uid,cust_name,cust_aging')->findOrEmpty()->toArray();
            if (!empty($userinfo)) {
                // 查询补贴是否创建
                $subsidyInfo = $userSubsidyModel
                    ->where('cust_uid',$userinfo['cust_uid'])
                    ->where('month',$month)
                    ->findOrEmpty()
                    ->toArray();
                // 创建补贴
                if (empty($subsidyInfo)) {
                    // 判断是否补贴类别
                    $v36 = match ($v[11]) {
                        '高龄自理' => 1,
                        '轻度失能/失智' => 2,
                        '中度失能/失智' => 3,
                        '重度失能/失智' => 4,
                        default => null,
                    };
                    if(!empty($v36)) {
                        $inputAmount = match ($v36) {
                            1 => 300,
                            2 => 500,
                            3 => 900,
                            4 => 600,
                            default => null,
                        };
                        $insertData[] = [
                            'cust_uid' => $userinfo['cust_uid'],
                            'month' => $month,
                            'input_amount' => $inputAmount,
                            'real_amount' => $v['17'],
                            'spending_amount' => 0,
                            'start_time' => $month.'-01',
                            'end_time' => $lastDay,
                            'status' => 1,
                            'type' => 1,
                            'created_by' => $request->adminId,
                            'create_time' => date('Y-m-d H:i:s'),
                        ];
                        // 判断是否添加运营补贴
                        if($v['16'] == '是' && $userinfo['cust_aging'] == '已改造') {
                            // 查询运营补贴是否创建
                            $billModel = new CustBalanceRecord();
                            $subsidyInfo = $billModel
                                ->where('cust_uid',$userinfo['cust_uid'])
                                ->where('usage_date','>=',$month.'-01')
                                ->where('usage_date','<=',$month.'-31')
                                ->where('record_details_type',4)
                                ->findOrEmpty()
                                ->toArray();
                            if(empty($subsidyInfo)) {
                                $billModel->insert([
                                    'cust_uid' => $userinfo['cust_uid'],
                                    'balance_id' => $userinfo['cust_uid'],
                                    'link_id' => '',
                                    'usage_date' => $month . '-01',
                                    'pm' => 0,
                                    'record_title' => '运营补贴',
                                    'record_details_category' => '运营补贴',
                                    'record_details_type' => 4,
                                    'number' => 0,
                                    'subsidy_money' => 500,
                                    'subsidy_id' => '',
                                    'create_time' => date('Y-m-d H:i:s'),
                                    'created_by' => $request->adminId,
                                ]);
                            }
                        }
                        // 修改老人信息
                        $userModel->where('cust_uid',$userinfo['cust_uid'])->update([
                            'cust_evaluation_level' => $v36,
                        ]);
                    }
                } else {
                    // 更新运营补贴
                    $userSubsidyModel
                        ->where('month', $month)
                        ->where('cust_uid', $userinfo['cust_uid'])
                        ->update([
                            'real_amount' => $v['17'],
                            'updated_by' => $request->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                        ]);
                }
            } else {
                if(!empty($v[5])) {
                    $noData[] = [
                        'name' => $v[5],
                        'idcard' => $v[8],
                    ];
                }
            }
        }
        if (!empty($insertData)) {
            $userSubsidyModel->insertAll($insertData);
        }
        return $this->success([
            'noData' => $noData,
        ]);
    }

    /**
     * 导出补贴金额统计表
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws DbException
     * @throws BadRequestHttpException
     */
    public function export(Request $request, Response $response): Response
    {
        $month = $request->input('month');
        $userSubsidyModel = new CustSubsidy();
        $model = $userSubsidyModel
            ->when(isset($month) && $month, function ($query) use ($month) {
                return $query->where('month','>=', $month.'-01')->where('month','<=', $month.'-12');
            })->where('type',1)->hidden([
                'delete_time',
                'created_by',
                'updated_by',
            ]);
        // 获取所有补贴用户信息
        $userArr = $model->with(['userInfo'])->field('cust_uid')->distinct(true)->select()->toArray();
        // 生成导出数组
        $arr = [];
        if(!empty($userArr)) {
            $i = 1;
            foreach ($userArr as $v) {
                if(empty($v['userInfo']['cust_name'])) {
                    var_dump($v);
                    continue;
                }
                $arr[$v['cust_uid']] = [
                    'id' => $i,
                    'year' => $month,
                    'name' => $v['userInfo']['cust_name'],
                    'health' => match ($v['userInfo']['cust_evaluation_level']) {
                        1=>'高龄自理',
                        2=>'轻度失能/失智',
                        3=>'中度失能/失智',
                        4=>'重度失能/失智',
                        default => null,
                    },
                    'created_by' => $this->dao->get('eb_system_user',[
                        'id' => $v['userInfo']['cust_consultant'],
                    ],'nickname'),
                    $month.'-01' => '',
                    $month.'-02' => '',
                    $month.'-03' => '',
                    $month.'-04' => '',
                    $month.'-05' => '',
                    $month.'-06' => '',
                    $month.'-07' => '',
                    $month.'-08' => '',
                    $month.'-09' => '',
                    $month.'-10' => '',
                    $month.'-11' => '',
                    $month.'-12' => '',
                ];
                $i++;
            }
        }
        // 获取所有补贴信息
        $data = $userSubsidyModel
            ->when(isset($month) && $month, function ($query) use ($month) {
                return $query->where('month','>=', $month.'-01')->where('month','<=', $month.'-12');
            })->where('type',1)->hidden([
                'delete_time',
                'created_by',
                'updated_by',
            ])->select()->toArray();
        if(!empty($data)) {
            foreach ($data as $v) {
                if (empty($arr[$v['cust_uid']][$v['month']])) {
                    $arr[$v['cust_uid']][$v['month']] = (float)$v['real_amount'];
                } else {
                    $arr[$v['cust_uid']][$v['month']] += (float)$v['real_amount'];
                }
            }
        }
        // 创建导出表
        $title = '补贴金额统计表_'.$month.'.xlsx';
        $headers = [
            '序号',
            '年份',
            '姓名',
            '评估等级',
            '负责人',
            '1月',
            '2月',
            '3月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月',
        ];

        $url = Excel::export($title,$headers,$arr);
        return $this->success([
            'url' => $url
        ]);
    }
}