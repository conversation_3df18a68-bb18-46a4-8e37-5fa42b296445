<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 规划项目模型
 */
class CustPlanItem extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_plan_item_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_plan_item';

    
    /**
     * 服务项目名称 搜索
     */
    public function searchCustPlanItemNameAttr($query, $value)
    {
        $query->where('cust_plan_item_name', 'like', '%'.$value.'%');
    }


}
