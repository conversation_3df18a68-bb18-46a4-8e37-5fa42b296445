<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasMany;

/**
 * 档案管理模型
 */
class CustUser extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_uid';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_user';

    
    /**
     * 姓名 搜索
     */
    public function searchCustNameAttr($query, $value)
    {
        $query->where('cust_name', 'like', '%'.$value.'%');
    }

    /**
     * 原材料名称 搜索
     */
    public function searchCustCityAttr($query, $value)
    {
        $query->where('cust_city', 'like', '%'.$value.'%');
    }

    /**
     * 一对多关联 创建人信息
     * @return HasMany
     */
    public function consultantLog(): HasMany
    {
        return $this->hasMany(CustUserConsultantLog::class,'cust_uid','cust_uid')->order('update_time desc');
    }
}
