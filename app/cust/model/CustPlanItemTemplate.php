<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 项目模板模型
 */
class CustPlanItemTemplate extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_plan_item_template_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_plan_item_template';

    
    /**
     * 项目名称 搜索
     */
    public function searchCustPlanItemTemplateNameAttr($query, $value)
    {
        $query->where('cust_plan_item_template_name', 'like', '%'.$value.'%');
    }


}
