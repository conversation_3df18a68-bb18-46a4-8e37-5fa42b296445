<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 表单记录模型
 */
class CustSurveyRecord extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_survey_record_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_survey_record';

    // 设置json类型字段
    protected $json = ['cust_survey_content'];

    /**
     * 调查人员保存数组转换
     */
    public function setEmployeeUidAttr($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

     /**
     * 调查人员读取数组转换
     */
    public function getEmployeeUidAttr($value)
    {
        return json_decode($value ?? '', true);
    }
    
    /**
     * 问卷名称 搜索
     */
    public function searchCustSurveyNameAttr($query, $value)
    {
        $query->where('cust_survey_name', 'like', '%'.$value.'%');
    }


}
