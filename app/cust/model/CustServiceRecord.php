<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 服务记录模型
 */
class CustServiceRecord extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_service_record';


    // 设置json类型字段
    protected $json = ['service_picture'];

    /**
     * 服务日期 搜索
     */
    public function searchServiceDateAttr($query, $value)
    {
        if(!empty($value)) {
            $query->whereTime('service_date', '>=', $value[0])->whereTime('service_date', '<=', $value[1]);
        }
    }
}
