<?php

namespace app\cust\model;


use think\model\relation\HasOne;
use plugin\saiadmin\basic\BaseModel;

/**
 * @mixin BaseModel
 */
class CustSubsidy extends BaseModel
{
    /**
     * 表名
     * @var string
     */
    protected $table = 'cust_subsidy_bill';

    /**
     * 主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据转换为驼峰命名
     * @var boolean
     */
    protected $convertNameToCamel = false;

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function userInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_live_address,cust_community,cust_evaluation_level,cust_aging,cust_consultant');
    }
}