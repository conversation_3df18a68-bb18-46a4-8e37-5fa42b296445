<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 表单模板模型
 */
class CustSurveyTemplate extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_survey_template_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_survey_template';


    protected $json = [
        'content'
    ];

    /**
     * 表单名称 搜索
     */
    public function searchNameAttr($query, $value)
    {
        $query->where('name', 'like', '%'.$value.'%');
    }


}
