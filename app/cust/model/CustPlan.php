<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 服务规划模型
 */
class CustPlan extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_plan_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_plan';

    
    /**
     * 规划名称 搜索
     */
    public function searchCustPlanNameAttr($query, $value)
    {
        $query->where('cust_plan_name', 'like', '%'.$value.'%');
    }


}
