<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 评估记录模型
 */
class CustSurveyAssessment extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_survey_assessment_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_survey_assessment';

    // 设置json类型字段
    protected $json = ['cust_survey_content'];
    
    /**
     * 评估名称 搜索
     */
    public function searchCustSurveyNameAttr($query, $value)
    {
        $query->where('cust_survey_name', 'like', '%'.$value.'%');
    }


}
