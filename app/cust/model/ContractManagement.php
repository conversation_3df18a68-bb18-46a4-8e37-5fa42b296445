<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\cust\model;

use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasOne;
use app\cust\model\CustUser;
use app\employee\model\EmployeeUser;

/**
 * 合同管理模型
 */
class ContractManagement extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'contract_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'contract_management';

    // 设置json类型字段
    protected $json = ['contract_documents2'];

    /**
     * 分类 搜索
     */
    public function searchCustContractClassificationAttr($query, $value)
    {
        $query->where('cust_contract_classification', 'like', '%'.$value.'%');
    }
        
    /**
     * 姓名 搜索
     */
    public function searchContractNumberAttr($query, $value)
    {
        $query->where('contract_number', 'like', '%'.$value.'%');
    }
    
    /**
     * 合同开始时间 搜索
     */
    public function searchContractPeriodStartAttr($query, $value)
    {
        $query->whereTime('contract_period_start', '>=', $value);
    }
    
    /**
     * 合同结束时间 搜索
     */
    public function searchContractPeriodEndAttr($query, $value)
    {
        $query->whereTime('contract_period_end', '<=', $value);
    }

    /**
     * contractType 搜索器
     */
    public function searchContractTypeAttr($query, $value)
    {
        $query->whereIn('contract_type', $value);
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function custInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_consultant');
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function employeeInfo(): HasOne
    {
        return $this->hasOne(EmployeeUser::class,'employee_uid','employee_uid')->field('employee_uid,employee_name,employee_sex');
    }
}
