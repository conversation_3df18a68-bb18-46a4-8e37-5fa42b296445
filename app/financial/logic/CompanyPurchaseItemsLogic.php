<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\logic;

use plugin\saiadmin\basic\BaseLogic;
use plugin\saiadmin\exception\ApiException;
use plugin\saiadmin\utils\Helper;
use app\financial\model\CompanyPurchaseItems;

/**
 * 采购清单表逻辑层
 */
class CompanyPurchaseItemsLogic extends BaseLogic
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CompanyPurchaseItems();
    }

    /**
     * 数据修改
     */
    public function update($data, $where)
    {
        if (!isset($data['purchase_application_id'])) {
            $data['purchase_application_id'] = 0;
        }
        if ($data['purchase_application_id'] == $where['id']) {
            throw new ApiException('不能设置父级为自身');
        }
        return $this->model->update($data, $where);
    }

    /**
     * 数据删除
     */
    public function destroy($ids, $force = false)
    {
        $num = $this->model->where('purchase_application_id', 'in', $ids)->count();
        if ($num > 0) {
            throw new ApiException('该分类下存在子分类，请先删除子分类');
        } else {
            return $this->model->destroy($ids, $force);
        }
    }

    /**
     * 树形数据
     */
    public function tree($where)
    {
        $query = $this->search($where);
        if (request()->input('tree', 'false') === 'true') {
            $query->field('id, id as value, purchase_name as label, purchase_application_id');
        }
        $data = $this->getAll($query);
        return Helper::makeTree($data);
    }

}
