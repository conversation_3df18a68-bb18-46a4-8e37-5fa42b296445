<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 采购清单表模型
 */
class CompanyPurchaseItems extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'company_purchase_items';

    
    /**
     * 采购商品名称 搜索
     */
    public function searchPurchaseNameAttr($query, $value)
    {
        $query->where('purchase_name', 'like', '%'.$value.'%');
    }


}
