<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 发票申请模型
 */
class CompanyInvoiceApplications extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'company_invoice_applications';

    
    /**
     * 客户名称 搜索
     */
    public function searchInvoiceNameAttr($query, $value)
    {
        $query->where('invoice_name', 'like', '%'.$value.'%');
    }


}
