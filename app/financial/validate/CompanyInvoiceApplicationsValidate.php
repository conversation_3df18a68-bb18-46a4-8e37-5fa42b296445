<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\validate;

use think\Validate;

/**
 * 发票申请验证器
 */
class CompanyInvoiceApplicationsValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'invoice_name' => 'require',
        'status' => 'require',
        'amount' => 'require',
        'applicant_id' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'invoice_name' => '客户名称必须填写',
        'status' => '申请状态必须填写',
        'amount' => '发票金额必须填写',
        'applicant_id' => '申请人id必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'invoice_name',
            'status',
            'amount',
            'applicant_id',
        ],
        'update' => [
            'invoice_name',
            'status',
            'amount',
            'applicant_id',
        ],
    ];

}
