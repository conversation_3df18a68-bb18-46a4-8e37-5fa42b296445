<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\validate;

use think\Validate;

/**
 * 退款申请验证器
 */
class CompanyRefundApplicationsValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'elderly_id' => 'require',
        'refund_method' => 'require',
        'bank_card_id' => 'require',
        'usage_amount' => 'require',
        'status' => 'require',
        'applicant_id' => 'require',
        'created_by' => 'require',
        'create_time' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'elderly_id' => '老人id必须填写',
        'refund_method' => '退款方式必须填写',
        'bank_card_id' => '银行卡id必须填写',
        'usage_amount' => '退款金额必须填写',
        'status' => '申请状态必须填写',
        'applicant_id' => '申请人id必须填写',
        'created_by' => '创建人必须填写',
        'create_time' => '创建时间必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'elderly_id',
            'refund_method',
            'bank_card_id',
            'usage_amount',
            'status',
            'applicant_id',
            'created_by',
            'create_time',
        ],
        'update' => [
            'elderly_id',
            'refund_method',
            'bank_card_id',
            'usage_amount',
            'status',
            'applicant_id',
            'created_by',
            'create_time',
        ],
    ];

}
