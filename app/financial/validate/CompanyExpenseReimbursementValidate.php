<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\validate;

use think\Validate;

/**
 * 报销申请验证器
 */
class CompanyExpenseReimbursementValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'payment_screenshot' => 'require',
        'invoice_amount' => 'require',
        'status' => 'require',
        'employee_id' => 'require',
        'bank_card_id' => 'require',
        'applicant_id' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'payment_screenshot' => '付款截图必须填写',
        'invoice_amount' => '报销金额必须填写',
        'status' => '报销状态必须填写',
        'employee_id' => '老人id必须填写',
        'bank_card_id' => '银行卡id必须填写',
        'applicant_id' => '申请人id必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'payment_screenshot',
            'invoice_amount',
            'status',
            'employee_id',
            'bank_card_id',
            'applicant_id',
        ],
        'update' => [
            'payment_screenshot',
            'invoice_amount',
            'status',
            'employee_id',
            'bank_card_id',
            'applicant_id',
        ],
    ];

}
