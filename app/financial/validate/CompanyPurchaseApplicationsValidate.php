<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\validate;

use think\Validate;

/**
 * 采购申请验证器
 */
class CompanyPurchaseApplicationsValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'applicant_id' => 'require',
        'name' => 'require',
        'amount' => 'require',
        'status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'applicant_id' => '申请人id必须填写',
        'name' => '申请名称必须填写',
        'amount' => '总金额必须填写',
        'status' => '申请状态必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'applicant_id',
            'name',
            'amount',
            'status',
        ],
        'update' => [
            'applicant_id',
            'name',
            'amount',
            'status',
        ],
    ];

}
