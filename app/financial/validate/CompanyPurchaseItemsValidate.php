<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\financial\validate;

use think\Validate;

/**
 * 采购清单表验证器
 */
class CompanyPurchaseItemsValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'purchase_application_id' => 'require',
        'purchase_merchants' => 'require',
        'purchase_name' => 'require',
        'purchase_price' => 'require',
        'purchase_quantity' => 'require',
        'shipping_cost' => 'require',
        'purchase_amount' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'purchase_application_id' => '采购申请id必须填写',
        'purchase_merchants' => '采购商家必须填写',
        'purchase_name' => '采购商品名称必须填写',
        'purchase_price' => '采购单价必须填写',
        'purchase_quantity' => '采购数量必须填写',
        'shipping_cost' => '采购运费必须填写',
        'purchase_amount' => '采购金额必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'purchase_application_id',
            'purchase_merchants',
            'purchase_name',
            'purchase_price',
            'purchase_quantity',
            'shipping_cost',
            'purchase_amount',
        ],
        'update' => [
            'purchase_application_id',
            'purchase_merchants',
            'purchase_name',
            'purchase_price',
            'purchase_quantity',
            'shipping_cost',
            'purchase_amount',
        ],
    ];

}
