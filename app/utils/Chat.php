<?php

namespace app\utils;

class Chat
{
    /**
     * AI接口URL
     * @var string
     */
    private string $apiUrl = 'https://one.zkshlm.com/v1/chat/completions';

    /**
     * API密钥
     * @var string
     */
    private string $apiKey = 'sk-7QZ7rZhuSbkujNQK35078211C9Ef400c8a07C1Ee72E84854';

    /**
     * 发送聊天请求
     * @param array $messages 消息数组
     * @param float $temperature 温度参数
     * @param int $maxTokens 最大token数
     * @return array
     */
    public function sendChatRequest(array $messages, float $temperature = 0.2, int $maxTokens = 1000): array
    {
        try {
            // 构建请求数据
            $requestData = [
                'messages' => $messages,
                'model' => 'gemini-2.0-flash-exp',
                "stream"=> false,
                "tools" => [
                    [
                        "type" => "function",
                        "function" => [
                            "name" => "googleSearch"
                        ],
                    ]
                ],
                'temperature' => $temperature,
                'max_tokens' => $maxTokens
            ];

            // 设置请求头
            $headerArray = [
                "Content-type:application/json;charset='utf-8'",
                "Accept:application/json",
                "Authorization:Bearer " . $this->apiKey
            ];

            // 发送请求
            return httpPost($this->apiUrl, $requestData, $headerArray);
        } catch (\Exception $e) {
            return [
                'error' => [
                    'message' => $e->getMessage()
                ]
            ];
        }
    }

    /**
     * 查询地址所属街道
     * @param string $address 地址
     * @return array
     */
    public function queryAddressStreet(string $address): array
    {
        $messages = [
            [
                'role' => 'system',
                'content' => '你是一个专业的北京市海淀区地址查询助手。请帮我查询给定地址所属的街道。要求：
1. 所有输入的地址都属于北京市海淀区，不会有其他区域的地址
2. 必须返回该地址所属的街道名称，因为每个地址都一定属于某个街道
3. 如果输入的是小区名称，一定要查询出该小区所在的街道，因为海淀区的每个小区都有对应的街道
4. 返回的街道名称必须以"街道"或"地区"结尾，例如"中关村街道"或"万柳地区"
5. 只返回街道名称，不要包含任何其他文字
6. 示例：
   输入："北京市海淀区中关村东路18号"，返回："中关村街道"
   输入："知春路地铁站"，返回："知春路街道"
   输入："万柳"，返回："万柳地区"'
            ],
            [
                'role' => 'user',
                'content' => $address
            ]
        ];

        $response = $this->sendChatRequest($messages);
        if (isset($response['error'])) {
            return [
                'success' => false,
                'message' => 'API错误：' . json_encode($response['error'], JSON_UNESCAPED_UNICODE)
            ];
        }

        if (isset($response['choices'][0]['message']['content'])) {
            $street = trim($response['choices'][0]['message']['content']);
            if (!str_ends_with($street, '街道') && !str_ends_with($street, '地区')) {
                $street = '未找到街道信息';
            }
            return [
                'success' => true,
                'street' => $street
            ];
        }

        return [
            'success' => false,
            'message' => '未能获取街道信息'
        ];
    }

    /**
     * 解析完整地址信息
     * @param string $address 地址
     * @return array
     */
    public function parseAddress(string $address): array
    {
        $messages = [
            [
                'role' => 'system',
                'content' => '你是一个专业的北京市海淀区地址解析助手。请帮我解析给定地址的行政区划信息。要求：
1. 所有输入的地址都属于北京市海淀区，不会有其他区域的地址
2. 必须返回完整的行政区划信息，因为每个地址都一定属于某个街道和居委会
3. 返回格式必须是JSON格式，包含以下字段：
   - province: 固定返回"北京市"
   - city: 固定返回"北京市"
   - area: 固定返回"海淀区"
   - street: 必须返回街道名称，以"街道"或"地区"结尾，例如"中关村街道"或"万柳地区"
   - community: 必须返回居委会名称，以"居委会"或"村委会"结尾
4. 如果是小区名称，一定要查询出对应的街道和居委会信息
5. 示例输入："北京市海淀区中关村东路18号"
   示例输出：{
     "province": "北京市",
     "city": "北京市",
     "area": "海淀区",
     "street": "中关村街道",
     "community": "科春社区居委会"
   }
6. 示例输入："万柳"
   示例输出：{
     "province": "北京市",
     "city": "北京市",
     "area": "海淀区",
     "street": "万柳地区",
     "community": "青龙桥村委会"
   }
7. 注意：community字段必须以"居委会"或"村委会"结尾，不接受其他后缀'
            ],
            [
                'role' => 'user',
                'content' => $address
            ]
        ];

        $response = $this->sendChatRequest($messages);

        if (isset($response['error'])) {
            return [
                'success' => false,
                'message' => 'API错误：' . json_encode($response['error'], JSON_UNESCAPED_UNICODE)
            ];
        }

        if (isset($response['choices'][0]['message']['content'])) {
            $content = $response['choices'][0]['message']['content'];
            // 移除markdown的json标记
            $content = preg_replace('/```json\s*/', '', $content);
            $content = preg_replace('/\s*```/', '', $content);
            
            try {
                $addressInfo = json_decode($content, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($addressInfo)) {
                    // 确保所有必要的键都存在
                    $defaultKeys = ['province', 'city', 'area', 'street', 'community'];
                    foreach ($defaultKeys as $key) {
                        if (!isset($addressInfo[$key])) {
                            $addressInfo[$key] = '';
                        }
                    }
                    
                    // 验证街道格式
                    if (!str_ends_with($addressInfo['street'], '街道') && !str_ends_with($addressInfo['street'], '地区')) {
                        $addressInfo['street'] = '';
                    }
                    
                    // 验证居委会格式
                    if (!empty($addressInfo['community']) &&
                        !str_ends_with($addressInfo['community'], '居委会') &&
                        !str_ends_with($addressInfo['community'], '村委会')) {
                        $addressInfo['community'] = '';
                    }
                    
                    return [
                        'success' => true,
                        'data' => $addressInfo
                    ];
                }
            } catch (\Exception $e) {
                // JSON解析失败
            }
        }

        return [
            'success' => false,
            'message' => '未能解析地址信息',
            'data' => [
            ]
        ];
    }
}
