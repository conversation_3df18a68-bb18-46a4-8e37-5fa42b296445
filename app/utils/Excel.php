<?php

namespace app\utils;

use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class Excel
{

    /**
     * 导出操作
     * @param $title
     * @param array $header
     * @param array $export
     * @return string
     * @throws BadRequestHttpException
     */
    public static function export($title,array $header,array $export = []): string
    {
        $config = [
            'path' => public_path() . '/excel'
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 获取单元格列
        if(empty($header)) {
            throw new BadRequestHttpException('请设置单元格信息');
        }
        // 获取单元格长度
        $headerCount = count($header) - 1;
        $headerEndColumnIndex = \Vtiful\Kernel\Excel::stringFromColumnIndex($headerCount);
        // 获取操作对象
        $excel = $excel->fileName($title, 'sheet1')
            ->header($header);
        // 获取样式对象
        $fileHandle = $excel->getHandle();
        // 定义全局样式
        $format     = new \Vtiful\Kernel\Format($fileHandle);
        $defaultStyle = $format
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->fontSize(15)
            ->toResource();
        // 定义header样式
        $format     = new \Vtiful\Kernel\Format($fileHandle);
        $styleHeader = $format
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->bold()
            ->fontSize(20)
            ->toResource();
        // fileName 会自动创建一个工作表，你可以自定义该工作表名称，工作表名称为可选参数
        $excel
            ->setColumn('A:'.$headerEndColumnIndex, 30, $defaultStyle)
            ->setRow('A1:'.$headerEndColumnIndex.'1', 35,$styleHeader)
            ->data($export)
            ->freezePanes(1, 0)
            ->output();
        // 关闭当前打开的所有文件句柄 并 回收资源
        $excel->close();
        // 返回下载链接
        if(config('app.debug')) {
            return 'http://10.0.0.247/excel/' . $title;
        }
        return 'https://api.v2.zkshlm.com/excel/' . $title;
    }

    /**
     * 读取表格内容
     * @param $data
     * @return array
     * @throws BadRequestHttpException
     */
    public static function readExcel($data): array
    {
        // 验证开启
        $array = [
            'xlsx',
        ];
        if(empty(request()->file()['file'])) {
            throw new BadRequestHttpException('请选择上传文件');
        }
        $file = request()->file()['file'];
        if (!$file->isValid()) {
            throw new BadRequestHttpException('无效文件');
        }
        if(!in_array($file->getUploadExtension(),$array)) {
            throw new BadRequestHttpException('请选择正确的文件类型');
        }
        if($file->getSize() > 10485760) {
            throw new BadRequestHttpException('文件大小超过10M');
        }
        // 验证结束
        // 实例化对象
        $path = $file->getPath();
        $realPath = $file->getRealPath();
        $realPath = str_replace($path,'',$realPath);
        $config = [
            'path' => $file->getPath()
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 实例化结束
        // 读取内容
        $excel->openFile($realPath);
        $res = [];
        foreach ($data as $v) {
            $res[$v] = $excel->openSheet($v)->getSheetData();
        }
        // 返回
        // 关闭当前打开的所有文件句柄 并 回收资源
        $excel->close();
        return $res;
    }


    /**
     * 导入操作
     * @param $header
     * @param int $startRow 从第几行开始解析，默认是0
     * @return array
     * @throws BadRequestHttpException
     */
    public static function import($header, int $startRow = 0): array
    {
        // 验证开启
        $array = [
            'xlsx',
        ];
        if(empty(request()->file()['file'])) {
            throw new BadRequestHttpException('请选择上传文件');
        }
        $file = request()->file()['file'];
        if (!$file->isValid()) {
            throw new BadRequestHttpException('无效文件');
        }
        if(!in_array($file->getUploadExtension(),$array)) {
            throw new BadRequestHttpException('请选择正确的文件类型');
        }
        if($file->getSize() > 10485760) {
            throw new BadRequestHttpException('文件大小超过10M');
        }
        // 验证结束

        // 实例化对象
        $path = $file->getPath();
        $realPath = $file->getRealPath();
        $realPath = str_replace($path,'',$realPath);
        $config = [
            'path' => $file->getPath()
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 实例化结束
        $excel->openFile($realPath)
            ->openSheet('Sheet1', \Vtiful\Kernel\Excel::SKIP_EMPTY_CELLS);
        $data = self::processExcel($excel, $header, $startRow);
        // 关闭当前打开的所有文件句柄 并 回收资源
        $excel->close();
        return $data;
    }

    /**
     * 处理键值替换
     * @param $excel
     * @param $header
     * @param int $startRow 从第几行开始解析
     * @return array
     */
    protected static function processExcel($excel, $header, int $startRow = 0): array
    {
        $data = [];
        $time = date('Y-m-d H:i:s');
        // 定义替换规则
        $map = [];
        // 进行数组键值替换
        $i = 0;
        while (($row = $excel->nextRow()) !== null) {
            // 跳过指定的起始行之前的数据
            if ($i < $startRow) {
                $i++;
                continue;
            }
            if(!empty($row[0]) && $row[0] == '服务记录表') {
                $i++;
                continue;
            }
            // 进行Header处理
            if($i === 0 || $i === $startRow) {
                foreach ($row as $k => $v) {
                    // 判断值是否存在
                    if (isset($header[$v])) {
                        $map[$k] = $header[$v];
                    }
                }
            } else {
                // 处理键值替换
                $value = array_reduce(array_keys($map), function ($carry, $key) use ($row, $map) {
                    if (isset($map[$key]) && isset($row[$key])) {
                        $carry[$map[$key]] = $row[$key];
                    }
                    return $carry;
                }, []);
                if (!empty($value)) {
                    // 添加默认属性
                    $value['created_at'] = $time;
                    $value['updated_at'] = $time;
                    $value['created_by'] = request()->adminId;
                    $data[] = $value;
                }
            }
            $i++;
        }
        return $data;
    }

    /**
     * 多表导出
     * @param $title
     * @param $data
     * @return string
     * @throws BadRequestHttpException
     */
    public static function exportTables($title,$data): string
    {
        $header = $data[0]['header'];
        $export = $data[0]['export'];
        $table = $data[0]['title'];
        $config = [
            'path' => public_path() . '/excel'
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 获取单元格列
        if(empty($header)) {
            throw new BadRequestHttpException('请设置单元格信息');
        }
        // 获取单元格长度
        $headerCount = count($header) - 1;
        $headerEndColumnIndex = \Vtiful\Kernel\Excel::stringFromColumnIndex($headerCount);
        // 获取操作对象

        $excel = $excel->fileName($title, $table)
            ->header($header);
        // 获取样式对象
        $fileHandle = $excel->getHandle();
        // 定义全局样式
        $format     = new \Vtiful\Kernel\Format($fileHandle);
        $defaultStyle = $format
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->fontSize(15)
            ->toResource();
        // 定义header样式
        $format     = new \Vtiful\Kernel\Format($fileHandle);
        $styleHeader = $format
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->bold()
            ->fontSize(20)
            ->toResource();
        // fileName 会自动创建一个工作表，你可以自定义该工作表名称，工作表名称为可选参数
        $dataCount = count($data) - 1;
        foreach ($data as $k=>$v) {
            if($k == 0) {
                $excel
                    ->setColumn('A:'.$headerEndColumnIndex, 30, $defaultStyle)
                    ->setRow('A1:'.$headerEndColumnIndex.'1', 35,$styleHeader)
                    ->data($export);
            } else {
                $excel->addSheet($v['title'])
                    ->setColumn('A:'.$headerEndColumnIndex, 30, $defaultStyle)
                    ->setRow('A1:'.$headerEndColumnIndex.'1', 35,$styleHeader)
                    ->header($v['header'])->data($v['export']);
            }
            if($dataCount == $k) {
                $excel->freezePanes(1, 0)->output();
            }
        }

        // 关闭当前打开的所有文件句柄 并 回收资源
        $excel->close();
        // 返回下载链接
        if(config('app.debug')) {
            return 'http://10.0.0.247/excel/' . $title;
        }
        return 'https://api.v2.zkshlm.com/excel/' . $title;
    }

    /**
     * 将数字列索引转换为 Excel 列标识（A, B, C, ... AA, AB, etc.）
     * @param int $index 列索引（从0开始）
     * @return string
     */
    protected static function numberToColumn($index): string
    {
        $column = '';
        
        while ($index >= 0) {
            $column = chr(($index % 26) + 65) . $column;
            $index = floor($index / 26) - 1;
        }
        
        return $column;
    }
    

    /**
     * 多表导出支持合并单元格
     * @param $title
     * @param $data
     * @return string
     * @throws BadRequestHttpException
     */
    public static function exportTablesV2($title,$data): string
    {
        $config = [
            'path' => public_path() . '/excel'
        ];
        $excel = new \Vtiful\Kernel\Excel($config);
        
        // 获取操作对象
        $excel = $excel->fileName($title);
        $fileHandle = $excel->getHandle();
        
        // 定义样式
        $format = new \Vtiful\Kernel\Format($fileHandle);
        $defaultStyle = $format
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->fontSize(15)
            ->toResource();
            
        $format = new \Vtiful\Kernel\Format($fileHandle);
        $styleHeader = $format
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->bold()
            ->fontSize(20)
            ->toResource();
            
        // 处理每个sheet
        foreach ($data as $k => $v) {
            // 获取当前sheet的最大列数
            $maxColumns = 0;
            if (!empty($v['header'])) {
                $maxColumns = count($v['header']);
            } elseif (!empty($v['export'])) {
                // 计算导出数据中的最大列数
                foreach ($v['export'] as $row) {
                    $colCount = 0;
                    foreach ($row as $cell) {
                        if (is_array($cell) && isset($cell['colspan'])) {
                            $colCount += $cell['colspan'];
                        } else {
                            $colCount++;
                        }
                    }
                    $maxColumns = max($maxColumns, $colCount);
                }
            }
            
            $headerEndColumnIndex = \Vtiful\Kernel\Excel::stringFromColumnIndex($maxColumns - 1);
            
            if ($k == 0) {
                $currentSheet = $excel->fileName($title, $v['title']);
            } else {
                $currentSheet = $excel->addSheet($v['title']);
            }
            
            // 设置基础样式
            $currentSheet->setColumn('A:' . $headerEndColumnIndex, 30, $defaultStyle);
            
            // 如果有表头，先写入表头
            if (!empty($v['header'])) {
                $currentSheet->header($v['header'])
                    ->setRow('A1:' . $headerEndColumnIndex . '1', 35, $styleHeader);
            }
            
            // 写入数据
            if (!empty($v['export'])) {
                $rowIndex = !empty($v['header']) ? 1 : 0;
                
                // 存储需要合并的单元格信息
                $mergeCells = [];
                $skipCells = []; // 存储需要跳过的单元格位置
                
                // 预处理行合并信息
                foreach ($v['export'] as $rowNum => $row) {
                    $colIndex = 0;
                    foreach ($row as $cellIndex => $cell) {
                        if (is_array($cell) && isset($cell['rowspan']) && $cell['rowspan'] > 1) {
                            $startRow = $rowIndex + $rowNum + 1;
                            $endRow = $startRow + $cell['rowspan'] - 1;
                            
                            // 记录需要跳过的单元格位置
                            for ($r = $startRow + 1; $r <= $endRow; $r++) {
                                $skipCells[$r][$colIndex] = true;
                            }
                        }
                        
                        if (is_array($cell) && isset($cell['colspan'])) {
                            $colIndex += $cell['colspan'];
                        } else {
                            $colIndex++;
                        }
                    }
                }
                
                // 写入数据并处理合并
                foreach ($v['export'] as $rowNum => $row) {
                    $rowIndex++;
                    $colIndex = 0;
                    $rowData = [];
                    
                    foreach ($row as $cellIndex => $cell) {
                        // 检查是否需要跳过当前单元格
                        while (isset($skipCells[$rowIndex][$colIndex])) {
                            $rowData[] = ''; // 填充空值
                            $colIndex++;
                        }
                        
                        if (is_array($cell)) {
                            // 处理合并单元格
                            if (isset($cell['value'])) {
                                $value = $cell['value'];
                                $startCol = self::numberToColumn($colIndex);
                                
                                if (isset($cell['colspan']) && isset($cell['rowspan'])) {
                                    // 同时有行合并和列合并
                                    $endCol = self::numberToColumn($colIndex + $cell['colspan'] - 1);
                                    $endRow = $rowIndex + $cell['rowspan'] - 1;
                                    $mergeCells[] = [
                                        'range' => $startCol . $rowIndex . ':' . $endCol . $endRow,
                                        'value' => $value
                                    ];
                                    
                                    $rowData[] = $value;
                                    for ($i = 1; $i < $cell['colspan']; $i++) {
                                        $rowData[] = '';
                                    }
                                    $colIndex += $cell['colspan'];
                                } 
                                elseif (isset($cell['colspan'])) {
                                    // 只有列合并
                                    $endCol = self::numberToColumn($colIndex + $cell['colspan'] - 1);
                                    $mergeCells[] = [
                                        'range' => $startCol . $rowIndex . ':' . $endCol . $rowIndex,
                                        'value' => $value
                                    ];
                                    
                                    $rowData[] = $value;
                                    for ($i = 1; $i < $cell['colspan']; $i++) {
                                        $rowData[] = '';
                                    }
                                    $colIndex += $cell['colspan'];
                                } 
                                elseif (isset($cell['rowspan'])) {
                                    // 只有行合并
                                    $endRow = $rowIndex + $cell['rowspan'] - 1;
                                    $mergeCells[] = [
                                        'range' => $startCol . $rowIndex . ':' . $startCol . $endRow,
                                        'value' => $value
                                    ];
                                    
                                    $rowData[] = $value;
                                    $colIndex++;
                                } 
                                else {
                                    // 普通值
                                    $rowData[] = $value;
                                    $colIndex++;
                                }
                            } else {
                                // 数组但没有value字段
                                $rowData[] = '';
                                $colIndex++;
                            }
                        } else {
                            // 普通单元格
                            $rowData[] = $cell;
                            $colIndex++;
                        }
                    }
                    
                    // 一次性写入整行数据
                    $currentSheet->data([$rowData]);
                }
                
                // 执行所有单元格合并
                foreach ($mergeCells as $merge) {
                    $currentSheet->mergeCells($merge['range'], $merge['value']);
                }
            }
            
            // 最后一个sheet时输出
            if ($k == count($data) - 1) {
                $currentSheet->output();
            }
        }
        
        // 关闭文件句柄
        $excel->close();
        
        // 返回下载链接
        if (config('app.debug')) {
            return 'http://10.0.0.247/excel/' . $title;
        }
        return 'https://api.v2.zkshlm.com/excel/' . $title;
    }
}