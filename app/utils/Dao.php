<?php

namespace app\utils;

use Exception;
use Webman\Medoo\Medoo;

class Dao
{
    /**
     * @var \Medoo\Medoo
     * Medoo库
     */
    protected \Medoo\Medoo $database;

    /**
     * 初始化数据库
     */
    public function __construct()
    {
        $this->database = Medoo::instance();
    }

    /**
     * 通用数据搜索
     * @param string $table
     * @param array $where
     * @param string|array $columns
     * @param array $join
     * @return array|string|null
     */
    public function search(
        string       $table,
        array        $where = [],
        string|array $columns = '*',
        array        $join = []
    ): array|null|string
    {
        if ($join === []) {
            return $this->database->select($table, $columns, $where);
        }
        return $this->database->select($table, $join, $columns, $where);
    }

    /**
     * 通用查询
     * @param string $table
     * @param array $where
     * @param string|array $columns
     * @param array $join
     * @return array|null|string
     */
    public function get(
        string       $table,
        array        $where = [],
        string|array $columns = '*',
        array        $join = []
    ): array|null|string
    {
        if ($join === []) {
            return $this->database->get($table, $columns, $where);
        }
        return $this->database->get($table, $join, $columns, $where);
    }

    /**
     *新增
     * @param string $table
     * @param array $data
     * @return string|null
     */
    public function insert(string $table, array $data = []): ?string
    {
        $this->database->insert($table, $data);
        return $this->database->id();
    }

    /**
     * 修改
     * @param string $table
     * @param array $data
     * @param array $where
     * @return int
     */
    public function update(string $table, array $data = [], array $where = []): int
    {
        return $this->database->update($table, $data, $where)->rowCount();
    }

    /**
     * 是否存在
     * @param string $table
     * @param array $where
     * @return bool
     */
    public function has(string $table, array $where = []): bool
    {
        return $this->database->has($table, $where);
    }

    /**
     * 计数
     * @param string $table
     * @param array $where
     * @return int|null
     */
    public function count(string $table, array $where = []): ?int
    {
        return $this->database->count($table, $where);
    }

    /**
     * 查询列字段相加
     * @param string $table
     * @param string $column
     * @param array $where
     * @param array $join
     * @return int|string|null
     */
    public function sum(string $table, string $column, array $where = [], array $join = []): int|string|null
    {
        if ($join === []) {
            return $this->database->sum($table, $column, $where);
        }
        return $this->database->sum($table, $join, $column, $where);
    }

    /**
     * 删除
     * @param string $table
     * @param array $where
     * @return int
     */
    public function del(string $table, array $where = []): int
    {
        $data = $this->database->delete($table, $where);
        return $data->rowCount();
    }

    /**
     * 开启事务
     * @throws Exception
     */
    public function action($db): void
    {
        $this->database->action($db);
    }

    /**
     * 原生查询
     * @param string $sql
     */
    public function query(string $sql)
    {
        return $this->database->query($sql);
    }

    /**
     * log
     */
    public function log()
    {
        return $this->database->log();
    }
}