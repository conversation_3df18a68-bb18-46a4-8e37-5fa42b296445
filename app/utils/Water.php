<?php

namespace app\utils;

/**
 * Encryption Utility Class
 * PHP implementation of Java EncryptUtils
 */
class Water
{
    /**
     * Character set for encoding/decoding
     */
    public const CHARSET = "UTF-8";

    /**
     * RSA Public Key
     */
    public const PUBLIC_KEY = "-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEAqguBBB5ngwF4xfscjbwpP18/joVuOxNXqz0eGyGS0MU1wLKIcA1W
bRk2cs0DhE/2NK2OLb2v3dP7XiI7mkURlRpM7ohPDoKN+nj8kaVY1dFUn0UPA+OP
fOEmcBNwPvU2aHeIx8M7bO3/iAQ/bUwxKdp9kUuB3suXxMVZxTk6WtZBx0ZwkMuk
XtuiUPLpAbroFWSjeZULDNIlNCdIfgY3mB8v75o2y7Vy5cuYL9VCXSX0pcBAYsXo
XmTaNbTgZAQ6F8bU+TuZYO9nViUY/5ayscs8N2/0kV0Oytk77kSMdbOMi+HvslDn
HS71wq2LiL67OBoYhgOsJDk+HPwg5H/UCQIDAQAB
-----END RSA PUBLIC KEY-----";

    /**
     * RSA Private Key
     */
    public const PRIVATE_KEY = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

    /**
     * @return \OpenSSLAsymmetricKey|false
     * @throws \Exception
     */
    public static function getRSAPublicKey() {
        $publicKey = openssl_pkey_get_public(self::PUBLIC_KEY);
        if ($publicKey === false) {
            throw new \Exception("Invalid public key: " . openssl_error_string());
        }
        return $publicKey;
    }

    /**
     * @return \OpenSSLAsymmetricKey|false
     * @throws \Exception
     */
    public static function getRSAPrivateKey() {
        $privateKey = openssl_pkey_get_private(self::PRIVATE_KEY);
        if ($privateKey === false) {
            throw new \Exception("Invalid private key: " . openssl_error_string());
        }
        return $privateKey;
    }

    /**
     * @param string $str
     * @param \OpenSSLAsymmetricKey $rsaPublicKey
     * @return string
     * @throws \Exception
     */
    public static function encryptRsa(string $str, $rsaPublicKey): string {
        $encrypted = '';
        if (!openssl_public_encrypt($str, $encrypted, $rsaPublicKey, OPENSSL_PKCS1_PADDING)) {
            throw new \Exception("RSA Encryption failed: " . openssl_error_string());
        }
        return base64_encode($encrypted);
    }

    /**
     * @param string $str
     * @param \OpenSSLAsymmetricKey $rsaPrivateKey
     * @return string
     * @throws \Exception
     */
    public static function decryptRsa(string $str, $rsaPrivateKey): string {
        $inputByte = base64_decode($str);
        if ($inputByte === false) {
            throw new \Exception("Base64 decoding failed for RSA encrypted string.");
        }
        $decrypted = '';
        if (!openssl_private_decrypt($inputByte, $decrypted, $rsaPrivateKey, OPENSSL_PKCS1_PADDING)) {
            throw new \Exception("RSA Decryption failed: " . openssl_error_string());
        }
        return $decrypted;
    }

    private static function randomAlphanumericString(int $length): string {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            try {
                $randomString .= $characters[random_int(0, $charactersLength - 1)];
            } catch (\Exception $e) {
                $randomString .= $characters[mt_rand(0, $charactersLength - 1)];
            }
        }
        return $randomString;
    }

    public static function getEncryptData(string $data): ?string {
        try {
            $resultJson = [];
            $keyStr = self::randomAlphanumericString(16);
            $rsaPublicKey = self::getRSAPublicKey();
            $rsaEncryptedData = self::encryptRsa($keyStr, $rsaPublicKey);
            // openssl_free_key($rsaPublicKey); // Removed - Deprecated in PHP 8.0+

            $aesCipher = 'aes-128-ecb';
            $rawAesEncryptedData = openssl_encrypt($data, $aesCipher, $keyStr, OPENSSL_RAW_DATA);
            if ($rawAesEncryptedData === false) {
                throw new \Exception("AES Encryption failed: " . openssl_error_string());
            }
            $aesEncryptedData = bin2hex($rawAesEncryptedData);

            $resultJson['rsaEncryptedData'] = $rsaEncryptedData;
            $resultJson['aesEncryptedData'] = $aesEncryptedData;

            return json_encode($resultJson);

        } catch (\Exception $e) {
            // This will log messages like "Encryption Error: AES Encryption failed: error:0607A082:digital envelope routines:EVP_EncryptFinal_ex:data not multiple of block length"
            error_log("Encryption Error in getEncryptData: " . $e->getMessage());
            return null;
        }
    }

    public static function getDecryptData(array $json): ?string {
        try {
            $resultJson = [];
            $rsaEncryptedData = $json['rsaEncryptedData'];
            $aesEncryptedDataHex = $json['aesEncryptedData'];
            $rsaPrivateKey = self::getRSAPrivateKey();
            $keyStr = self::decryptRsa($rsaEncryptedData, $rsaPrivateKey);
            // openssl_free_key($rsaPrivateKey); // Removed - Deprecated in PHP 8.0+

            $resultJson['rsaEncryptedData'] = $keyStr;

            $aesCipher = 'aes-128-ecb';
            $aesEncryptedDataBytes = hex2bin($aesEncryptedDataHex);
            if ($aesEncryptedDataBytes === false) {
                throw new \Exception("Hex decoding failed for AES encrypted data.");
            }

            $decryptedPayload = openssl_decrypt($aesEncryptedDataBytes, $aesCipher, $keyStr, OPENSSL_RAW_DATA);
            if ($decryptedPayload === false) {
                // This will log messages like "Decryption Error: AES Decryption failed: error:06065064:digital envelope routines:EVP_DecryptFinal_ex:bad decrypt"
                throw new \Exception("AES Decryption failed: " . openssl_error_string());
            }
            $resultJson['aesEncryptedData'] = $decryptedPayload;

            return json_encode($resultJson);

        } catch (\Exception $e) {
            error_log("Decryption Error in getDecryptData: " . $e->getMessage());
            return null;
        }
    }
}