<?php

namespace app\utils;

use yzh52521\EasyHttp\Http;

class NocoDbApi
{
    /**
     * 请求接口-单次
     * @param $params
     * @param string $method
     * @return array|mixed
     */
    public static function send($params, string $method = 'get')
    {
        $header = [
            'xc-token' => 'mr29Jn17Iiy3QNu3xfbF_Aedh-zIRCs9xXiE1FcS'
        ];
        $url = 'http://form.test.zkshlm.com/api/v2/tables/'
            . $params['table_id']
            . '/'
            . 'records?viewId=' . $params['view_id'];
        unset($params['table_id']);
        unset($params['view_id']);
        $result = match ($method) {
            'post' => Http::withHeaders($header)->post($url,$params)->array(),
            'put' =>  Http::withHeaders($header)->put($url,$params)->array(),
            'patch' =>  Http::withHeaders($header)->patch($url,$params)->array(),
            'DELETE' =>  Http::withHeaders($header)->delete($url,$params)->array(),
            default => Http::withHeaders($header)->get($url,$params)->array(),
        };
        if(!is_array($result) && !empty($result)) {
            $result = json_decode($result,true);
        }
        return $result;
    }

    /**
     * 分组统计
     */
    public static function groupSend($params, string $method = 'get')
    {
        $header = [
            'xc-token' => 'mr29Jn17Iiy3QNu3xfbF_Aedh-zIRCs9xXiE1FcS'
        ];
        $url = 'http://form.test.zkshlm.com/api/v1/db/data/noco/p57v56piubotqaa/'. $params['table_id'].'/views/'
            . $params['view_id'] .'/'
            . 'groupby';
        unset($params['table_id']);
        unset($params['view_id']);
        $result = match ($method) {
            'post' => Http::withHeaders($header)->post($url,$params)->array(),
            'put' =>  Http::withHeaders($header)->put($url,$params)->array(),
            'patch' =>  Http::withHeaders($header)->patch($url,$params)->array(),
            'DELETE' =>  Http::withHeaders($header)->delete($url,$params)->array(),
            default => Http::withHeaders($header)->get($url,$params)->array(),
        };
        if(!is_array($result) && !empty($result)) {
            $result = json_decode($result,true);
        }
        return $result;
    }
}