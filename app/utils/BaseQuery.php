<?php
declare(strict_types=1);

namespace app\utils;

use think\db\Query;


class BaseQuery extends Query
{
    /**
     * 快速搜索
     *
     * @param array $likeField
     * @param array $excludeFields
     * @return Query
     */
    public function quickSearch(array $likeField = [], array $excludeFields = ['pageNum', 'pageSize', 'page', 'limit']): Query
    {
        $requestParams = request()->all();
        if (empty($requestParams)) {
            return $this;
        }
        foreach ($requestParams as $field => $value) {
            if(empty($value)) {
                continue;
            }
            // 参数键值过滤
            if (in_array($field, $excludeFields) || is_null($value)) {
                continue;
            }
            // 区间范围 数据库字段_start & 数据库字段_end
            $endField = "_start";
            if (Str::endsWith($field, $endField)) {
                $field = Str::snake(Str::substr($field, 0, Str::length($field) - Str::length($endField)));
                $this->where($field, '>=', strtotime($value));
            }
            $endField = "_end";
            if (Str::endsWith($field, $endField)) {
                $field = Str::snake(Str::substr($field, 0, Str::length($field) - Str::length($endField)));
                $this->where($field, '<=', strtotime($value));
                continue;
            }
            // 模糊搜索
            $endField = "_like";
            if (Str::endsWith($field, $endField)) {
                $field = Str::snake(Str::substr($field, 0, Str::length($field) - Str::length($endField)));
                $this->whereLike($field, $value);
                continue;
            }
            // 模糊搜索
            $endField = "_leftlike";
            if (Str::endsWith($field, $endField)) {
                $field = Str::snake(Str::substr($field, 0, Str::length($field) - Str::length($endField)));
                $this->whereLeftLike($field, $value);
                continue;
            }
            // 模糊搜索
            $endField = "_rightlike";
            if (Str::endsWith($field, $endField)) {
                $field = Str::snake(Str::substr($field, 0, Str::length($field) - Str::length($endField)));
                $this->whereRightLike($field, $value);
                continue;
            }
            // = 值搜索
            $tablefield = Str::snake($field);
            if (in_array($field, $likeField)) {
                $this->whereLike($tablefield, $value);
            } else {
                $this->where($tablefield, $value);
            }
        }
        return $this;
    }

    /**
     * 默认排序
     * @time 2023年02月21日
     * @param string $order
     * @return $this
     */
    public function defaultOrder(string $order = 'desc'): Query
    {
        $fields = $this->getFields();
        if (in_array('sort', array_keys($fields))) {
            $this->order($this->getTable() . '.sort desc');
        }

        $this->order($this->getTable() . '.' . $this->getPk(), $order);

        return $this;
    }

    /**
     *
     * @time 2022年03月18日
     * @return mixed
     */
    public function getAlias(): mixed
    {
        return isset($this->options['alias']) ? $this->options['alias'][$this->getTable()] : $this->getTable();
    }

    /**
     * @time 2023年02月21日
     * @param string $field
     * @param mixed $condition
     * @param string $option
     * @param string $logic
     * @return Query
     */
    public function whereLike(string $field, $condition, string $logic = 'AND', string $option = 'both'): Query
    {
        switch ($option) {
            case 'both':
                $condition = '%' . $condition . '%';
                break;
            case 'left':
                $condition = '%' . $condition;
                break;
            default:
                $condition .= '%';
        }

        if (!str_contains($field, '.')) {
            $field = $this->getAlias() . '.' . $field;
        }

        return parent::whereLike($field, $condition, $logic);
    }

    /**
     * @param string $field
     * @param $condition
     * @param string $logic
     * @return Query
     */
    public function whereLeftLike(string $field, $condition, string $logic = 'AND'): Query
    {
        return $this->where($field, $condition, $logic, 'left');
    }

    /**
     * @param string $field
     * @param $condition
     * @param string $logic
     * @return Query
     */
    public function whereRightLike(string $field, $condition, string $logic = 'AND'): Query
    {
        return $this->where($field, $condition, $logic, 'right');
    }
}