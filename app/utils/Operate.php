<?php

namespace app\utils;

use support\Container;

/**
 * Code Generator
 */
class Operate
{
    /**
     * 记录操作日志
     * @param $data
     * @return void
     */
    public static function OperateLog($data): void
    {
        $data['service_name'] = self::getServiceName($data['service_name']);
        $data['ip_location'] = self::getIpLocation($data['ip_location']);
        $data['request_data'] = self::filterParams($data['request_data']);
        if($data['app'] !== 'saiadmin') {
            if(str_starts_with($data['app'], "app\controller\OauthController")) {
                $data['app'] = '授权应用';
            }
            if(str_starts_with($data['app'], "app\cust\controller")) {
                $data['app'] = '被照护人应用';
            }
            if(str_starts_with($data['app'], 'app\food\controller')) {
                $data['app'] = '助餐应用';
            }
        } else {
            $data['app'] = '系统应用';
        }
        $dao = Container::get(Dao::class);
        $dao->insert('eb_system_oper_log',$data);
    }

    /**
     * 获取业务名称
     * @param $path
     * @return string
     */
    protected static function getServiceName($path) : string
    {
        $dao = Container::get(Dao::class);
        if (preg_match("/\{[^}]+\}/", $path)) {
            $path = rtrim(preg_replace("/\{[^}]+\}/", '', $path), '/');
        }
        $menuName = $dao->get('eb_system_menu', ['code' => $path],'name');
        if (!empty($menuName)) {
            return $menuName;
        } else {
            return match ($path) {
                '/oauth/getCode' => '获取授权code',
                '/oauth/getToken' => '解密code',
                '/core/system/getloginloglist' => '获取登录日志',
                '/core/system/getoperationloglist' => '获取操作日志',
                '/core/system/getloginlogpagelist' => '获取登录日志列表',
                '/core/system/dictdata' => '字典数据查询',
                '/core/user/modifyPassword' => '修改密码',
                '/core/user/initUserPassword' => '重置密码',
                '/core/system/user' => '获取当前登录用户信息',
                '/core/system/clearAllCache' => '清除缓存',
                default => '未知',
            };
        }
    }

    /**
     * 过滤敏感字段
     * @param $params
     * @return string
     */
    protected static function filterParams($params): string
    {
        $blackList = ['password', 'oldPassword', 'newPassword', 'content'];
        foreach ($params as $key => $value) {
            if (in_array($key, $blackList)) {
                $params[$key] = '******';
            }
        }
        return json_encode($params, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取IP地址
     * @param $ip
     * @return string
     */
    protected static function getIpLocation($ip): string
    {
        $ip2region = new \Ip2Region();
        try {
            $region = $ip2region->memorySearch($ip);
        } catch (\Exception $e) {
            return '未知';
        }
        list($country, $number, $province, $city, $network) = explode('|', $region['region']);
        if ($network === '内网IP') {
            return $network;
        }
        if ($country == '中国') {
            return $province.'-'.$city.':'.$network;
        } elseif ($country == '0') {
            return '未知';
        } else {
            return $country;
        }
    }

    /**
     * 清除长期日志
     * @return string
     */
    public function run(): string
    {
        // 获取保存日期 天
        $day = getConfigValue('site_log_save_time');
        $dao = Container::get(Dao::class);
        $rows = $dao->del('eb_system_oper_log', ['create_time[<]' => date('Y-m-d H:i:s', strtotime("-$day day"))]);
        return '清除成功，共删除'.$rows.'条日志';
    }
}