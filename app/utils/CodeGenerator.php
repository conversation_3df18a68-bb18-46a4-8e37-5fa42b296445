<?php

namespace app\utils;

use think\facade\Db;

/**
 * Code Generator
 */
class CodeGenerator
{
    /**
     * Create File
     * @param string $type
     * @param string $content
     * @param string $fileName
     */
    public static function createFile(string $type, string $content, string $fileName): void
    {
        $fileName = str_replace('php/', '', $fileName);
        switch ($type) {
            case 'php/controller.stub':
                self::createControllerMethod($content,$fileName);
                break;
            case 'php/logic.stub':
                self::createLogicLayer($content,$fileName);
                break;
            case 'php/model.stub':
                self::createModelLayer($content,$fileName);
                break;
            case 'php/validate.stub':
                self::createValidator($content,$fileName);
                break;
            case 'php/route.stub':
                self::createApiRoutes($content);
                break;
            case 'js/api.stub':
                self::createFrontendApiRoutes($content,$fileName);
                break;
            case 'vue/single.stub':
                self::createFrontendPage($content,$fileName);
                break;
            case 'sql/sql.stub':
                self::createMenuSQL($content);
                break;
            case 'vue/tree.stub':
                self::createTreePage($content,$fileName);
                break;
            default:
                error_log("未定义类型: $type");
                break;
        }
    }

    /**
     * Create Controller Method
     */
    private static function createControllerMethod($content,$fileName): void
    {
        $fileName = str_replace('\\', '/', $fileName);
        $fileName = config('app.api_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }

    /**
     * Create Logic Layer
     */
    private static function createLogicLayer($content,$fileName): void
    {
        $fileName = str_replace('\\', '/', $fileName);
        $fileName = config('app.api_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }

    /**
     * Create Model Layer
     */
    private static function createModelLayer($content,$fileName): void
    {
        $fileName = str_replace('\\', '/', $fileName);
        $fileName = config('app.api_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }

    /**
     * Create Validator
     */
    private static function createValidator($content,$fileName): void
    {
        $fileName = str_replace('\\', '/', $fileName);
        $fileName = config('app.api_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }

    /**
     * Create API Routes
     */
    private static function createApiRoutes($content): void
    {
        // 提取路由文件内容
        $content = extractContentAfterInput('use Webman\Route;',$content);
        if($content) {
            // 写入路由
            appendIfNotWritten('/app/config/route.php', $content);
        }
    }

    /**
     * Create Frontend API Routes
     */
    private static function createFrontendApiRoutes($content,$fileName): void
    {
        $fileName = config('app.web_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }

    /**
     * Create Frontend Page
     */
    private static function createFrontendPage($content,$fileName): void
    {
        $fileName = config('app.web_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }

    /**
     * Create Menu SQL
     */
    private static function createMenuSQL($content): void
    {
        $str = $content;
        // 检查SQL是否创建
        if (preg_match('/菜单\[(.*?)\] SQL/', $str, $menuStr)) {
            // $matches[1] 包含了匹配到的内容
            $isTure = Db::name('eb_system_menu')->where('name', $menuStr[1])->findOrEmpty();
            if ($isTure) {
                error_log("菜单SQL已经执行过了");
                return;
            }
        }
        Db::startTrans();
        try {
            // 获取concat值
            preg_match("/CONCAT\('(\d+)',/", $str, $concat);
            if (!empty($concat[1])) {
                $level = $concat[1];
            } else {
                $level = 0;
            }
            // 使用正则表达式提取所有 INSERT INTO 语句
            preg_match_all('/INSERT INTO `eb_system_menu`.*?;/s', $str, $matches);
            $lastInsertId = 0;
            if (!empty($matches[0])) {
                foreach ($matches[0] as $key => $sql) {
                    if($key == 0) {
                        Db::execute($sql);
                        $lastInsertId = Db::name('eb_system_menu')->getLastInsID();
                        $level = $level . ',' . $lastInsertId;
                    } else {
                        // 替换level
                        $sql = str_replace('@level', "\"".$level."\"", $sql);
                        $sql = str_replace('@id', $lastInsertId, $sql);
                        Db::execute($sql);
                    }
                }
            }
            // 提交事务
            Db::commit();
            error_log("菜单SQL执行成功");
        } catch (\Exception $e) {
            // 出现异常时回滚事务
            Db::rollback();
            error_log("菜单SQL执行失败: " . $e->getMessage());
        }
    }

    /**
     * createTreePage
     */
    private static function createTreePage($content,$fileName): void
    {
        $fileName = config('app.web_file').$fileName;
        createFileWithDirectories($fileName, $content);
    }
}