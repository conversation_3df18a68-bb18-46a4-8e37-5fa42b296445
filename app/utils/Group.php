<?php
declare(strict_types=1);

namespace app\utils;

class Group
{
    /**
     * @param BaseQuery $model
     * @param string $section
     * @param string $prefix
     * @param string $field
     * @return mixed
     */
    public static function getModelTime(BaseQuery $model, string $section, string $prefix = 'created_at', string $field = '~'): mixed
    {
        if (!isset($section)) {
            return $model;
        }
        switch ($section) {
            case 'tomorrow':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime("+1 days 00:00:00")), date('Y-m-d H:i:s', strtotime("+1 days 23:59"))]);
                break;
            case 'today':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime('today')), date('Y-m-d H:i:s', strtotime('tomorrow -1second'))]);
                break;
            case 'week':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime('this week 00:00:00')), date('Y-m-d H:i:s', strtotime('next week 00:00:00 -1second'))]);
                break;
            case 'lastWeek':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime('last week Monday')), date('Y-m-d H:i:s', strtotime('last week Sunday 23:59:59'))]);
                break;
            case 'month':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime('first Day of this month 00:00:00')), date('Y-m-d H:i:s', strtotime('first Day of next month 00:00:00 -1second'))]);
                break;
            case 'year':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime('this year 1/1')), date('Y-m-d H:i:s', strtotime('next year 1/1 -1second'))]);
                break;
            case 'yesterday':
                $model->whereBetween($prefix, [date('Y-m-d H:i:s', strtotime('yesterday')), date('Y-m-d H:i:s', strtotime('today -1second'))]);
                break;
            case 'quarter':
                list($startTime, $endTime) = self::getMonth();
                $model = $model->where($prefix, '>', $startTime);
                $model = $model->where($prefix, '<', $endTime);
                break;
            case 'lately7':
                $model = $model->where($prefix, 'between', [date('Y-m-d', strtotime("-7 day")), date('Y-m-d H:i:s')]);
                break;
            case 'lately30':
                $model = $model->where($prefix, 'between', [date('Y-m-d', strtotime("-30 day")), date('Y-m-d H:i:s')]);
                break;
            default:
                if (str_contains($section, $field)) {
                    list($startTime, $endTime) = explode($field, $section);
                    $model = $model->whereBetweenTime($prefix, date('Y-m-d H:i:s', strtotime($startTime)), date('Y-m-d H:i:s', strtotime($endTime . ' +1day -1second')));
                }
                break;
        }
        return $model;
    }

    /**
     * 获取本季度 time
     * @param int $ceil
     * @return array
     */
    protected static function getMonth(int $ceil = 0): array
    {
        if ($ceil != 0) {
            $season = ceil(date('n') / 3) - $ceil;
        } else {
            $season = ceil(date('n') / 3);
        }
        $firstDay = date('Y-m-01', mktime(0, 0, 0, ($season - 1) * 3 + 1, 1, (int)date('Y')));
        $lastDay = date('Y-m-t', mktime(0, 0, 0, $season * 3, 1, (int)date('Y')));
        return array($firstDay, $lastDay);
    }

    /**
     * 获取返回时间类型
     * @param int $time
     * @return string
     */
    protected static function getTimely(int $time): string
    {
        if ($time === 1) {
            $timeUnix = "%H";
        } elseif ($time === 30) {
            $timeUnix = "%Y-%m-%d";
        } elseif ($time === 365) {
            $timeUnix = "%Y-%m";
        } elseif ($time > 1 && $time < 30) {
            $timeUnix = "%Y-%m-%d";
        } elseif ($time > 30 && $time < 365) {
            $timeUnix = "%Y-%m";
        } else {
            $timeUnix = "%Y-%m-%d";
        }
        return $timeUnix;
    }

    /**
     * 搜索时间转换
     * @param $timeKey
     * @return array
     */
    public function timeConvert($timeKey): array
    {
        switch ($timeKey) {
            case "today" :
                $data['start_time'] = date('Y-m-d 00:00:00', time());
                $data['end_time'] = date('Y-m-d 23:59:59', time());
                $data['days'] = 1;
                break;
            case "yesterday" :
                $data['start_time'] = date('Y-m-d 00:00:00', strtotime('-1 day'));
                $data['end_time'] = date('Y-m-d 23:59:59', strtotime('-1 day'));
                $data['days'] = 1;
                break;
            case "last_month" :
                $data['start_time'] = date('Y-m-01 00:00:00', strtotime('-1 month'));
                $data['end_time'] = date('Y-m-t 23:59:59', strtotime('-1 month'));
                $data['days'] = 30;
                break;
            case "month" :
                $data['start_time'] = $monthStartTime = date('Y-m-01 00:00:00', strtotime(date("Y-m-d")));
                $data['end_time'] = date('Y-m-d 23:59:59', strtotime("$monthStartTime +1 month -1 day"));
                $data['days'] = 30;
                break;
            case "year" :
                $data['start_time'] = date('Y-01-01 00:00:00', time());
                $data['end_time'] = date('Y-12-t 23:59:59', time());
                $data['days'] = 365;
                break;
            case "last_year" :
                $data['start_time'] = date('Y-01-01 00:00:00', strtotime('-1 year'));
                $data['end_time'] = date('Y-12-t 23:59:59', strtotime('-1 year'));
                $data['days'] = 365;
                break;
            case 30 :
            case 15 :
            case 7 :
                $data['start_time'] = date("Y-m-d 00:00:00", strtotime("-$timeKey day"));
                $data['end_time'] = date('Y-m-d 23:59:59', time());
                $data['days'] = $timeKey;
                break;
            default:
                $data['start_time'] = date('Y-m-d H:i:s', strtotime('tomorrow -1second'));
                $data['end_time'] = date('Y-m-d H:i:s', strtotime('today'));
                $data['days'] = 1;
        }
        return $data;
    }

}