<?php

namespace app\utils;

use EasyWeChat\OfficialAccount\Application;
use Exception;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class Wechat
{
    /**
     * 通过code获取用户信息 默认登录
     * @param $config
     * @param $code
     * @return string
     * @throws BadRequestHttpException
     */
    public static function getUserInfo($config, $code) : string
    {
        try {
            $app = new Application($config);
            $oauth = $app->getOauth();
            $data = $oauth->tokenFromCode($code);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        return $data['openid'];
    }

    /**
     * 通过code获取用户信息
     * @param $config
     * @param $code
     * @return array
     * @throws BadRequestHttpException
     */
    public static function getUserInfoV2($config, $code) : array
    {
        try {
            $app = new Application($config);
            $oauth = $app->getOauth();
            $data = $oauth->tokenFromCode($code);
        } catch (Exception $e) {
            return [];
        }
        // 查询用户信息
        $data = httpPost('https://api.card.zkshlm.com/notice/getUserInfo',[
            'openid' => $data['openid'],
        ]);
        if(!empty($data) && $data['code'] == 200) {
            return $data['data'];
        }
        return [];
    }
}