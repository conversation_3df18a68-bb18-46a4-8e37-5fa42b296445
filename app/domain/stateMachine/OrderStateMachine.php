<?php

namespace app\domain\stateMachine;

use SM\SMException;
use SM\StateMachine\StateMachine;


class OrderStateMachine
{
    /**
     * @throws SMException
     */
    public static function create($object): StateMachine
    {
        // 定义状态机的配置
        $config = [
            'graph'         => 'order', // 图的名称
            'property_path' => 'order_status',  // 对象中保存状态的属性路径
            'states'        => ['待付款', '待完成', '已完成', '待审核','申请退款','已退款','订单已取消'],  // 定义所有可能的状态
            'transitions' => [
                // 定义 'create' 迁移，从 待付款 到 待完成 或 已完成
                'create' => [
                    'from' => ['待付款'],
                    'to'   => '待完成'
                ],
                // 定义 'confirm' 迁移，可以从 '待完成' 或 '待付款' 到 '已完成'
                'confirm' => [
                    'from' => ['待完成', '待付款'],
                    'to'   => '已完成'
                ],
                // 定义 'cancel' 迁移，从 已完成 待完成 到 '已退款'
                'cancel' => [
                    'from' => ['已完成','待完成'],
                    'to'   => '已退款'
                ]
            ],
            // 定义回调函数
            'callbacks' => [
                // 定义守卫回调 参数为状态名
                'guard' => [
                    'guard-cancel' => [
                        'to' => ['待完成'],  // 只在转换到 '待完成' 状态时调用
                        // 当试图迁移到 '待完成' 时执行此回调，返回 false 阻止迁移
                        'do' => function() { var_dump('guarding to cancelled state'); return true; }
                    ]
                ],
                // 在转换之前调用的回调 参数为状态名
                'before' => [
                    'from-cancel' => [
                        'from' => ['已完成'], // 只在从 '待完成' '待付款' 状态转换时调用
                        // 当从 'checkout' 迁移时执行此回调
                        'do'   => function() { var_dump('from checkout transition'); }
                    ]
                ],
                // 在转换之后调用的回调 参数为方法名
                'after' => [
                    'on-confirm' => [
                        'on' => ['cancel'], // 只在 'cancel' 转换时调用
                        'do' => function() { var_dump('on confirm transition'); }
                    ],
                    'confirm-date' => [
                        'on' => ['cancel'], // 只在 'cancel' 转换时调用
                        'do' => ['object','setConfirmedNow'], // 'object' 将被替换为正在进行转换的对象
                    ],
                ]
            ]
        ];

        return new StateMachine($object, $config);
    }
}