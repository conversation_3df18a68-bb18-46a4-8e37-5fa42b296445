<?php

namespace app\domain\model;

use plugin\saiadmin\basic\BaseModel;

class Order extends BaseModel
{

    protected $table = 'order_main'; // 指定表名

    protected $pk  = 'order_id'; // 指定主键
    public function getState()
    {
        return $this->order_status;
    }

    public function setState($state)
    {
        $this->order_status = $state;
        $this->save(); // 保存状态到数据库
        return $this->order_status;
    }

    public function setConfirmedNow()
    {
        // Set the confirmed date to now
        $this->order_confirm_time = date('Y-m-d H:i:s');
        $this->save(); // 保存状态到数据库
    }
}
