<?php

namespace app\domain\model;

use app\cust\model\CustUser;
use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasOne;

/**
 * 订单主表
 */
class SmartWater extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'smart_water';

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function custInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_live_address,cust_community,cust_evaluation_level,cust_aging,cust_consultant');
    }
}