<?php

namespace app\domain\model;

use app\cust\model\CustUser;
use app\employee\model\EmployeeUser;
use app\food\model\FoodPackage;
use app\food\model\FoodPackageItem;
use plugin\saiadmin\app\model\system\SystemUser;
use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasOne;
use think\model\relation\HasMany;

/**
 * 套餐订单表
 */
class OrderFoodItem extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'order_food_item_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'order_food_item';

    /**
     * 一对一关联 套餐信息
     * @return HasOne
     */
    public function packageInfo(): HasOne
    {
        return $this->hasOne(FoodPackage::class,'package_id','package_id')->field('package_id,name');
    }

    /**
     * 一对多关联套餐子订单带食品元素
     */
    public function packageList(): HasMany
    {
        return $this->hasMany(FoodPackageItem::class,'package_id','package_id')->field('package_id,food_name');
    }

    /**
     * 一对一送餐员信息
     */
    public function employee(): <PERSON>O<PERSON>
    {
        return $this->hasOne(EmployeeUser::class,'employee_uid','employee_uid')->field('employee_uid,employee_name');
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function custInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_live_address,cust_identity_type,cust_evaluation_level');
    }

    /**
     * 一对一关联 创建人信息
     * @return HasOne
     */
    public function createdByInfo(): HasOne
    {
        return $this->hasOne(SystemUser::class,'id','created_by')->field('id,nickname');
    }
}