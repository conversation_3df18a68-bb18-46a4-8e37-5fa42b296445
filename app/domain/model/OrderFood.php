<?php

namespace app\domain\model;

use app\employee\model\EmployeeUser;
use app\cust\model\CustUser;
use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasMany;
use think\model\relation\HasOne;
use plugin\saiadmin\app\model\system\SystemUser;

/**
 * 套餐订单表
 */
class OrderFood extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'order_food_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'order_food';

    /**
     * 一对多关联套餐子订单
     */
    public function foodItem(): HasMany
    {
        return $this->hasMany(OrderFoodItem::class,'order_id','order_id')
            ->where('num','>',0)
            ->field('order_food_item_id,order_id,package_id,package_name,price,num')
            ->order('package_id');
    }

    /**
     * 一对多关联套餐子订单带食品元素
     */
    public function package(): HasMany
    {
        return $this->hasMany(OrderFoodItem::class,'order_id','order_id')
            ->field('order_food_item_id,order_id,package_id,package_name,price,num,employee_uid,delivery_option')
            ->order('package_id');
    }

    /**
     * 一对一送餐员信息
     */
    public function employee(): HasOne
    {
        return $this->hasOne(EmployeeUser::class,'employee_uid','employee_uid')->field('employee_uid,employee_name');
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function custInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_live_address,cust_identity_type,cust_evaluation_level,cust_is_bed');
    }

    /**
     * 一对一关联 创建人信息
     * @return HasOne
     */
    public function createdByInfo(): HasOne
    {
        return $this->hasOne(SystemUser::class,'id','created_by')->field('id,nickname');
    }
}