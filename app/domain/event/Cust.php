<?php

namespace app\domain\event;

use app\domain\service\CustService;
use app\cust\model\CustBalance;

class Cust
{

    /**
     * 用户注册后置事件
     */
    public static function custRegisterAfterListener($data): void
    {
        if(empty($data['cust_uid'])) {
            var_dump('更新用户编号事件参数错误');
            sendNotice('更新用户编号事件参数错误');
            return;
        }
        $cust_uid = $data['cust_uid'];
        // 初始化服务
        $service = new CustService();
        // 更新余额账号
        $service->createBalance($cust_uid);
        // 更新用户编号
        $service->updateCustNo($cust_uid);
    }


    /**
     * 更新用户事件包含新增或修改
     */
    public static function custUpdateAfterListener($data): void
    {
        if(empty($data['cust_uid'])) {
            var_dump('更新用户编号事件参数错误');
            sendNotice('更新用户编号事件参数错误');
            return;
        }
        $cust_uid = $data['cust_uid'];
        // 初始化服务
        $service = new CustService();
        // 更新用户编号
        $service->updateCustNo($cust_uid);
    }

    /**
     * 用户注销后置事件
     */
    public static function custDeleteAfterListener($data): void
    {
        if(empty($data['cust_uid'])) {
            var_dump('更新用户编号事件参数错误');
            sendNotice('更新用户编号事件参数错误');
            return;
        }
        $cust_uid = $data['cust_uid'];
        // 删除用户账户
        $balanceModel = new CustBalance();
        $balanceModel->destroy(['cust_uid' => $cust_uid]);
    }
}