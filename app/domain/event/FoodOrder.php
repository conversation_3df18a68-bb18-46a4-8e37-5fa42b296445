<?php

namespace app\domain\event;

use app\domain\service\FoodOrderService;
use Exception;

class FoodOrder
{
    /**
     * 套餐订单创建后置事件
     */
    public function foodOrderCreateAfterListener($data): void
    {
        // 订单创建
    }

    /**
     * 套餐订单完成后置事件
     */
    public function foodOrderConfirmAfterListener($data): void
    {
        if(empty($data['order_id'])) {
            var_dump('套餐订单确认事件参数错误');
            sendNotice('套餐订单确认事件参数错误');
            return;
        }
        // 订单确认支付
        $foodOrderService = new FoodOrderService();
        $confirmStatus = $foodOrderService->completeOrder($data['order_id']);

        if ($confirmStatus) {
            // 添加订单记录
        }
    }

    /**
     * 补全一周订单
     * 确保一周内每天都有订单记录，方便查看
     * @param $data
     * @return bool 是否成功补全
     * @throws Exception
     */
    public function completeWeeklyOrders($data): bool
    {
        if (empty($data['order_id'])) {
            return false;
        }
        $orderId = $data['order_id'];
        // 初始化Dao
        $dao = new \app\utils\Dao();

        // 查询原始订单
        $orderInfo = $dao->get('order_food', [
            'order_id' => $orderId,
            'delete_time' => null
        ]);

        // 如果订单不存在，直接返回
        if (empty($orderInfo)) {
            return false;
        }

        // 获取订单的配送时间和客户ID
        $deliveryTime = $orderInfo['delivery_time'];
        $custUid = $orderInfo['cust_uid'];

        // 计算本周的开始和结束日期
        $weekStart = date('Y-m-d', strtotime('monday this week', strtotime($deliveryTime)));
        $weekEnd = date('Y-m-d', strtotime('sunday this week', strtotime($deliveryTime)));

        // 查询本周已存在的订单
        $existingOrders = $dao->search('order_food', [
            'cust_uid' => $custUid,
            'delete_time' => null,
            'delivery_time[>=]' => $weekStart,
            'delivery_time[<=]' => $weekEnd
        ], ['delivery_time']);

        // 将已存在的订单日期转为数组
        $existingDates = [];
        foreach ($existingOrders as $order) {
            $existingDates[] = date('Y-m-d', strtotime($order['delivery_time']));
        }

        // 生成本周所有日期
        $allDates = [];
        $currentDate = $weekStart;
        while ($currentDate <= $weekEnd) {
            $allDates[] = $currentDate;
            $currentDate = date('Y-m-d', strtotime('+1 day', strtotime($currentDate)));
        }

        // 找出缺少的日期
        $missingDates = array_diff($allDates, $existingDates);

        // 如果没有缺少的日期，直接返回
        if (empty($missingDates)) {
            return true;
        }
        
        // 检查已存在日期的订单项是否完整
        foreach ($existingDates as $existDate) {
            // 查询当天可用的套餐
            $packages = $dao->search('food_package', [
                'day_time' => $existDate,
                'delete_time' => null
            ]);
            
            if (empty($packages)) {
                continue; // 如果没有套餐，跳过这一天
            }
            
            // 查询当天的订单
            $existOrder = $dao->get('order_food', [
                'cust_uid' => $custUid,
                'delivery_time' => $existDate,
                'delete_time' => null
            ]);
            
            if (empty($existOrder)) {
                continue; // 如果没有订单，跳过这一天
            }
            
            // 查询已存在的订单项
            $existItems = $dao->search('order_food_item', [
                'order_id' => $existOrder['order_id'],
                'delete_time' => null
            ], ['package_id']);
            
            // 提取已存在的套餐ID
            $existPackageIds = array_column($existItems, 'package_id');
            
            // 检查是否有缺少的套餐项
            foreach ($packages as $package) {
                if (!in_array($package['package_id'], $existPackageIds)) {
                    // 创建缺少的订单项
                    $dao->insert('order_food_item', [
                        'order_id' => $existOrder['order_id'],
                        'delivery_time' => $existDate,
                        'delivery_option' => $existOrder['delivery_option'],
                        'delivery_site' => $existOrder['delivery_site'],
                        'cust_uid' => $existOrder['cust_uid'],
                        'employee_uid' => $existOrder['employee_uid'],
                        'num' => 0, // 设置为0，表示没有实际订单
                        'price' => $package['price'],
                        'pay_price' => 0,
                        'package_id' => $package['package_id'],
                        'package_type' => $existOrder['package_type'],
                        'package_name' => $package['name'],
                        'remark' => '系统自动补全',
                        'created_by' => $existOrder['created_by'],
                        'create_time' => date('Y-m-d H:i:s')
                    ]);
                }
            }
        }

        // 开始事务处理
        try {
            $dao->action(function ($database) use ($dao, $orderInfo, $missingDates) {
                foreach ($missingDates as $date) {
                    // 查询当天可用的套餐
                    $packages = $dao->search('food_package', [
                        'day_time' => $date,
                        'delete_time' => null
                    ]);

                    if (empty($packages)) {
                        continue; // 如果没有套餐，跳过这一天
                    }

                    // 创建新订单ID
                    $newOrderId = getId();

                    // 创建新订单记录
                    $newOrder = [
                        'order_id' => $newOrderId,
                        'delivery_option' => $orderInfo['delivery_option'],
                        'delivery_site' => $orderInfo['delivery_site'],
                        'delivery_num' => 0, // 设置为0，表示没有实际订单
                        'total_num' => 0,
                        'delivery_time' => $date,
                        'gov_fee' => 0,
                        'pay_price' => 0,
                        'cust_delivery_fee' => 0,
                        'sum_amount' => 0,
                        'cust_uid' => $orderInfo['cust_uid'],
                        'employee_uid' => $orderInfo['employee_uid'],
                        'package_type' => $orderInfo['package_type'],
                        'discounts_text' => '',
                        'discounts_price' => 0,
                        'remark' => '系统自动补全',
                        'created_by' => $orderInfo['created_by'],
                        'create_time' => date('Y-m-d H:i:s')
                    ];

                    // 插入订单
                    $database->insert('order_food', $newOrder);

                    // 为每个套餐创建订单项
                    foreach ($packages as $package) {
                        // 创建订单项
                        $orderItem = [
                            'order_id' => $newOrderId,
                            'delivery_time' => $date,
                            'delivery_option' => $orderInfo['delivery_option'],
                            'delivery_site' => $orderInfo['delivery_site'],
                            'cust_uid' => $orderInfo['cust_uid'],
                            'employee_uid' => $orderInfo['employee_uid'],
                            'num' => 0, // 设置为0，表示没有实际订单
                            'price' => $package['price'],
                            'pay_price' => 0,
                            'package_id' => $package['package_id'],
                            'package_type' => $orderInfo['package_type'],
                            'package_name' => $package['name'],
                            'remark' => '系统自动补全',
                            'created_by' => $orderInfo['created_by'],
                            'create_time' => date('Y-m-d H:i:s')
                        ];

                        $database->insert('order_food_item', $orderItem);
                    }
                }
            });

            return true;
        } catch (Exception $e) {
            // 记录错误日志
            error_log('补全订单失败: ' . $e->getMessage());
            return false;
        }
    }
}