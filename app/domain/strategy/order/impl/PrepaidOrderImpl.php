<?php

namespace app\domain\strategy\order\impl;

use app\domain\strategy\order\OrderInterface;

/**
 * 预支付订单
 */
class  PrepaidOrderImpl implements OrderInterface
{
    /**
     * 创建预支付订单
     * @param array $params
     * @return string orderId 返回订单Id
     */
    public function createOrder(array $params): string
    {
        return 'order_id';
    }

    /**
     * 更新订单
     * @param string $orderId 订单ID
     * @param array $updateData 需要更新的订单信息，如收货地址、支付方式等
     * @return bool 返回是否更新成功
     */
    public function updateOrder(string $orderId, array $updateData): bool
    {
        return true;
    }

    /**
     * 取消订单
     * @param string $orderId 订单ID
     * @return bool 返回是否取消成功
     */
    public function cancelOrder(string $orderId): bool
    {
        return true;
    }

    /**
     * 支付订单
     * @param string $orderId 订单ID
     * @param string $paymentMethod 支付方式，如支付宝、微信支付等
     * @return bool 返回是否支付成功
     */
    public function payOrder(string $orderId, string $paymentMethod): bool
    {
        return true;
    }

    /**
     * 退款订单申请
     * @param string $orderId 订单ID
     * @param float $refundAmount 退款金额
     * @return bool 返回是否申请退款成功
     */
    public function applyRefundOrder(string $orderId, float $refundAmount): bool
    {
        return true;
    }

    /**
     * 退款订单完成
     * @param string $orderId 订单ID
     * @param float $refundAmount 退款金额
     * @return bool 返回是否退款成功
     */
    public function refundOrder(string $orderId, float $refundAmount): bool
    {
        return true;
    }

    /**
     * 评价订单
     * @param string $orderId 订单ID
     * @param array $params 评价内容
     * @return bool 返回是否评价成功
     */
    public function rateOrder(string $orderId, array $params): bool
    {
        return true;
    }
}