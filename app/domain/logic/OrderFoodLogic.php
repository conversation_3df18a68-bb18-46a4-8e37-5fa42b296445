<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\domain\logic;

use plugin\saiadmin\basic\BaseLogic;
use app\domain\model\OrderFood;

/**
 * 套餐订单逻辑层
 */
class OrderFoodLogic extends BaseLogic
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new OrderFood();
    }

    /**
     * 订单搜索器
     */
    public function search(array $searchWhere = [])
    {
        return $this->model->where('total_num','>',0)->when(isset($searchWhere['delivery_time']) && $searchWhere['delivery_time'] !== '',function ($query) use ($searchWhere) {
            $query->whereBetweenTime('delivery_time', $searchWhere['delivery_time'][0], $searchWhere['delivery_time'][1]);
        })->when(isset($searchWhere['cust_uid']) && $searchWhere['cust_uid'] !== '',function ($query) use ($searchWhere) {
            $query->where('cust_uid', $searchWhere['cust_uid']);
        })->when(isset($searchWhere['delivery_site']) && $searchWhere['delivery_site'] !== '',function ($query) use ($searchWhere) {
            $query->where('delivery_site', $searchWhere['delivery_site']);
        })->when(isset($searchWhere['delivery_option']) && $searchWhere['delivery_option'] !== '',function ($query) use ($searchWhere) {
            $query->where('delivery_option', $searchWhere['delivery_option']);
        })->when(isset($searchWhere['employee_uid']) && $searchWhere['employee_uid'] !== '',function ($query) use ($searchWhere) {
            $query->where('employee_uid', $searchWhere['employee_uid']);
        })->when(isset($searchWhere['order_status']) && $searchWhere['order_status'] !== '',function ($query) use ($searchWhere) {
            $query->where('order_status', $searchWhere['order_status']);
        });
    }
}
