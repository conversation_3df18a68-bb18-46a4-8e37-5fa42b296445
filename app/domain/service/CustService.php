<?php

namespace app\domain\service;

use app\controller\BaseController;
use app\cust\model\CustBalance;
use app\utils\Dao;
use support\Container;

class CustService extends BaseController
{
    /**
     * 创建用户余额账号
     */
    public function createBalance($cust_uid): void
    {
        // 检查账户是否创建
        $balanceModel = new CustBalance();
        $balanceInfo = $balanceModel->where('cust_uid',$cust_uid)->findOrEmpty()->toArray();
        if(empty($balanceInfo)) {
            // 创建账户
            $balanceModel->insert([
                'balance_id' => $cust_uid,
                'balance_name' => '余额账户',
                'cust_uid' => $cust_uid,
                'now_amount' => 0,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    /**
     * 更新用户编号
     */
    public function updateCustNo($cust_uid): void
    {
        if(empty($cust_uid)) {
            return;
        }
        $dao = Container::get(Dao::class);
        // 获取用户信息
        $custInfo = $dao->get('cust_user', [
            'cust_uid' => $cust_uid,
            'delete_time' => null
        ],[
            'cust_is_bed',
            'cust_service_site',
            'cust_internal_number',
        ]);
        if (empty($custInfo)) {
            return;
        }
        
        // 第一步：获取编号前缀
        $prefix = null;
        $isBed = $custInfo['cust_is_bed'] ?? 1;
        
        switch ($isBed) {
            case 1:
                $prefix = 'SCH';
                break;
                
            case 2:
                $serviceSite = $custInfo['cust_service_site'] ?? 0;
                
                if ($serviceSite == 3) {
                    $prefix = 'KYYZ';
                } elseif ($serviceSite == 4) {
                    $prefix = 'KXYZ';
                } else {
                    // 其他值终止代码
                    $prefix = 'KYYZ';
                }
                break;
                
            case 3:
                $prefix = 'JBFW';
                break;
                
            case 4:
                $prefix = 'XZFW';

                // 终止代码
                break;
                
            default:
                // 无效的用户类型
                return;
        }
        // 检查前缀是否包含
        if (!empty($custInfo['cust_internal_number']) && str_contains($custInfo['cust_internal_number'],$prefix)) {
            return;
        }
        // 第二步：获取最大号码
        $maxNumberInfo = $dao->get('cust_max_number', [
            'label' => $prefix,
            'delete_time' => null
        ]);
            
        $maxNumber = 1;
        if (empty($maxNumberInfo)) {
            // 如果没有记录，创建一条新记录
            $dao->insert('cust_max_number', [
                'label' => $prefix,
                'max_number' => $maxNumber,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ]);
        } else {
            $maxNumber = $maxNumberInfo['max_number'] + 1;
        }
        // 生成新的内部编号
        $internalNumber = $prefix .'-'. $maxNumber;
        
        // 第三步：更新用户编号
        $dao->update('cust_user', [
            'cust_internal_number' => $internalNumber,
            'update_time' => date('Y-m-d H:i:s')
        ], [
            'cust_uid' => $cust_uid
        ]);
        
        // 第四步：更新最大号码
        $dao->update('cust_max_number', [
            'max_number' => $maxNumber,
            'update_time' => date('Y-m-d H:i:s')
        ], [
            'label' => $prefix
        ]);
    }
}