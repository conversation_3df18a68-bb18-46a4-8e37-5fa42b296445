<?php

namespace app\domain\service;

use app\domain\model\Order;
use app\domain\stateMachine\OrderStateMachine;
use plugin\saiadmin\exception\ApiException;
use SM\SMException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class OrderMainService
{
    /**
     * @return array
     * @throws SMException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function query(): array
    {

        $order =  Order::find(1); // 从数据库中获取订单
        $stateMachine = OrderStateMachine::create($order);
        // 检查是否可以应用 'create' 转换，会执行守卫回调
        if($stateMachine->can('cancel') === false){
            throw new ApiException('创建订单检查未通过');
        }
        try {
            $stateMachine->apply('cancel');
        } catch (SMException $e) {
            throw new ApiException('订单状态错误'.$e->getMessage());
        }
        return ['state' => $order->setState($order->getState())];
    }
}