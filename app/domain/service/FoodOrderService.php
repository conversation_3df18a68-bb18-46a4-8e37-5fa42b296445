<?php

namespace app\domain\service;

use app\controller\BaseController;
use app\domain\model\OrderFoodItem;
use Exception;
use app\utils\Dao;
use support\Container;
use plugin\saiadmin\utils\Cache;

class FoodOrderService extends BaseController
{
    /**
     *自动完成订单
     * @return string
     */
    public function run(): string
    {
        $dao = Container::get(Dao::class);
        // 查询当天未完成订单
        $nowTime = date('Y-m-d');
        $orderIdArr = $dao->search('order_food',[
            'delivery_time' => $nowTime,
            'order_status' => 0,
            'delete_time' => null,
            'total_num[>]' => 0,
        ], 'order_id');
        $rows = count($orderIdArr);
        foreach ($orderIdArr as $v) {
            self::completeOrder($v);
        }
        return '运行成功，共完成'.$rows.'条订单';
    }

    /**
     * 计算套餐金额变化
     */
    public function calculateDeliveryFee(int $orderId): array
    {
        $itemModel = new OrderFoodItem();
        $dao = Container::get(Dao::class);
        $foodOrderItemList = $itemModel
            ->where('order_id',"$orderId")
            ->where('num','>',0)
            ->field('order_id,price,pay_price,num,package_name,cust_uid,delivery_option,package_type,package_id,delivery_option')
            ->select()
            ->toArray();
        $foodOrderInfo = $dao->get('order_food',[
            'order_id' => "$orderId",
        ],[
            'discounts_text',
            'discounts_price',
            'package_type'
        ]);
        if(!empty($foodOrderItemList)) {
            // 计数数组
            $foodPackageArr = [
                'A1',
                'A2',
                'B1',
                'B2',
                '零1',
                '零2',
            ];
            $foodV1= 0;
            $foodV2 = 0;
            $deliveryNum = 0;
            $totalNum = 0;
            $payPrice = 0;
            $custUid = 0;
            $govFee = 0;
            $productStatus = false;
            $productPrice = 0;
            $discounts_price = 0;
            foreach ($foodOrderItemList as $v) {
                $custUid = $v['cust_uid'];
                $totalNum += $v['num'];
                $payPrice += round($v['price'] * $v['num'],2);
                if($v['delivery_option'] == '送餐') {
                    if(in_array($v['package_name'],$foodPackageArr)) {
                        $foodV1 += $v['num'];
                    } else {
                        $foodV2 += $v['num'];
                    }
                    if($foodV1 > 0) {
                        $deliveryNum = $foodV1;
                    } else {
                        if($foodV2 > 0) {
                            $deliveryNum = 1;
                        }
                    }
                }
                // 判断是否有特定商品
                if(str_contains($v['package_name'],'主食专供') && $v['num'] > 0 && $v['delivery_option'] == '送餐') {
                    // 查询商品名称
                    $status = $dao->has('food_package_item',[
                        'package_id' => $v['package_id'],
                        'food_id' => [125,126],
                        'delete_time' => null,
                    ]);
                    if($status) {
                        $productStatus = true;
                        $productPrice += 20 * $v['num'];
                    }
                }
                // 判断是否有员工餐优惠
                if(!str_contains($v['package_name'],'主食专供') && $foodOrderInfo['package_type'] == '工作餐' && $v['num'] > 0) {
                    $discounts_price += 5 * $v['num'];
                    $payPrice -= 5 * $v['num'];
                }
                // 判断结束
            }
            $deliveryFee = $deliveryNum < 1 ? 0 : 5 + ($deliveryNum - 1) * 3;
            // 折扣金额
            $price = 0;
            // 判断是否存在优惠规则
            if(!empty($foodOrderInfo['discounts_text']) && $foodOrderInfo['discounts_text'] == '华严北里9-25楼送餐优惠') {
                $price = $deliveryNum < 1 ? 0 : 3 + ($deliveryNum - 1) * 2;
            }
            if($price > 0) {
                $discounts_price = $deliveryFee - $price;
                $deliveryFee = $price;
            }
            // 判断结束

            // 判断是否存在特殊商品,存在则运费更改为20
            if($productStatus) {
                $deliveryFee = $productPrice;
                $discounts_price = 0; // 无优惠
            }
            // 判断结束

            $payPrice = $payPrice + $deliveryFee; // 费用加上运费
            // 政府补贴计算
            $custInfo = $dao->get('cust_user',[
                'cust_uid' => $custUid,
            ],[
                'cust_identity_type',
                'cust_evaluation_level',
                'cust_is_bed'
            ]);
            if(!empty($custInfo)) {
                if((in_array($custInfo['cust_identity_type'],[2,3,4]) || $custInfo['cust_evaluation_level'] == 4 || $custInfo['cust_is_bed'] == 3) && $payPrice > 5 && $foodOrderInfo['package_type'] != '照料餐') {
                    $govFee = 5;
                    $payPrice = $payPrice - $govFee;
                }
            }
            return [
                'delivery_num' => $deliveryNum,
                'cust_delivery_fee' => $deliveryFee,
                'total_num' => $totalNum,
                'pay_price' => $payPrice,
                'gov_fee' => $govFee,
                'discounts_price' => $discounts_price,
                'sum_amount' => $payPrice + $govFee + $discounts_price,
            ];
        }
        // 非配送订单0元
        return [
            'delivery_num' => 0,
            'cust_delivery_fee' => 0,
            'total_num' => 0,
            'pay_price' => 0,
            'gov_fee' => 0,
            'sum_amount' => 0
        ];
    }

    /**
     * 套餐订单完成
     * @param int $orderId
     * @return bool
     */
    public function completeOrder(int $orderId): bool
    {
        // 使用缓存锁避免并发执行
        $lockKey = "order_complete_lock_{$orderId}";
        if(Cache::get($lockKey)) {
            // 如果获取不到锁,说明有其他进程正在处理
            return false;
        } else {
            // 加锁
            Cache::set($lockKey,1,30);
        }
        try {
            // 查询订单支付信息
            $dao = new Dao();
            $foodOrderInfo = $dao->get('order_food',[
                'order_id' => "$orderId",
                'delete_time' => null,
                'total_num[>]' => 0,
            ]);
            if(empty($foodOrderInfo) || $foodOrderInfo['order_status'] != 0) {
                return false;
            }
            // 查询订单金额
            $orderNewInfo = self::calculateDeliveryFee("$orderId");
            if($orderNewInfo['pay_price'] != $foodOrderInfo['pay_price']) {
                var_dump('异常订单ID'.$orderId);
                // 订单金额异常
                $dao->update('order_food',$orderNewInfo,[
                    'order_id' => "$orderId",
                ]);
                // 添加异常记录
                $dao->insert('zk_order_log',[
                    'order_id' => "$orderId",
                    'old_price' => $foodOrderInfo['pay_price'],
                    'new_price' => $orderNewInfo['pay_price'],
                    'create_time' => date('Y-m-d H:i:s'),
                ]);
            }
            $database = $dao;
            // 查询账户Id
            $balanceInfo = $database->get('cust_balance',[
                'cust_uid' => $foodOrderInfo['cust_uid'],
                'balance_name' => '余额账户',
                'delete_time' => null,
            ]);
            // 查询订单详细信息
            $foodOrderItemList = $database->search('order_food_item',[
                'order_id' => "$orderId",
                'num[>]' => 0,
            ],[
                'num',
                'package_name',
            ]);
            // 获取账单明细
            $recordDetailsCategory = '';
            if(!empty($foodOrderItemList)) {
                foreach ($foodOrderItemList as $v) {
                    $recordDetailsCategory .= $v['package_name'] . '*' .$v['num'] .' ';
                }
            }
            if(!empty($balanceInfo)) {
                try {
                    $database->action(function ($database) use ($foodOrderInfo,$balanceInfo,$recordDetailsCategory) {
                        $subsidyId = 0;
                        if($foodOrderInfo['gov_fee'] > 0) {
                            $subsidyId = 1;
                        }
                        // 添加支付记录
                        $database->insert('cust_balance_record',[
                            'balance_id' => $balanceInfo['balance_id'],
                            'cust_uid' => $foodOrderInfo['cust_uid'],
                            'link_id' => $foodOrderInfo['order_id'],
                            'pm' => 0,
                            'usage_date' => $foodOrderInfo['delivery_time'],
                            'record_title' => '801营养餐 '.$recordDetailsCategory,
                            'record_details_category' => $recordDetailsCategory,
                            'subsidy_id' => $subsidyId,
                            'subsidy_money' => $foodOrderInfo['gov_fee'],
                            'number' => $foodOrderInfo['pay_price'],
                            'forward' => $balanceInfo['now_amount'],
                            'balance' => $balanceInfo['now_amount'] - $foodOrderInfo['pay_price'],
                            'record_status' => 1,
                            'create_time' => date('Y-m-d H:i:s'),
                        ]);
                        // 修改订单状态
                        $database->update('order_food',[
                            'order_status' => 1,
                            'update_time' => date('Y-m-d H:i:s'),
                        ],[
                            'order_id' => $foodOrderInfo['order_id'],
                        ]);
                        // 修改账单累计支出
                        $database->update('cust_balance',[
                            'spending_amount' => $balanceInfo['spending_amount'] + $foodOrderInfo['pay_price'],
                            'now_amount' =>  $balanceInfo['now_amount'] - $foodOrderInfo['pay_price'],
                        ],[
                            'balance_id' => $balanceInfo['balance_id'],
                        ]);
                    });
                } catch (Exception $e) {
                    var_dump($e->getMessage());
                    return false;
                }
                return true;
            }
            var_dump('未找到余额账户,用户id:'.$foodOrderInfo['cust_uid']);
        } finally {
            // 释放锁
            Cache::clear($lockKey);
        }
        return false;
    }
}