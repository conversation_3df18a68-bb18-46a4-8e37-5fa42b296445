<?php

namespace app\domain\service;

use app\utils\Dao;
use support\Container;
use yzh52521\EasyHttp\Http;


class SmartService
{
    protected Dao $dao;

    public function __construct()
    {
        $this->dao = new Dao();
    }

    /**
     *自动完成订单
     * @return string
     */
    public function run(): string
    {
        $dao = Container::get(Dao::class);
        // 查询更新设备
        $deviceInfo = $dao->search('smart_water',[
            "ORDER" => ["update_time" => "ASC"],
            "LIMIT" => 10
        ]);
        foreach ($deviceInfo as $v) {
            // 查询第三方设备数据
            $thirdData = $dao->get('smart_water_event',[
                'device_id' => $v['sn'],
                'event_type' => 'EVENT_WATER_DEVICE_REPORT',
                "ORDER" => ["create_time" => "DESC"],
            ],[
                'last_active_time',
                'csq',
                'online'
            ]);
            if(!empty($thirdData)) {
                // 判断在线状态,如果当前时间大于最后活跃时间120分钟则为离线
                if(strtotime($thirdData['last_active_time']) + 7200 < time()) {
                    $thirdData['online'] = 0;
                }
                $dao->update('smart_water',[
                    'last_active_time' => $thirdData['last_active_time'],
                    'csq' => $thirdData['csq'],
                    'online' => $thirdData['online'],
                    'update_time' => date('Y-m-d H:i:s'),
                ],[
                    'sn' => $v['sn']
                ]);
            }
        }
        return '运行成功，共完成'. count($deviceInfo) .'条设备数据更新';
    }


    /**
     * 解析设备实时推送信息
     */
    public function parseDeviceData($data): void
    {
        if(!empty($data['pkey'])) {
            var_dump('推送时间:'.date('Y-m-d H:i:s').'|设备pkey:'.$data['pkey']);
        } else {
            var_dump($data);
        }
        // 查询设备是否创建
        if(!empty($data['deviceId'])) {
            $deviceInfo = $this->dao->has('smart_water',[
                'sn' => $data['deviceId'],
            ]);
            if(!$deviceInfo) {
                // 创建设备
                $this->dao->insert('smart_water',[
                    'sn' => $data['deviceId'],
                    'pkey' => $data['pkey'],
                    'third_user_id' => $data['userId'],
                    'create_time' => date('Y-m-d H:i:s'),
                ]);
            }
        }
        // EVENT_WATER_DEVICE_REPORT 水记录仪设备数据变化事件
        if($data['eventType'] == 'EVENT_WATER_DEVICE_REPORT') {
            // 添加事件
            $this->dao->insert('smart_water_event',[
                'event_type' => 'EVENT_WATER_DEVICE_REPORT',
                'device_id' =>  $data['deviceId'],
                'event_desc' =>  $data['eventDesc'],
                'last_active_time' => date('Y-m-d H:i:s',$data['lastActiveTime'] / 1000),
                'last_start_time' => date('Y-m-d H:i:s',$data['lastStartTime'] / 1000),
                'last_end_time' => date('Y-m-d H:i:s',$data['lastEndTime'] / 1000),
                'last_mun' => $data['lastMun'],
                'csq' => $data['csq'],
                'online' => $data['online'],
                'create_time' => date('Y-m-d H:i:s'),
            ]);
        }
        // EVENT_WATER_DEVICE_DATA_REPORT 水记录仪用水数据变化事件
        if($data['eventType'] == 'EVENT_WATER_DEVICE_DATA_REPORT') {
            // 添加事件
            $this->dao->insert('smart_water_event',[
                    'event_type' => 'EVENT_WATER_DEVICE_DATA_REPORT',
                    'device_id' =>  $data['deviceId'],
                    'event_desc' =>  $data['eventDesc'],
                    'water' => $data['water'],
                    'ts' => date('Y-m-d H:i:s',$data['ts'] / 1000),
                    'create_time' => date('Y-m-d H:i:s'),
                ]
            );
        }
        // ALARM_EVENT 水记录仪报警
        if($data['eventType'] == 'ALARM_EVENT') {
            // 查询设备deviceId
            $deviceId = $this->dao->get('smart_water',[
                'third_user_id' => $data['userId']
            ],'sn');
            // 添加事件
            $this->dao->insert('smart_water_event',[
                    'event_type' => 'ALARM_EVENT',
                    'device_id' =>  $deviceId,
                    'event_desc' =>  $data['eventDesc'],
                    'alarm_head_id' => $data['alarmHeadId'],
                    'alarm_time' => $data['alarmTime'],
                    'alarm_type' => $data['alarmType'],
                    'alarm_content' => $data['alarmContent'],
                    'alarm_address' => !empty($data['alarmAddress']) ?? '未录入地址',
                    'create_time' => date('Y-m-d H:i:s'),
                ]
            );
        }
        // 删除超过7天的用水数据(不包含水记录仪报警)
        $this->dao->del('smart_water_event',[
            'event_type[<>]' => 'ALARM_EVENT',
            'create_time[<]' => date('Y-m-d H:i:s',strtotime('-7 day')),
        ]);
    }


    /**
     * 第三方服务接口请求
     */
    public function waterSend($url, $params = [], $type = 'get'): array|null
    {
        $header = [
            'TOKEN' => 'VdDnHGmBX0DcwYIEmjGjs8FGvDcf7Rj0DGoPZHEa67M7OtgRgpjxOg',
        ];
        $url = 'http://************:8550/' . $url;
        return match ($type) {
            'post' => Http::withHeaders($header)->post($url, $params)->array(),
            'put' => Http::withHeaders($header)->put($url, $params)->array(),
            'patch' => Http::withHeaders($header)->patch($url, $params)->array(),
            'delete' => Http::withHeaders($header)->delete($url, $params)->array(),
            default => Http::withHeaders($header)->get($url, $params)->array(),
        };
    }

    /**
     * 更新设备数据
     */
    public function updateDeviceData(): void
    {
        // 查询更新设备
        $deviceInfo = $this->dao->search('smart_water',[
            "ORDER" => ["update_time" => "ASC"],
            "LIMIT" => 10
        ]);
        foreach ($deviceInfo as $v) {
            // 查询第三方设备数据
            $thirdData = $this->dao->get('smart_water_event',[
                'device_id' => $v['sn'],
                'event_type' => 'EVENT_WATER_DEVICE_REPORT',
                "ORDER" => ["create_time" => "DESC"],
            ],[
                'last_active_time',
                'csq',
                'online'
            ]);
            if(!empty($thirdData)) {
                // 判断在线状态,如果当前时间大于最后活跃时间120分钟则为离线
                if(strtotime($thirdData['last_active_time']) + 7200 < time()) {
                    $thirdData['online'] = 0;
                }
                $this->dao->update('smart_water',[
                    'last_active_time' => $thirdData['last_active_time'],
                    'csq' => $thirdData['csq'],
                    'online' => $thirdData['online'],
                    'update_time' => date('Y-m-d H:i:s'),
                ],[
                    'sn' => $v['sn']
                ]);
            }
        }
    }
}