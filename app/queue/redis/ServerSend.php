<?php

namespace app\queue\redis;

use Exception;
use support\Log;
use Webman\RedisQueue\Consumer;

class ServerSend implements Consumer
{
    /**
     * Server酱 消息发送
     *
     * @var string
     */
    public string $queue = 'send_server';

    /**
     * 连接名
     *
     * @var string
     */
    public string $connection = 'default';

    /**
     * 消费
     * @param array $data
     * @return void
     */
    public function consume($data): void
    {
        $this->sc_send($data['title'], $data['des']);
    }

    /**
     * 消费
     * @param $text
     * @param string $des
     * @return void
     */
    protected function sc_send($text, string $des = ''): void
    {
        try {
            // Server酱
            $this->send_to_zksh($text, $des);
        } catch (Exception $e) {
            Log::channel('error')->info($e->getMessage());
        }
    }

    protected function send_to_zksh($text, string $des = ''): void
    {
        $params = [
            "touser"=>'ow-9xxNhy14ah2FBJUhZeXqCiJRA',
            "template_id"=>'4tUvGtTVHNsXOt3PaFlUzRi-p0LkIOTf2c7ZT7NGiCw',
            "url"=>'https://img.zkshlm.com/zksh/error.html?title='.$text.'&content='.$des,
            "data"=>[
                "thing2"=>[
                    "value"=>'服务异常',
                ],
                "thing5"=>[
                    "value"=>'养老管理系统V2',
                ],
                "time3"=>[
                    "value"=>date('Y-m-d H:i:s'),
                ],
            ],
        ];
        $code = 'dsd45d4a6d78w7d8ad749a7d7af4a4ga54f6as4dsa4g41a51sd2adw4d8s8cx4512c31g45';
        httpPost('https://api.card.zkshlm.com/notice/reserveSend',[
            'data'=>$params,
            'code'=>$code
        ]);
    }
}