<?php

namespace app\queue\redis;

use app\utils\Operate;
use Webman\RedisQueue\Consumer;

class OperateLogSend implements Consumer
{
    /**
     * Server酱 消息发送
     *
     * @var string
     */
    public string $queue = 'operate_log_server';

    /**
     * 连接名
     *
     * @var string
     */
    public string $connection = 'default';

    /**
     * 消费
     * @param array $data
     * @return void
     */
    public function consume($data): void
    {
        try {
            Operate::OperateLog($data);
        } catch (\Throwable $e) {
            eventExceptionNotice($e);
        }
    }
}