<?php
namespace app\middleware;

use plugin\saiadmin\exception\ApiException;
use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use Webman\RedisQueue\Redis;

class ActionHook implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        $startTime = microtime(true);
        $rule = trim(strtolower($request->path()));
        $whiteList = config('plugin.saiadmin.saithink.white_list', []);
        // 白名单返回
        if (in_array($rule, $whiteList)) {
            return $handler($request);
        }
        // 文档访问返回
        if(str_starts_with($request->controller, "\hg\apidoc\Controller")) {
            return $handler($request);
        }
        // 获取用户信息
        $userInfo = getCurrentInfo();
        if (!$userInfo) {
            throw new ApiException('用户信息读取失败,请重新登录',401);
        }
        if($userInfo['type'] == 'H5') {
            $request->wxUid= $userInfo['id'];
        } else {
            $request->adminId = $userInfo['id'];
            $request->adminName = $userInfo['username'];
        }
        // 获取返回请求
        $response = $handler($request);
        // 获取执行时间 以ms 为单位
        $finishTime = microtime(true);
        $costTime   = round($finishTime - $startTime,6) * 1000;
        // 获取响应码
        $code    = $response->getStatusCode();
        $code    = is_int($code) ? $code : 500;
        // 获取响应body
        $body = match (true) {
            $code != 200 => json_decode($response->rawBody(), true),
            default => [
                'code' => 200,
                'message' => 'success',
            ]
        };
        // 记录日志
        Redis::send('operate_log_server', [
            'username' => $userInfo['username'],
            'method' => $request->method(),
            'router' => trim(strtolower($request->uri())),
            'service_name' => $request->path(),
            'app' => !empty($request->plugin) ? $request->plugin : $request->controller,
            'ip' => $request->getRealIp(),
            'ip_location' => $request->getRealIp(),
            'request_data' => $request->all(),
            'operation_time' => number_format($costTime, 2),
            'operation_code'   => $response->getStatusCode() ?? '',
            'operation_body'   => json_encode($body),
            'create_time' => date('Y-m-d H:i:s'),
            'update_time' => date('Y-m-d H:i:s'),
            'created_by' => $userInfo['id'],
            'updated_by' => $userInfo['id'],
        ]);
        return $response;
    }
}