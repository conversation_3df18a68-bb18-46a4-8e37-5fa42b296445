<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\company\validate;

use think\Validate;

/**
 * 机构管理验证器
 */
class CompanyDepartValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'company_name' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'company_name' => '机构名称必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'company_name',
        ],
        'update' => [
            'company_name',
        ],
    ];

}
