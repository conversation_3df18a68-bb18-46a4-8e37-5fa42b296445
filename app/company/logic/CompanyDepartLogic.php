<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\company\logic;

use plugin\saiadmin\basic\BaseLogic;
use plugin\saiadmin\exception\ApiException;
use plugin\saiadmin\utils\Helper;
use app\company\model\CompanyDepart;

/**
 * 机构管理逻辑层
 */
class CompanyDepartLogic extends BaseLogic
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->model = new CompanyDepart();
    }

    /**
     * 数据修改
     */
    public function update($data, $where)
    {
        if (!isset($data['company_parent_id'])) {
            $data['company_parent_id'] = 0;
        }
        if ($data['company_parent_id'] == $where['company_cid']) {
            throw new ApiException('不能设置父级为自身');
        }
        return $this->model->update($data, $where);
    }

    /**
     * 数据删除
     */
    public function destroy($ids, $force = false)
    {
        $num = $this->model->where('company_parent_id', 'in', $ids)->count();
        if ($num > 0) {
            throw new ApiException('该分类下存在子分类，请先删除子分类');
        } else {
            return $this->model->destroy($ids, $force);
        }
    }

    /**
     * 树形数据
     */
    public function tree($where)
    {
        $query = $this->search($where);
        if (request()->input('tree', 'false') === 'true') {
            $query->field('company_cid, company_cid as value, company_name as label, company_parent_id');
        }
        $data = $this->getAll($query);
        return Helper::makeTree($data,'children','company_cid','company_parent_id');
    }

}
