<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\company\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 机构管理模型
 */
class CompanyDepart extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'company_cid';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'company_depart';

    
    /**
     * 机构名称 搜索
     */
    public function searchCompanyNameAttr($query, $value)
    {
        $query->where('company_name', 'like', '%'.$value.'%');
    }


}
