<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\revenue\validate;

use think\Validate;

/**
 * 收入统计验证器
 */
class CustBillValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'cust_uid' => 'require',
        'order_type' => 'require',
        'service_type' => 'require',
        'payment_type' => 'require',
        'amount_paid' => 'require',
        'payment_method' => 'require',
        'payment_time' => 'require',
        'payment_status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'cust_uid' => '客户必须填写',
        'order_type' => '订单类型必须填写',
        'service_type' => '缴费服务项目必须填写',
        'payment_type' => '缴费收入类型必须填写',
        'amount_paid' => '缴费金额必须填写',
        'payment_method' => '缴费方式必须填写',
        'payment_time' => '缴费时间必须填写',
        'payment_status' => '缴费状态必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'payment_voucher_number',
            'service_type',
            'payment_type',
        ],
        'update' => [
            'payment_voucher_number',
            'service_type',
            'payment_type',
        ],
        'updateStatus' => [
            'payment_status',
        ]
    ];

}
