<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\revenue\model;

use plugin\saiadmin\basic\BaseModel;
use think\model\relation\HasOne;
use app\cust\model\CustUser;
use plugin\saiadmin\app\model\system\SystemUser;
/**
 * 收入统计模型
 */
class CustBill extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'payment_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_bill';

    
    /**
     * 缴费时间 搜索
     */
    public function searchPaymentTimeAttr($query, $value)
    {
        $query->whereTime('payment_time', 'between', $value);
    }

    /**
     * 查询图片 搜索
     */
    public function searchPaymentVoucherImageAttr($query, $value)
    {
        if($value == 'true') {
            $query->whereNotNull('payment_voucher_image');
        } else {
            $query->whereNull('payment_voucher_image');
        }
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function custInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_live_address,cust_identity_type,cust_evaluation_level,cust_is_bed,cust_street');
    }

    /**
     * 一对一关联 创建人信息
     * @return HasOne
     */
    public function createdByInfo(): HasOne
    {
        return $this->hasOne(SystemUser::class,'id','created_by')->field('id,nickname');
    }
}
