<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\revenue\controller;

use plugin\saiadmin\basic\BaseController;
use app\revenue\logic\CustBillLogic;
use app\revenue\validate\CustBillValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;

/**
 * @Apidoc\Title("收入统计")
 */
class CustBillController extends BaseController
{
    protected string $pk = "payment_id";


    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new CustBillLogic();
        $this->validate = new CustBillValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/revenue/CustBill/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户id", default="")
     * @Apidoc\Query("payment_voucher_number", type="varchar", require=false, desc="缴费凭证单号", default="")
     * @Apidoc\Query("service_type", type="varchar", require=false, desc="服务项目", default="")
     * @Apidoc\Query("payment_type", type="varchar", require=false, desc="收入类型", default="")
     * @Apidoc\Query("payment_method", type="varchar", require=false, desc="缴费方式", default="")
     * @Apidoc\Query("payment_time", type="datetime", require=false, desc="缴费时间", default="")
     * @Apidoc\Query("payment_status", type="varchar", require=false, desc="缴费状态", default="")
     * @Apidoc\Query("cust_street", type="varchar", require=false, desc="街道名称", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['cust_uid', ''],
            ['service_type', ''],
            ['payment_type', ''],
            ['payment_method', ''],
            ['payment_time', ''],
            ['payment_status', ''],
            ['cust_street', ''],
            ['created_by', ''],
            ['payment_voucher_image', ''],
        ]);
        // 拼接查询条件
        $hasWhereStatus = false;
        $hasWhere = [];
        if($where['cust_street']){
            $hasWhereStatus = true;
            $hasWhere = ['cust_street' => $where['cust_street']];
            unset($where['cust_street']);
        }
        if(!empty($where['cust_uid'])) {
            $where['cust_bill.cust_uid'] = $where['cust_uid'];
            unset($where['cust_uid']);
        }
        if(!empty($where['created_by'])) {
            $where['cust_bill.created_by'] = $where['created_by'];
            unset($where['created_by']);
        }
        $query = $this->logic->search($where)->with('custInfo')->when($hasWhereStatus, function ($query) use ($hasWhere) {
            return $query->hasWhere('custInfo', $hasWhere);
        });
        $data = $this->logic->getList($query);
        // 添加用户街道名称
        if (!empty($data['data'])) {
            foreach ($data['data'] as &$v) {
                // 返回街道名称
                $v['cust_street']  = $this->dao->get('cust_user',[ 'cust_uid' =>  $v['cust_uid'] ],'cust_street');
            }
        }
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/revenue/CustBill/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户id", default="")
     * @Apidoc\Query("payment_voucher_number", type="varchar", require=false, desc="缴费凭证单号", default="")
     * @Apidoc\Query("payment_voucher_image", type="varchar", require=false, desc="缴费凭证图片", default="")
     * @Apidoc\Query("service_type", type="varchar", require=false, desc="服务项目", default="")
     * @Apidoc\Query("payment_type", type="varchar", require=false, desc="收入类型", default="")
     * @Apidoc\Query("payment_remark", type="varchar", require=false, desc="缴费备注", default="")
     * @Apidoc\Query("amount_due", type="decimal", require=false, desc="应缴费金额", default="")
     * @Apidoc\Query("amount_paid", type="decimal", require=false, desc="实际缴费金额", default="")
     * @Apidoc\Query("payment_method", type="varchar", require=false, desc="缴费方式", default="")
     * @Apidoc\Query("payment_time", type="datetime", require=false, desc="缴费时间", default="")
     * @Apidoc\Query("payment_status", type="varchar", require=false, desc="缴费状态", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/revenue/CustBill/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户id", default="")
     * @Apidoc\Query("payment_voucher_number", type="varchar", require=false, desc="缴费凭证单号", default="")
     * @Apidoc\Query("payment_voucher_image", type="varchar", require=false, desc="缴费凭证图片", default="")
     * @Apidoc\Query("service_type", type="varchar", require=false, desc="服务项目", default="")
     * @Apidoc\Query("payment_type", type="varchar", require=false, desc="收入类型", default="")
     * @Apidoc\Query("payment_remark", type="varchar", require=false, desc="缴费备注", default="")
     * @Apidoc\Query("amount_due", type="decimal", require=false, desc="应缴费金额", default="")
     * @Apidoc\Query("amount_paid", type="decimal", require=false, desc="实际缴费金额", default="")
     * @Apidoc\Query("payment_method", type="varchar", require=false, desc="缴费方式", default="")
     * @Apidoc\Query("payment_time", type="datetime", require=false, desc="缴费时间", default="")
     * @Apidoc\Query("payment_status", type="varchar", require=false, desc="缴费状态", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/revenue/CustBill/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            // 添加用户数据
            $data['custInfo'] = $this->dao->get('cust_user',[ 'cust_uid' => $data['cust_uid'], ],[ 'cust_uid', 'cust_name' ]);

            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/revenue/CustBill/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/revenue/CustBill/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/revenue/CustBill/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/revenue/CustBill/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/revenue/CustBill/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

}
