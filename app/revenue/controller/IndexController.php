<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\revenue\controller;

use app\cust\model\CustUser;
use plugin\saiadmin\basic\BaseController;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use app\revenue\logic\CustBillLogic;
use app\revenue\model\CustBill;
use app\revenue\model\CustBillLog;
use app\cust\model\CustBalanceRecord;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use app\utils\Dao;
use app\utils\Excel;
use app\cust\model\CustBalance;
use plugin\saiadmin\app\logic\system\SystemDictDataLogic;
use think\facade\Db;
use Exception;
use yzh52521\WebmanLock\Locker;

/**
 * @Apidoc\Title("收入管理")
 */
class IndexController extends BaseController
{
    /**
     * 导出缴费记录列表接口
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException|BadRequestHttpException
     */
    public function exportRevenueList(Request $request): Response
    {
        $where = $request->more([
            ['cust_uid', ''],
            ['service_type', ''],
            ['payment_type', ''],
            ['payment_method', ''],
            ['payment_time', ''],
            ['payment_status', ''],
            ['cust_street', ''],
            ['created_by', ''],
        ]);
        // 拼接查询条件
        $hasWhereStatus = false;
        $hasWhere = [];
        if($where['cust_street']){
            $hasWhereStatus = true;
            $hasWhere = ['cust_street' => $where['cust_street']];
        }
        if(!empty($where['created_by'])) {
            $where['cust_bill.created_by'] = $where['created_by'];
        }
        unset($where['created_by']);
        unset($where['cust_street']);
        // 查询缴费数据
        $billModel = new CustBillLogic();
        $query = $billModel->search($where)->with([
            'custInfo',
            'createdByInfo'
        ])->when($hasWhereStatus, function ($query) use ($hasWhere) {
            return $query->hasWhere('custInfo', $hasWhere);
        });
        $res = $billModel->getAllWithAuth($query);
        if(empty($res)) {
            throw new BadRequestHttpException('缴费记录不存在');
        }
        // 获取支付方式
        $logic = new SystemDictDataLogic();
        $paymentArr = $logic->where('status', 1)->where('code', 'payment_method')->order('sort','desc')->column('value');
        $paymentArr[] = '微信小程序支付';
        foreach ($paymentArr as $v) {
            $totalAmount[$v] = 0;
        }
        $i = 1;
        $data = [];
        foreach ($res as $v) {
            $value = [];
            if(empty($v['custInfo'])) {
                $v['custInfo'] = [
                    'cust_is_bed'=>'非家床',
                    'cust_name'=>'用户已删除',
                    'cust_street'=>'用户已删除',
                ];
            } else {
                // 查询家床字典
                $v['custInfo']['cust_is_bed'] = $logic->where('status', 1)->where('code', 'cust_is_bed')->where('value', $v['custInfo']['cust_is_bed'])->value('label');
            }
            // 序号
            $value['number'] = $i;
            $i++;
            $value['time'] = $v['payment_time'];
            $value['cust_street'] = $v['custInfo']['cust_street'];
            $value['payment_type'] = $v['payment_type'];
            $value['cust_is_bed'] = $v['custInfo']['cust_is_bed'];
            $value['service_type'] = $v['service_type'];
            $value['cust_name'] = $v['custInfo']['cust_name'];
            $value['createdName'] = !empty($v['createdByInfo']) && !empty($v['createdByInfo']['nickname']) ? $v['createdByInfo']['nickname'] : '用户暂未录入';
            foreach ($paymentArr as $vv) {
                if($vv == $v['payment_method']) {
                    $value[$vv] = $v['amount_paid'];
                    $totalAmount[$vv]+=$v['amount_paid'];
                } else {
                    $value[$vv] = '';
                }
            }
            $value['payment_status'] = $v['payment_status'];
            $value['payment_remark'] = $v['payment_remark'];
            $data[] = $value;
        }
        // 创建导出表
        $title = '收入表_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '日期',
            '街道名称',
            '收入类型(本次服务）',
            '是否家床(缴费老人)',
            '服务项目',
            '姓名',
            '收款人'
        ];
        $headers = array_merge($headers,$paymentArr);
        $headers[] = '审核状态';
        $headers[] =  '备注';
        // 列表
        $exportData[] = [
            'title' => $title,
            'header'=> $headers,
            'export'=> $data,
        ];
        // 汇总表
        // 获取所有收款人和日期
        $collectors = array_unique(array_column($data, 'createdName'));
        $dates = array_unique(array_column($data, 'time'));
        sort($dates); // 按日期排序
        
        // 初始化汇总数据
        $summaryData = [];
        foreach ($dates as $date) {
            foreach ($paymentArr as $payment) {
                foreach ($collectors as $collector) {
                    $summaryData[$date][$payment][$collector] = '0';
                }
            }
        }
        
        // 构建汇总数据
        foreach ($data as $row) {
            foreach ($paymentArr as $payment) {
                if (isset($row[$payment]) && $row[$payment] !== '') {
                    $summaryData[$row['time']][$payment][$row['createdName']] = bcadd(
                        $summaryData[$row['time']][$payment][$row['createdName']],
                        (string)$row[$payment],
                        2
                    );
                }
            }
        }
        
        // 计算每行的合计和每个支付方式的总计
        $rowTotals = [];
        $paymentTotals = [];
        $collectorTotals = [];
        
        foreach ($dates as $date) {
            $rowTotals[$date] = [];
            foreach ($paymentArr as $payment) {
                $rowTotals[$date][$payment] = '0';
                if (!isset($paymentTotals[$payment])) {
                    $paymentTotals[$payment] = [];
                    foreach ($collectors as $collector) {
                        $paymentTotals[$payment][$collector] = '0';
                    }
                }
                
                foreach ($collectors as $collector) {
                    $rowTotals[$date][$payment] = bcadd($rowTotals[$date][$payment], $summaryData[$date][$payment][$collector], 2);
                    $paymentTotals[$payment][$collector] = bcadd($paymentTotals[$payment][$collector], $summaryData[$date][$payment][$collector], 2);
                }
            }
        }
        
        // 计算总计
        $grandTotal = [];
        foreach ($paymentArr as $payment) {
            $grandTotal[$payment] = array_reduce($paymentTotals[$payment], function($carry, $item) {
                return bcadd($carry, $item, 2);
            }, '0');
        }
        
        // 格式化汇总数据为导出格式
        $summaryExport = [];
        
        // 第一行：序号和日期/月份合并，收入类型作为一级表头
        $row = [];
        $row[] = ['value' => '序号', 'rowspan' => 2]; // 序号占两行
        $row[] = ['value' => '日期/月份', 'rowspan' => 2]; // 日期占两行
        
        foreach ($paymentArr as $payment) {
            $row[] = ['value' => $payment, 'colspan' => count($collectors) + 1]; // +1 是为了包含小计列
        }
        $summaryExport[] = $row;
        
        // 第二行：收款人作为二级表头
        $row = [];
        // 序号和日期已经在第一行合并了，这里不需要再添加
        foreach ($paymentArr as $payment) {
            foreach ($collectors as $collector) {
                $row[] = $collector;
            }
            $row[] = '小计'; // 每个支付方式的小计
        }
        $summaryExport[] = $row;
        
        // 数据行：按日期展示
        $index = 1;
        foreach ($dates as $date) {
            $row = [];
            $row[] = $index++; // 序号
            $row[] = $date; // 日期
            
            foreach ($paymentArr as $payment) {
                foreach ($collectors as $collector) {
                    $row[] = number_format($summaryData[$date][$payment][$collector], 2, '.', '');
                }
                $row[] = number_format($rowTotals[$date][$payment], 2, '.', ''); // 每个支付方式的小计
            }
            $summaryExport[] = $row;
        }
        
        // 总计行
        $row = [];
        $row[] = ''; // 序号列留空
        $row[] = '总计';
        
        foreach ($paymentArr as $payment) {
            foreach ($collectors as $collector) {
                $row[] = number_format($paymentTotals[$payment][$collector], 2, '.', '');
            }
            $row[] = number_format($grandTotal[$payment], 2, '.', ''); // 每个支付方式的总计
        }
        $summaryExport[] = $row;
        
        // 添加汇总表到导出数据
        $exportData[] = [
            'title' => '汇总表',
            'header'=> [],
            'export'=> $summaryExport,
            'merge_cells' => true
        ];

        $url = Excel::exportTablesV2($title,$exportData);
        return $this->success([
            'url' => $url,
        ]);
    }

    /**
     * 下载开发票所需数据接口
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException|BadRequestHttpException
     */
    public function exportInvoicingData(Request $request): Response
    {
        // var_dump('exportInvoicingData-=-=-=---------------');

        $where = $request->more([
            ['service_type', ''],
            ['payment_type', ''],
            ['payment_method', ''],
            ['payment_time', ''],
            ['payment_status', ''],
        ]);
        // var_dump('exportInvoicingData-=-=-=--------1-------',$where);

        $dao = new Dao();
        // 查询居家照护合同的用户id
        $cust_uids = $dao->search('contract_management',
            ['contract_type' => [10,65],'contract_status' => '进行中','delete_time' => null],'cust_uid');
        // var_dump('exportInvoicingData-=-=-=--------1------cust_uids-',$cust_uids);
        if(!empty($cust_uids)) $cust_uids = array_unique($cust_uids);// 去重
        // var_dump('exportInvoicingData-=-=-=--------2------cust_uids-',$cust_uids);
        
        // 查询缴费数据
        $billModel = new CustBillLogic();
        $query = $billModel->search($where)->with([
            'custInfo',
            'createdByInfo'
        ])->when(!empty($cust_uids), function ($query) use ($cust_uids) {
            $query->whereIn('cust_bill.cust_uid', $cust_uids);
        });
        $res = $billModel->getAllWithAuth($query);
        if(empty($res)) {
            throw new BadRequestHttpException('缴费记录不存在');
        }

        $i = 1;
        $data = [];
        foreach ($res as $v) {
            $value = [];
            if(empty($v['custInfo'])) {
                $v['custInfo'] = [
                    'cust_name'=>'用户已删除',
                    'cust_street'=>'用户已删除',
                ];
            } 
            
            $value['number'] = $i;// 序号
            $i++;
            $value['cust_name'] = $v['custInfo']['cust_name']; // 姓名
            $value['time'] = $v['payment_time']; // 付款时间
            $value['payment_method'] = $v['payment_method']; // 付款方式
            $value['amount_paid'] = $v['amount_paid']; // 金额
            $value['cust_street'] = $v['custInfo']['cust_street'];// 街道
            // $value['payment_type'] = $v['payment_type']; // 收入类型
            $value['service_type'] = $v['service_type']; // 服务项目
            // $value['createdName'] = !empty($v['createdByInfo']) && !empty($v['createdByInfo']['nickname']) ? $v['createdByInfo']['nickname'] : '用户暂未录入';// 收款人员
            $value['payment_status'] = $v['payment_status']; // 收款状态
            $value['payment_remark'] = $v['payment_remark'];
            $data[] = $value;
        }
        // 创建导出表
        $title = '开发票_'.date('YmdHis').'.xlsx';
        $headers = [
            '序号',
            '姓名',
            '付款时间',
            '付款方式',
            '金额',
            '街道',
            '服务项目',
            '审核状态',
            '备注',
        ];

        $url = Excel::export($title,$headers,$data);
        return $this->success([ 'url' => $url ]);
    }

    /**
     * 缴费记录审核
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function auditRevenue(Request $request): Response
    {
        $params = $request->only([
            'payment_id',
            'auditor_status',
            'auditor_remark',
        ]);
        // payment_id 为数组
        if(!is_array($params['payment_id'])) {
            throw new BadRequestHttpException('缴费记录ID必须为数组');
        }
        // 增加默认值
        if(empty($params['auditor_remark'])) {
            $params['auditor_remark'] = '无';
        }
        // 查询缴费记录
        $billModel = new CustBill();
        $billInfo = $billModel->whereIn('payment_id', $params['payment_id'])->select()->toArray();
        if(empty($billInfo)) {
            throw new BadRequestHttpException('缴费记录不存在');
        }
        // 校验所有缴费记录状态是否正常
        foreach($billInfo as $item) {
            if(!in_array($item['payment_status'], ['待审核', '作废申请中'])) {
                throw new BadRequestHttpException('缴费记录状态错误');
            }
            // 根据缴费记录状态修改
            if($item['payment_status'] == '待审核') {
                // 已驳回
                if($params['auditor_status'] == '已驳回') {
                    $billModel->where('payment_id', $item['payment_id'])->update([
                        'payment_status' => '已驳回',
                        'auditor_remark' => $params['auditor_remark'],
                        'auditor_time' => date('Y-m-d H:i:s'),
                        'auditor_id' => $this->adminId,
                        'update_time' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->adminId,
                    ]);
                    // 添加修改状态
                    $logModel = new CustBillLog();
                    $logModel->insert([
                        'payment_id' => $item['payment_id'],
                        'old_payment_status' => $item['payment_status'],
                        'new_payment_status' => '已驳回',
                        'created_by' => $this->adminId,
                        'create_time' => date('Y-m-d H:i:s'),
                    ]);
                }
                // 已通过
                if($params['auditor_status'] == '已通过') {
                    // 事务修改缴费状态、用户余额
                    Db::transaction(function () use ($item, $params) {
                        $billModel = new CustBill();
                        $custBalanceModel = new CustBalance();
                        // 查询余额
                        $custBalanceInfo = $custBalanceModel->where('balance_name','余额账户')->where('cust_uid', $item['cust_uid'])->findOrEmpty()->toArray();
                        if(empty($custBalanceInfo)) {
                            throw new BadRequestHttpException('余额账户不存在');
                        }
                        // 更新缴费状态
                        $billModel->where('payment_id', $item['payment_id'])->update([
                            'payment_status' => '已通过',
                            'auditor_time' => date('Y-m-d H:i:s'),
                            'auditor_remark' => $params['auditor_remark'],
                            'auditor_id' => $this->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                            'updated_by' => $this->adminId,
                        ]);
                        // 更新余额
                        $custBalanceModel->where('balance_name','余额账户')->where('cust_uid', $item['cust_uid'])->update([
                            'now_amount' => $custBalanceInfo['now_amount'] + $item['amount_paid'],
                            'recharge_amount' => $custBalanceInfo['recharge_amount'] + $item['amount_paid'],
                            'update_time' => date('Y-m-d H:i:s'),
                            'updated_by' => $this->adminId,
                        ]);
                        // 添加充值记录
                        $custBalanceRecordModel = new CustBalanceRecord();
                        $custBalanceRecordModel->insert([
                            'balance_id' => $custBalanceInfo['balance_id'],
                            'cust_uid' => $custBalanceInfo['cust_uid'],
                            'link_id' => $item['payment_id'],
                            'pm' => 1,
                            'usage_date' => $item['payment_time'],
                            'record_title' => $item['payment_method'],
                            'record_details_category' => $item['payment_method'],
                            'record_details_type' => 2,
                            'forward' => $custBalanceInfo['now_amount'],
                            'balance' => $custBalanceInfo['now_amount'] + $item['amount_paid'],
                            'number' => $item['amount_paid'],
                            'created_by' => $this->adminId,
                            'create_time' => date('Y-m-d H:i:s'),
                        ]);
                    });
                    // 添加修改状态
                    $logModel = new CustBillLog();
                    $logModel->insert([
                        'payment_id' => $item['payment_id'],
                        'old_payment_status' => $item['payment_status'],
                        'new_payment_status' => '已通过',
                        'created_by' => $this->adminId,
                        'create_time' => date('Y-m-d H:i:s'),
                    ]);
                }
            }
            if($item['payment_status'] == '作废申请中') {
                // 查询申请前状态，然后退回该状态
                $logModel = new CustBillLog();
                $logInfo = $logModel->where('payment_id', $item['payment_id'])->order('id', 'desc')->findOrEmpty()->toArray();
                if(empty($logInfo)) {
                    throw new BadRequestHttpException('申请前状态不存在');
                }
                // 已驳回
                if($params['auditor_status'] == '已驳回') {
                    $billModel->where('payment_id', $item['payment_id'])->update([
                        'payment_status' => $logInfo['old_payment_status'],
                        'auditor_time' => date('Y-m-d H:i:s'),
                        'auditor_id' => $this->adminId,
                        'update_time' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->adminId,
                    ]);
                }
                // 已通过
                if($params['auditor_status'] == '已通过') {
                    // 事务修改缴费状态、用户余额
                    if($logInfo['old_payment_status'] == '已通过') {
                        Db::transaction(function () use ($item, $params) {
                            $billModel = new CustBill();
                            $custBalanceModel = new CustBalance();
                            // 更新缴费状态
                            $billModel->where('payment_id', $item['payment_id'])->update([
                                'payment_status' => '已作废',
                                'auditor_time' => date('Y-m-d H:i:s'),
                                'auditor_remark' => $params['auditor_remark'],
                                'auditor_id' => $this->adminId,
                                'update_time' => date('Y-m-d H:i:s'),
                                'updated_by' => $this->adminId,
                            ]);

                            // 查询余额
                            $custBalanceInfo = $custBalanceModel->where('balance_name','余额账户')->where('cust_uid', $item['cust_uid'])->findOrEmpty()->toArray();
                            // var_dump('custBalanceInfo---',$custBalanceInfo);
                            // 退回余额
                            $custBalanceModel->where('balance_name','余额账户')->where('cust_uid', $item['cust_uid'])->update([
                                'now_amount' => $custBalanceInfo['now_amount'] - $item['amount_paid'],
                                'recharge_amount' => $custBalanceInfo['recharge_amount'] - $item['amount_paid'],
                                'update_time' => date('Y-m-d H:i:s'),
                                'updated_by' => $this->adminId,
                            ]);
                            // 退回余额
                            // $custBalanceModel->where('balance_name','余额账户')->where('cust_uid', $item['cust_uid'])->setDec('now_amount', $item['amount_paid']);
                            // $custBalanceModel->where('balance_name','余额账户')->where('cust_uid', $item['cust_uid'])->setDec('recharge_amount', $item['amount_paid']);
                            // 删除充值记录
                            $custBalanceRecordModel = new CustBalanceRecord();
                            $custBalanceRecordModel->where('link_id', $item['payment_id'])->update([
                                'delete_time' => date('Y-m-d H:i:s'),
                            ]);
                        });
                    } else {
                        $billModel->where('payment_id', $item['payment_id'])->update([
                            'payment_status' => '已作废',
                            'auditor_time' => date('Y-m-d H:i:s'),
                            'auditor_remark' => $params['auditor_remark'],
                            'auditor_id' => $this->adminId,
                            'update_time' => date('Y-m-d H:i:s'),
                            'updated_by' => $this->adminId,
                        ]);
                    }
                    // 添加修改状态
                    $logModel = new CustBillLog();
                    $logModel->insert([
                        'payment_id' => $item['payment_id'],
                        'old_payment_status' => $logInfo['old_payment_status'],
                        'new_payment_status' => '已作废',
                        'created_by' => $this->adminId,
                        'create_time' => date('Y-m-d H:i:s'),
                    ]);
                }
            }
        }
        return $this->success('审核成功');
    }

    /**
     * 作废申请
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function applyRevenue(Request $request): Response
    {
        $params = $request->only([
            'payment_id',
            'service_remark'
        ]);
        // 查询缴费记录
        $billModel = new CustBill();
        $billInfo = $billModel->where('payment_id', $params['payment_id'])->findOrEmpty()->toArray();
        if(empty($billInfo) || ($billInfo['payment_status'] == '已作废' || $billInfo['payment_status'] == '作废申请中')) {
            throw new BadRequestHttpException('缴费记录不存在或已作废或作废申请中');
        }
        // 根据订单状态修改
        if($billInfo['payment_status'] == '待审核') {
            $billModel->where('payment_id', $params['payment_id'])->update([
                'payment_status' => '已作废',
                'service_remark' => $params['service_remark'],
            ]);
        } else {
            $billModel->where('payment_id', $params['payment_id'])->update([
                'payment_status' => '作废申请中',
                'service_remark' => $params['service_remark'],
            ]);
        }
        // 添加修改状态
        $logModel = new CustBillLog();
        $logModel->insert([
            'payment_id' => $params['payment_id'],
            'old_payment_status' => $billInfo['payment_status'],
            'new_payment_status' => $billInfo['payment_status'] == '待审核' ? '已作废' : '作废申请中',
            'created_by' => $this->adminId,
            'create_time' => date('Y-m-d H:i:s'),
        ]);
        return $this->success('申请成功');
    }

    /**
     * 修改状态接口
     * @throws BadRequestHttpException
     * @throws Exception
     */
    public function updateStatus(Request $request): Response
    {
        $params = $request->only([
            'payment_id',
            'payment_status',
        ]);
        // 查询缴费记录
        $billModel = new CustBill();
        $billInfo = $billModel->where('payment_id', $params['payment_id'])->findOrEmpty()->toArray();
        if(empty($billInfo)) {
            throw new BadRequestHttpException('缴费记录不存在');
        }
        // 修改
        $billModel = new CustBill();
        $billModel->where('payment_id', $params['payment_id'])->update([
            'payment_status' => $params['payment_status'],
            'update_time' => date('Y-m-d H:i:s'),
            'updated_by' => $this->adminId,
        ]);
        // 添加修改状态
        $logModel = new CustBillLog();
        $logModel->insert([
            'payment_id' => $params['payment_id'],
            'old_payment_status' => $billInfo['payment_status'],
            'new_payment_status' => $params['payment_status'],
            'created_by' => $this->adminId,
            'create_time' => date('Y-m-d H:i:s'),
        ]);
        return $this->success('修改成功');
    }

    /**
     * 导入收入统计
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function importRevenue(Request $request): Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        // 获取数据
        $data = Excel::readExcel([
            'Worksheet',
        ]);
        if(empty($data['Worksheet'])) {
            return $this->fail('无数据');
        }
        // 添加防抖
        $lock = Locker::lock($request->adminId);
        if (!$lock->acquire()) {
            throw new BadRequestHttpException('操作太频繁，请稍后再试');
        }
        try {
// 处理数据
            $data = $data['Worksheet'];
            $custModel = new CustUser();
            $billModel = new CustBill();
            $insertData = [];
            $fail = [];
            foreach($data as $key => $item) {
                if($key == 0 || empty($item[0])) {
                    continue;
                }
                // 只处理已付款且已完成的订单
                if($item[1] != '已完成' || $item[2] != '已付款') {
                    continue;
                }

                // 查询cust_uid
                $cust_uid = $custModel->where('cust_name', $item[3])->value('cust_uid');
                if(empty($cust_uid)) {
                    $fail[] = $item[3].'用户不存在';
                    continue;
                }
                // 添加检查
                $check = $billModel->where('payment_voucher_number', $item[0])->findOrEmpty()->toArray();
                if(!empty($check)) {
                    $fail[] = $item[0].'订单已存在';
                    continue;
                }
                // 处理数据
                $insertData[] = [
                    'cust_uid' => $cust_uid,
                    'service_type' => '营养餐',
                    'payment_type' => '市场化',
                    'payment_method' => '获得扫码(农行)',
                    'payment_status' => '已通过',
                    'auditor_time' => date('Y-m-d H:i:s'),
                    'auditor_remark' => '系统导入缴费信息',
                    'payment_voucher_number' => $item[0], // 订单号
                    'amount_paid' => str_replace('￥', '', $item[6]), // 订单金额，去除￥符号
                    'payment_time' => date('Y-m-d',strtotime($item[8])), // 付款时间
                    'created_by' => $this->adminId,
                    'auditor_id' => $this->adminId,
                    'create_time' => date('Y-m-d H:i:s'),
                ];
            }
            // 导入数据
            if(!empty($insertData)) {
                // 事务导入
                Db::transaction(function () use ($insertData) {
                    $billModel = new CustBill();
                    $custBalanceModel = new CustBalance();
                    $custBalanceRecordModel = new CustBalanceRecord();
                    $logModel = new CustBillLog();
                    
                    foreach($insertData as $item) {
                        // 插入缴费记录
                        $payment_id = $billModel->insertGetId($item);
                        
                        // 查询余额账户
                        $custBalanceInfo = $custBalanceModel->where('balance_name', '余额账户')
                            ->where('cust_uid', $item['cust_uid'])
                            ->findOrEmpty()
                            ->toArray();
                            
                        if(empty($custBalanceInfo)) {
                            throw new BadRequestHttpException('余额账户不存在');
                        }
                        
                        // 更新余额
                        $custBalanceModel->where('balance_name', '余额账户')
                            ->where('cust_uid', $item['cust_uid'])
                            ->update([
                                'now_amount' => $custBalanceInfo['now_amount'] + $item['amount_paid'],
                                'recharge_amount' => $custBalanceInfo['recharge_amount'] + $item['amount_paid'],
                                'update_time' => date('Y-m-d H:i:s'),
                                'updated_by' => $this->adminId,
                            ]);
                            
                        // 添加充值记录
                        $custBalanceRecordModel->insert([
                            'balance_id' => $custBalanceInfo['balance_id'],
                            'cust_uid' => $custBalanceInfo['cust_uid'],
                            'link_id' => $payment_id,
                            'pm' => 1,
                            'usage_date' => $item['payment_time'],
                            'record_title' => $item['payment_method'],
                            'record_details_category' => $item['payment_method'],
                            'record_details_type' => 2,
                            'forward' => $custBalanceInfo['now_amount'],
                            'balance' => $custBalanceInfo['now_amount'] + $item['amount_paid'],
                            'number' => $item['amount_paid'],
                            'created_by' => $this->adminId,
                            'create_time' => date('Y-m-d H:i:s'),
                        ]);
                        
                        // 添加状态日志
                        $logModel->insert([
                            'payment_id' => $payment_id,
                            'old_payment_status' => '待审核',
                            'new_payment_status' => '已通过',
                            'created_by' => $this->adminId,
                            'create_time' => date('Y-m-d H:i:s'),
                        ]);
                    }
                });
            }
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        } finally {
            $lock->release();
        }
        return $this->success([
            'fail' => $fail,
            'success' => '已成功导入'. count($insertData).'条数据',
        ]);
    }
}
