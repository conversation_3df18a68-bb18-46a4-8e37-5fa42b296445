<?php

namespace app\controller;

use app\cust\model\CustBalance;
use app\cust\model\CustBalanceRecord;
use app\domain\service\FoodOrderService;
use app\domain\service\SmartService;
use app\utils\NocoDbApi;
use app\utils\Water;
use app\validate\CommonValidate;
use Exception;
use Medoo\Medoo;
use plugin\saiadmin\utils\Cache;
use Ramsey\Uuid\Uuid;
use support\Redis;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use Webman\Captcha\CaptchaBuilder;
use Webman\Captcha\PhraseBuilder;
use yzh52521\EasyHttp\Http;
use app\utils\Excel;

/**
 * 公共控制器
 * @Apidoc\Title("公共管理")
 */
class CommonController extends BaseController
{

    /**
     * 获取验证码
     */
    public function captcha(Request $request, string $type = 'login') : Response
    {
        $builder = new PhraseBuilder(4, 'abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ');
        $captcha = new CaptchaBuilder(null, $builder);
        $captcha->setBackgroundColor(242,243,245);
        $captcha->build(120, 36);

        $uuid = Uuid::uuid4();
        $key = $uuid->toString();
        $mode = config('plugin.saiadmin.saithink.captcha.mode', 'session');
        $expire = config('plugin.saiadmin.saithink.captcha.expire', 300);
        $code = strtolower($captcha->getPhrase());
        if ($mode === 'redis') {
            try {
                Redis::set($key, $code, 'EX',$expire);
            } catch (Exception $e) {
                return json(['code' => -1, 'msg' => '验证码生成失败，请检查Redis配置']);
            }
        } else {
            $request->session()->set($key, $code);
        }
        $img_content = $captcha->get();
        return json(['code' => 200, 'uuid' => $key, 'img' => base64_encode($img_content)]);
    }

    public function getSignImage1($name){
        if(!empty($name)){
            // 获取签名图片
            return $this->getSelfSignImage($name);
        }else{
            return "";
        }
    }
    
    /**
     * 获取签名接口
     * @Apidoc\Title("手写签名生成")
     * @Apidoc\Url("/common/getSignImage")
     * @Apidoc\Query("name", type="varchar",require=true, desc="签名")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function getSignImage(Request $request): Response
    {
        $name = $request->input('name','');
        $validate = new CommonValidate;
        if (!$validate->scene('signImage')->check([
            'name' => $name
        ])) {
            return $this->fail('请输入正确的参数');
        }
        $fontArr = [
            '851手写杂字体.ttf',
            '李国夫手写体.ttf',
            '沐瑶随心手写体.ttf',
            '云烟体.ttf',
            '华阳手写体.ttf',
            '851手写杂字体.ttf',
            '851手写杂字体.ttf',
        ];
        // 判断名称长度
        if(mb_strlen($name) > 2) {
           $width = 700;
        } else {
           $width = 500;
        }
        $params = [
            'text' => $name,
            'background_image' => null,
            'font_size' => rand(100,130),
            'line_spacing' => 200,
            'fill' => '[0,0,0,255]',
            'width' => $width,
            'height' => 400,
            'top_margin' => rand(50,80),
            'bottom_margin' => rand(50,80),
            'left_margin' => rand(50,100),
            'right_margin' => rand(50,100),
            'line_spacing_sigma' => 0,
            'font_size_sigma' => 3,
            'word_spacing_sigma' => 7,
            'end_chars' => null,
            'perturb_x_sigma' => 7,
            'perturb_y_sigma' => 7,
            'perturb_theta_sigma' => 0,
            'word_spacing' => 1,
            'preview' => true,
            'font_option' => $fontArr[rand(0,6)],
            'strikethrough_length_sigma' => 2,
            'strikethrough_angle_sigma' => 2,
            'strikethrough_width_sigma' => 2,
            'strikethrough_probability' => 0,
            'strikethrough_width' => 0,
            'ink_depth_sigma' => 0,
            'pdf_save' => false,
            'isUnderlined' => false,
        ];
        // 请求生成签名
        $res = Http::post('http://81.70.20.222:5000/api/generate_handwriting', $params)->array();
        if(empty($res) || empty($res['preview_url'])) {
            return $this->fail('签名生成失败');
        }
        // 替换掉图片地址
        $url = str_replace('http://81.70.20.222:9000', 'https://static.zkshlm.com', $res['preview_url']);
        return $this->success([
            'url' => $url
        ]);
    }

    /**
     * 迁移minio图片
     */
    public function migrateMinio(): Response
    {
        // 创建源minio对象

        // 依次获取源minio文件

        // 检测目标文件是否存在

        // 移动文件

        // 记录迁移记录

        return $this->success();
    }

    /**
     * 更新用户签名图片
     */
    public function updateSignImage(): Response
    {
        // 查询不存在的签名的用户
        $userList = $this->dao->search('cust_user',[
            'delete_time' => null,
            'cust_signature' => null,
            'LIMIT' => 300
        ],[
            'cust_uid',
            'cust_name',
            'cust_signature'
        ]);
        // 循环更新用户签名图片
        foreach ($userList as $v) {
            $name = preg_replace('/[^\x{4e00}-\x{9fa5}]/u', '', $v['cust_name']);
            // 替换名称
            $noName = [
                '号楼',
                '微信',
                '华亭嘉园座',
                '居委会',
                '老年大学',
                '阿姨',
                '南沙滩',
                '龙翔',
            ];
            foreach ($noName as $item) {
                $name = str_replace($item, '', $name);
            }
            // 判断名称是否大于3位或小于等于1位
            if(mb_strlen($name) > 3 || mb_strlen($name) <= 1) {
                $name = null;
            }
            if(empty($name)) {
                $img = 'https://static.zkshlm.com/zksh/%E6%9A%82%E6%97%A0%E5%9B%BE%E7%89%87%20(1).png';
            } else {
                // 获取签名图片
                $img = $this->getSelfSignImage($name);
            }
            // 更新用户图片
            $this->dao->update('cust_user',[
                'cust_signature' => $img,
                'update_time' => date('Y-m-d H:i:s')
            ],[
                'cust_uid' => $v['cust_uid']
            ]);
        }

        //查询不存在的签名的员工
//        $userList = $this->dao->search('employee_user',[
//            'delete_time' => null,
//        ],[
//            'employee_uid',
//            'employee_name'
//        ]);
//        // 循环更新用户签名图片
//        foreach ($userList as $v) {
//            $name = preg_replace('/[^\x{4e00}-\x{9fa5}]/u', '', $v['employee_name']);
//            if(empty($name)) {
//                $img = 'https://static.zkshlm.com/zksh/%E6%9A%82%E6%97%A0%E5%9B%BE%E7%89%87%20(1).png';
//            } else {
//                // 获取签名图片
//                $img = $this->getSelfSignImage($name);
//            }
//            // 更新用户图片
//            $this->dao->update('employee_user',[
//                'employee_signature_picture' => $img,
//                'update_time' => date('Y-m-d H:i:s')
//            ],[
//                'employee_uid' => $v['employee_uid']
//            ]);
//        }
        return $this->success();
    }

    /**
     * 获取签名图片
     * @param $name
     * @return string
     */
    protected function getSelfSignImage($name): string
    {
        $fontArr = [
            '851手写杂字体.ttf',
            '李国夫手写体.ttf',
            '沐瑶随心手写体.ttf',
            '云烟体.ttf',
            '华阳手写体.ttf',
            '851手写杂字体.ttf',
            '851手写杂字体.ttf',
        ];
        // 判断名称长度
        if(mb_strlen($name) > 2) {
            $width = 700;
        } else {
            $width = 500;
        }
        $params = [
            'text' => $name,
            'background_image' => null,
            'font_size' => rand(100,130),
            'line_spacing' => 200,
            'fill' => '[0,0,0,255]',
            'width' => $width,
            'height' => 400,
            'top_margin' => rand(50,80),
            'bottom_margin' => rand(50,80),
            'left_margin' => rand(50,100),
            'right_margin' => rand(50,100),
            'line_spacing_sigma' => 0,
            'font_size_sigma' => 3,
            'word_spacing_sigma' => 7,
            'end_chars' => null,
            'perturb_x_sigma' => 7,
            'perturb_y_sigma' => 7,
            'perturb_theta_sigma' => 0,
            'word_spacing' => 1,
            'preview' => true,
            'font_option' => $fontArr[rand(0,6)],
            'strikethrough_length_sigma' => 2,
            'strikethrough_angle_sigma' => 2,
            'strikethrough_width_sigma' => 2,
            'strikethrough_probability' => 0,
            'strikethrough_width' => 0,
            'ink_depth_sigma' => 0,
            'pdf_save' => false,
            'isUnderlined' => false,
        ];
        // 请求生成签名
        $res = Http::post('http://81.70.20.222:5000/api/generate_handwriting', $params)->array();
        if(empty($res) || empty($res['preview_url'])) {
            return $this->fail('签名生成失败');
        }
        // 替换掉图片地址
        return str_replace('http://81.70.20.222:9000', 'https://static.zkshlm.com', $res['preview_url']);
    }

    /**
     * 添加问卷记录
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function addForm(Request $request): Response
    {
        $data = $request->all();
        if(empty($data['id'])) {
            return $this->fail('请输入正确的参数');
        }
        // 添加模板记录
        // 查询客户信息
        $custInfo = $this->dao->get('cust_user',[
            'cust_uid' => $data['老人编号'],
        ]);
        if(empty($custInfo)) {
            throw new BadRequestHttpException('档案信息不存在');
        }
        $templateInfo = $this->dao->get('cust_survey_template',[
            'cust_survey_template_id' => $data['id']
        ]);
        if(empty($templateInfo)) {
            throw new BadRequestHttpException('问卷不存在');
        }
        unset($data['id']);
        $params = array_merge($data,['table_id' => $templateInfo['table_id'],'view_id' => $templateInfo['view_id']]);
        $res = NocoDbApi::send($params,'post');
        return $this->success($res);
    }

    /**
     * 获取行政区域地址
     * @param Request $request
     * @return Response
     */
    public function getAreaList(Request $request): Response
    {
        $params = $request->all();
        if(!isset($params['id']) || !isset($params['level'])) {
            return $this->fail('请输入正确的参数');
        }
        // 查询id名称
        if(is_string($params['id'])) {
            if(!empty($params['name']) && $params['name'] == '天津市') {
                $params['id'] = '120100000000';
            } else {
                $params['id'] = $this->dao->get('sys_area',[
                    'name' => $params['id'],
                    'level' => $params['level'],
                ],'id');
            }
        }
        // 查询子集
        $info = $this->dao->get('sys_area', [
            'id' => $params['id']
        ]);
        if (!empty($info)) {
            // 查询下级
            $where['level'] = $info['level'] + 1;
            $where['pid'] = $info['id'];
        } else {
            $where['level'] = 1;
        }
        $data = [];
        if (!empty($where)) {
            $data = $this->dao->search('sys_area', $where, [
                'id',
                'name(value)',
                'name(label)',
                'level',
            ]);
        }
        return $this->success($data);
    }

    /**
     * 查询代码
     * @param Request $request
     * @return Response
     */
    public function getUserList(Request $request): Response
    {
        $name = $request->input('name');
        $where = [
            'delete_time' => null,
            "LIMIT" => 10
        ];
        if (!empty($name)) {
            $where["AND"] = [
                "OR" => [
                    "cust_name[~]" => $name,
                    "cust_private_phone[~]" => $name,
                ],
            ];
        }
        $data = $this->dao->search('cust_user',$where,[
            'cust_uid',
            'cust_name',
            'cust_sex',
            'cust_private_phone',
            'cust_city',
            'cust_live_address',
            'cust_signature',
            'cust_evaluation_level',
            'cust_identity_type',
            'cust_is_bed',
            'cust_consultant'
        ]);
        if(!empty($data)) {
            foreach ($data as &$v) {
                $v['nameText'] = $v['cust_name'] . '(' . $v['cust_private_phone'] . ')';
                $balanceInfo = $this->dao->get('cust_balance',[
                    'delete_time' => null,
                    'balance_name' => '余额账户',
                    'cust_uid' => $v['cust_uid'],
                ],[
                    'balance_id',
                    'now_amount'
                ]);
                if(empty($balanceInfo)) {
                    $balanceInfo = [
                        'now_amount' => 0,
                        'balance_id' => $v['cust_uid']
                    ];
                    // 创建默认余额账号
                    $this->dao->insert('cust_balance',[
                        'balance_id' => $v['cust_uid'],
                        'balance_name' => '余额账户',
                        'cust_uid' => $v['cust_uid'],
                        'now_amount' => 0,
                        'create_time' => date('Y-m-d H:i:s'),
                        'update_time' => date('Y-m-d H:i:s'),
                    ]);
                }
                $v['balance'] = $balanceInfo['now_amount'];
                // 查询老人账号id
                $v['balance_id'] = $balanceInfo['balance_id'];
                // 查询老人上一单信息
                $v['employee_name'] = '';
                $v['employee_uid'] = '';
                $v['delivery_site'] = '';
                $v['delivery_option'] = '';
                $v['package_type'] = '';
                $orderInfo = $this->dao->get('order_food',[
                    'cust_uid' => $v['cust_uid'],
                    'delete_time' => null,
                    'ORDER' => [
                        'order_food_id' => 'DESC'
                    ]
                ]);
                if(!empty($orderInfo)) {
                    $v['employee_uid'] = $orderInfo['employee_uid'];
                    $v['employee_name'] = $this->dao->get('employee_user', [
                        'employee_uid' => $orderInfo['employee_uid']
                    ], 'employee_name');
                    $v['delivery_site'] = $orderInfo['delivery_site'];
                    $v['delivery_option'] = $orderInfo['delivery_option'];
                    $v['package_type'] = $orderInfo['package_type'];
                }
            }
        }
        return $this->success($data);
    }

    /**
     * 访问one-api系统创建数据
     * @Apidoc\Title("访问one-api系统")
     * @Apidoc\Url("/common/createData")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function createData(Request $request): Response
    {
        try {
            // 定义表头映射关系
            $header = [
                '序号' => 'index',
                '所属街道' => 'street',
                '所属社区' => 'community',
                '老年人姓名' => 'name',
                '联系方式' => 'phone',
                '身份证号' => 'id_card',
                '家庭住址' => 'address'
            ];

            // 导入Excel数据
            $excelData = Excel::import($header,4);
            if (empty($excelData)) {
                return $this->fail('Excel数据为空');
            }
            $result = [];
            foreach ($excelData as  $v) {
                if(empty($v['id_card'])) {
                    continue;
                }
                // 查询用户信息
                $custInfo = $this->dao->get('cust_user',[
                    'cust_name' => $v['name'],
                ],[
                    'cust_uid'
                ]);
                if(!empty($custInfo)) {
                    // 更新街道数据
                    $this->dao->update('cust_user',[
                        'cust_street' => $v['street'],
                        'cust_id_card' => $v['id_card'],
                        'cust_live_address' => $v['address'],
                        'cust_city' => json_encode([
                            'province' => '北京市',
                            'city' => '市辖区',
                            'area' => '海淀区',
                            'street' => $v['street'],
                            'community' => $v['community'],
                        ],JSON_UNESCAPED_UNICODE),
                        'update_time' => date('Y-m-d H:i:s')
                    ],[
                        'cust_uid' => $custInfo['cust_uid']
                    ]);
                } else {
                    $result[] = $v;
                }
            }

            return $this->success([
                'total' => count($result),
                'list' => $result
            ]);
            
        } catch (Exception $e) {
            return $this->fail('系统错误：' . $e->getMessage());
        }
    }

    /**
     * 处理bug数据
     */
    protected function updateBug() : Response
    {
        $page = Cache::get('NUM');
        // 查询2025年1月13日包含主食专供三的订单
        $orderList = $this->dao->search('order_food_item',[
            'delivery_time[>]' => '2025-01-14',
            'num[>]' => 0,
            'delete_time' => null,
            //'order_id' => '718280242971542267',
            'LIMIT' => [($page - 1) * 20, 20],
        ],[
            'order_id' => Medoo::raw('DISTINCT order_id'),
        ]);
        if(empty($orderList)) {
            Cache::set('NUM', 999999);
            return $this->success('检查完成');
        }
        var_dump($page);
        $foodOrderService = new FoodOrderService();
        foreach ($orderList as $v) {
            // 查询订单
            $payPrice = $this->dao->get('order_food',[
                'order_id' => $v['order_id'],
            ],'pay_price');
            $updateInfo = $foodOrderService->calculateDeliveryFee($v['order_id']);
            if($updateInfo['pay_price'] != $payPrice) {
                $this->dao->update('order_food',$updateInfo,[
                    'order_id' => $v['order_id']
                ]);
                var_dump($v['order_id']);
            }
        }
        $page = $page + 1;
        Cache::set('NUM', $page);
        return $this->success($orderList);
    }
    /**
     * 检查余额记录
     */
    protected function testV222(): Response
    {
        $list = $this->dao->search('cust_balance_record',[
            'create_time[>=]' => '2025-01-10 16:00:00',
            'create_time[<=]' => '2025-02-07 16:30:00',
        ]);
        // 统计link_id和balance重复的次数
        $duplicates = [];
        foreach ($list as $item) {
            $key = $item['link_id'];
            if (!isset($duplicates[$key])) {
                $duplicates[$key] = [
                    'count' => 1,
                    'records' => [$item]
                ];
            } else {
                $duplicates[$key]['count']++;
                $duplicates[$key]['records'][] = $item;
            }
        }
        // 筛选出重复的记录
        $result = [];
        foreach ($duplicates as $duplicate) {
            if ($duplicate['count'] > 1) {
                $result = array_merge($result, $duplicate['records']);
            }
        }
        return $this->success($result);
        // 删除记录
        // 按link_id分组记录
        $groupedRecords = [];
        foreach ($result as $record) {
            $groupedRecords[$record['link_id']][] = $record;
        }
        // 对每组重复记录,保留一条,删除其他
        foreach ($groupedRecords as $records) {
            if (count($records) > 1) {
                // 保留第一条记录,删除其他记录
                for ($i = 1; $i < count($records); $i++) {
                    // 查询是否存在关联的订单
                    $billList = $this->dao->search('cust_balance_record',[
                       'cust_uid' => $records[$i]['cust_uid'],
                       'balance_record_id[>]' => $records[$i]['balance_record_id'],
                    ]);
                    if(!empty($billList)) {
                        $nowBalance = $records[$i]['forward'];
                        // 修改订单
                        foreach ($billList as $v) {
                            // 修改账单余额
                            if($v['pm'] == 1) {
                                $v['number'] = - $v['number'];
                            }
                            $this->dao->update('cust_balance_record',[
                                'forward' => $nowBalance,
                                'balance' => $nowBalance - $v['number']
                            ],[
                                'balance_record_id' => $v['balance_record_id']
                            ]);
                            // 更新当前余额,用于下一条记录
                            $nowBalance = $nowBalance - $v['number'];
                        }
                        $this->dao->update('cust_balance',[
                            'now_amount' => $nowBalance,
                            'spending_amount[-]' => $records[$i]['number'],
                        ],[
                            'cust_uid' => $records[$i]['cust_uid'],
                        ]);
                        // 删除记录
                        $this->dao->del('cust_balance_record',[
                            'balance_record_id' => $records[$i]['balance_record_id']
                        ]);
                    } else {
                        // 删除记录
                        $this->dao->del('cust_balance_record',[
                            'balance_record_id' => $records[$i]['balance_record_id']
                        ]);
                    }
                }
            }
        }
        return $this->success($result);
    }

    /**
     * 检查余额
     */
    protected function testV333(): Response
    {
        $recordModel = new CustBalanceRecord();
        $balanceModel = new CustBalance();
        // 更新余额,获取每个用户最新的余额记录
//        $result = $model->distinct(true)->field('cust_uid')->select()->toArray();
//        foreach ($result as $v) {
//            $balance = $model->where('cust_uid',$v['cust_uid'])->order('balance_record_id','desc')->value('balance');
//            // 更新余额
//            $this->dao->update('cust_balance',[
//                'now_amount' => $balance
//            ],[
//                'cust_uid' => $v['cust_uid']]
//            );
//        }
//        $balanceModel = new CustBalance();
//        $result = $balanceModel->where('now_amount',914)->column('cust_uid');
//        foreach ($result as $v) {
//            // 查询账单
//            $now = $model->where('cust_uid',$v)->field('cust_uid')->findOrEmpty()->toArray();
//            if(empty($now)) {
//                var_dump($v);
//                $this->dao->update('cust_balance',[
//                    'now_amount' => 0,
//                    'spending_amount' => 0
//                ],[
//                    'cust_uid' => $v
//                ]);
//            }
//        }
        // 查询用户余额
        Cache::set('NUM', 1);
        $page = Cache::get('NUM');
        $orderList = $this->dao->search('cust_temp',[
            'LIMIT' => [($page - 1) * 20, 20],
            'cust_uid' => 76
        ],[
            'cust_uid',
        ]);
        if(empty($orderList)) {
            Cache::set('NUM', 999999);
            return $this->success('检查完成');
        }
        // var_dump($page);
        foreach ($orderList as $v) {
            // 查询累计消费金额
            $spendingAmount = $this->dao->sum('cust_balance_record','number',[
                'cust_uid' => $v['cust_uid'],
                'record_status[>=]' => 0,
                'pm' => 0,
                'delete_time' => null,
            ]);
            // 查询累计充值金额
            $rechargeAmount = $this->dao->sum('cust_balance_record','number',[
                'cust_uid' => $v['cust_uid'],
                'record_status[>=]' => 0,
                'pm' => 1,
                'delete_time' => null,
            ]);
            $now_amount = $this->dao->get('cust_balance',[
                'cust_uid' => $v['cust_uid'],
            ],'now_amount');
            // 值格式为小数点后两位
            $num = (float)$spendingAmount - (float)$rechargeAmount + (float)$now_amount;
            // 保留两位小数
            $num = round($num, 2);
            if($num == 0) {
                $this->dao->update('cust_balance',[
                    'recharge_amount' => $rechargeAmount,
                    'spending_amount' => $spendingAmount,
                ],[
                    'cust_uid' => $v['cust_uid'],
                ]);
            } else {
                var_dump($v['cust_uid']);
                var_dump($num);
                var_dump($rechargeAmount);
                var_dump($spendingAmount);
                var_dump($now_amount);
            }
        }
        $page = $page + 1;
        Cache::set('NUM', $page);
        return $this->success([]);
    }

    /**
     * 合并相同身份证号的账号
     * @return Response
     * @throws BadRequestHttpException
     */
    public function test()
    {
        // // 查询有重复身份证号的用户
        // $sql = "SELECT cust_id_card, COUNT(*) AS count FROM cust_user WHERE cust_id_card IS NOT NULL AND cust_id_card != '' AND LENGTH(cust_id_card) >= 15 AND delete_time IS NULL GROUP BY cust_id_card HAVING count > 1";
        // $result = $this->dao->query($sql)->fetchAll();
        
        // $mergeCount = 0;
        // foreach ($result as $v) {
        //     // 获取同一身份证号的所有用户
        //     $users = $this->dao->search('cust_user', [
        //         'cust_id_card' => $v['cust_id_card'],
        //         'delete_time' => null,
        //         'ORDER' => ['cust_uid' => 'ASC']
        //     ]);
            
        //     if (count($users) <= 1) continue;
            
        //     // 以第一个账号为主账号
        //     $mainUser = $users[0];
            
        //     // 处理其他账号
        //     for ($i = 1; $i < count($users); $i++) {
        //         $mergeUser = $users[$i];
                
        //         // 1. 合并余额
        //         $balanceInfo = $this->dao->get('cust_balance', [
        //             'cust_uid' => $mergeUser['cust_uid'],
        //             'delete_time' => null
        //         ]);
                
        //         if (!empty($balanceInfo)) {
        //             // 更新主账号余额
        //             $this->dao->update('cust_balance', [
        //                 'now_amount[+]' => $balanceInfo['now_amount'],
        //                 'recharge_amount[+]' => $balanceInfo['recharge_amount'],
        //                 'spending_amount[+]' => $balanceInfo['spending_amount'],
        //                 'update_time' => date('Y-m-d H:i:s')
        //             ], [
        //                 'cust_uid' => $mainUser['cust_uid']
        //             ]);
                    
        //             // 标记被合并账号的余额记录为删除
        //             $this->dao->update('cust_balance', [
        //                 'delete_time' => date('Y-m-d H:i:s')
        //             ], [
        //                 'cust_uid' => $mergeUser['cust_uid']
        //             ]);
        //         }
                
        //         // 2. 更新余额记录表
        //         $this->dao->update('cust_balance_record', [
        //             'cust_uid' => $mainUser['cust_uid']
        //         ], [
        //             'cust_uid' => $mergeUser['cust_uid'],
        //             'delete_time' => null
        //         ]);
                
        //         // 3. 更新订单相关表
        //         $tables = [
        //             'order_food',           // 订单表
        //             'cust_bill',           // 缴费记录表
        //             'cust_plan',           // 服务规划表
        //             'cust_service_record', // 服务记录表
        //             'cust_survey_record',  // 问卷记录表
        //             'contract_management',   // 合同表
        //             'order_food_item',   // 订单附表
        //         ];
                
        //         foreach ($tables as $table) {
        //             $this->dao->update($table, [
        //                 'cust_uid' => $mainUser['cust_uid']
        //             ], [
        //                 'cust_uid' => $mergeUser['cust_uid'],
        //                 'delete_time' => null
        //             ]);
        //         }
                
        //         // 4. 标记被合并的账号为删除状态
        //         $this->dao->update('cust_user', [
        //             'delete_time' => date('Y-m-d H:i:s'),
        //             'update_time' => date('Y-m-d H:i:s'),
        //             'cust_remark' => '账号已合并至ID:' . $mainUser['cust_uid']
        //         ], [
        //             'cust_uid' => $mergeUser['cust_uid']
        //         ]);
                
        //         $mergeCount++;
        //     }
        // }
        
        return $this->success([
            'message' => '账号合并完成',
            'merge_count' => 0
        ]);
    }

    /**
     * 水资源推送地址4
     * @param Request $request
     * @return Response
     * @throws Exception
     */
    public function water_push_url(Request $request): Response
    {
        $data = Water::getDecryptData($request->all());
        if(!empty($data)) {
            $data = json_decode($data, true);
            if(!empty($data['aesEncryptedData'])) {
                $data['aesEncryptedData'] = json_decode($data['aesEncryptedData'], true);
                if(!empty($data['aesEncryptedData']['eventData'])) {
                    $eventData = json_decode($data['aesEncryptedData']['eventData'], true);
                    $smartService = new SmartService();
                    $smartService->parseDeviceData($eventData);
                }
            }
        }
        return $this->success([
            'message' => '推送成功',
            'params' => $request->all()
        ]);
    }


    /**
     * 查询cust_user 中cust_work_unit 字段等于 中科院 的老人在order_food 表中 有 pay_price 大于0 记录的数据，要求导出 包含老人姓名、地址、电话、订餐单数（pay_price 大于0的订单数）
     * @Apidoc\Title("中科院老人订餐数据导出")
     * @Apidoc\Url("/common/daoExcel")
     * @Apidoc\Method("GET")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function daoExcel(Request $request): Response
    {
        try {
            // 使用原生SQL查询，获取中科院老人的订餐统计数据
            $sql = "
                SELECT
                    cu.cust_name as elderly_name,
                    cu.cust_live_address as address,
                    cu.cust_private_phone as phone,
                    COUNT(of.order_food_id) as order_count
                FROM cust_user cu
                LEFT JOIN order_food of ON cu.cust_uid = of.cust_uid
                    AND of.pay_price > 0
                    AND of.delete_time IS NULL
                WHERE cu.cust_work_unit = '中科院'
                    AND cu.delete_time IS NULL
                GROUP BY cu.cust_uid, cu.cust_name, cu.cust_live_address, cu.cust_private_phone, cu.
                HAVING order_count > 0
                ORDER BY order_count DESC, cu.cust_name ASC
            ";

            // 执行查询
            $results = $this->dao->query($sql)->fetchAll();

            if (empty($results)) {
                throw new BadRequestHttpException('暂无符合条件的数据');
            }

            // 处理数据，添加序号
            $data = [];
            foreach ($results as $index => $row) {
                $data[] = [
                    $index + 1, // 序号
                    $row['elderly_name'] ?: '未填写', // 老人姓名
                    $row['address'] ?: '未填写', // 地址
                    $row['phone'] ?: '未填写', // 电话
                    $row['order_count'] // 订餐单数
                ];
            }

            // 创建导出表
            $title = '中科院老人订餐统计_' . date('YmdHis') . '.xlsx';
            $headers = [
                '序号',
                '老人姓名',
                '居住地址',
                '联系电话',
                '订餐单数'
            ];

            $url = Excel::export($title, $headers, $data);

            return $this->success([
                'url' => $url,
                'total_count' => count($results),
                'message' => '导出成功'
            ]);

        } catch (Exception $e) {
            throw new BadRequestHttpException('导出失败：' . $e->getMessage());
        }
    }
}
