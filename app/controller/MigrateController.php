<?php

namespace app\controller;

use app\utils\Dao;
use support\Request;
use support\Response;
use DI\Attribute\Inject;
use Webman\Medoo\Medoo;
use app\utils\NocoDbApi;
use plugin\saiadmin\utils\Cache;

class MigrateController extends BaseController
{
    #[Inject]
    protected Dao $dao;

    /**
     * 迁移老人用户表
     */
    protected function migrateCustUid(): Response
    {
        // 查询原表数据
        $mainUser = Medoo::instance('v1')->select('main_user', [
            'id(cust_uid)', // 老人id
            'v1(cust_name)', // 姓名
            'v3(cust_avatar)', // 头像
            'v4(cust_sex)', // 性别  字典
            'v5(cust_identity_type)', // 身份类别  字典
            'v6(cust_id_type)', // 身份证类型
            'v7(cust_id_card)', //身份证号
            'v8(cust_birth)', // 出生年月
            'v9(cust_nation)', // 民族
            'v10(cust_home_phone)', // 私人电话
            'v11(cust_private_phone)', // 家庭电话
            'v12(cust_marital_status)', // 婚姻状态  字典
            'v13(cust_live_status)', // 居住状况  字典
            'v14(cust_belief)', // 宗教信仰  字典
            'v16(cust_consultant)', // 负责人id
            'v22(cust_area)', // 所属片区
            'v23(cust_is_bed)', // 是否家床
            'v25(cust_community)', // 社区
            'v26(cust_live_address)', // 居住详细地址
            'v27(cust_disability_type)', // 残疾类别  字典
            'v28(cust_disability_level)', // 残疾等级  字典
            'v29(cust_disability_card)', // 残疾证号
            'v36(cust_evaluation_level)', // 评估等级  字典
            'v37(cust_service_site)', // 服务机构  字典
            'created_by(created_by)', // 创建人
            'created_at(create_time)', // 创建时间
        ], [
            'deleted_at' => null,
            'id[>]' => 2018,
        ]);
        // 转换格式
        if(!empty($mainUser)) {
            foreach ($mainUser as &$v) {
                // 性别
                if(!empty($v['cust_sex'])) {
                    $v['cust_sex'] = match ($v['cust_sex']) {
                        '男' => 1,
                        '女' => 2,
                        default => 0
                    };
                }
                // 身份类别
                if(!empty($v['cust_identity_type'])) {
                    $v['cust_identity_type'] = match ($v['cust_identity_type']) {
                        '特困家庭' => 2,
                        '低保' => 3,
                        '计划生育特殊家庭' => 4,
                        '中科院退休老人' => 7,
                        '退伍军人' => 5,
                        '其他' => 6,
                        default => 1
                    };
                }
                // 身份证类型
                if(!empty($v['cust_id_type'])) {
                    $v['cust_id_type'] = match ($v['cust_id_type']) {
                        '其他' => 4,
                        '港澳通行证' => 3,
                        '护照' => 2,
                        default => 1
                    };
                }
                // 婚姻状态
                if(!empty($v['cust_marital_status'])) {
                    $v['cust_marital_status'] = match ($v['cust_marital_status']) {
                        '未婚' => 1,
                        '已婚' => 2,
                        '丧偶' => 3,
                        '离婚' => 4,
                        '分居' => 5,
                        default => 6
                    };
                }
                // 居住状况
                if(!empty($v['cust_live_status'])) {
                    $v['cust_live_status'] = match ($v['cust_live_status']) {
                        '空巢' => 2,
                        '子女未共同居住且丧偶的老人' => 3,
                        '无子女无配偶的孤寡老人' => 4,
                        default => 1
                    };
                }
                // 宗教信仰
                if(!empty($v['cust_belief'])) {
                    $v['cust_belief'] = match ($v['cust_belief']) {
                        '无' => 2,
                        '佛教' => 3,
                        '道教' => 4,
                        '伊斯兰教' => 5,
                        '基督教' => 6,
                        '其他' => 7,
                        default => 1
                    };
                }
                // 是否家床
                if(!empty($v['cust_is_bed'])) {
                    $v['cust_is_bed'] = match ($v['cust_is_bed']) {
                        '是', '家床' => 2,
                        default => 3
                    };
                }
                // 残疾类别
                if(!empty($v['cust_disability_type'])) {
                    $v['cust_disability_type'] = match ($v['cust_disability_type']) {
                        '无' => 2,
                        '视力' => 3,
                        '听力' => 4,
                        '言语' => 5,
                        '肢体' => 6,
                        '智力' => 7,
                        '精神' => 8,
                        '多重' => 9,
                        default => 1
                    };
                }
                // 残疾等级
                if(!empty($v['cust_disability_level'])) {
                    $v['cust_disability_level'] = match ($v['cust_disability_level']) {
                        '无' => 2,
                        '一级' => 3,
                        '二级' => 4,
                        '三级' => 5,
                        '四级' => 6,
                        default => 1
                    };
                }
                // 评估等级
                if(!empty($v['cust_evaluation_level'])) {
                    $v['cust_evaluation_level'] = match ($v['cust_evaluation_level']) {
                        '轻度失能' => 2,
                        '中度失能' => 3,
                        '重度失能' => 4,
                        default => 1
                    };
                }
                // 服务机构
                if(!empty($v['cust_service_site'])) {
                    $v['cust_service_site'] = match ($v['cust_service_site']) {
                        '龙翔社区', '奥运村龙祥社区服务站' => 13,
                        '科苑酒店' => 12,
                        '科苑家政服务中心' => 14,
                        '科源驿站', '海淀科源社区养老驿站', '中关村科源社区养老驿站', '中关村科源社区养老服务驿站' => 3,
                        '科学院南里五区', '奥运村科学园社区南里五区站' => 8,
                        '海淀科星社区养老驿站', '中关村科星社区养老驿站' => 4,
                        '奥运村科学园社区南里三区站' => 7,
                        '奥运村枫林绿洲社区服务站' => 9,
                        '亚运村华严北里社区服务站' => 11,
                        '中关村黄庄社区803服务站' => 5,
                        '中关村黄庄社区801服务站', '801服务点' => 6,
                        default => 3
                    };
                }
                $v['cust_content'] = '[{"label":"身份证正面","type":"upload","dict":[],"accept":"image","multiple":false,"value":null},{"label":"身份证反面","type":"upload","dict":[],"accept":"image","multiple":false,"value":null},{"label":"户口本","type":"upload","dict":[],"accept":"image","multiple":false,"value":null},{"label":"养老助残卡","type":"upload","dict":[],"accept":"image","multiple":false,"value":null},{"label":"残疾证","type":"upload","dict":[],"accept":"image","multiple":false,"value":null},{"label":"社保卡上传","type":"upload","dict":[],"accept":"image","multiple":false,"value":null},{"label":"是否适老化改造","type":"select","dict":[{"label":"未改造","value":"未改造"},{"label":"已改造","value":"已改造"}],"value":null},{"label":"经济来源","type":"select","dict":[{"label":"退休金/养老金","value":"退休金/养老金"},{"label":"子女补贴","value":"子女补贴"},{"label":"亲友资助","value":"亲友资助"},{"label":"其他补贴","value":"其他补贴"}],"value":null},{"label":"社保卡号","type":"input","value":null},{"label":"文化程度","type":"select","dict":[{"id":162,"label":"本科以上","value":"5"},{"id":160,"label":"本科","value":"3"},{"id":159,"label":"大专","value":"2"},{"id":158,"label":"高中","value":"1"},{"id":161,"label":"高中以下","value":"4"}],"dictName":"employee_education","value":null},{"label":"机构内编号","type":"input","value":null}]';
                $v['cust_city'] = '{"province":"北京市","city":"市辖区","area":"海淀区","street":"中关村街道","community":"科源社区居委会"}';

            }
        }
        // 录入新表
        $this->dao->insert('cust_user', $mainUser);
        // 返回数据
        return $this->success($mainUser);
    }


    /**
     * 生成需求调查记录
     */
    protected function createDemandRecord(): Response
    {
        // 查询所有用户
        $userList = $this->dao->search('cust_user', [
            'delete_time' => null,
        ],[
            'cust_uid',
            'cust_sex',
            'created_by',
            'cust_birth'
        ]);
        // 遍历用户
        if(!empty($userList)) {
            foreach ($userList as  $v) {
                if(!empty($v['cust_birth'])) {
                    // 通过出生日期生成年龄
                    $v['cust_birth'] = date('Y') - date('Y', strtotime($v['cust_birth']));
                    // 判断年龄区间
                    $v['cust_birth'] = match (true) {
                        $v['cust_birth'] >= 61 && $v['cust_birth'] <= 70 => '61-70',
                        $v['cust_birth'] >= 71 && $v['cust_birth'] <= 80 => '71-80',
                        $v['cust_birth'] >= 80=> '80以上',
                        default => '50-60'
                    };
                } else {
                    // 随机年龄 1 ~ 4
                    $v['cust_birth'] = match (rand(1,4)) {
                        1 => '50-60',
                        2 => '61-70',
                        3 => '71-80',
                        4 => '80以上'
                    };
                }
                // 生成数据
                $insertData = [
                  "table_id" => 'mvi5n3fsoo0weag',
                  'view_id' => "vwqy989rm41x0jz4",
                  "老人编号" => $v['cust_uid'],
                  "调查员编号" => $v['created_by'],
                  "您的性别" => match ($v['cust_sex']) {
                      1 => '男',
                      default => '女'
                  },
                  "您的年龄" => $v['cust_birth'],
                  "您有几位子女" => match (rand(1,4)) {
                      1 => '无',
                      2 => '一个',
                      3 => '两个',
                      4 => '三个及以上'
                  },
                  "您觉得您自己的经济状况如何" => match (rand(1,3)) {
                      1 => '有盈余',
                      2 => '刚好维持生活',
                      3 => '不能维持生活',
                  },
                  "您目前的居住状况" => match (rand(1,4)) {
                      1 => '独居',
                      2 => '老年夫妇同住',
                      3 => '与子女或孙辈同住',
                      4 => '其他',
                  },
                  "您现在日常生活起居由谁来照顾" => match (rand(1,6)) {
                      1 => '自己',
                      2 => '配偶',
                      3 => '儿女、子孙或其他亲戚',
                      4 => '钟点工',
                      5 => '保姆',
                      6 => '其他',
                  },
                  "您觉得您目前的身体状况如何" => match (rand(1,4)) {
                      1 => '较差，完全需要别人照顾',
                      2 => '一般，有些事情需要别人照顾',
                      3 => '较好，一般不需要别人照顾',
                      4 => '很好，完全不需要别人照顾',
                  },
                  "您日常的主要活动场所有哪些？（多选）" => match (rand(1,4)) {
                      1 => '家里',
                      2 => '社区老年活动中心',
                      3 => '小区花园广场',
                      4 => '其他',
                  },
                  "下列哪些方面是您感觉较为困难的？（多选）" => match (rand(1,4)) {
                      1 => '经济困难',
                      2 => '起居生活无人照顾',
                      3 => '家务繁重',
                      4 => '外出不便',
                      5 => '其他',
                      6 => '无',
                  },
                  "您最迫切希望驿站为您提供的服务是什么？（多选）" => match (rand(1,12)) {
                      1 => '助餐服务',
                      2 => '陪同就医',
                      3 => '居家保洁',
                      4 => '家电清洗',
                      5 => '助浴服务',
                      6 => '代办业务',
                      7 => '医护上门',
                      8 => '康复理疗',
                      9 => '健康指导',
                      10 => '文娱活动',
                      11 => '旅居旅游',
                      12 => '其他',
                  },
                  "请您在这里写下您对本驿站为老服务的其他意见或建议" => '无',
                  "调查时间" => $this->generateRandomWorkdayTimeInRange(),
                ];
                // 插入数据
                $list = NocoDbApi::send($insertData,'post');
                var_dump($list);
            }
        }
        // 返回数据
        return $this->success();
    }


    /**
     * 生成需求调查记录
     */
    protected function createRecordV2(): Response
    {
        // 查询所有用户
        $userList = $this->dao->search('cust_user', [
            'delete_time' => null,
        ],[
            'cust_uid',
            'cust_sex',
            'created_by',
            'cust_birth'
        ]);
        // 遍历用户
        if(!empty($userList)) {
            foreach ($userList as  $v) {
                if(empty($v['created_by'])) {
                    $v['created_by'] = 46;
                }
                // 生成数据
                $insertData = [
                    "table_id" => 'm7lhk9cfaz03725',
                    'view_id' => "vwdc7t8cb3i3c838",
                    "老人编号" => $v['cust_uid'],
                    "调查员编号" => $v['created_by'],
                    "您使用驿站服务多长时间了？" => match (rand(1,4)) {
                        1 => '一个月',
                        2 => '一季度',
                        3 => '半年',
                        4 => '一年及以上',
                    },
                    "您曾经使用过驿站的哪些服务？" => match (rand(1,9)) {
                        1 => '老年营养餐',
                        2 => '保洁/小时工',
                        3 => '住家保姆',
                        4 => '理发',
                        5 => '修脚/足疗',
                        6 => '助浴服务',
                        7 => '康复理疗',
                        8 => '医护服务',
                        9 => '其他服务',
                    },
                    "你对驿站的整体服务是否满意？" => match (rand(1,4)) {
                        1 => '非常满意',
                        2 => '满意',
                        3 => '一般',
                        4 => '不满意',
                    },
                    "您认为驿站服务的优势有哪些方面?（多选）" => match (rand(1,6)) {
                        1 => '安全可靠',
                        2 => '方便快捷',
                        3 => '质量优良',
                        4 => '态度友善',
                        5 => '价格合理',
                        6 => '售后保障',
                    },
                    "您认为驿站的养老服务范围是否能满足您的需求？" => match (rand(1,4)) {
                        1 => '完全满足',
                        2 => '比较满足',
                        3 => '一般',
                        4 => '不太满足',
                        5 => '完全不满足',
                    },
                    "您认为驿站养老服务的宣传是否充分?" => match (rand(1,3)) {
                        1 => '非常充分',
                        2 => '充分',
                        3 => '一般',
                    },
                    "您是否愿意推荐驿站的养老服务给其他老年人？" => match (rand(1,4)) {
                        1,2,3 => '愿意',
                        4 => '不愿意',
                    },
                    "您对驿站服务的意见与建议"=> "",
                    "调查时间" => $this->generateRandomWorkdayTimeInRange(),
                ];
                // 插入数据
                $list = NocoDbApi::send($insertData,'post');
                var_dump($list);
            }
        }
        // 返回数据
        return $this->success();
    }

    protected function generateRandomWorkdayTimeInRange() {
        $startDate = strtotime('2023-01-01');
        $endDate = strtotime('2024-08-10');
        do {
            $randomTimestamp = mt_rand($startDate, $endDate);
            $hour = date('G', $randomTimestamp);
            $minute = date('i', $randomTimestamp);
            $second = date('s', $randomTimestamp);
            $totalSeconds = $hour * 3600 + $minute * 60 + $second;
            $startWorkSeconds = 8 * 3600 + 30 * 60;
            $endWorkSeconds = 17 * 3600 + 30 * 60;
        } while (!$this->isWeekday($randomTimestamp) || $totalSeconds < $startWorkSeconds || $totalSeconds > $endWorkSeconds);
        return date('Y-m-d H:i:s', $randomTimestamp);
    }

    protected function isWeekday($timestamp) {
        return date('N', $timestamp) >= 1 && date('N', $timestamp) <= 5;
    }

    /**
     * 迁移余额账号
     */
    protected function migrateBalanceAccount(): Response
    {
        // 查询原表数据
        $mainUserBalance = Medoo::instance('v1')->select('main_user_balance', [
            'uid', // 老人id
            'recharge_amount', // 充值金额
            'spending_amount', // 消费金额
            'now_amount', // 余额
            'created_at', // 创建时间
        ], [
            'deleted_at' => null,
        ]);
        // 插入新表
        foreach($mainUserBalance as $v) {
            // 如果不存在则创建
            $balanceInfo = $this->dao->has('cust_balance',[
                'balance_id' => $v['uid'],
            ]);
            if(!$balanceInfo) {
                $this->dao->insert('cust_balance',[
                    'balance_id' => $v['uid'],
                    'balance_name' => '余额账户',
                    'cust_uid' => $v['uid'],
                    'recharge_amount' => $v['recharge_amount'],
                    'spending_amount' => $v['spending_amount'],
                    'now_amount' => $v['now_amount'],
                    'create_time' => $v['created_at'],
                ]);
            }
        }
        // 返回数据
        return $this->success();
    }

    /**
     * 迁移余额记录
     */
    protected function migrateBalanceRecord(Request $request): Response
    {
        $page = Cache::get('NUM');
        // 查询原表数据
        $mainUserBalanceRecord = Medoo::instance('v1')->select('main_user_bill', [
            'bill_id', // 账单id
            'uid', // 老人id
            'balance_id', // 余额id
            'link_id', // 金额
            'pm', // 创建时间
            'usage_date', // 创建时间
            'title', // 创建时间
            'category', // 创建时间
            'type', // 创建时间
            'subsidy_id', // 创建时间
            'subsidy_money', // 创建时间
            'number', // 创建时间
            'forward', // 创建时间
            'balance', // 创建时间
            'mark', // 创建时间
            'status', // 创建时间
            'created_by', // 创建时间
            'created_at', // 创建时间
            'deleted_at'
        ],[
            'deleted_at[!]' => null,
            'LIMIT' => [($page - 1) * 100, 200],
        ]);
        if(empty($mainUserBalanceRecord)) {
            Cache::set('NUM', 999999);
            return $this->success('迁移完成');
        }
        // 插入新表
        foreach($mainUserBalanceRecord as $v) {
            // 匹配值
            $v['category'] = match($v['category']) {
                '套餐消费','套餐增加' => '801营养餐',
                default => $v['category']
            };
            if(empty($v['usage_date'])) {
                preg_match("/\d{4}-\d{2}-\d{2}/", $v['title'], $matches);
                $month = null;
                if(!empty($matches[0])) {
                    $month = $matches[0];
                }
                $v['usage_date'] = $month;
            }
            // 查询数据是否存在
            $balanceRecordInfo = $this->dao->has('cust_balance_record',[
                'balance_record_id' => $v['bill_id'],
            ]);
            if(!$balanceRecordInfo) {
                $this->dao->insert('cust_balance_record',[
                    'balance_record_id' => $v['bill_id'],
                    'cust_uid' => $v['uid'],
                    'balance_id' => $v['uid'],
                    'link_id' => $v['link_id'],
                    'pm' => $v['pm'],
                    'usage_date' => $v['usage_date'],
                    'record_title' => $v['title'],
                    'record_details_category' => $v['category'],
                    'record_details_type' => $v['type'],
                    'subsidy_id' => $v['subsidy_id'],
                    'subsidy_money' => $v['subsidy_money'],
                    'number' => $v['number'],
                    'forward' => $v['forward'],
                    'balance' => $v['balance'],
                    'record_mark' => $v['mark'],
                    'record_status' => $v['status'],
                    'created_by' => $v['created_by'],
                    'create_time' => $v['created_at'],
                    'delete_time' => $v['deleted_at']
                ]);
            } else {
                $this->dao->update('cust_balance_record',[
                    'delete_time' => $v['deleted_at'],
                ],[
                    'balance_record_id' => $v['bill_id'],
                ]);
            }
        }
        // 返回数据
        var_dump($page);
        $page = $page + 1;
        Cache::set('NUM', $page);
        return $this->success();
    }

    /**
     * 迁移充值记录
     */
    protected function migrateRechargeRecord(): Response
    {
        $page = Cache::get('NUM');
        // 查询原表数据
        $mainUserRechargeRecord = Medoo::instance('v1')->select('main_user_bill_audit',"*",[
           'deleted_at' => null,
           'LIMIT' => [($page - 1) * 100, 200],
           'payment_id[>]' => 19021,
        ]);
        if(empty($mainUserRechargeRecord)) {
            Cache::set('NUM', 999999);
            return $this->success('迁移完成');
        }
        var_dump($page);
        // 插入新表
        foreach($mainUserRechargeRecord as $v) {
            // 查询数据是否存在
            $balanceRecordInfo = $this->dao->has('cust_bill', [
                'payment_id' => $v['payment_id'],
            ]);
            if (!$balanceRecordInfo) {
                $this->dao->insert('cust_bill', [
                    'payment_id' => $v['payment_id'],
                    'cust_uid' => $v['uid'],
                    'balance_id' => $v['uid'],
                    'payment_voucher_number' => $v['payment_voucher_number'],
                    'payment_voucher_image' => $v['payment_voucher_image'],
                    'service_type' => $v['service_type'],
                    'payment_type' => $v['payment_type'],
                    'payment_remark' => $v['service_remark'],
                    'amount_due' => $v['amount_due'],
                    'amount_paid' => $v['amount_paid'],
                    'payment_method' => $v['payment_method'],
                    'payment_time' => $v['payment_time'],
                    'payment_status' => $v['payment_status'],
                    'service_remark' => $v['service_remark'],
                    'auditor_id' => $v['auditor_id'],
                    'auditor_time' => $v['auditor_time'],
                    'auditor_remark' => $v['admin_remark'],
                    'create_time' => $v['created_at'],
                    'created_by' => $v['created_by'],
                ]);
            }
        }
        $page = $page + 1;
        Cache::set('NUM', $page);
        // 返回数据
        return $this->success();
    }
    
    /**
     * 迁移订单数据
     */
    public function migrateOrder(): Response
    {
        $startTime = microtime(true);
        $page = Cache::get('NUM');
        // 查询原表数据
        $mainUserOrder = Medoo::instance('v1')->select('food_order',"*",[
           'deleted_at' => null,
           'LIMIT' => [($page - 1) * 10, 10],
        ]);
        if(empty($mainUserOrder)) {
            Cache::set('NUM', 999999);
            return $this->success('迁移完成');
        }
        var_dump($page);
        // 插入新表
        foreach($mainUserOrder as $v) {
            // 查询数据是否存在
            $balanceRecordInfo = $this->dao->has('order_food', [
                'order_id' => $v['order_id'],
            ]);
            if(!$balanceRecordInfo) {
                var_dump($v['order_id']);
                // 查询子订单
                $subOrder = Medoo::instance('v1')->select('food_order_item',"*",[
                    'deleted_at' => null,
                    'order_id' => $v['order_id'],
                ]);
                if(empty($subOrder)) {
                    var_dump('订单异常'.$v['order_id']);
                    return $this->success('迁移完成');
                }
                $v['delivery_site'] = match($v['delivery_site']) {
                    '中关村科源社区养老驿站' => '海淀科源社区养老驿站',
                    '中关村科星社区养老驿站' => '海淀科星社区养老驿站',
                    '中关村黄庄社区803服务站' => '803服务点',
                    '中关村黄庄社区801服务站' => '801服务点',
                    '奥运村科学园社区南里三区站' => '科学院南里三区',
                    '奥运村科学园社区南里五区站' => '科学院南里五区',
                    '奥运村枫林绿洲社区服务站' => '枫林绿洲',
                    '奥运村博世祥园社区站' => '博世祥园',
                    '亚运村华严北里社区服务站' => '华严北里',
                    '中关村科苑酒店' => '科苑酒店',
                    '奥运村龙祥社区服务站' => '龙翔社区',
                    '亚运村祁家豁子服务站' => '亚运村祁家豁子服务站',
                    default => $v['delivery_site']
                };
                $orderFoodItem = [];
                $total_num = 0;
                foreach ($subOrder as $vv) {
                    $value = [];
                    // 设置子订单数据
                    $value['order_id'] = $v['order_id'];
                    $value['cust_uid'] = $v['user_id'];
                    $value['employee_uid'] = $v['staff_id'];
                    $value['delivery_option'] = $v['delivery_option'];
                    $value['delivery_site'] = $v['delivery_site'];
                    $value['delivery_time'] = $v['package_order_time'];
                    $value['num'] = $vv['num'];
                    $value['price'] = $vv['price'];
                    $value['pay_price'] = $vv['pay_price'];
                    $value['package_id'] = $vv['package_id'];
                    $value['package_type'] = $v['package_type'];
                    $value['package_name'] = $vv['package_name'];
                    $value['created_by'] = $v['created_by'];
                    $value['create_time'] = $v['created_at'];
                    $orderFoodItem[] = $value;
                    $total_num += $vv['num'];
                }
                $this->dao->insert('order_food', [
                    'order_id' => $v['order_id'],
                    'cust_uid' => $v['user_id'],
                    'employee_uid' => $v['staff_id'],
                    'delivery_num' => $v['delivery_num'],
                    'delivery_option' => $v['delivery_option'],
                    'delivery_site' => $v['delivery_site'],
                    'delivery_time' => $v['package_order_time'],
                    'package_type' => $v['package_type'],
                    'total_num' => $total_num,
                    'pay_price' => $v['payment_amount'],
                    'sum_amount' => $v['payment_amount'] + $v['delivery_cost'],
                    'cust_delivery_fee' => $v['delivery_cost'],
                    'remark' => $v['remark'],
                    'order_status' => 1,
                    'discounts_text' => $v['discount_setting'],
                    'discounts_price' => $v['discount_price'],
                    'created_by' => $v['created_by'],
                    'create_time' => $v['created_at'],
                    'user_longitude' => $v['user_longitude'],
                    'user_latitude' => $v['user_latitude'],
                    'user_address' => $v['user_address'],
                ]);
                // 插入子订单
                $this->dao->insert('order_food_item', $orderFoodItem);
            }
        }
        $page = $page + 1;
        Cache::set('NUM', $page);
        // 返回数据
        $finishTime = microtime(true);
        $costTime   = round($finishTime - $startTime,6);
        return $this->success(date('Y-m-d H:i:s').'用时：'.$costTime.'秒');
    }

    /**
     * 迁移套餐数据
     */
    protected function migratePackage(): Response
    {
        $page = Cache::get('NUM');
        // 查询原表数据
        $mainUserOrder = Medoo::instance('v1')->select('food_package',"*",[
            'deleted_at' => null,
            'LIMIT' => [($page - 1) * 100, 200],
        ]);
        var_dump($page);
        if(empty($mainUserOrder)) {
            Cache::set('NUM', 999999);
            return $this->success('迁移完成');
        }
        // 插入新表
        foreach($mainUserOrder as $v) {
            if($v['day_time'] >= '2024-12-31') {
                continue;
            }
            // 查询是否存在，不存在则创建
            if(!$this->dao->has('food_package', ['package_id' => $v['package_id']])){
                $this->dao->insert('food_package', [
                    'package_id' => $v['package_id'],
                    'config_id' => match($v['config_id']) {
                        21 => 36,  // A1
                        22 => 38,
                        23 => 39,
                        24 => 40,
                        25 => 41,
                        26 => 42,
                        27 => 43,
                        30 => 44,
                        31 => 45,
                        32 => 46,
                        33 => 47,
                        34 => 48,
                        35 => 49,
                        36 => 50,
                        37 => 51,
                        38 => 52,
                        default => $v['config_id']
                    },
                    'name' => $v['name'],
                    'price' => $v['price'],
                    'day_time' => $v['day_time'],
                    'sort' => $v['sort'],
                    'created_by' => $v['created_by'],
                    'create_time' => $v['created_at'],
                ]);
            }
        }
        $page = $page + 1;
        Cache::set('NUM', $page);
        // 返回数据
        return $this->success();
    }
}
