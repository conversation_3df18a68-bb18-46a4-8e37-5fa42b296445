<?php
namespace app\controller\tool;

use support\Request;
use support\Response;
use yzh52521\EasyHttp\Http;

class IndexController
{
    private $config = [
        'api_key' => '0zQnOET9EuDQm4qfkaHYbAOC',
        'secret_key' => 'B2AIyIlZC98XdTVHTst7gIH1aRur8vBm',
        'aes_key' => '' // 如果启用了加密
    ];

    /**
     * OCR识别接口
     */
    public function recognize(Request $request): Response
    {
        try {
            $type = $request->input('type');
            $url = $request->input('url');

            if (!in_array($type, ['id_card', 'id_card_back', 'bank_card', 'elderly_card'])) {
                return json(['code' => 400, 'msg' => '不支持的识别类型']);
            }

            if (empty($url)) {
                return json(['code' => 400, 'msg' => 'URL不能为空']);
            }

            // 获取access token
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return json(['code' => 500, 'msg' => '获取access_token失败']);
            }

            // 根据类型调用不同的识别接口
            $result = match($type) {
                'id_card' => $this->recognizeIdCard($url, $accessToken, 'front'),
                'id_card_back' => $this->recognizeIdCard($url, $accessToken, 'back'),
                'bank_card' => $this->recognizeBankCard($url, $accessToken),
                'elderly_card' => $this->recognizeElderlyCard($url, $accessToken),
                default => throw new \Exception('不支持的识别类型'),
            };

            return json(['code' => 200, 'msg' => 'success', 'data' => $result]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取access token，使用Redis缓存
     */
    private function getAccessToken(): ?string
    {
        return getRedisCache('baidu_ocr_token', function() {
            $url = sprintf(
                'https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s',
                $this->config['api_key'],
                $this->config['secret_key']
            );

            $response = httpPost($url, [], ['Content-Type: application/json']);
            return $response['access_token'] ?? null;
        }, 2502000); // 30天缓存
    }

    /**
     * 身份证识别
     */
    private function recognizeIdCard(string $url, string $accessToken, string $side = 'front'): array
    {
        $apiUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token=" . $accessToken;
        
        $params = [
            'url' => $url,
            'id_card_side' => $side,
            'detect_risk' => 'false'
        ];
        
        $result = Http::post($apiUrl, $params)->array();
        
        if (isset($result['words_result'])) {
            $data = $result['words_result'];
            
            if ($side === 'front') {
                // 正面识别
                // 计算年龄
                $birth = str_replace(['年','月','日'], ['-','-',''], $data['出生']['words']);
                $age = date('Y') - date('Y', strtotime($birth));
                
                return [
                    'name' => $data['姓名']['words'],
                    'gender' => $data['性别']['words'],
                    'nation' => $data['民族']['words'], // 新增民族字段
                    'birth' => $data['出生']['words'],
                    'address' => $data['住址']['words'], // 新增住址字段
                    'id_number' => $data['公民身份号码']['words'],
                    'age' => (string)$age
                ];
            } else {
                // 反面识别
                // 格式化日期
                $issueDate = $this->formatIdCardDate($data['签发日期']['words']);
                $expiryDate = $this->formatIdCardDate($data['失效日期']['words']);
                
                return [
                    'issue_authority' => $data['签发机关']['words'],
                    'issue_date' => $issueDate,
                    'expiry_date' => $expiryDate,
                    'valid_date' => $issueDate . ' - ' . $expiryDate
                ];
            }
        }
        
        throw new \Exception('身份证识别失败');
    }

    /**
     * 格式化身份证日期
     * 将YYYYMMDD格式转换为Y-m-d格式
     */
    private function formatIdCardDate(string $dateStr): string
    {
        if (strlen($dateStr) == 8) {
            $year = substr($dateStr, 0, 4);
            $month = substr($dateStr, 4, 2);
            $day = substr($dateStr, 6, 2);
            return $year . '-' . $month . '-' . $day;
        }
        return $dateStr;
    }

    /**
     * 银行卡识别
     */
    private function recognizeBankCard(string $url, string $accessToken): array
    {
        $apiUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/bankcard?access_token=" . $accessToken;
        
        $params = [
            'url' => $url
        ];

        $result = Http::post($apiUrl, $params)->array();
        if (isset($result['result'])) {
            return [
                'card_number'=>str_replace(' ', '', trim($result['result']['bank_card_number'])),
                'issuing_bank'=>trim($result['result']['bank_name'])
            ];
        }
        
        throw new \Exception('银行卡识别失败');
    }

    /**
     * 社保卡识别
     */
    private function recognizeElderlyCard(string $url, string $accessToken): array
    {
        $apiUrl = "https://aip.baidubce.com/rest/2.0/ocr/v1/numbers?access_token=" . $accessToken;
        
        $params = [
            'url' => $url
        ];

        $result = Http::post($apiUrl, $params)->array();
        if (isset($result['words_result'])) {
            return [
                'card_number' => trim($result['words_result']['1']['words'] ?? '')
            ];
        }
        
        throw new \Exception('社保卡识别失败');
    }
}