<?php
namespace app\controller\tool;

use support\Request;
use support\Response;
use yzh52521\EasyHttp\Http;

class FileController
{
    /**
     * @var string[]
     */
    private static array $config = [
        'key' => '1fPOVmlIhgCsKTwv1IDP',
        'secret' => 'LOHTMLkAURU8DL9iTrg4uURrKGYxZ8EbvRLeElIk',
        'region' => 'cn-beijing',
        'version' => 'latest',
        'endpoint' => 'https://img.zkshlm.com',
        'bucket' => 'zksh',
    ];

    /**
     * 上传文件
     */
    public function upload(Request $request) : Response
    {
        // 验证key
        if($request->input('key') !== 'aJBotUChDI7k1TFeokjGR4Qtogsjh49z') {
            return json(['code' => 1, 'msg' => '非法上传']);
        }
        $file = $request->file('file');
        if (!$file) {
            return json(['code' => 1, 'msg' => '请选择文件']);
        }

        $ext = $file->getUploadExtension();
        $filename = sprintf('pension_%s_%s.%s',  'temp', uniqid(), $ext);

        // 文件大小不能超过8M
        $fileSize = $file->getSize(); // 返回字节
        if($fileSize >= 8 * 1024 * 1024) {
            return json(['code' => 11, 'msg' => '文件大小不能超过8M']);
        }
        $imageUrl = self::uploadFile($file, $filename);
        if (!$imageUrl) {
            return json(['code' => 1, 'msg' => '文件上传失败']);
        }
        return json([
            'code' => 0,
            'msg' => '上传成功',
            'data' => ['url' => $imageUrl]
        ]);
    }


    /**
     * s3上传文件
     * @param $file
     * @param $name
     * @return string
     */
    private static function uploadFile($file,$name): string
    {
        // minio配置信息
        $minioConfig = self::$config;
        $objectClient = new \Minoi\Client\ObjectClient($minioConfig);
        // 上传
        $objectClient->putObjectBySavePath($file, "pension/" . $name);
        // 返回文件路径
        return $minioConfig['endpoint'] . '/zksh/pension/' . $name;
    }

    /**
     * 删除文件
     * @param $name
     * @return bool
     */
    private static function deleteFile($name): bool
    {
        // minio配置信息
        $minioConfig = self::$config;
        $objectClient = new \Minoi\Client\ObjectClient($minioConfig);
        $name = 'hotel/' . $name;
        return $objectClient->removeObject($name);
    }
}