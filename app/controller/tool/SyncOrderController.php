<?php

namespace app\controller\tool;

use app\cust\model\CustBalance;
use app\cust\model\CustBalanceRecord;
use app\domain\service\FoodOrderService;
use app\domain\service\SmartService;
use app\utils\NocoDbApi;
use app\utils\Water;
use app\validate\CommonValidate;
use Exception;
use Medoo\Medoo;
use plugin\saiadmin\utils\Cache;
use Ramsey\Uuid\Uuid;
use support\Redis;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use Webman\Captcha\CaptchaBuilder;
use Webman\Captcha\PhraseBuilder;
use yzh52521\EasyHttp\Http;
use app\utils\Excel;


class SyncOrderController
{

    /**
     * 新表系统同步到旧版系统套餐信息
     */
    public function syncPackage(): Response
    {
        try {
            // 旧db
            $oldDb = \Webman\Medoo\Medoo::instance('v1');
            // 新db
            $newDb = \Webman\Medoo\Medoo::instance('default');

            // 使用 'next monday' 获取下周一
            $nextWeekStart = date('Y-m-d', strtotime('next monday'));

            // 使用 'sunday next week' 获取下周的周日
            $nextWeekEnd = date('Y-m-d', strtotime('sunday next week'));

            $syncResults = [
                'next_week_start' => $nextWeekStart,
                'next_week_end' => $nextWeekEnd,
                'new_packages_found' => 0,
                'old_packages_found' => 0,
                'synced_packages' => 0,
                'synced_package_items' => 0,
                'errors' => []
            ];

            // 1. 查询新版下周套餐是否存在
            $newPackages = $newDb->select('food_package', [
                'package_id',
                'config_id',
                'name',
                'price',
                'day_time',
                'sort',
                'created_by',
                'create_time'
            ], [
                'day_time[>=]' => $nextWeekStart,
                'day_time[<=]' => $nextWeekEnd,
                'delete_time' => null
            ]);

            $syncResults['new_packages_found'] = count($newPackages);

            if (empty($newPackages)) {
                // 2. 查询旧版下周套餐是否存在
                $oldPackages = $oldDb->select('food_package', [
                    'package_id',
                    'config_id',
                    'name',
                    'day_time'
                ], [
                    'day_time[>=]' => $nextWeekStart,
                    'day_time[<=]' => $nextWeekEnd,
                    'deleted_at' => null
                ]);

                $syncResults['old_packages_found'] = count($oldPackages);

                if (empty($oldPackages)) {
                    return $this->success($syncResults, '新版下周套餐数据不存在，无需同步');
                } else {
                    // 旧系统已存在下周套餐数据，需要检查并同步套餐子项的商品名称
                    $this->syncPackageItemNames($newDb, $oldDb, $nextWeekStart, $nextWeekEnd, $syncResults);
                    return $this->success($syncResults, '旧系统已存在下周套餐数据，已检查并同步商品名称');
                }
            }

            // 3. 同步套餐数据（从新表到旧表）
            foreach ($newPackages as $newPackage) {
                try {
                    // 检查旧系统中是否已存在该套餐
                    $existingPackage = $oldDb->get('food_package', 'package_id', [
                        'name' => $newPackage['name'],
                        'day_time' => $newPackage['day_time'],
                        'deleted_at' => null
                    ]);
                    $oldPackageId = null;

                    if ($existingPackage) {
                        // 套餐已存在，使用现有的套餐ID
                        $oldPackageId = $existingPackage;
                    } else {
                        // 套餐不存在，创建新套餐
                        // 反向映射config_id（新表到旧表）
                        $oldConfigId = $this->reverseMapConfigId($oldDb, $newPackage['config_id']);

                        // 插入旧系统套餐
                        $oldPackageData = [
                            'config_id' => $oldConfigId,
                            'name' => $newPackage['name'],
                            'price' => $newPackage['price'],
                            'day_time' => $newPackage['day_time'],
                            'sort' => $newPackage['sort'],
                            'created_by' => $newPackage['created_by'] ?? 1,
                            'created_at' => $newPackage['create_time'] ?? date('Y-m-d H:i:s')
                        ];

                        $oldDb->insert('food_package', $oldPackageData);
                        $oldPackageId = $oldDb->id();
                        $syncResults['synced_packages']++;
                    }

                    // 无论套餐是否已存在，都要同步套餐子项（从新表到旧表）
                    $itemCount = $this->syncPackageItemsToOld($newDb, $oldDb, $newPackage['package_id'], $oldPackageId);
                    $syncResults['synced_package_items'] += $itemCount;

                } catch (\Exception $e) {
                    $syncResults['errors'][] = "套餐 {$newPackage['name']} 同步失败: " . $e->getMessage();
                }
            }

            return $this->success($syncResults, '套餐同步完成');

        } catch (\Exception $e) {
            return $this->fail('套餐同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 同步套餐子项商品名称
     * @param $newDb
     * @param $oldDb
     * @param string $nextWeekStart
     * @param string $nextWeekEnd
     * @param array &$syncResults
     */
    private function syncPackageItemNames($newDb, $oldDb, $nextWeekStart, $nextWeekEnd, &$syncResults)
    {
        try {
            // 查询新系统下周的套餐
            $newPackages = $newDb->select('food_package', [
                'package_id',
                'name',
                'day_time'
            ], [
                'day_time[>=]' => $nextWeekStart,
                'day_time[<=]' => $nextWeekEnd,
                'delete_time' => null
            ]);

            $syncResults['checked_packages'] = 0;
            $syncResults['updated_items'] = 0;
            $syncResults['item_errors'] = [];

            foreach ($newPackages as $newPackage) {
                // 查找旧系统中对应的套餐
                $oldPackage = $oldDb->get('food_package', [
                    'package_id',
                    'name',
                    'day_time'
                ], [
                    'name' => $newPackage['name'],
                    'day_time' => $newPackage['day_time'],
                    'deleted_at' => null
                ]);

                if (!$oldPackage) {
                    continue; // 旧系统中没有对应套餐，跳过
                }

                $syncResults['checked_packages']++;

                // 获取新系统套餐的子项
                $newPackageItems = $newDb->select('food_package_item', [
                    'food_id',
                    'food_name',
                    'food_cate'
                ], [
                    'package_id' => $newPackage['package_id'],
                    'delete_time' => null
                ]);

                // 获取旧系统套餐的子项
                $oldPackageItems = $oldDb->select('food_package_item', [
                    'id',
                    'food_id',
                    'food_name',
                    'food_cate'
                ], [
                    'package_id' => $oldPackage['package_id'],
                    'deleted_at' => null
                ]);

                // 比较并更新商品名称
                $this->compareAndUpdatePackageItems($oldDb, $newPackageItems, $oldPackageItems, $oldPackage['package_id'], $syncResults);
            }

        } catch (\Exception $e) {
            $syncResults['item_errors'][] = "检查套餐子项失败: " . $e->getMessage();
        }
    }

    /**
     * 比较并更新套餐子项
     * @param $oldDb
     * @param array $newPackageItems
     * @param array $oldPackageItems
     * @param int $oldPackageId
     * @param array &$syncResults
     */
    private function compareAndUpdatePackageItems($oldDb, $newPackageItems, $oldPackageItems, $oldPackageId, &$syncResults)
    {
        // 如果旧版套餐子项为空，需要添加所有新版套餐子项
        if (empty($oldPackageItems)) {
            $this->addAllPackageItems($oldDb, $newPackageItems, $oldPackageId, $syncResults);
            return;
        }

        // 创建新系统商品的映射表（按food_cate分组）
        $newItemsMap = [];
        foreach ($newPackageItems as $newItem) {
            $key = $newItem['food_cate']; // 使用食品分类作为匹配键
            $newItemsMap[$key] = $newItem;
        }

        // 检查旧系统的每个商品
        foreach ($oldPackageItems as $oldItem) {
            $key = $oldItem['food_cate'];

            // 如果新系统中有相同分类的商品
            if (isset($newItemsMap[$key])) {
                $newItem = $newItemsMap[$key];

                // 检查商品名称是否不一致
                if ($oldItem['food_name'] !== $newItem['food_name']) {
                    try {
                        // 更新旧系统中的商品名称
                        $oldDb->update('food_package_item', [
                            'food_name' => $newItem['food_name'],
                            'updated_at' => date('Y-m-d H:i:s')
                        ], [
                            'id' => $oldItem['id']
                        ]);

                        // 同时更新food_content表中的商品名称（如果存在）
                        $this->updateFoodContentName($oldDb, $oldItem['food_id'], $newItem['food_name']);

                        $syncResults['updated_items']++;

                        // 记录更新日志
                        error_log("套餐子项商品名称已更新: {$oldItem['food_name']} -> {$newItem['food_name']}");

                    } catch (\Exception $e) {
                        $syncResults['item_errors'][] = "更新商品名称失败 ({$oldItem['food_name']} -> {$newItem['food_name']}): " . $e->getMessage();
                    }
                }
            }
        }

        // 检查新系统中是否有旧系统没有的商品（需要添加）
        foreach ($newPackageItems as $newItem) {
            $found = false;
            foreach ($oldPackageItems as $oldItem) {
                if ($oldItem['food_cate'] === $newItem['food_cate']) {
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                // 新系统有但旧系统没有的商品，需要添加到旧系统
                try {
                    // 反向映射food_id
                    $oldFoodId = $this->reversMapFoodId($oldDb, $newItem['food_id'], $newItem['food_name'], $newItem['food_cate']);

                    // 插入新的套餐子项
                    $oldDb->insert('food_package_item', [
                        'food_id' => $oldFoodId,
                        'food_name' => $newItem['food_name'],
                        'food_cate' => $newItem['food_cate'],
                        'package_id' => $oldPackageId,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                    $syncResults['updated_items']++;
                    error_log("新增套餐子项: {$newItem['food_name']} ({$newItem['food_cate']})");

                } catch (\Exception $e) {
                    $syncResults['item_errors'][] = "新增商品失败 ({$newItem['food_name']}): " . $e->getMessage();
                }
            }
        }
    }

    /**
     * 添加所有套餐子项（当旧版套餐子项为空时）
     * @param $oldDb
     * @param array $newPackageItems
     * @param int $oldPackageId
     * @param array &$syncResults
     */
    private function addAllPackageItems($oldDb, $newPackageItems, $oldPackageId, &$syncResults)
    {
        if (empty($newPackageItems)) {
            return;
        }

        foreach ($newPackageItems as $newItem) {
            try {
                // 反向映射food_id
                $oldFoodId = $this->reversMapFoodId($oldDb, $newItem['food_id'], $newItem['food_name'], $newItem['food_cate']);

                // 插入新的套餐子项
                $oldDb->insert('food_package_item', [
                    'food_id' => $oldFoodId,
                    'food_name' => $newItem['food_name'],
                    'food_cate' => $newItem['food_cate'],
                    'package_id' => $oldPackageId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                $syncResults['updated_items']++;
                error_log("添加套餐子项到空套餐: {$newItem['food_name']} ({$newItem['food_cate']})");

            } catch (\Exception $e) {
                $syncResults['item_errors'][] = "添加套餐子项失败 ({$newItem['food_name']}): " . $e->getMessage();
            }
        }
    }

    /**
     * 更新food_content表中的商品名称
     * @param $oldDb
     * @param int $foodId
     * @param string $newFoodName
     */
    private function updateFoodContentName($oldDb, $foodId, $newFoodName)
    {
        try {
            // 检查food_content表中是否存在该商品
            $existingFood = $oldDb->get('food_content', 'id', [
                'id' => $foodId,
                'deleted_at' => null
            ]);

            if ($existingFood) {
                // 更新商品名称
                $oldDb->update('food_content', [
                    'name' => $newFoodName,
                    'updated_at' => date('Y-m-d H:i:s')
                ], [
                    'id' => $foodId
                ]);
            }
        } catch (\Exception $e) {
            // 记录错误但不影响主流程
            error_log("更新food_content表失败: " . $e->getMessage());
        }
    }

    /**
     * 同步旧版微信支付订单
     */
    public function syncOldOrder(): Response
    {
        try {
            // 旧db
            $oldDb = \Webman\Medoo\Medoo::instance('v1');
            // 新db
            $newDb = \Webman\Medoo\Medoo::instance('default');

            // 计算上周一的日期
            $lastMonday = date('Y-m-d', strtotime('last monday', strtotime('-1 week')));

            // 配送点映射关系 (旧表值 => 新表值)
            $deliverySiteMapping = [
                '中关村黄庄社区801服务站' => '801服务点',
                '中关村黄庄社区803服务站' => '803服务点',
                '中关村科源社区养老驿站' => '海淀科源社区养老驿站',
                '中关村科星社区养老驿站' => '海淀科星社区养老驿站',
                '中关村科苑酒店' => '科苑酒店',
                '奥运村枫林绿洲社区服务站' => '枫林绿洲',
                '奥运村科学园社区南里三区站' => '科学院南里三区',
                '奥运村科学园社区南里五区站' => '科学院南里五区',
                '奥运村博世祥园社区站' => '博世祥园',
                '奥运村龙祥社区服务站' => '龙翔社区',
                '亚运村华严北里社区服务站' => '华严北里',
            ];

            // 查询旧版订单 food_order, 查询条件delivery_start_date >= 上周一
            $oldOrders = $oldDb->select('food_order', [
                'order_id',
                'user_id',
                'staff_id',
                'delivery_num',
                'delivery_option',
                'delivery_site',
                'package_order_time',
                'package_type',
                'payment_amount',
                'delivery_cost',
                'discount_setting',
                'discount_price',
                'remark',
                'created_by',
                'created_at',
                'user_longitude',
                'user_latitude',
                'user_address'
            ], [
                'delivery_start_date[>=]' => $lastMonday,
                'deleted_at' => null
            ]);

            $syncResults = [
                'total_orders' => count($oldOrders),
                'synced_orders' => 0,
                'created_users' => 0,
                'skipped_orders' => 0,
                'errors' => []
            ];

            foreach ($oldOrders as $oldOrder) {
                try {
                    // 处理用户映射
                    $userMappingResult = $this->handleUserMapping($oldDb, $newDb, $oldOrder['user_id']);
                    if (!$userMappingResult['cust_uid']) {
                        $syncResults['errors'][] = "订单 {$oldOrder['order_id']} 用户映射失败";
                        $syncResults['skipped_orders']++;
                        continue;
                    }

                    $custUid = $userMappingResult['cust_uid'];
                    if ($userMappingResult['is_new_user']) {
                        $syncResults['created_users']++;
                    }

                    // 检查新系统中是否已存在该订单
                    $existingOrder = $newDb->get('order_food', 'order_id', [
                        'cust_uid' => $custUid,
                        'delivery_time' => $oldOrder['package_order_time']
                    ]);

                    if ($existingOrder) {
                        // 订单已存在，检查数据是否匹配
                        $this->validateOrderData($newDb, $oldOrder, $existingOrder, $deliverySiteMapping);
                        $syncResults['skipped_orders']++;
                        continue;
                    }

                    // 同步订单数据
                    $this->syncOrderData($oldDb, $newDb, $oldOrder, $custUid, $deliverySiteMapping);
                    $syncResults['synced_orders']++;

                } catch (\Exception $e) {
                    $syncResults['errors'][] = "订单 {$oldOrder['order_id']} 同步失败: " . $e->getMessage();
                    $syncResults['skipped_orders']++;
                }
            }

            return $this->success($syncResults);

        } catch (\Exception $e) {
            return $this->fail('同步失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理用户映射
     * @param $oldDb
     * @param $newDb
     * @param $oldUserId
     * @return array
     */
    private function handleUserMapping($oldDb, $newDb, $oldUserId)
    {
        // 首先检查cust_sync表中是否已存在该用户的映射关系
        $syncRecord = $newDb->get('cust_sync', [
            'old_cust_uid',
            'new_cust_uid'
        ], [
            'old_cust_uid' => $oldUserId
        ]);

        if ($syncRecord && $syncRecord['new_cust_uid']) {
            // 验证新用户是否真实存在
            $existingUser = $newDb->get('cust_user', 'cust_uid', [
                'cust_uid' => $syncRecord['new_cust_uid'],
                'delete_time' => null
            ]);

            if ($existingUser) {
                return [
                    'cust_uid' => $syncRecord['new_cust_uid'],
                    'is_new_user' => false
                ];
            } else {
                // 删除记录
                $newDb->delete('cust_sync', [
                    'old_cust_uid' => $oldUserId
                ]);
            }
        }

        // 如果不存在，从旧系统获取用户信息并创建新用户
        $oldUser = $oldDb->get('main_user', [
            'id',
            'v1', // 姓名
            'v11', // 手机号
            'v4', // 性别
            'v5', // 身份类别
            'v8', // 出生年月
            'v22', // 所属片区
            'v23', // 是否家床
            'v25', // 社区
            'v26', // 居住详细地址
            'v36', // 评估等级
            'v37', // 服务机构
            'created_by',
            'created_at'
        ], [
            'id' => $oldUserId,
            'deleted_at' => null
        ]);

        if (!$oldUser) {
            return [
                'cust_uid' => null,
                'is_new_user' => false
            ];
        }

        // 创建新用户，使用自增ID而不是旧系统ID
        $newUserData = [
            'cust_name' => $oldUser['v1'] ?? '',
            'cust_private_phone' => $oldUser['v11'] ?? '',
            'cust_sex' => $this->mapGender($oldUser['v4'] ?? ''),
            'cust_identity_type' => $this->mapIdentityType($oldUser['v5'] ?? ''),
            'cust_birth' => $oldUser['v8'] ?? null,
            'cust_area' => $oldUser['v22'] ?? '',
            'cust_is_bed' => $this->mapBedType($oldUser['v23'] ?? ''),
            'cust_community' => $oldUser['v25'] ?? '',
            'cust_live_address' => $oldUser['v26'] ?? '',
            'cust_evaluation_level' => $this->mapEvaluationLevel($oldUser['v36'] ?? ''),
            'cust_service_site' => $this->mapServiceSite($oldUser['v37'] ?? ''),
            'cust_city' => '{"province":"北京市","city":"市辖区","area":"海淀区","street":"中关村街道","community":"科源社区居委会"}',
            'cust_content' => '[{"label":"身份证正面","type":"upload","dict":[],"accept":"image","multiple":false,"value":null}]',
            'created_by' => $oldUser['created_by'] ?? 1,
            'create_time' => $oldUser['created_at'] ?? date('Y-m-d H:i:s')
        ];

        try {
            // 开始事务
            $newDb->pdo->beginTransaction();

            // 插入新用户并获取新的用户ID
            $newDb->insert('cust_user', $newUserData);
            $newCustUid = $newDb->id();

            // 创建用户映射记录
            $syncData = [
                'old_cust_uid' => $oldUser['id'],
                'new_cust_uid' => $newCustUid,
                'create_time' => date('Y-m-d H:i:s')
            ];
            $newDb->insert('cust_sync', $syncData);

            // 提交事务
            $newDb->pdo->commit();

            return [
                'cust_uid' => $newCustUid,
                'is_new_user' => true
            ];
        } catch (\Exception $e) {
            // 回滚事务
            $newDb->pdo->rollBack();
            return [
                'cust_uid' => null,
                'is_new_user' => false
            ];
        }
    }

    /**
     * 验证订单数据
     * @param $newDb
     * @param $oldOrder
     * @param $existingOrderId
     * @param $deliverySiteMapping
     */
    private function validateOrderData($newDb, $oldOrder, $existingOrderId, $deliverySiteMapping)
    {
        $existingOrder = $newDb->get('order_food', '*', [
            'order_id' => $existingOrderId
        ]);

        if (!$existingOrder) {
            return;
        }

        // 检查配送点映射
        $expectedDeliverySite = $deliverySiteMapping[$oldOrder['delivery_site']] ?? $oldOrder['delivery_site'];
        if ($existingOrder['delivery_site'] !== $expectedDeliverySite) {
            // 可以记录不匹配的情况，但不抛出异常
            error_log("订单 {$oldOrder['order_id']} 配送点不匹配: 期望 {$expectedDeliverySite}, 实际 {$existingOrder['delivery_site']}");
        }

        // 检查金额
        if (abs($existingOrder['pay_price'] - $oldOrder['payment_amount']) > 0.01) {
            error_log("订单 {$oldOrder['order_id']} 金额不匹配");
        }
    }

    /**
     * 同步订单数据
     * @param $oldDb
     * @param $newDb
     * @param $oldOrder
     * @param $custUid
     * @param $deliverySiteMapping
     */
    private function syncOrderData($oldDb, $newDb, $oldOrder, $custUid, $deliverySiteMapping)
    {
        // 获取旧订单的子项
        $oldOrderItems = $oldDb->select('food_order_item', [
            'order_id',
            'package_id',
            'package_name',
            'num',
            'price',
            'pay_price'
        ], [
            'order_id' => $oldOrder['order_id'],
            'deleted_at' => null
        ]);

        if (empty($oldOrderItems)) {
            throw new \Exception("订单 {$oldOrder['order_id']} 没有找到子项");
        }

        // 映射配送点 (旧表值 => 新表值)
        $deliverySite = $deliverySiteMapping[$oldOrder['delivery_site']] ?? $oldOrder['delivery_site'];

        // 计算总数量
        $totalNum = array_sum(array_column($oldOrderItems, 'num'));

        // 插入主订单
        $orderData = [
            'order_id' => $oldOrder['order_id'],
            'cust_uid' => $custUid,
            'employee_uid' => $oldOrder['staff_id'] ?? null,
            'delivery_num' => $oldOrder['delivery_num'] ?? 1,
            'delivery_option' => $oldOrder['delivery_option'] ?? '',
            'delivery_site' => $deliverySite,
            'delivery_time' => $oldOrder['package_order_time'],
            'package_type' => $oldOrder['package_type'] ?? '',
            'total_num' => $totalNum,
            'pay_price' => $oldOrder['payment_amount'] ?? 0,
            'sum_amount' => ($oldOrder['payment_amount'] ?? 0) + ($oldOrder['delivery_cost'] ?? 0),
            'cust_delivery_fee' => $oldOrder['delivery_cost'] ?? 0,
            'remark' => $oldOrder['remark'] ?? '',
            'order_status' => 0, // 默认未完成
            'discounts_text' => $oldOrder['discount_setting'] ?? '',
            'discounts_price' => $oldOrder['discount_price'] ?? 0,
            'created_by' => $oldOrder['created_by'] ?? 1,
            'create_time' => $oldOrder['created_at'] ?? date('Y-m-d H:i:s'),
            'user_longitude' => $oldOrder['user_longitude'] ?? null,
            'user_latitude' => $oldOrder['user_latitude'] ?? null,
            'user_address' => $oldOrder['user_address'] ?? ''
        ];

        $newDb->insert('order_food', $orderData);

        // 插入订单子项
        $orderItemsData = [];
        foreach ($oldOrderItems as $item) {
            // 通过套餐名称和配送时间查询新表中的package_id
            $newPackageId = $this->getNewPackageId($newDb, $item['package_name'], $oldOrder['package_order_time']);

            $orderItemsData[] = [
                'order_id' => $oldOrder['order_id'],
                'cust_uid' => $custUid,
                'employee_uid' => $oldOrder['staff_id'] ?? null,
                'delivery_option' => $oldOrder['delivery_option'] ?? '',
                'delivery_site' => $deliverySite,
                'delivery_time' => $oldOrder['package_order_time'],
                'num' => $item['num'],
                'price' => $item['price'],
                'pay_price' => $item['pay_price'],
                'package_id' => $newPackageId ?: $item['package_id'], // 使用新的package_id，如果找不到则使用原值
                'package_type' => $oldOrder['package_type'] ?? '',
                'package_name' => $item['package_name'],
                'created_by' => $oldOrder['created_by'] ?? 1,
                'create_time' => $oldOrder['created_at'] ?? date('Y-m-d H:i:s')
            ];
        }

        $newDb->insert('order_food_item', $orderItemsData);
    }

    /**
     * 获取新表中的package_id
     * @param $newDb
     * @param string $packageName
     * @param string $deliveryTime
     * @return int|null
     */
    private function getNewPackageId($newDb, $packageName, $deliveryTime)
    {
        // 从配送时间中提取日期部分作为day_time
        $dayTime = date('Y-m-d', strtotime($deliveryTime));

        // 查询新表中匹配的套餐
        $newPackage = $newDb->get('food_package', 'package_id', [
            'name' => $packageName,
            'day_time' => $dayTime
        ]);

        return $newPackage ?: null;
    }

    /**
     * 映射config_id
     * @param $newDb
     * @param int $oldConfigId
     * @return int
     */
    private function mapConfigId($newDb, $oldConfigId)
    {
        // 根据MigrateController中的映射关系
        $configMapping = [
            21 => 36,  // A1
            22 => 38,
            23 => 39,
            24 => 40,
            25 => 41,
            26 => 42,
            27 => 43,
            30 => 44,
            31 => 45,
            32 => 46,
            33 => 47,
            34 => 48,
            35 => 49,
            36 => 50,
            37 => 51,
            38 => 52,
        ];

        // 如果有直接映射，使用映射值
        if (isset($configMapping[$oldConfigId])) {
            return $configMapping[$oldConfigId];
        }

        // 如果没有直接映射，尝试通过名称查找
        // 首先获取旧系统的config名称
        $oldDb = \Webman\Medoo\Medoo::instance('v1');
        $oldConfigName = $oldDb->get('food_package_config', 'name', [
            'config_id' => $oldConfigId,
            'deleted_at' => null
        ]);

        if ($oldConfigName) {
            // 在新系统中查找同名的config
            $newConfigId = $newDb->get('food_package_config', 'config_id', [
                'name' => $oldConfigName,
                'delete_time' => null
            ]);

            if ($newConfigId) {
                return $newConfigId;
            }
        }

        // 如果都找不到，返回默认值
        return $oldConfigId;
    }

    /**
     * 反向映射config_id（新表到旧表）
     * @param $oldDb
     * @param int $newConfigId
     * @return int
     */
    private function reverseMapConfigId($oldDb, $newConfigId)
    {
        // 反向映射关系（新表值 => 旧表值）
        $reverseConfigMapping = [
            36 => 21,  // A1
            38 => 22,
            39 => 23,
            40 => 24,
            41 => 25,
            42 => 26,
            43 => 27,
            44 => 30,
            45 => 31,
            46 => 32,
            47 => 33,
            48 => 34,
            49 => 35,
            50 => 36,
            51 => 37,
            52 => 38,
        ];

        // 如果有直接映射，使用映射值
        if (isset($reverseConfigMapping[$newConfigId])) {
            return $reverseConfigMapping[$newConfigId];
        }

        // 如果没有直接映射，尝试通过名称查找
        // 首先获取新系统的config名称
        $newDb = \Webman\Medoo\Medoo::instance('default');
        $newConfigName = $newDb->get('food_package_config', 'name', [
            'config_id' => $newConfigId,
            'delete_time' => null
        ]);

        if ($newConfigName) {
            // 在旧系统中查找同名的config
            $oldConfigId = $oldDb->get('food_package_config', 'config_id', [
                'name' => $newConfigName,
                'deleted_at' => null
            ]);

            if ($oldConfigId) {
                return $oldConfigId;
            }
        }

        // 如果都找不到，返回默认值
        return $newConfigId;
    }

    /**
     * 同步套餐子项（从新表到旧表）
     * @param $newDb
     * @param $oldDb
     * @param int $newPackageId
     * @param int $oldPackageId
     * @return int
     */
    private function syncPackageItemsToOld($newDb, $oldDb, $newPackageId, $oldPackageId)
    {
        // 查询新系统的套餐子项
        $newPackageItems = $newDb->select('food_package_item', [
            'food_id',
            'food_name',
            'food_cate',
            'created_by',
            'create_time'
        ], [
            'package_id' => $newPackageId,
            'delete_time' => null
        ]);

        if (empty($newPackageItems)) {
            return 0;
        }

        $syncedCount = 0;
        foreach ($newPackageItems as $item) {
            try {
                // 检查旧系统中是否已存在该套餐子项
                $existingItem = $oldDb->get('food_package_item', 'id', [
                    'package_id' => $oldPackageId,
                    'food_name' => $item['food_name'],
                    'food_cate' => $item['food_cate'],
                    'deleted_at' => null
                ]);

                if ($existingItem) {
                    continue; // 子项已存在，跳过
                }

                // 反向映射food_id（新表到旧表）
                $oldFoodId = $this->reversMapFoodId($oldDb, $item['food_id'], $item['food_name'], $item['food_cate']);

                // 插入旧系统的套餐子项
                $oldItemData = [
                    'food_id' => $oldFoodId,
                    'food_name' => $item['food_name'],
                    'food_cate' => $item['food_cate'],
                    'package_id' => $oldPackageId,
                    'created_by' => $item['created_by'] ?? 1,
                    'created_at' => $item['create_time'] ?? date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $oldDb->insert('food_package_item', $oldItemData);
                $syncedCount++;

            } catch (\Exception $e) {
                // 记录错误但继续处理其他子项
                error_log("套餐子项同步失败: " . $e->getMessage());
            }
        }

        return $syncedCount;
    }

    /**
     * 同步套餐子项
     * @param $oldDb
     * @param $newDb
     * @param int $oldPackageId
     * @param int $newPackageId
     * @return int
     */
    private function syncPackageItems($oldDb, $newDb, $oldPackageId, $newPackageId)
    {
        // 查询旧系统的套餐子项
        $oldPackageItems = $oldDb->select('food_package_item', [
            'food_id',
            'food_name',
            'food_cate',
            'created_by',
            'created_at'
        ], [
            'package_id' => $oldPackageId,
            'deleted_at' => null
        ]);

        if (empty($oldPackageItems)) {
            return 0;
        }

        $syncedCount = 0;
        foreach ($oldPackageItems as $item) {
            try {
                // 检查新系统中是否已存在该套餐子项
                $existingItem = $newDb->get('food_package_item', 'id', [
                    'package_id' => $newPackageId,
                    'food_name' => $item['food_name'],
                    'food_cate' => $item['food_cate'],
                    'delete_time' => null
                ]);

                if ($existingItem) {
                    continue; // 子项已存在，跳过
                }

                // 映射food_id（如果需要的话）
                $newFoodId = $this->mapFoodId($newDb, $item['food_id'], $item['food_name'], $item['food_cate']);

                // 插入新的套餐子项
                $newItemData = [
                    'food_id' => $newFoodId,
                    'food_name' => $item['food_name'],
                    'food_cate' => $item['food_cate'],
                    'package_id' => $newPackageId,
                    'created_by' => $item['created_by'] ?? 1,
                    'create_time' => $item['created_at'] ?? date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];

                $newDb->insert('food_package_item', $newItemData);
                $syncedCount++;

            } catch (\Exception $e) {
                // 记录错误但继续处理其他子项
                error_log("套餐子项同步失败: " . $e->getMessage());
            }
        }

        return $syncedCount;
    }

    /**
     * 反向映射food_id（新表到旧表）
     * @param $oldDb
     * @param int $newFoodId
     * @param string $foodName
     * @param string $foodCate
     * @return int
     */
    private function reversMapFoodId($oldDb, $newFoodId, $foodName, $foodCate)
    {
        // 首先尝试在旧系统中查找同名的食品
        $oldFoodId = $oldDb->get('food_content', 'id', [
            'name' => $foodName,
            'deleted_at' => null
        ]);

        if ($oldFoodId) {
            return $oldFoodId;
        }

        // 如果找不到，创建新的食品记录
        try {
            $oldFoodData = [
                'name' => $foodName,
                'cate_name' => $foodCate,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $oldDb->insert('food_content', $oldFoodData);
            return $oldDb->id();
        } catch (\Exception $e) {
            // 如果创建失败，返回原ID
            return $newFoodId;
        }
    }

    /**
     * 映射food_id
     * @param $newDb
     * @param int $oldFoodId
     * @param string $foodName
     * @param string $foodCate
     * @return int
     */
    private function mapFoodId($newDb, $oldFoodId, $foodName, $foodCate)
    {
        // 首先尝试在新系统中查找同名的食品
        $newFoodId = $newDb->get('food_content', 'id', [
            'name' => $foodName,
            'delete_time' => null
        ]);

        if ($newFoodId) {
            return $newFoodId;
        }

        // 如果找不到，创建新的食品记录
        try {
            $newFoodData = [
                'name' => $foodName,
                'cate_name' => $foodCate,
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $newDb->insert('food_content', $newFoodData);
            return $newDb->id();
        } catch (\Exception $e) {
            // 如果创建失败，返回原ID
            return $oldFoodId;
        }
    }

    /**
     * 映射性别
     * @param string $gender
     * @return int
     */
    private function mapGender($gender)
    {
        return match ($gender) {
            '男' => 1,
            '女' => 2,
            default => 0
        };
    }

    /**
     * 映射身份类别
     * @param string $identityType
     * @return int
     */
    private function mapIdentityType($identityType)
    {
        return match ($identityType) {
            '特困家庭' => 2,
            '低保' => 3,
            '计划生育特殊家庭' => 4,
            '中科院退休老人' => 7,
            '退伍军人' => 5,
            '其他' => 6,
            default => 1
        };
    }

    /**
     * 映射家床类型
     * @param string $bedType
     * @return int
     */
    private function mapBedType($bedType)
    {
        return match ($bedType) {
            '是', '家床' => 2,
            default => 3
        };
    }

    /**
     * 映射评估等级
     * @param string $evaluationLevel
     * @return int
     */
    private function mapEvaluationLevel($evaluationLevel)
    {
        return match ($evaluationLevel) {
            '轻度失能' => 2,
            '中度失能' => 3,
            '重度失能' => 4,
            default => 1
        };
    }

    /**
     * 映射服务机构
     * @param string $serviceSite
     * @return int
     */
    private function mapServiceSite($serviceSite)
    {
        return match ($serviceSite) {
            '龙翔社区', '奥运村龙祥社区服务站' => 13,
            '科苑酒店' => 12,
            '科苑家政服务中心' => 14,
            '科源驿站', '海淀科源社区养老驿站', '中关村科源社区养老驿站', '中关村科源社区养老服务驿站' => 3,
            '科学院南里五区', '奥运村科学园社区南里五区站' => 8,
            '海淀科星社区养老驿站', '中关村科星社区养老驿站' => 4,
            '奥运村科学园社区南里三区站' => 7,
            '奥运村枫林绿洲社区服务站' => 9,
            '亚运村华严北里社区服务站' => 11,
            '中关村黄庄社区803服务站' => 5,
            '中关村黄庄社区801服务站', '801服务点' => 6,
            default => 3
        };
    }

    /**
     * 成功响应
     * @param mixed $data
     * @param string $message
     * @return Response
     */
    private function success($data = [], $message = 'success')
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * 失败响应
     * @param string $message
     * @param int $code
     * @return Response
     */
    private function fail($message = 'error', $code = 400)
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => []
        ]);
    }
}
