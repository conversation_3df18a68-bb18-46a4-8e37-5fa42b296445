<?php
namespace app\controller\tool;

use app\controller\BaseController;
use app\utils\Excel;
use support\Request;
use support\Response;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use yzh52521\EasyHttp\Http;

class TableController extends BaseController
{
    /**
     * 导入档案用户编号
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function importExcel(Request $request): Response
    {
        // 获取数据
        $data = Excel::readExcel([
            'Sheet1',
        ]);
        // 处理数据开始
        if (!empty($data) && !empty($data['Sheet1']) && count($data['Sheet1']) > 1) {
            // 获取表头和数据
            $headers = $data['Sheet1'][0];
            $rows = array_slice($data['Sheet1'], 1);
            
            // 查找关键列的索引
            $nameIndex = array_search('*老人姓名', $headers);
            $idCardIndex = array_search('*身份证', $headers);
            $internalNumberIndex = array_search('编号', $headers);
            $statusIndex = array_search('*状态', $headers);
            
            // 转换后的数据
            $convertedData = [];
            
            foreach ($rows as $row) {
                // 跳过空行
                if (empty($row[$nameIndex]) || empty($row[$idCardIndex])) {
                    continue;
                }
                
                $custData = [
                    'cust_name' => $row[$nameIndex] ?? '',
                    'cust_id_card' => $row[$idCardIndex] ?? '',
                    'cust_internal_number' => 'KYYZ-' . ($row[$internalNumberIndex] ?? ''),
                ];
                
                // 处理老人状态
                $status = $row[$statusIndex] ?? '';
                if($status == '非家床') {
                    continue;
                }
                if ($status === '不续签') {
                    $custData['cust_status'] = '终止(不续签)';
                } elseif ($status === '转出') {
                    $custData['cust_status'] = '终止(转出)';
                } elseif ($status === '去世') {
                    $custData['cust_status'] = '终止(死亡)';
                } else {
                    $custData['cust_status'] = '服务中';
                }
                
                // 处理评估等级
                if (in_array($status, ['轻度', '中度', '重度', '自理'])) {
                    $evalLevel = match($status) {
                        '轻度' => 2, // 轻度失能
                        '中度' => 3, // 中度失能
                        '重度' => 4, // 重度失能
                        '自理' => 1, // 能力完好
                    };
                    $custData['cust_evaluation_level'] = $evalLevel;
                }
                
                $convertedData[] = $custData;
            }
            
            // 替换原始数据为转换后的数据
            $data = $convertedData;
        }
        // 处理数据结束
        // 更新数据开始
        if (!empty($data) && is_array($data)) {
            // 引入Dao类进行数据库操作
            $dao = new \app\utils\Dao();
            $successCount = 0;
            $failCount = 0;
            $result = [];

            foreach ($data as $item) {
                // 先检查身份证是否存在
                $where = [];
                if (!empty($item['cust_id_card'])) {
                    $where['cust_id_card'] = $item['cust_id_card'];
                    $where['delete_time'] = null;
                    $custInfo = $dao->get('cust_user', $where);

                    if (empty($custInfo) && !empty($item['cust_name'])) {
                        // 如果身份证查不到，尝试通过姓名查询
                        $where = ['cust_name' => $item['cust_name']];
                        $custInfo = $dao->get('cust_user', $where);
                    }

                    if (!empty($custInfo)) {
                        // 找到记录，更新数据
                        $updateData = [
                            'cust_internal_number' => $item['cust_internal_number'],
                            'cust_status' => $item['cust_status'],
                            'update_time' => date('Y-m-d H:i:s')
                        ];

                        // 只有当评估等级有值时才更新
                        if (!empty($item['cust_evaluation_level'])) {
                            $updateData['cust_evaluation_level'] = $item['cust_evaluation_level'];
                        }

                        $updateResult = $dao->update('cust_user', $updateData, ['cust_uid' => $custInfo['cust_uid']]);

                        if ($updateResult) {
                            $successCount++;
                            $item['update_status'] = '更新成功';
                        } else {
                            $failCount++;
                            $item['update_status'] = '更新失败';
                        }
                    } else {
                        $failCount++;
//                        $custInsertData = [
//                            'cust_internal_number' => $item['cust_internal_number'],
//                            'cust_name' => $item['cust_name'],
//                            'cust_id_card' => $item['cust_id_card'],
//                            'cust_status' => $item['cust_status'],
//                            'cust_service_site' => 3,
//                            'cust_is_bed' => 2,
//                            'created_by' => 1,
//                            'updated_by' => 1,
//                            'update_time' => date('Y-m-d H:i:s')
//                        ];
//                        // 创建用户信息
                        $item['update_status'] = '未找到匹配记录';
                    }
                } else {
                    $failCount++;
                    $item['update_status'] = '身份证号为空';
                }

                $result[] = $item;
            }

            // 返回更新结果
            $data = [
                'total' => count($data),
                'success' => $successCount,
                'fail' => $failCount,
                'result' => $result
            ];
        }
        // 更新数据结束
        return $this->success($data);
    }

    /**
     * 合并老人账号
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function mergeCustUser(Request $request): Response
    {
        $mergeFromId = $request->post('merge_from_id'); // 待合并的ID
        $mergeToId = $request->post('merge_to_id'); // 合并到的ID
        
        // 参数验证
        if (empty($mergeFromId) || empty($mergeToId)) {
            throw new BadRequestHttpException('合并ID和目标ID不能为空');
        }
        
        if ($mergeFromId == $mergeToId) {
            throw new BadRequestHttpException('合并ID和目标ID不能相同');
        }
        
        // 检查两个ID是否存在
        $fromUser = $this->dao->get('cust_user', [
            'cust_uid' => $mergeFromId,
            'delete_time' => null
        ]);
        
        $toUser = $this->dao->get('cust_user', [
            'cust_uid' => $mergeToId,
            'delete_time' => null
        ]);
        
        if (empty($fromUser)) {
            throw new BadRequestHttpException('待合并的老人账号不存在');
        }
        
        if (empty($toUser)) {
            throw new BadRequestHttpException('目标老人账号不存在');
        }
        $fromName = preg_replace('/^[\s　]+|[\s　]+$/u', '', $fromUser['cust_name']);
        $toName = preg_replace('/^[\s　]+|[\s　]+$/u', '', $toUser['cust_name']);
        if($fromName != $toName) {
            var_dump($fromName);
            var_dump($toName);
            throw new BadRequestHttpException('待合并的老人账号和目标老人账号姓名不一致');
        }
        // 执行合并操作
        $result = $this->mergeUserData($fromUser, $toUser);
        
        return $this->success([
            'message' => '账号合并完成',
            'merge_result' => $result
        ]);
    }

    /**
     * 合并用户数据
     * @param array $fromUser 待合并的用户
     * @param array $toUser 合并到的用户
     * @return array 合并结果
     */
    private function mergeUserData(array $fromUser, array $toUser): array
    {
        $result = [
            'success' => true,
            'messages' => [],
            'merged_tables' => []
        ];
        
        try {
            // 1. 合并余额
            $balanceInfo = $this->dao->get('cust_balance', [
                'cust_uid' => $fromUser['cust_uid'],
                'delete_time' => null
            ]);
            
            if (!empty($balanceInfo)) {
                // 检查目标用户是否有余额记录
                $toBalanceInfo = $this->dao->get('cust_balance', [
                    'cust_uid' => $toUser['cust_uid'],
                    'delete_time' => null
                ]);
                
                if (!empty($toBalanceInfo)) {
                    // 更新主账号余额
                    $this->dao->update('cust_balance', [
                        'now_amount[+]' => $balanceInfo['now_amount'],
                        'recharge_amount[+]' => $balanceInfo['recharge_amount'],
                        'spending_amount[+]' => $balanceInfo['spending_amount'],
                        'update_time' => date('Y-m-d H:i:s')
                    ], [
                        'cust_uid' => $toUser['cust_uid'],
                        'delete_time' => null  // 添加条件确保只更新未删除的记录
                    ]);
                } else {
                    // 如果目标用户没有余额记录，直接更新待合并用户的余额记录
                    $this->dao->update('cust_balance', [
                        'cust_uid' => $toUser['cust_uid'],
                        'update_time' => date('Y-m-d H:i:s')
                    ], [
                        'cust_uid' => $fromUser['cust_uid'],
                        'delete_time' => null  // 添加条件确保只更新未删除的记录
                    ]);
                }
                
                // 标记被合并账号的余额记录为删除
                $this->dao->update('cust_balance', [
                    'delete_time' => date('Y-m-d H:i:s')
                ], [
                    'cust_uid' => $fromUser['cust_uid'],
                    'delete_time' => null  // 添加条件确保只更新未删除的记录
                ]);
                
                $result['merged_tables'][] = 'cust_balance';
                $result['messages'][] = "已合并余额信息";
            }
            
            // 2. 更新余额记录表
            $balanceRecordCount = $this->dao->update('cust_balance_record', [
                'cust_uid' => $toUser['cust_uid']
            ], [
                'cust_uid' => $fromUser['cust_uid'],
                'delete_time' => null
            ]);
            
            if ($balanceRecordCount > 0) {
                $result['merged_tables'][] = 'cust_balance_record';
                $result['messages'][] = "已合并 {$balanceRecordCount} 条余额记录";
            }
            
            // 3. 更新订单相关表
            $tables = [
                'order_food' => '订单表',
                'cust_bill' => '缴费记录表',
                'cust_plan' => '服务规划表',
                'cust_service_record' => '服务记录表',
                'cust_survey_record' => '问卷记录表',
                'contract_management' => '合同表',
                'order_food_item' => '订单附表',
            ];
            
            foreach ($tables as $table => $tableName) {
                // 检查表是否存在
                try {
                    $updateCount = $this->dao->update($table, [
                        'cust_uid' => $toUser['cust_uid']
                    ], [
                        'cust_uid' => $fromUser['cust_uid'],
                        'delete_time' => null
                    ]);
                    
                    if ($updateCount > 0) {
                        $result['merged_tables'][] = $table;
                        $result['messages'][] = "已合并 {$updateCount} 条{$tableName}记录";
                    }
                } catch (\Exception $e) {
                    $result['messages'][] = "合并{$tableName}失败: " . $e->getMessage();
                }
            }
            
            // 4. 标记被合并的账号为删除状态
            $updateResult = $this->dao->update('cust_user', [
                'delete_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
                'cust_remark' => '账号已合并至ID:' . $toUser['cust_uid']
            ], [
                'cust_uid' => $fromUser['cust_uid'],
                'delete_time' => null  // 添加条件确保只更新未删除的记录
            ]);
            
            if ($updateResult) {
                $result['messages'][] = "已将ID为 {$fromUser['cust_uid']} 的账号标记为已删除";
            } else {
                $result['messages'][] = "标记ID为 {$fromUser['cust_uid']} 的账号为已删除失败";
            }
            
        } catch (\Exception $e) {
            $result['success'] = false;
            $result['error'] = $e->getMessage();
        }
        
        return $result;
    }

    
}