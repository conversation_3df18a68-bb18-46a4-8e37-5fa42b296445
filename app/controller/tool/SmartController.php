<?php
namespace app\controller\tool;

use app\controller\BaseController;
use app\domain\service\SmartService;
use support\Request;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use yzh52521\EasyHttp\Http;

class SmartController extends BaseController
{
    /**
     * 水资源设备第三方Api请求
     */
    public function waterSend(Request $request): Response
    {
        $url = $request->input('url');
        $params = $request->input('params',[]);
        $type = $request->input('type');
        if(empty($url)) {
            return $this->fail('url不能为空');
        }
        $smartService = new SmartService();
        $result = $smartService->waterSend($url, $params, $type);
        return $this->returnJson([
            'code' => 200,
            'data' => !empty($result['data']) ? $result['data'] : [],
        ]);
    }


    /**
     * 更新水仪器设备信息
     */
    public function updateDevice(Request $request): Response
    {
        $smartService = new SmartService();
        $smartService->updateDeviceData();
        return $this->success('更新成功');
    }

    /**
     * 查询设备列表接口
     * @param Request $request
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDeviceList(Request $request): Response
    {
        // 获取请求参数
        $page = (int)$request->input('page', 1); // 当前页码，从1开始
        $pageSize = (int)$request->input('pageSize', 10); // 每页显示条数
        $status = $request->input('status', 'all'); // 设备状态筛选

        // 验证页码和每页条数
        if ($page < 1) {
            $page = 1;
        }
        // 计算偏移量
        $offset = ($page - 1) * $pageSize;

        // 构建查询条件
        $where = [];

        // 根据状态筛选
        if ($status !== 'all') {
            $where['online'] = (int)$status; // 0表示离线，1表示在线
        }

        // 实例化模型
        $model = new \app\domain\model\SmartWater();

        // 查询总记录数
        $total = $model->where($where)->count();

        // 查询数据列表
        $list = $model->where($where)
            ->with(['custInfo'])
            ->order('update_time', 'desc')
            ->limit($offset, $pageSize)
            ->select()
            ->toArray();

        // 返回结果
        return $this->success([
            'list' => $list,
            'page' => $page,
            'pageSize' => $pageSize,
            'total' => $total
        ]);
    }
}