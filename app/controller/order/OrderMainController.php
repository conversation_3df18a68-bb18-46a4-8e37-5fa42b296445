<?php

namespace app\controller\order;

use app\controller\BaseController;
use app\domain\service\OrderMainService;
use app\validate\OrderValidate;
use DI\Attribute\Inject;
use Exception;
use SM\SMException;
use support\Request;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use yzh52521\WebmanLock\Locker;

/**
 * 文章管理控制器
 */
class OrderMainController extends BaseController
{

    #[Inject]
    private OrderMainService $orderMainService;

    /**
     * 提交预占订单服务
     * @return Response
     * @throws SMException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function submitOrder(): Response
    {
        $data = $this->orderMainService->query();
        return $this->success($data);
    }

    /**
     * 创建套餐订单
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function createAdminFoodOrder(Request $request): Response
    {
        $params = $request->only(
            [
                'cust_uid',
                'user_longitude',
                'user_latitude',
                'user_address',
                'delivery_option',
                'delivery_site',
                'delivery_fee',
                'employee_uid',
                'remark',
                'package_data',
                'package_type',
            ]
        );
        try {
            validate(OrderValidate::class)
                ->scene('createOrder')
                ->check($params);
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        // 验证开始
        if ($params['delivery_option'] == '送餐') {
            // 配送必须填写地址
            if(empty($params['user_address'])) {
                throw new BadRequestHttpException('配送必须填写地址');
            }
            // 配送必须填写送餐员
            if(empty($params['employee_uid'])) {
                throw new BadRequestHttpException('配送必须填写送餐员');
            }
        }
        // 查询本周是否下单
        if($this->dao->has('order_food_item',[
            'cust_uid' => $params['cust_uid'],
            'delete_time' => null,
            'num[>]' => 0,
            'delivery_time[<>]' => [$params['delivery_fee'][0]['time'],$params['delivery_fee'][array_key_last($params['delivery_fee'])]['time']],
        ])) {
            throw new BadRequestHttpException('本周已下单,请编辑已有订单！');
        }
        // 删除未下单预订单
        $this->dao->update('order_food',[
            'delete_time' => date('Y-m-d H:i:s')
        ],[
            'cust_uid' => $params['cust_uid'],
            'delete_time' => null,
            'delivery_time[<>]' => [$params['delivery_fee'][0]['time'],$params['delivery_fee'][array_key_last($params['delivery_fee'])]['time']],
        ]);
        $this->dao->update('order_food_item',[
            'delete_time' => date('Y-m-d H:i:s')
        ],[
            'cust_uid' => $params['cust_uid'],
            'delete_time' => null,
            'delivery_time[<>]' => [$params['delivery_fee'][0]['time'],$params['delivery_fee'][array_key_last($params['delivery_fee'])]['time']],
        ]);
        // 验证结束
        // 添加防抖
        $lock = Locker::lock($request->adminId);
        if (!$lock->acquire()) {
            throw new BadRequestHttpException('操作太频繁，请稍后再试');
        }

        $nowTime = date('Y-m-d H:i:s');
        // 拆分订单-以天为主
        $orderInfo = [];
        $orderFoodSuccessId = [];
        foreach ($params['delivery_fee'] as $v) {
            $orderId = getId();
            $orderFoodSuccessId[] = $orderId; // 订单通知
            $value = [];
            $value['order_id'] = $orderId; // 订单号
            $value['delivery_option'] = $v['delivery_option']; // 配送方式
            $value['delivery_site'] = $v['delivery_site']; // 配送站点
            $value['delivery_num'] = $v['num']; // 配送数量
            $value['total_num'] = $v['num']; // 购买总数
            $value['delivery_time'] = $v['time']; // 配送时间
            $value['gov_fee'] = $v['discount']; // 政府补贴费用
            $value['pay_price'] = $v['pay_price']; // 用户实际支付金额
            $value['cust_delivery_fee'] = $v['fee']; // 客户支付配送费
            $value['sum_amount'] = $v['pay_price'] + $v['fee'] + $v['discount'];
            $value['cust_uid'] = $params['cust_uid']; // 客户UID
            $value['employee_uid'] = !empty($v['employee_uid']) ? $v['employee_uid'] : 0; // 配送员ID
            $value['package_type'] = $params['package_type']; // 套餐类型
            $value['discounts_text'] = $v['discounts_text']; // 折扣名称
            $value['discounts_price'] = $v['discounts_price']; // 折扣金额
            $value['remark'] = $params['remark']; // 备注
            $value['created_by'] = $request->adminId; // 创建人
            $value['create_time'] = $nowTime; // 创建时间
            $value['order_food_item'] = [];
            // 计算数量
            $orderInfo[$v['time']] = $value;
        }
        unset($v);
        // 获取套餐预定数量
        foreach ($params['package_data'] as $v) {
            foreach ($v as $vv) {
                $orderFoodInfo = $orderInfo[$vv['time']];
                $orderInfoItem = [];
                $orderInfoItem['order_id'] = $orderFoodInfo['order_id'];
                $orderInfoItem['delivery_time'] = $vv['time'];// 配送时间
                $orderInfoItem['delivery_option'] = $orderFoodInfo['delivery_option']; // 配送方式
                $orderInfoItem['delivery_site'] = $orderFoodInfo['delivery_site']; // 配送站点
                $orderInfoItem['cust_uid'] = $params['cust_uid']; // 客户UID
                $orderInfoItem['employee_uid'] = $orderFoodInfo['employee_uid']; // 配送员ID
                $orderInfoItem['num'] = $vv['num']; // 购买数量
                $orderInfoItem['price'] = $vv['price']; // 单价
                $orderInfoItem['pay_price'] = $vv['price'] * $vv['num']; // 支付金额
                $orderInfoItem['package_id'] = $vv['package_id']; // 套餐Id
                $orderInfoItem['package_type'] = $params['package_type']; // 套餐类型
                $orderInfoItem['package_name'] = !empty($vv['food_name']) ? $vv['food_name'] : $vv['name']; // 套餐名称
                $orderInfoItem['remark'] = $params['remark']; // 备注
                $orderInfoItem['created_by'] = $request->adminId; // 创建人
                $orderInfoItem['create_time'] = $nowTime; // 创建时间
                $orderInfo[$orderFoodInfo['delivery_time']]['order_food_item'][] = $orderInfoItem;
            }
        }
        unset($v);
        unset($vv);
        try {
            // 添加订单
            $dao = $this->dao;
            $dao->action(function ($database) use ($orderInfo) {
                foreach ($orderInfo as $v) {
                    if(!empty($v['order_food_item'])) {
                        $orderFoodItem = $v['order_food_item'];
                        unset($v['order_food_item']);
                        $database->insert('order_food_item',$orderFoodItem);
                    }
                    $database->insert('order_food',$v);
                }
            });
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        } finally {
            $lock->release();
        }
        // 执行订单创建后置事件
        return $this->success('添加成功');
    }
}
