<?php

namespace app\controller\order;

use app\controller\BaseController;
use app\cust\model\CustBalance;
use app\domain\logic\OrderFoodLogic;
use app\domain\model\OrderFood;
use app\domain\service\FoodOrderService;
use app\food\model\FoodPackageItem;
use app\utils\Excel;
use app\validate\OrderValidate;
use DI\Attribute\Inject;
use Exception;
use support\Request;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
class FoodOrderController extends BaseController
{
    /**
     * @var bool 数据边界启用状态
     */
    protected $scope = false;

    #[Inject]
    private OrderFoodLogic $logic;

    #[Inject]
    private FoodOrderService $service;

    /**
     * 导出订单
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function exportOrderTable(Request $request): Response
    {
        $params = $request->only([
            'cust_uid',
            'delivery_option',
            'delivery_site',
            'employee_uid',
            'start_time',
            'end_time',
        ]);
        // 时间查询
        if (!empty($request->input('start_time')) && !empty($request->input('end_time'))) {
            // 判断是否时间正确
            if ($request->input('start_time') > $request->input('end_time')) {
                throw new BadRequestHttpException('开始时间不能大于结束时间');
            }
            $params['delivery_time'] = [
                $request->input('start_time',date('Y-m-d H:i:s')),
                $request->input('end_time',date('Y-m-d H:i:s')),
            ];
        }
        $query = $this->logic->search($params)->with([
            'foodItem',
            'employee',
            'custInfo',
            'createdByInfo'
        ]);
        $data = $this->logic->getList($query);
        // 处理数据
        $res = [];
        foreach ($data as $v) {
            $value = [];
            if (!empty($v['custInfo'])) {
                $value['userName'] = $v['custInfo']['cust_name'];
                $value['userPhone'] = $v['custInfo']['cust_private_phone'];
                $value['addressDetail'] = $v['custInfo']['cust_live_address'];
            }
            $value['deliverySite'] = $v['delivery_site'];
            $value['deliveryOption'] = $v['delivery_option'];
            $value['staffName'] = '无送餐员';
            if(!empty($v['employee'])) {
                $value['staffName'] = $v['employee']['employee_name'];
            }
            $value['packageOrderTime'] = $v['delivery_time'];
            $value['paymentStatus'] = empty($v['order_status']) ? '待完成' : '已完成';
            $packageText = '';
            foreach ($v['foodItem'] as $vv) {
                $packageText .= $vv['package_name'] . ':' . $vv['num'] . '份 ';
            }
            $value['packageText'] = $packageText;
            $value['remark'] = $v['remark'];
            $res[] = $value;
        }
        // 创建导出表
        $title = '养老餐订单导出表_'.date('YmdHis').'.xlsx';
        $headers = [
            '老人姓名',
            '老人手机号',
            '老人地址',
            '站点',
            '取餐方式',
            '送餐员',
            '订餐日期',
            '支付状态',
            '订餐详情',
            '备注'
        ];
        $url = Excel::export($title,$headers,$res);
        return $this->success([
            'url' => $url
        ]);
    }


    /**
     * 套餐订单查询
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function getFoodOrderList(Request $request): Response
    {
        try {
            validate(OrderValidate::class)
                ->scene('getFoodOrderList')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only([
            'cust_uid',
            'delivery_site',
            'delivery_option',
            'employee_uid',
            'order_status',
        ]);
        // 时间查询
        if (!empty($request->input('start_time')) && !empty($request->input('end_time'))) {
            // 判断是否时间正确
            if ($request->input('start_time') > $request->input('end_time')) {
                throw new BadRequestHttpException('开始时间不能大于结束时间');
            }
            $params['delivery_time'] = [
                $request->input('start_time',date('Y-m-d H:i:s')),
                $request->input('end_time',date('Y-m-d H:i:s')),
            ];
        }
        $query = $this->logic->search($params)->with([
            'foodItem',
            'employee',
            'custInfo',
            'createdByInfo'
        ]);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * 修改套餐订单配置信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function updateFoodOrderConfig(Request $request): Response
    {
        $params = $request->only(
            [
                'order_id',
                'user_longitude',
                'user_latitude',
                'user_address',
                'delivery_option',
                'delivery_site',
                'employee_uid',
                'remark',
                'package_type',
            ]
        );
        try {
            validate(OrderValidate::class)
                ->scene('updateFoodOrderConfig')
                ->check($params);
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        // 更新订单金额
        $orderInfo = $this->dao->get('order_food',[
            'order_id' => $params['order_id']
        ],[
            'gov_fee',
            'cust_delivery_fee',
            'pay_price'
        ]);
        if(empty($orderInfo)) {
            throw new BadRequestHttpException('订单不存在');
        }
        $payPrice = $this->dao->sum('order_food_item','pay_price',[
            'order_id' => $params['order_id']
        ]);
        // 更改订单
        if($params['delivery_option'] != '送餐') {
            $params['employee_uid'] = null;
            $params['pay_price'] = $payPrice - $orderInfo['gov_fee']; // 金额扣除运费
        } else {
            $params['pay_price'] = $payPrice + $orderInfo['cust_delivery_fee'] - $orderInfo['gov_fee']; // 金额增加运费
        }
        $result = $this->logic->update($params, ['order_id' => $params['order_id']]);
        // 更新子订单
        $this->dao->update('order_food_item',[
            'delivery_option' => $params['delivery_option'],
            'delivery_site' => $params['delivery_site'],
            'employee_uid' => $params['employee_uid'],
            'remark' => $params['remark'],
            'package_type' => $params['package_type'],
            'update_time' => date('Y-m-d H:i:s'),
            'updated_by' => $request->adminId,
        ],[
            'order_id' => $params['order_id']
        ]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * 更新套餐订单信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function updateFoodOrder(Request $request): Response
    {
        $params = $request->only(
            [
                'order_id',
                'employee_uid',
                'delivery_site',
                'delivery_option',
                'discounts_text',
                'discounts_price',
            ]
        );
        try {
            validate(OrderValidate::class)
                ->scene('updateFoodOrder')
                ->check($params);
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        // 更新订单金额
        $orderInfo = $this->dao->get('order_food',[
            'order_id' => $params['order_id']
        ],[
            'gov_fee',
            'cust_delivery_fee',
            'pay_price'
        ]);
        if(empty($orderInfo)) {
            throw new BadRequestHttpException('订单不存在');
        }
        // 更新优惠规则
        if(empty($params['discounts_text'])) {
            $params['discounts_text'] = null;
            $params['discounts_price'] = null;
        }
        $this->logic->update($params, ['order_id' => $params['order_id']]);
        unset($params['discounts_text']);
        unset($params['discounts_price']);
        // 更新子订单
        $params['update_time'] = date('Y-m-d H:i:s');
        $params['updated_by'] = $request->adminId;
        $this->dao->update('order_food_item',$params,[
            'order_id' => $params['order_id']
        ]);
        // 计算送餐费
        $data = $this->service->calculateDeliveryFee($params['order_id']);
        // 更新订单信息金额
        $result = $this->logic->update($data, ['order_id' => $params['order_id']]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * 修改套餐订单数量信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function updateFoodOrderNum(Request $request): Response
    {
        $params = $request->only(
            [
                'order_food_item_id',
                'num',
            ]
        );
        try {
            validate(OrderValidate::class)
                ->scene('updateFoodOrderNum')
                ->check($params);
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        // 查询子订单数量
        $orderFoodItemInfo = $this->dao->get('order_food_item',[
            'order_food_item_id' => $params['order_food_item_id'],
            'delete_time' => null,
        ]);
        if(empty($orderFoodItemInfo)) {
            throw new BadRequestHttpException('订单不存在');
        }
        // 查询订单差量
        $payPrice = (float)$orderFoodItemInfo['price'] * (int)$params['num'];
        // 精度额小数点后两位
        $payPrice = round($payPrice, 2);
        // 更新子订单
        $this->dao->update('order_food_item', [
            'pay_price' => $payPrice,
            'num' => (int)$params['num'],
            'updated_by' => $request->adminId,
            'update_time' => date('Y-m-d H:i:s')
        ], [
            'order_food_item_id' => $params['order_food_item_id']
        ]);
        // 计算送餐费
        $data = $this->service->calculateDeliveryFee($orderFoodItemInfo['order_id']);
        // 更新订单信息
        $this->logic->update($data, ['order_id' => $orderFoodItemInfo['order_id']]);
        return $this->success('修改成功');
    }

    /**
     * 批量修改接口
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function batchUpdateFoodOrder(Request $request): Response
    {
        $orderId = $request->input('order_id',[]);
        $employeeUid = $request->input('employee_uid');
        $deliverySite = $request->input('delivery_site');
        if(empty($orderId)) {
            throw new BadRequestHttpException('请选择订单');
        }
        if(!empty($employeeUid)) {
            // 更新订单送餐员
            $this->dao->update('order_food',[
                'employee_uid' => $employeeUid,
                'update_time' => date('Y-m-d H:i:s'),
                'updated_by' => $request->adminId,
            ],[
                'order_id' => $orderId
            ]);
            $this->dao->update('order_food_item',[
                'employee_uid' => $employeeUid,
                'update_time' => date('Y-m-d H:i:s'),
                'updated_by' => $request->adminId,
            ],[
                'order_id' => $orderId
            ]);
        }
        if(!empty($deliverySite)) {
            // 更新订单送餐地址
            $this->dao->update('order_food',[
                'delivery_site' => $deliverySite,
                'update_time' => date('Y-m-d H:i:s'),
                'updated_by' => $request->adminId,
            ],[
                'order_id' => $orderId
            ]);
            $this->dao->update('order_food_item',[
                'delivery_site' => $deliverySite,
                'update_time' => date('Y-m-d H:i:s'),
                'updated_by' => $request->adminId,
            ],[
                'order_id' => $orderId
            ]);
        }
        return $this->success('更新成功');
    }

    /**
     * 确认订单完成
     */
    public function confirmFoodOrder(Request $request): Response
    {
        $order_id = $request->input('order_id');
        event('food.orderConfirmAfter', [
            'order_id' => $order_id
        ]);
        return $this->success('确认成功');
    }

    /**
     * 订单详情查询
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getOrderInfo(Request $request): Response
    {
        $params = $request->only(
            [
                'order_id',
            ]
        );
        try {
            validate(OrderValidate::class)
                ->scene('getOrderInfo')
                ->check($params);
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $orderId = $params['order_id'];
        // 查询订单详情
        $orderFoodModel = new OrderFood();
        $orderInfo = $orderFoodModel
            ->where('order_food.order_id',$orderId)
            ->with(['createdByInfo', 'custInfo', 'employee'])
            ->hidden([
                'created_by',
                'updated_by',
                'update_time',
                'delete_time',
            ])
            ->findOrEmpty()
            ->toArray();
        if (empty($orderInfo)) {
            throw new BadRequestHttpException('订单套餐未找到');
        }
        if (empty($orderInfo['custInfo'])) {
            throw new BadRequestHttpException('老人信息已删除');
        }
        $orderInfo['cust_name'] = $orderInfo['custInfo']['cust_name'];
        $orderInfo['cust_private_phone'] = $orderInfo['custInfo']['cust_private_phone'];
        $orderInfo['cust_evaluation_level'] = $orderInfo['custInfo']['cust_evaluation_level'];
        $orderInfo['cust_identity_type'] = $orderInfo['custInfo']['cust_identity_type'];
        $orderInfo['cust_live_address'] = $orderInfo['custInfo']['cust_live_address'];
        $orderInfo['cust_is_bed'] = $orderInfo['custInfo']['cust_is_bed'];
        $orderInfo['createByUserName'] = $orderInfo['createdByInfo']['nickname'];
        // 获取老人账单余额
        $balanceModel = new CustBalance();
        $balanceInfo = $balanceModel->where('cust_uid',$orderInfo['cust_uid'])->findOrEmpty()->toArray();
        if(empty($balanceInfo)) {
            $orderInfo['balance'] = 0;
        } else {
            $orderInfo['balance'] = $balanceInfo['now_amount'];
        }
        unset($orderInfo['createdByInfo']);
        unset($orderInfo['custInfo']);
        // 查询该订单下 PackageList
        // 获取 delivery_time 所在周的周一
        $weekStart = date("Y-m-d", strtotime('monday this week', strtotime($orderInfo['delivery_time'])));
        // 获取 delivery_time 所在周的周日
        $weekEnd = date("Y-m-d", strtotime("sunday this week", strtotime($orderInfo['delivery_time'])));

        $data = $orderFoodModel->with(['package'])->where(
            [
                ['delivery_time', '>=', $weekStart],
                ['delivery_time', '<=', $weekEnd],
                ['order_food.cust_uid', '=', $orderInfo['cust_uid']],
            ]
        )->order(['delivery_time'])->select()->toArray();
        $packageList = [];
        // map拼接
        $count = count($data[count($data) - 1]['package']);
        $packageItemModel = new FoodPackageItem();
        // 送餐费数组获取
        $delivery_fee = [];
        $pay_price = 0; // 优惠金额
        $gov_fee = 0; // 应付金额
        $discounts_price = 0; // 优惠金额
        for ($i = 0; $i < $count; $i++) {
            $arr = [];
            foreach ($data as $v) {
                $value = [];
                $day = $this->getChineseWeekday($v['delivery_time']);
                if(empty($v['package'][$i])) {
                    $value = [
                        'name' => '无',
                        'num' => 0,
                        'price' => 0,
                    ];
                } else {
                    $packInfo = $v['package'][$i];
                    $value['order_food_item_id'] = $packInfo['order_food_item_id'];
                    $foodName = null;
                    if(str_contains($packInfo['package_name'], '主食专供')) {
                        $packageName = $packageItemModel->where('package_id', $packInfo['package_id'])->value('food_name');
                        if(!empty($packageName)) {
                            $foodName = $packageName;
                        }
                    }
                    $value['name'] = $packInfo['package_name'];
                    $value['foodName'] = $foodName;
                    $value['price'] = $packInfo['price'];
                    $value['packageId'] = $packInfo['package_id'];
                    $value['dayTime'] = $v['delivery_time'];
                    $value['weekDay'] = $day;
                    $value['num'] = $packInfo['num'];
                    if($i == 0) {
                        // 送餐费数组
                        $deliveryFeeValue = [];
                        $deliveryFeeValue['order_id'] = $v['order_id'];
                        $deliveryFeeValue['time'] = $v['delivery_time'];
                        $deliveryFeeValue['fee'] = $v['cust_delivery_fee'];
                        $deliveryFeeValue['discount'] = $v['gov_fee'];
                        $deliveryFeeValue['pay_price'] = $v['pay_price'];
                        $deliveryFeeValue['delivery_option'] = $packInfo['delivery_option'];
                        $deliveryFeeValue['employee_uid'] = $packInfo['employee_uid'];
                        $deliveryFeeValue['delivery_site'] = $v['delivery_site'];
                        $deliveryFeeValue['discounts_text'] = $v['discounts_text'];
                        $deliveryFeeValue['discounts_price'] = $v['discounts_price'];

                        $delivery_fee[] = $deliveryFeeValue;

                        $pay_price += (float)$v['pay_price'];
                        $gov_fee += (float)$v['gov_fee'];
                        $discounts_price += (float)$v['discounts_price'];
                    }
                }
                $arr[] = $value;
            }
            $packageList[] = $arr;
        }
        $orderInfo['packageList'] = $packageList;
        $orderInfo['delivery_fee'] = $delivery_fee;
        $orderInfo['discounts_price'] = $discounts_price;
        $orderInfo['pay_price'] = number_format($pay_price,2);
        $orderInfo['gov_fee'] = number_format($gov_fee,2);
        $orderInfo['total_amount'] = number_format($gov_fee+$pay_price+$discounts_price,2);
        return $this->success($orderInfo);
    }
}