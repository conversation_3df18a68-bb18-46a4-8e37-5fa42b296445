<?php

namespace app\controller;

use app\utils\Dao;
use app\utils\Wechat;
use Exception;
use plugin\saiadmin\exception\ApiException;
use support\Request;
use support\Response;
use DI\Attribute\Inject;
use hg\apidoc\annotation as Apidoc;
use yzh52521\EasyHttp\Http;
use yzh52521\WebmanLock\Locker;
use plugin\saiadmin\utils\JwtAuth;
use Webman\Medoo\Medoo;

/**
 * 授权管理控制器
 * @Apidoc\Title("授权管理")
 */
class OauthController extends BaseController
{
    #[Inject]
    protected Dao $dao;

    /**
     * 获取code接口
     * @Apidoc\Title("获取一次性code")
     * @Apidoc\Url("/oauth/getCode")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     * @throws ApiException|Exception
     */
    public function getCode(Request $request): Response
    {
        // 创建code
        $lock = Locker::lock($request->adminId);
        if (!$lock->acquire()) {
            throw new ApiException('操作太频繁，请稍后再试');
        }
        $code = getId();
        $expireTime = date('Y-m-d H:i:s', time() + 60);
        try {
            $this->dao->insert('sys_oauth_code',[
                'code' => $code,
                'expire_time' => $expireTime,
                'token' => $request->header('Authori-zation'),
                'create_time' => date('Y-m-d H:i:s'),
            ]);
        } finally {
            $lock->release();
        }
        return $this->success([
            'code' => $code,
            'expire_time' => $expireTime
        ]);
    }

    /**
     * 获取token接口
     * @Apidoc\Title("通过code获取token")
     * @Apidoc\Url("/oauth/getToken")
     * @Apidoc\Query("code", type="varchar",require=true, desc="授权code")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function getToken(Request $request): Response
    {
        $code = $request->input('code','code');
        // 检查code是否存在
        $oauthCode = $this->dao->get('sys_oauth_code', ['code' => $code],[
            'is_status',
            'expire_time',
            'token'
        ]);
        if (!$oauthCode) {
            throw new ApiException('code不存在');
        }
        // 检查code是否过期
        if ($oauthCode['expire_time'] < date('Y-m-d H:i:s')) {
            throw new ApiException('code已过期');
        }
        // 检查code是否已使用
        if ($oauthCode['is_status'] == -1) {
            throw new ApiException('code已使用');
        }
        // 更新code状态
        $this->dao->update('sys_oauth_code', [
            'is_status' => -1,
            'update_time' => date('Y-m-d H:i:s'),
        ], ['code' => $code]);
        return $this->success([
            'token' => $oauthCode['token']
        ]);
    }

    /**
     * 检查token是否有效
     * @Apidoc\Title("检查token是否有效")
     * @Apidoc\Url("/oauth/checkToken")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function checkToken(Request $request): Response
    {
        return $this->success([
            'status' => true
        ]);
    }

    /**
     * 获取token接口
     * @Apidoc\Title("通过code获取V1平台token")
     * @Apidoc\Url("/oauth/getV1Token")
     * @Apidoc\Query("code", type="varchar",require=true, desc="授权code")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function getV1Token(Request $request): Response
    {
        $code = $request->input('code','code');
        // 检查code是否存在
        $oauthCode = $this->dao->get('sys_oauth_code', ['code' => $code],[
            'is_status',
            'expire_time',
            'token'
        ]);
        if (!$oauthCode) {
            throw new ApiException('code不存在');
        }
        // 检查code是否过期
        if ($oauthCode['expire_time'] < date('Y-m-d H:i:s')) {
            throw new ApiException('code已过期');
        }
        // 解析token
        $token = trim($oauthCode['token']);
        if ($token !== 'null' && $token !== '') {
            $jwt = new JwtAuth();
            $tokenInfo = $jwt->parseToken($token);
            if(!empty($tokenInfo)) {
                // 查询相关用户v1平台token
                $thirdUid = $this->dao->get('eb_system_user',[
                    'id' => $tokenInfo[0]
                ],'third_v1_uid');
                if(!empty($thirdUid)) {
                    // 查询是否存在有效Token
                    $token = $this->dao->get('sys_oauth_code',[
                        'third_v1_uid' => $thirdUid,
                        'expire_time[>]' => date('Y-m-d H:i:s', time() + 6 * 3600)
                    ],'token');
                    if(empty($token)) {
                        // 创建token
                        $data = Http::post('https://web.pension.zkshlm.com/api/v1/system/login',[
                            'password' => 'zksh5d487gs64ddd7g8a6d4d47gx2s3',
                            'username' => Medoo::instance('v1')->get('sys_user', 'user_name', [
                                'id' => $thirdUid
                            ]),
                            'verifyCode' => 'zksh',
                            'verifyKey' => 'zksh',
                        ]);
                        $token = json_decode($data,true);
                        if(!empty($token['data']) && !empty($token['data']['token'])) {
                            $token = 'Bearer '. $token['data']['token'];
                        } else {
                            throw new ApiException('当前用户未绑定v1平台账号');
                        }
                        // 写入token
                        $this->dao->update('sys_oauth_code',[
                            'expire_time' => date('Y-m-d H:i:s', time() + 604800),
                            'token' => $token,
                            'update_time' => date('Y-m-d H:i:s'),
                            'third_v1_uid' => $thirdUid,
                            'is_status' => -1
                        ],[
                            'code' => $code,
                        ]);
                    } else {
                        // 更新code状态
                        $this->dao->update('sys_oauth_code', [
                            'third_v1_uid' => $thirdUid,
                            'update_time' => date('Y-m-d H:i:s'),
                            'token' => $token,
                            'is_status' => -1
                        ], ['code' => $code]);
                    }
                    return $this->success([
                        'token' => $token
                    ]);
                }
            }

        }
        throw new ApiException('当前用户未绑定v1平台账号');
    }

    /**
     * 查询V1系统用户
     * @Apidoc\Title("查询V1系统用户")
     * @Apidoc\Url("/oauth/getBindUser")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     */
    public function getBindUser(Request $request): Response
    {
        $userList = Medoo::instance('v1')->select('sys_user', [
            'id(value)',
            'user_nickname(label)'
        ], ['deleted_at' => null]);
        return $this->success($userList);
    }

    /**
     * 微信登录
     * @param Request $request
     * @return Response
     */
    public function wxLogin(Request $request): Response
    {
        try {
            $code = $request->input('code');
            if (empty($code)) {
                return $this->fail('参数错误');
            }
            // 获取微信用户信息
            $config = [
                'app_id' => getConfigValue('app_id'),
                'secret' => getConfigValue('app_secret'),
                'token' => getConfigValue('token'),
            ];
            $openid = Wechat::getUserInfo($config,$code);
            // 查询用户是否存在
            $userInfo = $this->dao->get('eb_system_user',[
                'zksh_openid' => $openid,
                'delete_time' => null
            ]);

            if (empty($userInfo)) {
                // 未绑定，需跳转绑定页
                return $this->success([
                    'is_bind' => false,
                    'zksh_openid' => $openid
                ]);
            }
            // 获取登录token
            $jwt = new JwtAuth();
            $token = $jwt->createToken($userInfo['id'], $userInfo['nickname'], 'mobile');

            return $this->success([
                'is_bind' => true,
                'token' => $token['token'],
                'expires_time' => $token['params']['exp']
            ]);
        } catch (\Exception $e) {
            return $this->fail('登录失败,请重新登录');
        }
    }

    /**
     * 微信授权绑定账号
     */
    public function  bindUser(Request $request): Response
    {
        $openid = $request->input('openid');
        $username = $request->input('username');
        $password = $request->input('password');
        // 参数验证
        if(empty($openid) || empty($username) || empty($password)) {
            return $this->fail('参数错误');
        }
        // 判断openid 是否已经绑定
        $userInfo = $this->dao->get('eb_system_user',[
            'zksh_openid' => $openid,
            'delete_time' => null
        ]);
        if(!empty($userInfo)) {
            return  $this->fail('微信账号已绑定');
        }
        // 查询账号
        $userInfo = $this->dao->get('eb_system_user',[
            'username' => $username,
            'delete_time' => null
        ]);
        if (empty($userInfo) || !password_verify($password, $userInfo['password'])) {
            return  $this->fail('账号或密码错误，请重新输入!');
        }
        // 绑定账号openid
        $res = $this->dao->update('eb_system_user',[
            'zksh_openid' => $openid,
        ],[
            'username' => $username,
            'delete_time' => null
        ]);
        if($res) {
            // 获取登录token
            $jwt = new JwtAuth();
            $token = $jwt->createToken($userInfo['id'], $userInfo['nickname'], 'mobile');
            return $this->success([
                'token' => $token['token'],
                'expires_time' => $token['params']['exp']
            ]);
        } else {
            return  $this->fail('微信账号绑定失败');
        }
    }
}
