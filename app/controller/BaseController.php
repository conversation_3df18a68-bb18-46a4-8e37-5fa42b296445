<?php

namespace app\controller;

use app\utils\Dao;
use DI\Attribute\Inject;
use support\Response;

class BaseController
{
    #[Inject]
    protected Dao $dao;

    /**
     * 返回json内容
     */
    public function returnJson(array $data): Response
    {
        if (!isset($data['code'])) {
            $data['code'] = 200;
        }
        if (!isset($data['msg'])) {
            $data['msg'] = 'success';
        }
        if (!isset($data['data'])) {
            $data['data'] = [];
        }
        return json($data);
    }

    /**
     * 成功返回json内容
     * @param array|string|null $data
     * @param string $msg
     * @return Response
     */
    public function success(array|string|null $data = [], string $msg = 'success'): Response
    {
        if (is_string($data)) {
            $msg = $data;
        }
        return json(['code' => 200, 'message' => $msg, 'data' => $data]);
    }

    /**
     * 失败返回json内容
     * @param string $msg
     * @return Response
     */
    public function fail(string $msg = 'fail'): Response
    {
        return json(['code' => 400, 'message' => $msg]);
    }

    /**
     * 日期转星期
     * @param $dateString
     * @return string
     */
    protected function getChineseWeekday($dateString): string
    {
        $weekdayNames = array("日", "一", "二", "三", "四", "五", "六");
        $weekday = date('w', strtotime($dateString));
        return "周" . $weekdayNames[$weekday];
    }
}
