<?php

namespace app\wx\listener;

use app\wx\service\UnifiedPackageOrderService;
use support\Log;
use Exception;

/**
 * 套餐订单事件监听器
 */
class PackageOrderEventListener
{
    /**
     * 支付成功事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onPaymentSuccess(array $data, string $eventName = ''): void
    {
        try {
            Log::info("收到支付成功事件", [
                'order_id' => $data['order_id'],
                'order_no' => $data['order_no'],
                'order_type' => $data['order_type']
            ]);
            
            // 只处理套餐订单
            if ($data['order_type'] != 3) {
                Log::info("非套餐订单，跳过处理", [
                    'order_id' => $data['order_id'],
                    'order_type' => $data['order_type']
                ]);
                return;
            }
            
            // 直接使用统一服务创建套餐订单
            UnifiedPackageOrderService::createAndSync($data);
            
        } catch (Exception $e) {
            Log::error("支付成功事件处理失败", [
                'order_id' => $data['order_id'] ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 套餐订单创建成功事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onPackageOrderCreated(array $data, string $eventName = ''): void
    {
        try {
            Log::info("套餐订单创建成功", [
                'wx_order_id' => $data['wx_order_id'],
                'sync_status' => $data['sync_status'],
                'food_orders_count' => count($data['food_orders'] ?? [])
            ]);
            
            // 这里可以添加后续处理逻辑，比如发送通知等
            
        } catch (Exception $e) {
            Log::error("套餐订单创建成功事件处理失败", [
                'wx_order_id' => $data['wx_order_id'] ?? '',
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 套餐订单创建失败事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onPackageOrderCreateFailed(array $data, string $eventName = ''): void
    {
        try {
            Log::error("套餐订单创建失败", [
                'wx_order_id' => $data['wx_order_id'],
                'error_message' => $data['error_message']
            ]);
            
            // 这里可以添加失败处理逻辑，比如重试、告警等
            
        } catch (Exception $e) {
            Log::error("套餐订单创建失败事件处理失败", [
                'wx_order_id' => $data['wx_order_id'] ?? '',
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 退款处理事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onRefundProcessed(array $data, string $eventName = ''): void
    {
        try {
            Log::info("退款处理事件", [
                'order_id' => $data['order_id'] ?? '',
                'refund_amount' => $data['refund_amount'] ?? 0,
                'needs_audit' => $data['needs_audit'] ?? false
            ]);

            // 如果不需要审核，直接同步套餐订单更新
            if (!($data['needs_audit'] ?? false)) {
                $this->syncRefundToFoodOrder($data);
            }

        } catch (Exception $e) {
            Log::error("退款处理事件处理失败", [
                'order_id' => $data['order_id'] ?? '',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 退款成功事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onRefundSuccess(array $data, string $eventName = ''): void
    {
        try {
            Log::info("退款成功事件", [
                'order_id' => $data['order_id'] ?? '',
                'refund_amount' => $data['refund_amount'] ?? 0
            ]);
            
            // 这里可以添加退款成功后的处理逻辑
            
        } catch (Exception $e) {
            Log::error("退款成功事件处理失败", [
                'order_id' => $data['order_id'] ?? '',
                'error' => $e->getMessage()
            ]);
        }
    }
    


    /**
     * 同步退款到套餐订单系统（已移至统一服务）
     * @param array $data 退款数据
     */
    private function syncRefundToFoodOrder(array $data): void
    {
        try {
            Log::info("使用统一服务同步退款", [
                'order_id' => $data['order_id'],
                'refund_amount' => $data['refund_amount']
            ]);

            // 获取退款详情
            $refundDetails = $data['refund_details'] ?? [];

            if (empty($refundDetails)) {
                Log::warning("退款详情为空，跳过同步", [
                    'order_id' => $data['order_id']
                ]);
                return;
            }

            // 使用统一服务处理退款同步
            $result = UnifiedPackageOrderService::processRefund($data['order_id'], $refundDetails);

            if ($result) {
                Log::info("退款同步成功", [
                    'order_id' => $data['order_id'],
                    'refund_items_count' => count($refundDetails)
                ]);
            } else {
                Log::error("退款同步到套餐订单系统失败", [
                    'order_id' => $data['order_id']
                ]);

                // 触发同步失败事件
                event('wx.refundSyncFailed', [
                    'order_id' => $data['order_id'],
                    'refund_id' => $data['refund_id'] ?? '',
                    'error_message' => '同步失败'
                ]);
            }

        } catch (Exception $e) {
            Log::error("同步退款到套餐订单系统异常", [
                'order_id' => $data['order_id'] ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 触发同步失败事件
            event('wx.refundSyncFailed', [
                'order_id' => $data['order_id'] ?? '',
                'refund_id' => $data['refund_id'] ?? '',
                'error_message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 退款同步完成事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onRefundSyncCompleted(array $data, string $eventName = ''): void
    {
        try {
            Log::info("退款同步完成事件", [
                'order_id' => $data['order_id'] ?? '',
                'refund_id' => $data['refund_id'] ?? '',
                'sync_result' => $data['sync_result'] ?? ''
            ]);

            // 这里可以添加同步完成后的处理逻辑
            // 例如：发送通知、更新状态等

        } catch (Exception $e) {
            Log::error("退款同步完成事件处理失败", [
                'order_id' => $data['order_id'] ?? '',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 退款同步失败事件监听
     * @param array $data 事件数据
     * @param string $eventName 事件名称
     */
    public function onRefundSyncFailed(array $data, string $eventName = ''): void
    {
        try {
            Log::error("退款同步失败事件", [
                'order_id' => $data['order_id'] ?? '',
                'refund_id' => $data['refund_id'] ?? '',
                'error_message' => $data['error_message'] ?? ''
            ]);

            // 这里可以添加同步失败后的处理逻辑
            // 例如：重试机制、告警通知等

        } catch (Exception $e) {
            Log::error("退款同步失败事件处理失败", [
                'order_id' => $data['order_id'] ?? '',
                'error' => $e->getMessage()
            ]);
        }
    }
}
