<?php

namespace app\wx\model;

use think\model\relation\HasMany;
use think\model\relation\HasOne;

/**
 * 客户账户模型 - 微信端专用
 */
class CustAccountModel extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_account_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_account';

    /**
     * 软删除字段
     * @var string
     */
    protected string $deleteTime = 'delete_time';

    /**
     * 时间字段
     * @var string
     */
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 一对一关联 客户信息
     * @return HasOne
     */
    public function custUser(): HasOne
    {
        return $this->hasOne(CustUserModel::class, 'cust_uid', 'cust_uid')
            ->field('cust_uid,cust_name,cust_private_phone');
    }

    /**
     * 一对多关联 余额记录
     * @return HasMany
     */
    public function balanceRecords(): HasMany
    {
        return $this->hasMany(CustBalanceRecordModel::class, 'cust_uid', 'cust_uid')
            ->order('create_time desc');
    }

    /**
     * 一对多关联 交易记录
     * @return HasMany
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(CustTransactionModel::class, 'cust_uid', 'cust_uid')
            ->order('create_time desc');
    }

    /**
     * 账户类型搜索
     */
    public function searchCustAccountTypeAttr($query, $value)
    {
        $query->where('cust_account_type', $value);
    }

    /**
     * 账户状态搜索
     */
    public function searchCustAccountStatusAttr($query, $value)
    {
        $query->where('cust_account_status', $value);
    }

    /**
     * 获取用户账户信息
     * @param int $custUid
     * @param int $accountType 1:余额账户 2:补贴账户
     * @return array
     */
    public function getAccountInfo(int $custUid, int $accountType = 1): array
    {
        return $this->where('cust_uid', $custUid)
            ->where('cust_account_type', $accountType)
            ->where('cust_account_status', 1)
            ->field('cust_account_id,cust_account_name,cust_account_type,cust_balance')
            ->findOrEmpty()
            ->toArray();
    }

    /**
     * 获取用户所有账户信息
     * @param int $custUid
     * @return array
     */
    public function getAllAccountInfo(int $custUid): array
    {
        return $this->where('cust_uid', $custUid)
            ->where('cust_account_status', 1)
            ->field('cust_account_id,cust_account_name,cust_account_type,cust_balance')
            ->select()
            ->toArray();
    }

    /**
     * 更新账户余额
     * @param int $accountId
     * @param float $amount
     * @param string $type 'inc' 增加 'dec' 减少
     * @return bool
     */
    public function updateBalance(int $accountId, float $amount, string $type = 'dec'): bool
    {
        if ($type === 'inc') {
            return $this->where('cust_account_id', $accountId)->inc('cust_balance', $amount)->update();
        } else {
            return $this->where('cust_account_id', $accountId)->dec('cust_balance', $amount)->update();
        }
    }

    /**
     * 检查账户余额是否足够
     * @param int $accountId
     * @param float $amount
     * @return bool
     */
    public function checkBalance(int $accountId, float $amount): bool
    {
        $account = $this->where('cust_account_id', $accountId)->field('cust_balance')->findOrEmpty();
        return !empty($account) && $account['cust_balance'] >= $amount;
    }
}
