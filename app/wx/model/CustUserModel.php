<?php

namespace app\wx\model;

use think\model\relation\HasMany;
use think\model\relation\HasOne;

/**
 * 客户用户模型 - 微信端专用
 */
class CustUserModel extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_uid';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_user';

    /**
     * 软删除字段
     * @var string
     */
    protected string $deleteTime = 'delete_time';

    /**
     * 时间字段
     * @var string
     */
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 一对一关联 微信用户信息
     * @return HasOne
     */
    public function wxUser(): HasOne
    {
        return $this->hasOne(WxUserModel::class, 'cust_uid', 'cust_uid')
            ->field('uid,cust_uid,truename,phone,nickname,headimgurl,role');
    }

    /**
     * 一对多关联 客户账户信息
     * @return HasMany
     */
    public function accounts(): HasMany
    {
        return $this->hasMany(CustAccountModel::class, 'cust_uid', 'cust_uid')
            ->where('cust_account_status', 1);
    }

    /**
     * 一对一关联 余额账户
     * @return HasOne
     */
    public function balanceAccount(): HasOne
    {
        return $this->hasOne(CustAccountModel::class, 'cust_uid', 'cust_uid')
            ->where('cust_account_type', 1)
            ->where('cust_account_status', 1);
    }

    /**
     * 一对一关联 补贴账户
     * @return HasOne
     */
    public function subsidyAccount(): HasOne
    {
        return $this->hasOne(CustAccountModel::class, 'cust_uid', 'cust_uid')
            ->where('cust_account_type', 2)
            ->where('cust_account_status', 1);
    }

    /**
     * 姓名搜索
     */
    public function searchCustNameAttr($query, $value)
    {
        $query->where('cust_name', 'like', '%' . $value . '%');
    }

    /**
     * 手机号搜索
     */
    public function searchCustPrivatePhoneAttr($query, $value)
    {
        $query->where('cust_private_phone', $value);
    }

    /**
     * 获取客户基本信息（微信端专用）
     * @param int $custUid
     * @return array
     */
    public function getCustBasicInfo(int $custUid): array
    {
        $data = $this->where('cust_uid', $custUid)
            ->field('cust_uid,cust_name,cust_private_phone,cust_live_address,cust_community,cust_is_bed,cust_aging,cust_city')
            ->findOrEmpty()
            ->toArray();

        if (!empty($data['cust_city'])) {
            $custCity = json_decode($data['cust_city'], true);
            $area = implode('', array_map(fn($key) => !empty($custCity[$key]) ? $custCity[$key] : '', 
                ['province', 'city', 'area', 'street', 'community']));
            $data['area'] = $area;
        }

        return $data;
    }
}
