<?php

namespace app\wx\model\users;


use app\wx\model\BaseModel;
use app\wx\model\store\StoreProduct;
use app\wx\model\store\StoreProductCate;
use app\wx\model\store\StoreProductReplyModel;
use think\model\relation\HasOne;


class WxStoreOrderModel extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'order_id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_order';
    protected $table = 'wx_store_order';

    /**
     * 设置json类型字段
     */
    protected $json = [
        'order_info',
        'pay_info',
    ];

    /**
     * 设置JSON数据返回数组
     */
    protected $jsonAssoc = true;

    /**
     * 一对一关联 商品信息
     * @return HasOne
     */
    public function productInfo(): HasOne
    {
        return $this->hasOne(StoreProduct::class,'product_id','product_id')->field('product_id,cate_id,store_name');
    }

    /**
     * 一对一关联 评价信息
     * @return HasOne
     */
    public function replyInfo(): HasOne
    {
        return $this->hasOne(StoreProductReplyModel::class,'order_id','order_id')->field('order_id,reply_id,product_score,comment');
    }

    /**
     * 一对一关联 商品分类信息
     * @return HasOne
     */
    public function productCateInfo(): HasOne
    {
        return $this->hasOne(StoreProductCate::class,'product_id','product_id')->field('id,product_id,cate_id');
    }
}
