<?php

namespace app\wx\model\users;

use app\model\SysUserModel;
use app\wx\model\BaseModel;
use think\model\relation\HasOne;


class StoreOrderService extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_order_service';
    protected $table = 'wx_store_order_service';

    /**
     * 设置json类型字段
     */
    protected $json = ['service_info','user_info'];

    /**
     * 设置json类型字段返回数组
     */
    protected $jsonAssoc = true;

    /**
     * 关联支付订单
     * @return HasOne
     */
    public function payOrderInfo(): HasOne
    {
        return $this->hasOne(StoreOrder::class,'order_id','order_id')->with(['mainInfo']);
    }

    /**
     * 关联账单订单
     * @return HasOne
     */
    public function billInfo(): HasOne
    {
        return $this->hasOne(StoreOrderBillModel::class,'order_id','order_id');
    }

    /**
     * 关联账单订单
     * @return HasOne
     */
    public function refundInfo(): HasOne
    {
        return $this->hasOne(StoreOrderRefundModel::class,'order_id','order_id');
    }

    /**
     * 一对一关联 创建人信息
     * @return HasOne
     */
    public function createdInfo(): HasOne
    {
        return $this->hasOne(SysUserModel::class,'id','created_by')->field('id,user_nickname');
    }

}