<?php

namespace app\wx\model;

use think\model\relation\HasOne;

/**
 * 客户余额记录模型 - 微信端专用
 */
class CustBalanceRecordModel extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'cust_balance_record_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'cust_balance_record';

    /**
     * 软删除字段
     * @var string
     */
    protected string $deleteTime = 'delete_time';

    /**
     * 时间字段
     * @var string
     */
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * 一对一关联 客户信息
     * @return HasOne
     */
    public function custUser(): HasOne
    {
        return $this->hasOne(CustUserModel::class, 'cust_uid', 'cust_uid')
            ->field('cust_uid,cust_name,cust_private_phone');
    }

    /**
     * 一对一关联 账户信息
     * @return HasOne
     */
    public function account(): HasOne
    {
        return $this->hasOne(CustAccountModel::class, 'cust_account_id', 'cust_account_id')
            ->field('cust_account_id,cust_account_name,cust_account_type');
    }

    /**
     * 记录类型搜索
     */
    public function searchRecordTypeAttr($query, $value)
    {
        $query->where('record_type', $value);
    }

    /**
     * 时间范围搜索
     */
    public function searchCreateTimeAttr($query, $value)
    {
        $query->whereTime('create_time', 'between', $value);
    }

    /**
     * 获取用户余额记录
     * @param int $custUid
     * @param int $page
     * @param int $limit
     * @return array
     */
    public function getUserBalanceRecords(int $custUid, int $page = 1, int $limit = 20): array
    {
        $offset = ($page - 1) * $limit;
        
        $list = $this->where('cust_uid', $custUid)
            ->field('cust_balance_record_id,record_type,record_amount,record_balance,record_remark,create_time')
            ->order('create_time desc')
            ->limit($offset, $limit)
            ->select()
            ->toArray();

        $total = $this->where('cust_uid', $custUid)->count();

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }

    /**
     * 创建余额记录
     * @param array $data
     * @return bool
     */
    public function createRecord(array $data): bool
    {
        $data['create_time'] = date('Y-m-d H:i:s');
        return $this->save($data);
    }
}
