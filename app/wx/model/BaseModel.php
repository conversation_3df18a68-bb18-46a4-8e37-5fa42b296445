<?php

namespace app\wx\model;

use think\Model;
use think\model\concern\SoftDelete;

class BaseModel extends Model
{
    /**
     * 引入软删除、通用方法类
     */
    use SoftDelete;

    /**
     * 软删除标记字段
     * @var string
     */
    protected string $deleteTime = 'deleted_at';

    /**
     * 自动写入create_time、update_time
     * @var boolean
     */
    protected $autoWriteTimestamp = 'datetime';

    /**
     * 定义时间戳字段名 添加
     * @var string
     */
    protected $createTime = 'created_at';

    /**
     * 定义时间戳字段名 修改
     * @var string
     */
    protected $updateTime = 'updated_at';
}
