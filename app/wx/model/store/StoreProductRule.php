<?php

namespace app\wx\model\store;


use app\wx\model\BaseModel;
use think\Model;

/**
 * 商品规则
 * Class StoreProductRule
 */
class StoreProductRule extends BaseModel
{
    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_product_rule';
    protected $table = 'wx_store_product_rule';

    /**
     * 属性模板名称搜索器
     * @param Model $query
     * @param $value
     * @param $data
     */
    public function searchRuleNameAttr($query, $value)
    {
        $query->where('rule_name', 'like', '%' . $value . '%');
    }
}
