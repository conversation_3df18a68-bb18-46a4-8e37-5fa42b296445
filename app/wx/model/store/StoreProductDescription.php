<?php

namespace app\wx\model\store;


use app\wx\model\BaseModel;
use think\Model;

/**
 *  商品详情Model
 * Class StoreDescription
 */
class StoreProductDescription extends BaseModel
{
    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_product_description';
    protected $table = 'wx_store_product_description';


    public function getDescriptionAttr($value)
    {
        return htmlspecialchars_decode($value);
    }

    /**
     * 商品ID搜索器
     * @param $query
     * @param $value
     */
    public function searchProductIdAttr($query, $value)
    {
        if ($value) {
            $query->where('product_id', $value);
        }
    }

    /**
     * 类型搜索器
     * @param $query
     * @param $value
     */
    public function searchTypeAttr($query, $value)
    {
        $query->where('type', $value);
    }
}
