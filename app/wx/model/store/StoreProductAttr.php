<?php

namespace app\wx\model\store;

use app\wx\model\BaseModel;
use think\Model;

/**
 *   商品属性Model
 * Class StoreProductAttr
 */
class StoreProductAttr extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_product_attr';
    protected $table = 'wx_store_product_attr';

    /**
     * 设置json类型字段
     */
    protected $json = ['rule'];

    /**
     * 设置json类型字段返回数组
     */
    protected $jsonAssoc = true;
}
