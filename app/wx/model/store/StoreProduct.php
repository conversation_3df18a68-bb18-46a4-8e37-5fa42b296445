<?php

namespace app\wx\model\store;

use app\wx\model\BaseModel;
use think\Model;
use think\model\relation\HasMany;
use think\model\relation\HasOne;
use app\wx\model\store\StoreProductDescription;
use app\wx\model\store\StoreProductCate;
use app\wx\model\store\StoreProductAttr;
use app\wx\model\store\StoreProductAttrValue;

/**
 *  商品Model
 * Class StoreProduct
 */
class StoreProduct extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'product_id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_product';

    protected $table = 'wx_store_product';

    /**
     * 一对一关联
     * 商品关联商品商品详情
     * @return HasOne
     */
    public function description()
    {
        return $this->hasOne(StoreProductDescription::class, 'product_id', 'product_id')->bind(['desc'=>'description']);
    }



    /**
     * 分类一对多
     * @return HasMany
     */
    public function cateName()
    {
        return $this->hasMany(StoreProductCate::class, 'product_id', 'product_id')->with('cateName');
    }

    /**
     * 规格一对一
     * @return hasOne
     */
    public function attrName()
    {
        return $this->hasOne(StoreProductAttr::class, 'product_id', 'product_id');
    }


    /**
     * 分类一对多
     * @return HasMany
     */
    public function attrValue()
    {
        return $this->hasMany(StoreProductAttrValue::class, 'product_id', 'product_id');
    }

    /**
     * 轮播图获取器
     * @param $value
     * @return array|mixed
     */
    public function getSliderImageAttr($value)
    {
        return is_string($value) ? json_decode($value, true) : [];
    }

    /**
     * 是否显示搜索器
     * @param $query
     * @param $value
     */
    public function searchIsShowAttr($query, $value)
    {
        $query->where('is_show', $value ?? 1);
    }

    /**
     * @param Model $query
     * @param $value
     */
    public function searchIdAttr($query, $value)
    {
        if (is_array($value)) {
            $query->whereIn('id', $value);
        } else {
            $query->where('id', $value);
        }
    }

    /**
     * 是否删除搜索器
     * @param Model $query
     * @param $value
     */
    public function searchIsDelAttr($query, $value)
    {
        $query->where('is_del', $value ?: 0);
    }

    /**
     * 商户ID搜索器
     * @param Model $query
     * @param $value
     */
    public function searchMerIdAttr($query, $value)
    {
        $query->where('mer_id', $value ?? 0);
    }

    /**
     * keyword搜索器
     * @param Model $query
     * @param $value
     * @param $data
     */
    public function searchStoreNameAttr($query, $value, $data)
    {
        if ($value != '') {
            $field = 'keyword|store_name|store_info|id';
            if (is_string($value)) {
                $query->whereLike($field, htmlspecialchars("%" . trim($value) . "%"));
            } elseif (is_array($value) && count($value) > 0) {
                $query->where(function ($q) use ($value, $field) {
                    $data = [];
                    foreach ($value as $k) {
                        $data[] = [$field, 'like', "%" . trim($k) . "%"];
                    }
                    $q->whereOr($data);
                });
            }
        }
    }


    /**
     * SPU搜索器
     * @param Model $query
     * @param int $value
     */
    public function searchSpuAttr($query, $value)
    {
        $query->where('spu', $value);
    }

    /**
     * 库存搜索器
     * @param Model $query
     * @param int $value
     */
    public function searchStockAttr($query, $value)
    {
        $query->where('stock', $value);
    }


    /**
     * 分类搜索器
     * @param Model $query
     * @param int $value
     */
    public function searchCateIdAttr($query, $value)
    {
        if ($value) {
            if (is_array($value)) {
                $query->whereIn('id', function ($query) use ($value) {
                    $query->name('wx_store_product_cate')->where('cate_id', 'IN', $value)->field('product_id')->select();
                });
            } else {
                $query->whereFindInSet('cate_id', $value);
            }
        }
    }

    /**
     * 在当前id中查询
     * @param $query
     * @param $value
     */
    public function searchIdsAttr($query, $value)
    {
        if (is_string($value)) {
            $value = explode(',', $value);
        }
        if (count($value)) {
            $query->whereIn('id', $value);
        }
    }

    /**
     * 不在当前id中查询
     * @param $query
     * @param $value
     */
    public function searchNotIdsAttr($query, $value)
    {
        if ($value != '') {
            $query->whereNotIn('id', $value);
        }
    }
}
