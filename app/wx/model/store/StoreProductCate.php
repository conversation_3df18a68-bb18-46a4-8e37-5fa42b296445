<?php

namespace app\wx\model\store;

use app\wx\model\BaseModel;
use think\Model;
use app\wx\model\store\StoreCategory;


/**
 *  商品分类关联Model
 * Class StoreProductCate
 */
class StoreProductCate extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    /**
     * 模型名称
     * @var string
     */
    protected $name = 'wx_store_product_cate';
    protected $table = 'wx_store_product_cate';

    /**
     * 一对一关联获取分类名称
     */
    public function cateName()
    {
        return $this->hasOne(StoreCategory::class, 'id', 'cate_id')->bind([
            'cate_name' => 'cate_name'
        ]);
    }

}
