<?php

namespace app\wx\controller;

use Exception;
use plugin\saiadmin\utils\JwtAuth;
use support\Request;
use support\Response;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use yzh52521\EasyHttp\Http;

class LoginController extends BaseController
{
    /**
     * token检查接口
     */
    public function checkToken(Request $request): Response
    {
        // 检查token
        $uid = $request->wxUid;
        return $this->success($uid);
    }

    /**
     * 微信开发平台登录
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function wechatOpenUserInfo(Request $request): Response
    {
        try {
            validate(\app\wx\validate\CommonValidate::class)
                ->scene('login')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $code = $request->input('code');
        // 通过code 获取微信用户信息
        $response = http::post('api.card.zkshlm.com/notice/wechatOpenUserInfo', ['code' => $code])->array();
        if($response['code'] !== 200) {
            return $this->fail($response['message']);
        }
        $wxInfo = $response['data'];
        // 查询用户是否注册
        $userId = $this->dao->get('wx_user',[
            'deleted_at' => null,
            'unionid' => $wxInfo['unionid'],
        ],'uid');
        if(empty($userId)) {
            // 注册用户
            $userId = $this->dao->insert('wx_user',[
                'created_at' => date('Y-m-d H:i:s'),
                'truename' => $wxInfo['nickname'],
                'nickname' => $wxInfo['nickname'],
                'unionid' => $wxInfo['unionid'],
                'headimgurl' => !empty($wxInfo['headimgurl']) ? $wxInfo['headimgurl'] : $wxInfo['avatar'],
                'ip' => $request->getLocalIp(),
                'register_source' => '微信开放平台注册',
                'role' => '普通用户'
            ]);
        }
        $jwt = new JwtAuth();
        $token = $jwt->createToken($userId, '微信客户', 'H5');
        return $this->success($token);
    }

    /**
     * 微信公众平台登录
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function wechatMpUserInfo(Request $request): Response
    {
        try {
            validate(\app\wx\validate\CommonValidate::class)
                ->scene('login')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $code = $request->input('code');
        // 通过code 获取微信用户信息
        $response = http::post('api.card.zkshlm.com/notice/wechatMpUserInfo', ['code' => $code])->array();
        // 判断是否首次授权
        if($response['code'] == 48001) {
            return json(['code' => 1001]);
        }
        if($response['code'] !== 200) {
            return $this->fail($response['message']);
        }
        $wxInfo = $response['data'];
        // 查询用户是否注册
        $userId = $this->dao->get('wx_user',[
            'deleted_at' => null,
            'unionid' => $wxInfo['unionid'],
        ],'uid');
        if(empty($userId)) {
            // 注册用户
            $userId = $this->dao->insert('wx_user',[
                'created_at' => date('Y-m-d H:i:s'),
                'truename' => $wxInfo['nickname'],
                'nickname' => $wxInfo['nickname'],
                'unionid' => $wxInfo['unionid'],
                'headimgurl' => !empty($wxInfo['headimgurl']) ? $wxInfo['headimgurl'] : $wxInfo['avatar'],
                'ip' => $request->getLocalIp(),
                'register_source' => '微信公平台注册',
                'role' => '普通用户'
            ]);
        }
        $jwt = new JwtAuth();
        $token = $jwt->createToken($userId, '微信客户', 'H5');
        return $this->success($token);
    }
}