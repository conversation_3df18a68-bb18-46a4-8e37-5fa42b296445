<?php

namespace app\wx\controller;

use app\cust\model\CustUser;
use app\wx\model\users\WxStoreAddressModel;
use app\wx\model\CustAccountModel;
use app\wx\service\UnifiedPackageOrderService;
use Exception;
use support\Request;
use support\Response;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use Webman\Medoo\Medoo;
use app\library\common\Minio;

class UserController extends BaseController
{
    protected string $pk = "uid";

    /**
     * 获取用户信息
     * @param Request $request
     * @return Response
     */
    public function getUserInfo(Request $request): Response
    {
        // 获取微信用户信息
        $data = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($data)) {
            throw new BadRequestHttpException('用户信息不存在');
        }

        // 查询客户档案信息
        if (!empty($data['cust_uid'])) {
            $mainModel = new CustUser();
            $mainUserInfo = $mainModel
                ->where('cust_uid', $data['cust_uid'])
                ->field('cust_is_bed as bed_status,cust_aging as bed_type')
                ->findOrEmpty()
                ->toArray();
            $data['bed_info'] = $mainUserInfo;

            // 获取账户信息
            $accountModel = new CustAccountModel();
            $accounts = $accountModel->getAllAccountInfo($data['cust_uid']);
            $data['account_info'] = $accounts;
        } else {
            $data['bed_info'] = [];
            $data['account_info'] = [];
        }

        return $this->success($data);
    }

    /**
     * 绑定查询
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function bindQuery(Request $request): Response
    {
        $phone = $request->input('phone',0);
        // 查询老人信息
        $mainModel = new CustUser();
        $mainUserInfo = $mainModel->where([ 'cust_private_phone' => $phone, 'delete_time' => null ])
            ->field('cust_uid,cust_name as truename,cust_live_address as address,cust_city')
            ->findOrEmpty()->toArray();
        if(empty($mainUserInfo)) {
            return $this->success('无绑定信息');
        }
        $mainUserInfo['phone'] = $phone;
        if(!empty($mainUserInfo['truename']) && preg_match('/[\x{4e00}-\x{9fa5}]/u', $mainUserInfo['address'])) {
            $mainUserInfo['truename'] = mb_substr($mainUserInfo['truename'], 0, 1) . '**';
        }
        if(!empty($mainUserInfo['address']) && preg_match('/[\x{4e00}-\x{9fa5}]/u', $mainUserInfo['address'])) {
            $mainUserInfo['address'] = mb_substr($mainUserInfo['address'], 0, 3) . '**';
        }
         if(!empty($mainUserInfo['cust_city'])) {
            $cust_city = !empty($mainUserInfo['cust_city']) ? json_decode($mainUserInfo['cust_city'], true) : [];
            // var_dump('bindQuery-=-=-=---1-----cust_city-------',$cust_city );
            $area = implode('', array_map(fn($key) => !empty($cust_city[$key]) ? $cust_city[$key] : '',  ['province', 'city', 'area', 'street', 'community']));
            // var_dump('bindQuery-=-=-=---1-----area-------',$area );
            $mainUserInfo['address'] = $area . $mainUserInfo['address'];
            // $mainUserInfo['address'] = mb_substr($mainUserInfo['address'], 0, 3) . '**';
        }
        // 返回绑定信息
        return $this->success($mainUserInfo);
    }

    /**
     * 绑定用户信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function bindUserInfo(Request $request): Response
    {
        $phone = $request->input('phone',null);
        $cust_uid = $request->input('cust_uid','');
        // var_dump('bindUserInfo-=-=-=-------1----cust_uid----',$cust_uid );

        $mainModel = new CustUser();
        $mainUserInfo = [];
        if(empty($cust_uid)){
            // 创建老人档案


        }else {
            // 查询老人信息
            $mainUserInfo = $mainModel->where('cust_uid',$cust_uid)
                ->field('cust_name as truename,cust_city as area,cust_live_address as address')
                ->findOrEmpty()->toArray();
        }
        // var_dump('bindUserInfo-=-=-=-------1--------', );

        if(empty($mainUserInfo)) {
            // 修改用户手机号
            // $userModel = new WxUserModel();
            // $userModel->where('uid',$request->wxUid)
            //     ->update([
            //         'cust_uid'=>$cust_uid,
            //     ]);
            // return $this->success('无绑定信息');
            
            throw new BadRequestHttpException('无绑定信息');
        } else {
            $cust_city = !empty($mainUserInfo['area']) ? json_decode($mainUserInfo['area'], true) : [];
            $area = implode('', array_map(fn($key) => !empty($cust_city[$key]) ? $cust_city[$key] : '',  ['province', 'city', 'area', 'street', 'community']));
            // var_dump('$area-=-=-=---------------',$area);
            if(!empty($area))$mainUserInfo['area'] = $area;
        }

        // var_dump('bindUserInfo-=-=-=---------------',$mainUserInfo );
        // 创建默认地址
        // $wxUserAddressModel = new WxStoreAddressModel();
        // $wxUserAddressModel->where('uid',$request->wxUid)->update([ 'is_default' => 0 ]);
        // var_dump('bindUserInfo-=-=-=------------wxUserAddressModel---', );
        $this->dao->update('wx_user_address',[ 'is_default' => 0 ], ['uid' => $request->wxUid]);
        
        // var_dump('bindUserInfo-=-=-=------------wxUserAddressModel-1--', );
        
        $data1 = [
            'uid'=>$request->wxUid,
            'name'=>$mainUserInfo['truename'],
            'phone'=>$phone,
            'area'=>$mainUserInfo['area'],
            'address'=>$mainUserInfo['address'],
            // 'longitude'=>$mainUserInfo['userLongitude'],
            // 'latitude'=>$mainUserInfo['userLatitude'],
            'is_default'=>1,
            'created_by' => $request->wxUid,
            'created_at' => date('Y-m-d H:i:s'),
        ];
        // var_dump('bindUserInfo-=-=-=------------data1-1--', $data1);
        // return '';
        // $wxUserAddressModel->create($data1);
        // 创建默认地址
        $this->dao->insert('wx_user_address',$data1);
        // var_dump('bindUserInfo-=-=-=------------wxUserAddressModel-2--', );

        // 更新微信用户信息
        // $userModel = new WxUserModel();
        // $userModel->where('uid',$request->wxUid)
        //     ->update([
        //         'truename'=>$mainUserInfo['truename'],
        //         'phone'=>$phone,
        //         'user_address'=>$mainUserInfo['area'],
        //         'user_address_detail'=>$mainUserInfo['address'],
        //     ]);
        $this->dao->update('wx_user',[
            'cust_uid'=>$cust_uid,
            'truename'=>$mainUserInfo['truename'],
            'phone'=>$phone,
            'user_address'=>$mainUserInfo['area'],
            'user_address_detail'=>$mainUserInfo['address'],
            'updated_at' => date('Y-m-d H:i:s'),
        ], ['uid' => $request->wxUid]);

        return $this->success($mainUserInfo);
    }

    /**
     * 修改用户信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function saveUserInfo(Request $request): Response
    {
        $params = $request->only([
            'truename',
            'nickname',
            'headimgurl',
            'sex',
            'phone',
            'user_address',
            'user_address_detail',
            'birthday',
        ]);
        // var_dump('saveUserInfo-=-=-=---------------',$params );

        // try {
        //     validate(\app\validate\WxUserValidate::class)
        //         ->scene('user')
        //         ->check($params);
        // } catch (Exception $e) {
        //     throw new BadRequestHttpException($e->getMessage());
        // }
        //     var_dump('saveUserInfo-=-=-=---1------------' );

        // $userModel = new WxUserModel();
        // $data = $userModel->update($params,[
        //     'uid'=>$request->wxUid
        // ])->toArray();

        $data = $this->dao->get('wx_user',[  'deleted_at' => null,  'uid' => $request->wxUid, ]);
        // var_dump('saveUserInfo-=-=-=------------$data---',$data );
        if (!$data) {
            return $this->fail('没有修改用户数据!');
        }

        $params['updated_by'] = $request->wxUid;
        $params['updated_at'] =  date('Y-m-d H:i:s');
        $result = $this->dao->update('wx_user',$params, ['uid' => $request->wxUid]);
        // var_dump('saveUserInfo-=-=-=------------$result---',$result );

        if ($result) {
            return $this->success($data);
        }else{
            return $this->fail('操作失败');
        }
    }

    /**
     * 上传头像图片
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function upload(Request $request): Response
    {
        // 验证图片
        if(empty(request()->file()['file'])) {
            throw new BadRequestHttpException('请选择上传文件');
        }
        $file = request()->file()['file'];
        if (!$file->isValid()) {
            throw new BadRequestHttpException('无效内容');
        }
        $md5 = md5_file($file->getRealPath());
        // 查询图片
        $array = [
            'jpg',
            'png',
        ];
        $size = $this->getSize($file->getSize());
        $realName = $file->getUploadName();
        $type = $file->getUploadExtension();
        if(!in_array($type,$array)) {
            throw new BadRequestHttpException('请选择正确的文件类型');
        }
        if($file->getSize() > 10485760) {
            throw new BadRequestHttpException('文件大小超过10M');
        }
        // 上传图片
        $url = Medoo::get('zk_system_attachment','src',[
            'md5'=>$md5
        ]);
        if(empty($url)) {
            $url = Minio::uploadFile($file, $realName);
        }
        // 添加图片信息
        try {
            $data['name'] = $realName;
            $data['real_name'] = $realName;
            $data['pid'] = 0;
            $data['src'] = $url;
            $data['ext'] = $type;
            $data['type'] = $type;
            $data['size'] = $size;
            $data['md5'] = $md5;
            $data['module_type'] = 2;
            $data['created_by'] = request()->wxUid;
            Medoo::insert('zk_system_attachment',$data);
        } catch (\Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        return $this->success($data);
    }

    /**
     * 测试接口
     */
    public function test(Request $request): Response
    {
        UnifiedPackageOrderService::createAndSync([
            "order_id"=>1000,
            "order_no"=>"791915699848087203"
        ]);
        return $this->success('ok');
    }
}