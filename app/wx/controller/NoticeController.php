<?php

namespace app\wx\controller;

use app\wx\service\WxPaymentService;
use EasyWeChat\Pay\Application;
use EasyWeChat\Pay\Message;
use support\Log;
use support\Request;
use support\Response;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\Request as SymfonyRequest;
use Throwable;
use Webman\Config;

/**
 * 通知回调控制器
 */
class NoticeController
{
    /**
     * 微信支付回调通知
     * @param Request $request
     * @return Response
     * @throws Throwable
     */
    public function wxPayNotify(Request $request): Response
    {
        try {
            // 转换请求格式
            $symfony_request = new SymfonyRequest(
                $request->get(), 
                $request->post(), 
                [], 
                $request->cookie(), 
                [], 
                [], 
                $request->rawBody()
            );
            $symfony_request->headers = new HeaderBag($request->header());
            
            // 获取微信支付配置（与旧版一致）
            $config = Config::get('wechat.pay');
            if (empty($config)) {
                Log::error('微信支付配置未设置');
                return response('FAIL');
            }
            
            // 创建微信支付应用
            $app = new Application($config);
            $app->setRequestFromSymfonyRequest($symfony_request);
            $server = $app->getServer();
            
            // 处理支付成功回调
            $server->handlePaid(function (Message $message, \Closure $next) {
                Log::info('收到微信支付成功回调', [
                    'out_trade_no' => $message->out_trade_no,
                    'transaction_id' => $message->transaction_id,
                    'total_amount' => $message->amount['total'] ?? 0,
                    'trade_state' => $message->trade_state
                ]);
                
                // 处理支付成功
                $result = WxPaymentService::handlePaymentSuccess($message->out_trade_no, [
                    'transaction_id' => $message->transaction_id,
                    'total_amount' => ($message->amount['total'] ?? 0) / 100,
                    'trade_state' => $message->trade_state,
                    'trade_type' => $message->trade_type ?? '',
                    'success_time' => $message->success_time ?? ''
                ]);
                
                if (!$result) {
                    Log::error('支付成功处理失败', ['out_trade_no' => $message->out_trade_no]);
                }
                
                return $next($message);
            });
            
            // 处理退款回调
            $server->handleRefunded(function (Message $message, \Closure $next) {
                Log::info('收到微信退款成功回调', [
                    'out_trade_no' => $message->out_trade_no,
                    'out_refund_no' => $message->out_refund_no,
                    'refund_status' => $message->refund_status
                ]);
                
                // 这里可以添加退款成功的处理逻辑
                // 例如：更新订单状态、发送通知等
                
                return $next($message);
            });
            
            // 处理回调
            $response = $server->serve();
            
            Log::info('微信支付回调处理完成');
            return response($response->getBody());
            
        } catch (Throwable $e) {
            Log::error('微信支付回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response('FAIL');
        }
    }
    
    /**
     * 微信退款回调通知
     * @param Request $request
     * @return Response
     * @throws Throwable
     */
    public function wxRefundNotify(Request $request): Response
    {
        try {
            // 转换请求格式
            $symfony_request = new SymfonyRequest(
                $request->get(), 
                $request->post(), 
                [], 
                $request->cookie(), 
                [], 
                [], 
                $request->rawBody()
            );
            $symfony_request->headers = new HeaderBag($request->header());
            
            // 获取微信支付配置（与旧版一致）
            $config = Config::get('wechat.pay');
            if (empty($config)) {
                Log::error('微信支付配置未设置');
                return response('FAIL');
            }
            
            // 创建微信支付应用
            $app = new Application($config);
            $app->setRequestFromSymfonyRequest($symfony_request);
            $server = $app->getServer();
            
            // 处理退款成功回调
            $server->handleRefunded(function (Message $message, \Closure $next) {
                Log::info('处理微信退款回调', [
                    'out_trade_no' => $message->out_trade_no,
                    'out_refund_no' => $message->out_refund_no,
                    'refund_status' => $message->refund_status,
                    'refund_amount' => $message->amount['refund'] ?? 0
                ]);

                // 调用退款回调处理服务
                $notifyData = [
                    'out_trade_no' => $message->out_trade_no,
                    'out_refund_no' => $message->out_refund_no,
                    'refund_id' => $message->refund_id ?? '',
                    'refund_status' => $message->refund_status,
                    'amount' => [
                        'refund' => $message->amount['refund'] ?? 0,
                        'total' => $message->amount['total'] ?? 0
                    ],
                    'success_time' => $message->success_time ?? date('Y-m-d H:i:s'),
                    'raw_message' => $message->toArray()
                ];

                $result = \app\wx\service\WxPaymentService::handleRefundNotify($notifyData);

                if (!$result) {
                    Log::error('退款回调处理失败', ['out_refund_no' => $message->out_refund_no]);
                }

                return $next($message);
            });
            
            // 处理回调
            $response = $server->serve();
            
            Log::info('微信退款回调处理完成');
            return response($response->getBody());
            
        } catch (Throwable $e) {
            Log::error('微信退款回调处理异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response('FAIL');
        }
    }
    

    
    /**
     * 系统监控检查
     * @param Request $request
     * @return Response
     */
    public function monitor(Request $request): Response
    {
        $status = [
            'timestamp' => time(),
            'datetime' => date('Y-m-d H:i:s'),
            'status' => 'ok',
            'services' => [
                'database' => 'ok',
                'redis' => 'ok',
                'payment' => 'ok'
            ]
        ];
        
        // 这里可以添加各种服务的健康检查
        
        return json($status);
    }
}
