<?php

namespace app\wx\controller\store;

use app\wx\controller\BaseController;
use app\wx\model\store\StoreCategory;
use app\wx\model\store\StoreProduct;
use support\Request;
use support\Response;

class ProductController extends BaseController
{
    /**
     * 商品列表
     * @Apidoc\Url("/wx/store/product/productList")
     * @param Request $request
     * @return Response
     */
    public function productList(Request $request): Response
    {
        (int)$cateId = $request->input('cate_id',0);
        // var_dump('$productList-=-=-=-----------$cateId----',$cateId);
        $productModel = new StoreProduct();
        $data = $productModel->with(['cateName'])
        ->when(isset($cateId) && $cateId, function ($query) use ($cateId) {
            // var_dump('$productList-=-=-=----2.1-----------');
            return $query->hasWhere('cateName', [ 'cate_id' => $cateId ]);
        })->where('is_show',1)->where('wx_store_product.deleted_at',null)
        ->field('wx_store_product.product_id,recommend_image,slider_image,store_name,store_info,price,unit')
        ->order('sort desc')->select()->toArray();
        // var_dump('$productList-=-=-=----2-----------');
        return $this->success($data);
    }

    /**
     * 分类列表
     * @Apidoc\Url("/wx/store/product/cateList")
     * @param Request $request
     * @return Response
     */
    public function cateList(Request $request): Response
    {
        // var_dump('$cateList-=-=-=---------------');
        $cateModel = new StoreCategory();
        $data = $cateModel->where('is_show',1)->where('deleted_at',null)->field('cate_name,id')->order('sort desc,id ASC')->select()->toArray();
        // var_dump('$cateList-=-=-=------------data---',$data);
        return $this->success($data);
    }

    /**
     * 商品详情
     * @Apidoc\Url("/wx/store/product/productDetails")
     * @param Request $request
     * @return Response
     */
    public function productDetails(Request $request): Response
    {
        (int)$productId = $request->input('product_id',0);
        $productModel = new StoreProduct();
        $data = $productModel->with([
            'description',
            'cateName',
            'attrName',
            'attrValue'
        ])->findOrEmpty($productId)->toArray();
        return $this->success($data);
    }

    /**
     * 轮播图列表
     * @param Request $request
     * @return Response
     */
    public function bannerList(Request $request): Response
    {
        $data = [
            [
                'value'=>'http://img.zkshlm.com/zksh/pension/output.jpg',
                'path'=>'',
            ],
        ];
        return $this->success($data);
    }
}