<?php

namespace app\wx\controller\store;

use app\food\model\FoodPackage;
use app\wx\controller\BaseController;
use app\wx\model\store\StoreProduct;
use app\wx\model\users\WxStoreOrderModel;
use Exception;
use support\Request;
use support\Response;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class FoodController extends BaseController
{
    /**
     * 员工套餐查询
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function packageQueryV2(Request $request): Response
    {
        try {
            validate(\app\wx\validate\WxStoreFoodValidate::class)
                ->scene('list')
                ->check($request->all());
        } catch (Exception $e) {
            // 验证失败 输出错误信息
            throw new BadRequestHttpException($e->getMessage());
        }
        $params = $request->only(['start_time', 'end_time']);
        // 判断是否时间正确
        if ($params['start_time'] > $params['end_time']) {
            throw new BadRequestHttpException('开始时间不能大于结束时间');
        }
        $packageModel = new FoodPackage();
        // 查询有那些套餐
        $queryPackageName = $packageModel->withSearch(['day_time'], [
            'day_time' => [$params['start_time'], $params['end_time']],
        ])->where('type',0)->distinct(true)->field('name')->select()->toArray();
        // 初始化套餐模板
        if (empty($queryPackageName)) {
            return $this->success([], '套餐信息还未设置');
        }
        // 数据集处理
        $packageData = $packageModel->with(['foods'])
            ->withSearch(['day_time'], [
                'day_time' => [$params['start_time'], $params['end_time']],
            ])
            ->whereIn('name',['A1','A2','B1','B2','主食专供一','主食专供二','主食专供三','主食专供四','主食专供五','主食专供六','主食专供七'])
            ->where('type',0)->where('delete_time',null)
            ->visible([
                'package_id', 'name',  'price', 'config_id', 'day_time',
                'foods' => ['id', 'food_id', 'package_id', 'food_name', 'food_cate']])
            ->order('day_time', 'ASC')
            ->order('sort', 'ASC')
            ->select();
        $data = [];
        if ($packageData->isEmpty()) {
            return $this->success([], '请设置默认套餐模块');
        }
        // 进行初始化
        $diff = $this->dateDiff($params['start_time'], $params['end_time']) + 1;
        if ($diff > 0) {
            for ($i = 0; $i < $diff; $i++) {
                $time = date('Y-m-d', strtotime($params['start_time']) + $i * 86400);
                $arr = $packageData
                    ->where('day_time', $time)
                    ->toArray();
                $value = [];
                $ii = 0;
                foreach ($arr as $vv) {
                    if($ii == 0) {
                        $value = [
                            'time' => $time,
                            'fee' => 0,
                        ];
                    }
                    $menuArr = [];
                    foreach ($vv['foods'] as $vvv) {
                        $menuArr[] = $vvv['food_name'];
                    }
                    if($vv['name'] == '员工餐') {
                        continue;
                    }
                    $v2 = [
                        'package_id' => $vv['package_id'],
                        'name' => $vv['name'],
                        'price' => $vv['price'],
                        'num' => 0,
                        'data' => $menuArr,
                    ];
                    $value['options'][] = $v2;
                    $ii++;
                }
                $data[] = $value;
            }
        }
        // 查询套餐商品详情
        $foodModel = new StoreProduct();
        $productInfo = $foodModel->where('product_id',147)->findOrEmpty()->toArray();
        return $this->success([
            'options'=>$data,
            'productInfo'=>$productInfo
        ]);
    }



    /**
     * 套餐预定
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function packageBooking(Request $request): Response
    {
        // 站点名称转换（与旧版一致）
        $site = [
            '801服务点' => '中关村黄庄社区801服务站',
            '803服务点' => '中关村黄庄社区803服务站',
            '海淀科源社区养老驿站' => '中关村科源社区养老驿站',
            '海淀科星社区养老驿站' => '中关村科星社区养老驿站',
            '科苑酒店' => '中关村科苑酒店',
            '枫林绿洲' => '奥运村枫林绿洲社区服务站',
            '科学院南里三区' => '奥运村科学园社区南里三区站',
            '科学院南里五区' => '奥运村科学园社区南里五区站',
            '博世祥园' => '奥运村博世祥园社区站',
            '龙翔社区' => '奥运村龙祥社区服务站',
            '华严北里' => '亚运村华严北里社区服务站',
        ];

        $params = $request->only([
            'data',
            'product_id',
            'env',
            'real_name',
            'user_phone',
            'user_address',
            'user_notes',
            'user_longitude',
            'user_latitude',
            'delivery_option',
            'delivery_site',
            'bed_status',
            'bed_type',
        ]);

        foreach ($site as $k => $v) {
            if ($k == $params['delivery_site']) {
                $params['delivery_site'] = $v;
                break;
            }
        }

        $data = \app\wx\service\WxOrderService::createPackageOrderLegacy($params);
        return $this->success($data);
    }

    /**
     * 套餐预定-员工
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function packageBookingV2(Request $request): Response
    {
        $params = $request->only([
            'packages',
            'delivery_site',
            'delivery_address',
            'delivery_phone',
            'delivery_name',
            'remark'
        ]);

        try {
            validate(\app\wx\validate\WxStoreFoodValidate::class)
                ->scene('booking')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo)) {
            throw new BadRequestHttpException('用户信息不存在');
        }

        // 检查员工角色
        if ($userInfo['role'] !== '员工') {
            throw new BadRequestHttpException('只有员工可以使用此功能');
        }

        // 解析套餐数据
        $packages = json_decode($params['packages'], true);
        if (empty($packages)) {
            throw new BadRequestHttpException('套餐数据不能为空');
        }

        // 员工餐价格计算（可能有优惠）
        $totalPrice = 0;
        $orderItems = [];

        foreach ($packages as $package) {
            if (empty($package['package_id']) || empty($package['num']) || $package['num'] <= 0) {
                continue;
            }

            // 获取套餐信息
            $packageInfo = $this->dao->get('food_package', [
                'package_id' => $package['package_id'],
                'delete_time' => null
            ]);

            if (empty($packageInfo)) {
                throw new BadRequestHttpException('套餐信息不存在：' . $package['package_id']);
            }

            // 员工餐可能有特殊价格
            $employeePrice = $packageInfo['price']; // 这里可以根据业务需求调整员工价格
            $itemPrice = $employeePrice * $package['num'];
            $totalPrice += $itemPrice;

            $orderItems[] = [
                'package_id' => $package['package_id'],
                'package_name' => $packageInfo['name'],
                'package_price' => $employeePrice,
                'package_num' => $package['num'],
                'item_total' => $itemPrice,
                'day_time' => $package['day_time'] ?? ''
            ];
        }

        if ($totalPrice <= 0) {
            throw new BadRequestHttpException('订单金额不能为0');
        }

        // 创建员工订单
        $orderData = [
            'cust_uid' => $userInfo['cust_uid'] ?? 0,
            'order_type' => 1, // 套餐订单
            'product_name' => '员工营养套餐',
            'order_price' => $totalPrice,
            'order_status' => '待付款',
            'order_info' => json_encode([
                'packages' => $orderItems,
                'delivery_site' => $params['delivery_site'],
                'delivery_address' => $params['delivery_address'],
                'delivery_phone' => $params['delivery_phone'],
                'delivery_name' => $params['delivery_name'],
                'remark' => $params['remark'] ?? '',
                'is_employee' => true
            ])
        ];

        $orderModel = new \app\wx\model\OrderMainModel();
        $orderId = $orderModel->createOrder($orderData);

        if (!$orderId) {
            throw new BadRequestHttpException('订单创建失败');
        }

        // 站点名称转换（与旧版一致）
        $site = [
            '801服务点' => '中关村黄庄社区801服务站',
            '803服务点' => '中关村黄庄社区803服务站',
            '海淀科源社区养老驿站' => '中关村科源社区养老驿站',
            '海淀科星社区养老驿站' => '中关村科星社区养老驿站',
            '科苑酒店' => '中关村科苑酒店',
            '枫林绿洲' => '奥运村枫林绿洲社区服务站',
            '科学院南里三区' => '奥运村科学园社区南里三区站',
            '科学院南里五区' => '奥运村科学园社区南里五区站',
            '博世祥园' => '奥运村博世祥园社区站',
            '龙翔社区' => '奥运村龙祥社区服务站',
            '华严北里' => '亚运村华严北里社区服务站',
        ];

        $params = $request->only([
            'data',
            'product_id',
            'env',
            'real_name',
            'user_phone',
            'user_address',
            'user_notes',
            'user_longitude',
            'user_latitude',
            'delivery_option',
            'delivery_site',
            'bed_status',
            'bed_type',
        ]);

        foreach ($site as $k => $v) {
            if ($k == $params['delivery_site']) {
                $params['delivery_site'] = $v;
                break;
            }
        }

        $data = \app\wx\service\WxOrderService::createPackageOrderLegacy($params);
        return $this->success($data);
    }

    /**
     * 站点查询
     * @param Request $request
     * @return Response
     */
    public function stationQuery(Request $request): Response
    {
        // var_dump('stationQuery-=-=-=---------------',);
        $site = $this->dao->search('company_depart', [ 'company_label' => '801营养餐站点', 'delete_time' => null, ],['company_name(label)','company_name(value)']);
        // var_dump('stationQuery-=-=-=-------1--------', $site);
        // 获取用户上次下单信息
        $wxOrderModel = new WxStoreOrderModel();
        // var_dump('stationQuery-=-=-=----------2-----',);
        $orderInfo = $wxOrderModel->where('uid',$request->wxUid)
            ->where('order_type',3) 
            ->order('order_id','DESC')
            ->findOrEmpty()->toArray();
        $selectSite = $site[0]['value'];
        if(!empty($orderInfo)) {
            $selectSite = $orderInfo['order_info']['delivery_site'];
        }
        // 更新值
        foreach ($site as &$v) {
            $v['label'] = self::getNewSite($v['label']);
        }
        $selectSite = self::getNewSite($selectSite);
        return $this->success([
            'siteOption' => $site,
            'selectOption' => $selectSite
        ]);
    }

    /**
     * 套餐查询
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function packageQuery(Request $request): Response
    {
        $params = $request->only(['start_time', 'end_time']);

        try {
            validate(\app\wx\validate\WxStoreFoodValidate::class)
                ->scene('list')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        if ($params['start_time'] > $params['end_time']) {
            throw new BadRequestHttpException('开始时间不能大于结束时间');
        }

        $packageModel = new FoodPackage();
        $queryPackageName = $packageModel->withSearch(['day_time'], [
            'day_time' => [$params['start_time'], $params['end_time']],
        ])->where('type', 0)->distinct(true)->field('name')->select()->toArray();

        if (empty($queryPackageName)) {
            return $this->fail('套餐信息还未设置');
        }

        $packageData = $packageModel->with(['foods'])
            ->withSearch(['day_time'], [
                'day_time' => [$params['start_time'], $params['end_time']],
            ])->where('type', 0)->where('delete_time', null)
            ->visible([
                'package_id', 'name', 'price', 'config_id', 'day_time',
                'foods' => ['id', 'food_id', 'package_id', 'food_name', 'food_cate']])
            ->order('day_time', 'ASC')
            ->order('sort', 'ASC')
            ->select();

        if ($packageData->isEmpty()) {
            return $this->fail('请设置默认套餐模块');
        }

        $data = [];
        $diff = $this->dateDiff($params['start_time'], $params['end_time']) + 1;

        if ($diff > 0) {
            for ($i = 0; $i < $diff; $i++) {
                $time = date('Y-m-d', strtotime($params['start_time']) + $i * 86400);
                $arr = $packageData->where('day_time', $time)->toArray();

                $value = [];
                $ii = 0;
                foreach ($arr as $vv) {
                    if ($ii == 0) {
                        $value = [
                            'time' => $time,
                            'fee' => 0,
                        ];
                    }

                    $menuArr = [];
                    foreach ($vv['foods'] as $vvv) {
                        $menuArr[] = $vvv['food_name'];
                    }

                    if ($vv['name'] == '员工餐') {
                        continue;
                    }

                    $v2 = [
                        'package_id' => $vv['package_id'],
                        'name' => $vv['name'],
                        'price' => $vv['price'],
                        'num' => 0,
                        'data' => $menuArr,
                    ];

                    $value['options'][] = $v2;
                    $ii++;
                }
                $data[] = $value;
            }
        }

        return $this->success($data);
    }

    /**
     * 订单提交
     * @param Request $request
     * @return Response
     */
    public function orderSubmission(Request $request): Response
    {
        return $this->success([]);
    }
}