<?php

namespace app\wx\controller\users;

use app\wx\controller\BaseController;
use app\wx\model\CustAccountModel;
use app\wx\model\CustBalanceRecordModel;
use app\wx\model\CustTransactionModel;
use app\wx\model\CustUserModel;
use Exception;
use support\Request;
use support\Response;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * 账户管理控制器
 */
class AccountController extends BaseController
{
    /**
     * 获取用户账户信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function getAccountInfo(Request $request): Response
    {
        try {
            validate(\app\wx\validate\WxAccountValidate::class)
                ->scene('balance')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        $accountType = $request->input('account_type', 1);
        
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整，请先绑定客户档案');
        }

        $accountModel = new CustAccountModel();
        $accountInfo = $accountModel->getAccountInfo($userInfo['cust_uid'], $accountType);

        if (empty($accountInfo)) {
            // 如果没有账户，返回默认信息
            $accountInfo = [
                'cust_account_id' => 0,
                'cust_account_name' => $accountType == 1 ? '余额账户' : '补贴账户',
                'cust_account_type' => $accountType,
                'cust_balance' => 0.00
            ];
        }

        return $this->success($accountInfo);
    }

    /**
     * 获取用户所有账户信息
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function getAllAccountInfo(Request $request): Response
    {
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整，请先绑定客户档案');
        }

        $accountModel = new CustAccountModel();
        $accounts = $accountModel->getAllAccountInfo($userInfo['cust_uid']);

        // 如果没有账户，创建默认账户信息
        if (empty($accounts)) {
            $accounts = [
                [
                    'cust_account_id' => 0,
                    'cust_account_name' => '余额账户',
                    'cust_account_type' => 1,
                    'cust_balance' => 0.00
                ],
                [
                    'cust_account_id' => 0,
                    'cust_account_name' => '补贴账户',
                    'cust_account_type' => 2,
                    'cust_balance' => 0.00
                ]
            ];
        }

        return $this->success($accounts);
    }

    /**
     * 获取余额记录
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function getBalanceRecords(Request $request): Response
    {
        try {
            validate(\app\wx\validate\WxAccountValidate::class)
                ->scene('records')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整，请先绑定客户档案');
        }

        $page = $request->input('page', 1);
        $limit = $request->input('limit', 20);

        $recordModel = new CustBalanceRecordModel();
        $records = $recordModel->getUserBalanceRecords($userInfo['cust_uid'], $page, $limit);

        return $this->success($records);
    }

    /**
     * 获取交易记录
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function getTransactionRecords(Request $request): Response
    {
        try {
            validate(\app\wx\validate\WxAccountValidate::class)
                ->scene('transactions')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整，请先绑定客户档案');
        }

        $page = $request->input('page', 1);
        $limit = $request->input('limit', 20);

        $transactionModel = new CustTransactionModel();
        $transactions = $transactionModel->getUserTransactions($userInfo['cust_uid'], $page, $limit);

        return $this->success($transactions);
    }

    /**
     * 账户余额扣费（内部方法，供订单系统调用）
     * @param int $custUid
     * @param float $amount
     * @param string $orderId
     * @param string $remark
     * @param int $accountType
     * @return bool
     */
    public function consumeBalance(int $custUid, float $amount, string $orderId, string $remark = '', int $accountType = 1): bool
    {
        $accountModel = new CustAccountModel();
        $accountInfo = $accountModel->getAccountInfo($custUid, $accountType);

        if (empty($accountInfo)) {
            return false;
        }

        // 检查余额是否足够
        if (!$accountModel->checkBalance($accountInfo['cust_account_id'], $amount)) {
            return false;
        }

        // 扣费
        if ($accountModel->updateBalance($accountInfo['cust_account_id'], $amount, 'dec')) {
            // 创建交易记录
            $transactionModel = new CustTransactionModel();
            $transactionModel->createTransaction([
                'cust_uid' => $custUid,
                'transaction_type' => $accountType == 1 ? 'balance' : 'subsidy',
                'transaction_amount' => $amount,
                'transaction_remark' => $remark ?: '订单消费',
                'order_id' => $orderId
            ]);

            // 创建余额记录
            $recordModel = new CustBalanceRecordModel();
            $recordModel->createRecord([
                'cust_uid' => $custUid,
                'cust_account_id' => $accountInfo['cust_account_id'],
                'record_type' => 'consume',
                'record_amount' => $amount,
                'record_balance' => $accountInfo['cust_balance'] - $amount,
                'record_remark' => $remark ?: '订单消费'
            ]);

            return true;
        }

        return false;
    }

    /**
     * 获取账户类型名称
     * @param int $type
     * @return string
     */
    public static function getAccountTypeName(int $type): string
    {
        $types = [
            1 => '余额账户',
            2 => '补贴账户'
        ];
        
        return $types[$type] ?? '未知账户';
    }
}
