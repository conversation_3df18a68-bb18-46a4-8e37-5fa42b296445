<?php

namespace app\wx\controller\users;

use app\wx\controller\BaseController;
use support\Request;
use support\Response;
use Exception;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class AddressController extends BaseController
{
    /**
     * 地址列表获取
     * @param Request $request
     * @return Response
     */
    public function getAddressList(Request $request): Response
    {
        $type = $request->input('type', 0);
        $id = $request->input('id');

        if (!empty($id)) {
            $addressList = $this->dao->get('wx_user_address', [
                'uid' => $request->wxUid,
                'id' => $id,
                'deleted_at' => null
            ]);
            return $this->success($addressList ?: []);
        }

        if ($type == 1) {
            // 获取默认地址
            $addressList = $this->dao->get('wx_user_address', [
                'uid' => $request->wxUid,
                'is_default' => 1,
                'deleted_at' => null
            ]);
            return $this->success($addressList ?: []);
        } else {
            // 获取所有地址
            $addressList = $this->dao->search('wx_user_address', [
                'uid' => $request->wxUid,
                'deleted_at' => null,
                'ORDER' => ['is_default' => 'DESC', 'created_at' => 'DESC']
            ]);
            return $this->success($addressList);
        }
    }

    /**
     * 地址列表添加
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function createAddress(Request $request): Response
    {
        $params = $request->only([
            'name',
            'area',
            'phone',
            'address',
            'longitude',
            'latitude',
            'is_default'
        ]);

        try {
            validate(\app\wx\validate\WxUserValidate::class)
                ->scene('address')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        $params['uid'] = $request->wxUid;
        $params['created_at'] = date('Y-m-d H:i:s');

        // 将其他地址设置为非默认
        if ($params['is_default'] == 1) {
            $this->dao->update('wx_user_address', [
                'is_default' => 0
            ], [
                'uid' => $request->wxUid,
                'deleted_at' => null
            ]);
        }

        $id = $this->dao->insert('wx_user_address', $params);
        return $this->success(['id' => $id]);
    }

    /**
     * 地址列表修改
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function updateAddress(Request $request): Response
    {
        $params = $request->only([
            'id',
            'name',
            'area',
            'phone',
            'address',
            'longitude',
            'latitude',
            'is_default'
        ]);

        try {
            validate(\app\wx\validate\WxUserValidate::class)
                ->scene('updateAddress')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        $id = $params['id'];
        unset($params['id']);
        $params['updated_at'] = date('Y-m-d H:i:s');

        // 将其他地址设置为非默认
        if ($params['is_default'] == 1) {
            $this->dao->update('wx_user_address', [
                'is_default' => 0
            ], [
                'uid' => $request->wxUid,
                'deleted_at' => null
            ]);
        }

        $this->dao->update('wx_user_address', $params, [
            'uid' => $request->wxUid,
            'id' => $id,
            'deleted_at' => null
        ]);

        return $this->success('修改成功');
    }

    /**
     * 地址列表删除
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function delAddress(Request $request): Response
    {
        $id = $request->input('id');

        if (empty($id)) {
            throw new BadRequestHttpException('地址ID不能为空');
        }

        // 软删除
        $this->dao->update('wx_user_address', [
            'deleted_at' => date('Y-m-d H:i:s')
        ], [
            'uid' => $request->wxUid,
            'id' => $id,
            'deleted_at' => null
        ]);

        return $this->success('删除成功');
    }
}