<?php

namespace app\wx\controller\users;

use app\utils\Group;
use app\wx\controller\BaseController;
use app\wx\service\WxOrderService;
use app\wx\model\store\StoreExternalOrderModel;
use app\wx\model\users\WxStoreOrderModel;
use app\wx\model\users\StoreOrderService;

use app\wx\service\WxPaymentService;
use support\Request;
use support\Response;

use support\Log;
use Exception;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class OrderController extends BaseController
{
    /**
     * 创建订单
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function createOrder(Request $request): Response
    {
        $params = $request->only([
            'order_type',
            'product_id',
            'product_name',
            'order_price',
            'total_num',
            'real_name',
            'user_phone',
            'user_address',
            'user_notes',
            'service_time',
            'payment_method'
        ]);

        try {
            validate(\app\wx\validate\WxOrderValidate::class)
                ->scene('create')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整，请先绑定客户档案');
        }



        // 使用WxOrderService创建订单
        $result = WxOrderService::createPackageOrderLegacy($params);

        if (empty($result['order_id'])) {
            throw new BadRequestHttpException('订单创建失败');
        }

        $orderId = $result['order_id'];

        // 获取用户openid用于支付
        if (empty($userInfo['openid'])) {
            throw new BadRequestHttpException('用户openid不存在，无法创建支付');
        }

        // 创建支付配置
        $env = (int)($request->input('env', 1));
        try {
            $paymentData = \app\wx\service\WxPaymentService::createPayment($orderId, $userInfo['openid'], $env);

            return $this->success([
                'payConfig' => $paymentData,
                'orderId' => $orderId
            ]);
        } catch (Exception $e) {
            // 如果支付创建失败，返回订单信息让用户稍后支付
            return $this->success([
                'order_id' => $orderId,
                'total_price' => $params['order_price'],
                'message' => '订单创建成功，支付配置生成失败，请稍后重试'
            ]);
        }
    }

    /**
     * 取消订单
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function CancelOrder(Request $request): Response
    {
        (int)$orderId = $request->input('order_id', 0);
        $orderModel = new \app\wx\model\users\WxStoreOrderModel();
        $message = '用户手动取消订单';

        if ($orderModel
            ->where('uid', $request->wxUid)
            ->where('order_id', $orderId)
            ->where('order_status', 0)
            ->update([
                'order_status' => -1,
                'remark' => $message,
            ])
        ) {
            event('wx.cancelCreate', [
                'order_id' => $orderId,
                'change_message' => $message,
            ]);
            return $this->success('取消成功');
        } else {
            return $this->fail('取消失败');
        }
    }

    /**
     * 订单列表
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function orderList(Request $request): Response
    {
        // var_dump('orderList-=-=-=---------------',);
        try {
            validate(\app\wx\validate\WxOrderValidate::class)
                ->scene('list')
                ->check($request->all());
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        // var_dump('orderList-=-=-=------------1---',);
        (int)$order_status = $request->input('order_status', null);
        (int)$order_type = $request->input('order_type', null);
        $time = $request->input('time', null);
        $name = $request->input('name', null);
        $cate_id = $request->input('cate_id', null);
        (int)$pageSize = $request->input('pageSize', 10);
        (int)$pageNum = $request->input('pageNum', 1);
        // var_dump('orderList-=-=-=------------2---',);
        $orderModel = new WxStoreOrderModel();
        // 附表查询方法
        $hasWhere = [];
        $hasWhereStatus = false;
        if(isset($name) && $name) {
            $hasWhereStatus = true;
            $hasWhere[] = ['store_name','like','%' . $name . '%'];
        }
        // var_dump('orderList-=-=-=------------3---',);
        $data = $orderModel
            ->when(isset($order_type) && $order_type, function ($query) use ($order_type) {
                return $query->where('order_type', $order_type);
            })
            ->when(isset($cate_id) && $cate_id, function ($query) use ($cate_id) {
                return $query->hasWhere('productCateInfo', [
                    'cate_id' => $cate_id
                ]);
            })
            ->when($hasWhereStatus, function ($query) use ($hasWhere) {
                return $query->hasWhere('productInfo', $hasWhere);
            })
            ->when(isset($order_status) && $order_status, function ($query) use ($order_status) {
                // 待付款
                if($order_status == 1) {
                    return $query->where('pay_status',0)->where('order_status',0);
                }
                // 待收货
                if($order_status == 2) {
                    return $query->where('pay_status',1)->where('order_type',0)->whereIn('order_status',[1]);
                }
                // 待使用
                if($order_status == 3) {
                    return $query->where('pay_status',1)->where('order_type',2)->whereIn('order_status',[1]);
                }
                // 退款/售后
                if($order_status == 4) {
                    return $query->where('refund_status','>',0);return $query;
                }
            })
            ->when(isset($time) && $time, function ($query) use ($time) {
                Group::getModelTime($query, $time,'wx_store_order.created_at');
            })
            ->where('uid',$request->wxUid)
            ->where('is_admin',0)
            ->order('wx_store_order.created_at desc')
            ->page($pageNum, $pageSize)
            ->hidden([
            'deleted_at',
            'created_by',
            'updated_by',
            'updated_at'
            ])
            ->select()
            ->toArray();
        // var_dump('orderList-=-=-=------------4---',$data);
        // 查询订单上户时间
        if(!empty($data)) {
            foreach ($data as &$v) {
                if($v['order_type'] == 2) {
                    // var_dump('orderList-=-=-=------------5.0---',);
                    $orderServiceModel = new StoreOrderService();
                    // var_dump('orderList-=-=-=------------5.1---',);
                    $orderService = $orderServiceModel->where('order_id',$v['order_id'])->findOrEmpty()->toArray();
                    $v['down_time'] = $orderService['service_info']['down_time'];
                    if(!empty($orderService['service_info']['place_time'])) {
                        $v['place_time'] = $orderService['service_info']['place_time'];
                    }
                }
            }
        }
        // var_dump('orderList-=-=-=------------6---',);
        return $this->success($data);


        // $orderStatus = $request->input('order_status', '');
        // $orderType = (int)$request->input('order_type', 0);
        // $name = $request->input('name', '');
        // $page = (int)$request->input('page', 1);
        // $limit = (int)$request->input('limit', 20);

        // // 获取用户信息
        // $userInfo = $this->dao->get('wx_user', [
        //     'deleted_at' => null,
        //     'uid' => $request->wxUid,
        // ]);

        // if (empty($userInfo) || empty($userInfo['cust_uid'])) {
        //     throw new BadRequestHttpException('用户信息不完整');
        // }



        // // 如果有商品名称筛选
        // if (!empty($name)) {
        //     $filteredList = [];
        //     foreach ($orders['list'] as $order) {
        //         if (strpos($order['product_name'], $name) !== false) {
        //             $filteredList[] = $order;
        //         }
        //     }
        //     $orders['list'] = $filteredList;
        //     $orders['total'] = count($filteredList);
        //     $orders['pages'] = ceil($orders['total'] / $limit);
        // }

        // // 如果有订单状态筛选
        // if (!empty($orderStatus)) {
        //     $filteredList = [];
        //     foreach ($orders['list'] as $order) {
        //         if ($order['order_status'] === $orderStatus) {
        //             $filteredList[] = $order;
        //         }
        //     }
        //     $orders['list'] = $filteredList;
        //     $orders['total'] = count($filteredList);
        //     $orders['pages'] = ceil($orders['total'] / $limit);
        // }

        // return $this->success($orders);
    }

    /**
     * 订单详情
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function orderDetails(Request $request): Response
    {
        // $orderId = $request->input('order_id', '');

        // if (empty($orderId)) {
        //     throw new BadRequestHttpException('订单号不能为空');
        // }

        // // 获取用户信息
        // $userInfo = $this->dao->get('wx_user', [
        //     'deleted_at' => null,
        //     'uid' => $request->wxUid,
        // ]);

        // if (empty($userInfo) || empty($userInfo['cust_uid'])) {
        //     throw new BadRequestHttpException('用户信息不完整');
        // }

        // // 获取订单详情
        // $orderModel = new OrderMainModel();
        // $orderInfo = $orderModel->getOrderDetail($orderId, $userInfo['cust_uid']);

        // if (empty($orderInfo)) {
        //     throw new BadRequestHttpException('订单不存在');
        // }

        // // 获取订单记录
        // $recordModel = new OrderRecordModel();
        // $orderRecords = $recordModel->getOrderRecords($orderId);
        // $orderInfo['order_records'] = $orderRecords;

        // return $this->success($orderInfo);

        (int)$orderId = $request->input('order_id', 0);
        $orderModel = new WxStoreOrderModel();
        $data = $orderModel
            ->with(['replyInfo'])
            ->where('uid', $request->wxUid)
            ->where('order_id', $orderId)
            ->hidden([
                'deleted_at',
                'created_by',
                'updated_by',
                'updated_at'
            ])
            ->findOrEmpty()
            ->toArray();
        // 检查订单是否存在
        if(empty($data)) {
            throw new BadRequestHttpException('订单不存在');
        }
         // 查询服务订单
        $orderServiceModel = new StoreOrderService();
        if($data['order_type'] == 2) {
            $data['orderService'] = $orderServiceModel->where('order_id',$data['order_id'])->findOrEmpty()->toArray();
        }
        // 查询关联居家照护订单
        if($data['order_type'] == 1) {
            $externalModel = new StoreExternalOrderModel();
            // $homeModel = new HomeHousekeepingOrdersModel();
            // $cleaningModel = new HomeCleaningOrdersModel();
            $homeOrderInfo = $externalModel->where('order_id',$data['order_id'])->findOrEmpty()->toArray();
            if(empty($homeOrderInfo)) {
                $data['homeOrderInfo'] = [];
            } else {
                $data['homeOrderInfo'] = [];
                if ($homeOrderInfo['type'] == '家政') {
                    // $data['homeOrderInfo'] = $homeModel->where('order_id',$homeOrderInfo['external_id'])->findOrEmpty()->toArray();
                }
                if ($homeOrderInfo['type'] == '保洁') {
                    // $data['homeOrderInfo'] = $cleaningModel->where('order_id',$homeOrderInfo['external_id'])->findOrEmpty()->toArray();
                }
            }
        }
        return $this->success($data);
    }

    /**
     * 获取订阅消息
     * @param Request $request
     * @return Response
     */
    public function getSubscribeMessage(Request $request): Response
    {
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo)) {
            throw new BadRequestHttpException('用户信息不存在');
        }

        // 返回订阅消息模板ID配置
        $subscribeMessages = [
            'createOrder' => [
                'template_id' => 'template_id_for_create_order',
                'title' => '订单创建通知',
                'content' => '您的订单已创建成功，请及时支付'
            ],
            'paySuccess' => [
                'template_id' => 'template_id_for_pay_success',
                'title' => '支付成功通知',
                'content' => '您的订单支付成功，我们将尽快为您配送'
            ],
            'orderDelivery' => [
                'template_id' => 'template_id_for_delivery',
                'title' => '订单配送通知',
                'content' => '您的订单正在配送中，请注意查收'
            ],
            'orderComplete' => [
                'template_id' => 'template_id_for_complete',
                'title' => '订单完成通知',
                'content' => '您的订单已完成，感谢您的使用'
            ]
        ];

        return $this->success($subscribeMessages);
    }




    /**
     * 更新订单
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function updateOrder(Request $request): Response
    {
        $params = $request->only([
            'order_id',
            'real_name',
            'user_phone',
            'user_address',
            'user_longitude',
            'user_latitude',
            'user_notes'
        ]);

        if (empty($params['order_id'])) {
            throw new BadRequestHttpException('订单号不能为空');
        }

        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整');
        }

        // 验证订单归属
        $orderModel = new \app\wx\model\users\WxStoreOrderModel();
        $orderInfo = $orderModel
            ->where('order_id', $params['order_id'])
            ->where('uid', $request->wxUid)
            ->find();

        if (empty($orderInfo)) {
            throw new BadRequestHttpException('订单不存在或无权限访问');
        }

        // 检查订单状态是否可以修改
        if (!in_array($orderInfo['order_status'], ['待付款', '待配送', '配送中'])) {
            throw new BadRequestHttpException('当前订单状态不允许修改');
        }

        // 更新订单信息
        $updateData = [];
        if (!empty($params['real_name'])) {
            $updateData['real_name'] = $params['real_name'];
        }
        if (!empty($params['user_phone'])) {
            $updateData['user_phone'] = $params['user_phone'];
        }
        if (!empty($params['user_address'])) {
            $updateData['user_address'] = $params['user_address'];
        }
        if (!empty($params['user_longitude'])) {
            $updateData['user_longitude'] = $params['user_longitude'];
        }
        if (!empty($params['user_latitude'])) {
            $updateData['user_latitude'] = $params['user_latitude'];
        }
        if (!empty($params['user_notes'])) {
            $updateData['user_notes'] = $params['user_notes'];
        }

        if (!empty($updateData)) {
            $updateData['updated_at'] = date('Y-m-d H:i:s');

            $result = $orderModel
                ->where('order_id', $params['order_id'])
                ->where('uid', $request->wxUid)
                ->update($updateData);

            if ($result) {
                Log::info("订单信息更新成功", [
                    'order_id' => $params['order_id'],
                    'user_id' => $request->wxUid
                ]);

                return $this->success('订单信息更新成功');
            } else {
                throw new BadRequestHttpException('订单信息更新失败');
            }
        }

        return $this->success('没有需要更新的信息');
    }

    /**
     * 申请退款
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function refundOrder(Request $request): Response
    {
        $params = $request->only([
            'order_id',
            'data',
            'order_type',
        ]);

        try {
            validate(\app\wx\validate\WxOrderValidate::class)
                ->scene('refundOrder')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // 套餐退款（统一处理）
        if($params['order_type'] == 3) {
            if(empty($params['data']) || !is_array($params['data'])) {
                throw new BadRequestHttpException('请选择退款套餐');
            }
            // 执行套餐退款
            WxOrderService::refundOrderV3($params);
        } else {
            throw new BadRequestHttpException('暂不支持该类型订单的退款');
        }

        $wxOrder = new \app\wx\model\users\WxStoreOrderModel();
        $data = $wxOrder->where('order_id',$params['order_id'])->findOrEmpty()->toArray();
        return $this->success($data);
    }

    public function getPayConfig(Request $request): Response
    {
        $params = $request->only(['order_id', 'env']);

        try {
            validate(\app\wx\validate\WxPaymentValidate::class)
                ->scene('wechat_pay')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);

        if (empty($userInfo)) {
            throw new BadRequestHttpException('用户信息不存在');
        }

        if (empty($userInfo['h5_openid'])) {
            throw new BadRequestHttpException('用户openid不存在');
        }

        // 验证订单归属
        if (!empty($userInfo['uid'])) {
            $orderModel = new WxStoreOrderModel();
            $orderInfo = $orderModel->where('order_id', $params['order_id'])
                ->where('uid', $userInfo['uid'])
                ->findOrEmpty();

            if ($orderInfo->isEmpty()) {
                throw new BadRequestHttpException('订单不存在或无权限访问');
            }
        }

        $env = (int)($params['env'] ?? 1);
        $paymentData = WxPaymentService::createPayment($params['order_id'], $userInfo['h5_openid'], $env);

        return $this->success([
            'payConfig' => $paymentData,
        ]);
    }
}