<?php

namespace app\wx\controller;

use app\utils\Dao;
use DI\Attribute\Inject;
use support\Response;
use DateTime;

class BaseController
{
    #[Inject]
    protected Dao $dao;

    /**
     * 返回json内容
     */
    public function returnJson(array $data): Response
    {
        if (!isset($data['code'])) {
            $data['code'] = 200;
        }
        if (!isset($data['msg'])) {
            $data['msg'] = 'success';
        }
        if (!isset($data['data'])) {
            $data['data'] = [];
        }
        return json($data);
    }

    /**
     * 成功返回json内容
     * @param array|string|null $data
     * @param string $msg
     * @return Response
     */
    public function success(array|string|null $data = [], string $msg = 'success'): Response
    {
        if (is_string($data)) {
            $msg = $data;
        }
        return json(['code' => 200, 'message' => $msg, 'data' => $data]);
    }

    /**
     * 失败返回json内容
     * @param string $msg
     * @return Response
     */
    public function fail(string $msg = 'fail'): Response
    {
        return json(['code' => 400, 'message' => $msg]);
    }

    
    /**
     * 文件大小转换
     * @param $filesize
     * @return string
     */
    protected function getSize($filesize): string
    {
        if ($filesize >= 1073741824) {
            //转成GB
            $filesize = round($filesize / 1073741824 * 100) / 100 . ' GB';
        } elseif ($filesize >= 1048576) {
            //转成MB
            $filesize = round($filesize / 1048576 * 100) / 100 . ' MB';
        } elseif ($filesize >= 1024) {
            //转成KB
            $filesize = round($filesize / 1024 * 100) / 100 . ' KB';
        } else {
            //不转换直接输出
            $filesize = $filesize . ' 字节';
        }
        return $filesize;
    }

    /**
     * 计算两个日期之间的时间差
     * @param $date1
     * @param $date2
     * @return int
     * @throws Exception
     */
    protected function dateDiff($date1, $date2): int
    {
        $datetime1 = new DateTime($date1);
        $datetime2 = new DateTime($date2);
        $interval = $datetime1->diff($datetime2);
        return $interval->format('%a');
    }

    /**
     * 日期转星期
     * @param $dateString
     * @return string
     */
    protected function getChineseWeekday($dateString): string
    {
        $weekdayNames = array("日", "一", "二", "三", "四", "五", "六");
        $weekday = date('w', strtotime($dateString));
        return "周" . $weekdayNames[$weekday];
    }

     /**
     * 字典数据查询
     * @param array $data
     * @param string $name
     * @return array
     */
    public function getDictData(array $data, string $name): array
    {
        $key = Str::camel($name);
        $data[$key] = $this->dao->search('sys_dict_data', [
            'dict_type' => $name,
            'status' => 1,
            "ORDER" => [
                "dict_sort" => "ASC",
            ],
        ], [
            'dict_label(label)',
            'dict_value(value)',
        ]);
        return $data;
    }
    
    /**
     * 转一维数组并去重
     * @param $data
     * @return array
     */
    public static function uniqueArray($data): array
    {
        // 新的一维数组
        $array1D = array();
        // 遍历原始二维数组，将其中的每个元素添加到新的一维数组中
        foreach ($data as $item) {
            // 合并数组
            $array1D = array_merge($array1D, $item);
        }
        // 使用array_unique函数去重
        return array_unique($array1D);
    }
    
    /**
     * 匹配返回站点值
     * @param $name
     * @return string
     */
    public static function getNewSite($name): string
    {
        return match ($name) {
            '中关村黄庄社区801服务站', '801服务点' => '海淀黄庄社区－甲乙801楼自提点',
            '中关村黄庄社区803服务站', '803服务点' => '海淀黄庄社区－803楼自提点',
            '中关村科源社区养老驿站', '海淀科源社区养老驿站' => '中关村科源社区养老驿站',
            '中关村科星社区养老驿站', '海淀科星社区养老驿站' => '科星社区－科星养老驿站自提点',
            '中关村科苑酒店', '科苑酒店' => '保福寺桥－科苑酒店自提点',
            '奥运村枫林绿洲社区服务站', '枫林绿洲' => '奥运村枫林绿洲社区服务站',
            '奥运村科学园社区南里三区站', '科学院南里三区' => '科学园南里3区－为老服务站自提点',
            '奥运村科学园社区南里五区站', '科学院南里五区' => '奥运村科学园社区南里五区站',
            '奥运村博世祥园社区站', '博世祥园' => '奥运村博世祥园社区站',
            '奥运村龙祥社区服务站', '龙翔社区' => '龙祥社区－为老服务站自提点',
            '亚运村华严北里社区服务站', '华严北里' => '华严北里－为老服务站自提点',
            '亚运村华严北里社区服务站', '华严北里' => '华严北里－为老服务站自提点',
            '亚运村祁家豁子服务站', '祁家豁子服务站' => '亚运村祁家豁子服务站自提点',
            default => $name,
        };
    }

    /**
     * 匹配返回套餐模板名称
     * @param $name
     * @return string
     */
    public static function getNewMeal($name): string
    {
        return match ($name) {
            0 => '主食专供一',
            1 => '主食专供二',
            2 => '主食专供三',
            3 => '主食专供四',
            4 => '主食专供五',
            5 => '主食专供六',
            6 => '主食专供七',
            default => false,
        };
    }
}
