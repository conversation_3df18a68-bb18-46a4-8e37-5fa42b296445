<?php

namespace app\wx\controller;

use app\wx\controller\BaseController;
use app\wx\service\WxPaymentService;
use app\wx\model\users\WxStoreOrderModel;
use Exception;
use support\Request;
use support\Response;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * 支付控制器
 */
class PaymentController extends BaseController
{
    /**
     * 余额支付
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function balancePay(Request $request): Response
    {
        $params = $request->only(['order_id', 'account_type']);
        
        try {
            validate(\app\wx\validate\WxPaymentValidate::class)
                ->scene('balance_pay')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);
        
        if (empty($userInfo) || empty($userInfo['cust_uid'])) {
            throw new BadRequestHttpException('用户信息不完整，请先绑定客户档案');
        }
        
        $accountType = (int)($params['account_type'] ?? 1);
        $result = WxPaymentService::balancePayment($params['order_id'], $userInfo['cust_uid'], $accountType);
        
        if ($result) {
            return $this->success('支付成功');
        } else {
            throw new BadRequestHttpException('支付失败');
        }
    }
    
    /**
     * 查询支付状态
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function getPaymentStatus(Request $request): Response
    {
        $orderId = $request->input('order_id', '');
        
        if (empty($orderId)) {
            throw new BadRequestHttpException('订单号不能为空');
        }
        
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);
        
        if (empty($userInfo)) {
            throw new BadRequestHttpException('用户信息不存在');
        }
        
        // 验证订单归属
        if (!empty($userInfo['cust_uid'])) {
            $orderModel = new WxStoreOrderModel();
            $orderInfo = $orderModel->where('order_id', $orderId)
                ->where('user_id', $userInfo['cust_uid'])
                ->findOrEmpty();
            
            if (empty($orderInfo)) {
                throw new BadRequestHttpException('订单不存在或无权限访问');
            }
            
            // 如果订单已支付，直接返回成功状态
            if ($orderInfo['order_status'] !== '待付款') {
                return $this->success([
                    'trade_state' => 'SUCCESS',
                    'order_status' => $orderInfo['order_status']
                ]);
            }
        }
        
        $paymentStatus = WxPaymentService::getPaymentStatus($orderId);
        
        return $this->success($paymentStatus);
    }
    
    /**
     * 套餐支付（兼容旧版接口）
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function packagePay(Request $request): Response
    {
        $params = $request->only(['order_id', 'payment_method', 'account_type', 'env']);
        
        try {
            validate(\app\wx\validate\WxPaymentValidate::class)
                ->scene('package_pay')
                ->check($params);
        } catch (Exception $e) {
            throw new BadRequestHttpException($e->getMessage());
        }
        
        $paymentMethod = $params['payment_method'] ?? 'wechat';
        
        if ($paymentMethod === 'balance') {
            // 余额支付
            return $this->balancePay($request);
        } else {
            // 微信支付
            return $this->createWechatPay($request);
        }
    }
    
    /**
     * 获取支付方式列表
     * @param Request $request
     * @return Response
     */
    public function getPaymentMethods(Request $request): Response
    {
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);
        
        $methods = [
            [
                'method' => 'wechat',
                'name' => '微信支付',
                'icon' => '/static/images/wechat_pay.png',
                'available' => true
            ]
        ];
        
        // 如果用户已绑定客户档案，显示余额支付选项
        if (!empty($userInfo['cust_uid'])) {
            $methods[] = [
                'method' => 'balance',
                'name' => '余额支付',
                'icon' => '/static/images/balance_pay.png',
                'available' => true
            ];
            
            $methods[] = [
                'method' => 'subsidy',
                'name' => '补贴支付',
                'icon' => '/static/images/subsidy_pay.png',
                'available' => true
            ];
        }
        
        return $this->success($methods);
    }
    
    /**
     * 支付结果确认
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function confirmPayment(Request $request): Response
    {
        $orderId = $request->input('order_id', '');
        
        if (empty($orderId)) {
            throw new BadRequestHttpException('订单号不能为空');
        }
        
        // 获取用户信息
        $userInfo = $this->dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $request->wxUid,
        ]);
        
        if (empty($userInfo)) {
            throw new BadRequestHttpException('用户信息不存在');
        }
        
        // 验证订单归属
        if (!empty($userInfo['cust_uid'])) {
            $orderModel = new WxStoreOrderModel();
            $orderInfo = $orderModel->where('order_id', $orderId)
                ->where('user_id', $userInfo['cust_uid'])
                ->findOrEmpty();
            
            if (empty($orderInfo)) {
                throw new BadRequestHttpException('订单不存在或无权限访问');
            }
            
            return $this->success([
                'order_id' => $orderId,
                'order_status' => $orderInfo['order_status'],
                'order_price' => $orderInfo['order_price'],
                'product_name' => $orderInfo['product_name'],
                'paid' => $orderInfo['order_status'] !== '待付款'
            ]);
        }
        
        throw new BadRequestHttpException('用户信息不完整');
    }
}
