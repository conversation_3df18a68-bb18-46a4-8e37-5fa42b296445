<?php

namespace app\wx\validate;

use think\Validate;

class WxOrderValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'order_id|订单号' => 'require|max:36',
        'order_type|订单类型' => 'integer|in:1,2,3,4,5,6,7,8',
        'product_id|商品' => 'require|integer|max:11',
        'product_name|商品名称' => 'require|max:255',
        'order_price|订单价格' => 'require|float|gt:0',
        'env|环境' => 'require|integer|max:1',
        'real_name|姓名' => 'require|chsAlphaNum|max:40',
        'user_phone|手机号' => 'require|mobile',
        'user_address|地址' => 'require|max:255',
        'user_notes|用户备注' => 'max:600',
        'total_num|商品数量' => 'require|integer|max:6',
        'total_price|价格' => 'require|float|max:10',
        'sku_id|规格' => 'integer|max:10',
        'service_time|服务时间' => 'date',

        'cate_id|分类' => 'integer|max:10',
        'order_status|订单状态' =>  'integer|max:10',
        'name|商品名称' => 'max:40',
        'pageSize|分页条数' => 'require|integer',
        'pageNum|分页' => 'require|integer',
        'page|页码' => 'integer|gt:0',
        'limit|每页数量' => 'integer|between:1,100',

        'user_longitude|经度' => 'max:30',
        'user_latitude|纬度' => 'max:30',

        // 订单操作相关
        'cancel_reason|取消原因' => 'max:255',
        'refund_reason|退款原因' => 'max:255',
        'payment_method|支付方式' => 'in:balance,wechat,alipay',
    ];

    // 定义信息
    protected $message = [
    ];

    //定义场景
    protected $scene = [
        'create' => [
            'order_type',
            'product_id',
            'product_name',
            'order_price',
            'env',
            'real_name',
            'user_phone',
            'user_address',
            'user_notes',
            'total_num',
            'total_price',
            'sku_id',
            'service_time',
            'user_longitude',
            'user_latitude',
            'payment_method'
        ],
        'update' => [
            'order_id',
            'real_name',
            'user_phone',
            'user_address',
            'user_notes',
            'user_longitude',
            'user_latitude'
        ],
        'list' => [
            'name',
            'page',
            'limit',
            'cate_id',
            'order_type',
            'order_status'
        ],
        'detail' => [
            'order_id'
        ],
        'cancel' => [
            'order_id',
            'cancel_reason'
        ],
        'refund' => [
            'order_id',
            'refund_reason'
        ],
        'status' => [
            'order_id',
            'order_status'
        ],
        'refundOrder' => [
            'order_id',
        ]
    ];
}