<?php
namespace app\wx\validate;

use think\Validate;

class WxStoreFoodValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'pageSize|分页条数' => 'require|integer',
        'pageNum|分页' => 'require|integer',

        'start_time'  =>  'require|date',
        'end_time' =>  'require|date',

        'data|套餐数据' => 'require',
        'delivery_site|配送站点' => 'max:200',
        'delivery_address|配送地址' => 'max:800',
        'delivery_phone|配送电话' => 'mobile',
        'delivery_name|收货人姓名' => 'chsAlphaNum|max:40',
        'remark|备注' => 'max:600',

        'data|套餐内容' =>  'require',
        'product_id|商品' =>  'require|integer|max:11',
        'delivery_option|配送方式' =>  'require|chsDash|max:200|min:1',
        'real_name|姓名' => 'require|chsAlphaNum|max:40',
        'user_phone|手机号' => 'require|mobile',
        'user_notes|用户备注' => 'max:600',

        'user_longitude|经度' => 'require|max:30',
        'user_latitude|纬度' => 'require|max:30',
        'user_address|地址' => 'require|max:800',

        'bed_status|床位状态' =>  'chsDash|max:20',
        'bed_type|床位类型' =>  'chsDash|max:20',
    ];

    // 定义信息
    protected $message = [
    ];

    //定义场景
    protected $scene = [
        'list'  =>  ['start_time','end_time'],
        'booking' => [
            'data',
            'delivery_site',
            'delivery_address',
            'delivery_phone',
            'delivery_name',
            'remark',
        ],
        'bookingV2' => [
            'data',
            'delivery_site',
            'delivery_address',
            'delivery_phone',
            'delivery_name',
            'remark',
        ],
        'old_booking' => [
            'delivery_option',
            'delivery_site',
            'real_name',
            'user_phone',
            'user_latitude',
            'user_longitude',
            'user_address',
            'user_notes',
            'bed_status',
            'bed_type',
        ],
    ];
}