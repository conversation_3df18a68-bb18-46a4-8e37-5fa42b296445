<?php

namespace app\wx\validate;

use think\Validate;

/**
 * 支付相关验证器
 */
class WxPaymentValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'order_id|订单号' => 'require|max:36',
        'payment_method|支付方式' => 'require|in:wechat,balance,subsidy',
        'account_type|账户类型' => 'in:1,2',
        'env|环境' => 'in:0,1',
        'amount|金额' => 'require|float|gt:0',
        'openid|用户openid' => 'require|max:64',
    ];

    // 定义信息
    protected $message = [
        'order_id.require' => '订单号不能为空',
        'order_id.max' => '订单号格式不正确',
        'payment_method.require' => '支付方式不能为空',
        'payment_method.in' => '支付方式不正确',
        'account_type.in' => '账户类型只能是1(余额账户)或2(补贴账户)',
        'env.in' => '环境参数只能是0(公众号)或1(小程序)',
        'amount.require' => '金额不能为空',
        'amount.gt' => '金额必须大于0',
        'openid.require' => '用户openid不能为空',
    ];

    //定义场景
    protected $scene = [
        'wechat_pay' => [
            'order_id',
            'env',
        ],
        'balance_pay' => [
            'order_id',
            'account_type',
        ],
        'package_pay' => [
            'order_id',
            'payment_method',
            'account_type',
            'env',
        ],
        'payment_status' => [
            'order_id',
        ],
        'create_order' => [
            'amount',
            'payment_method',
        ],
    ];
}
