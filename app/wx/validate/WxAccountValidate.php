<?php

namespace app\wx\validate;

use think\Validate;

/**
 * 账户相关验证器
 */
class WxAccountValidate extends Validate
{
    // 定义规则
    protected $rule = [
        'account_type|账户类型' => 'require|in:1,2',
        'amount|金额' => 'require|float|gt:0',
        'transaction_type|交易类型' => 'require|in:payment,refund,balance,subsidy,recharge',
        'remark|备注' => 'max:255',
        'order_id|订单号' => 'max:36',
        'page|页码' => 'integer|gt:0',
        'limit|每页数量' => 'integer|between:1,100',
        'start_time|开始时间' => 'date',
        'end_time|结束时间' => 'date',
        'record_type|记录类型' => 'in:recharge,consume,refund,transfer',
    ];

    // 定义信息
    protected $message = [
        'account_type.require' => '账户类型不能为空',
        'account_type.in' => '账户类型只能是1(余额账户)或2(补贴账户)',
        'amount.require' => '金额不能为空',
        'amount.gt' => '金额必须大于0',
        'transaction_type.require' => '交易类型不能为空',
        'transaction_type.in' => '交易类型不正确',
    ];

    //定义场景
    protected $scene = [
        'balance' => [
            'account_type',
        ],
        'records' => [
            'page',
            'limit',
            'start_time',
            'end_time',
            'record_type',
        ],
        'transactions' => [
            'page',
            'limit',
            'start_time',
            'end_time',
            'transaction_type',
        ],
        'consume' => [
            'amount',
            'transaction_type',
            'order_id',
            'remark',
        ],
        'recharge' => [
            'account_type',
            'amount',
            'remark',
        ],
    ];
}
