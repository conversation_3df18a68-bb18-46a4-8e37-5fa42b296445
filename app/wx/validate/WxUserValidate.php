<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\wx\validate;

use think\Validate;

/**
 *  科苑养老H5
 */
class WxUserValidate extends Validate
{
     // 定义规则
    protected $rule = [
        'pageSize|分页条数' => 'require|integer',
        'pageNum|分页' => 'require|integer',

        'truename|姓名' => 'chsAlpha|max:40',
        'nickname|昵称' => 'max:40',
        'headimgurl|头像' => 'url',
        'sex|性别' => 'integer',
        'phone|手机号' => 'mobile',
        'user_address|地址' => 'max:800',
        'user_address_detail|详细地址' => 'max:800',
        'birthday|生日' => 'date',
        'admin_remark|后台管理员备注' => 'max:800',

        'id|Id' => 'require|integer',
        'uid|Id' => 'require|integer',
        'cust_uid|客户ID' => 'integer',
        'name|姓名' => 'chsAlpha|max:40',
        'role|角色' => 'require|chsAlpha|max:40',
        'area|地区' => 'require|max:80',
        'address|详细地址' => 'require|max:255',
        'longitude|经度' => 'max:30',
        'latitude|纬度' => 'max:30',
        'is_default|默认设置' => 'require|in:0,1',

        // 账户相关
        'account_type|账户类型' => 'require|in:1,2',
        'amount|金额' => 'require|float|gt:0',
        'transaction_type|交易类型' => 'require|in:payment,refund,balance,subsidy',
        'remark|备注' => 'max:255',
    ];

    // 定义信息
    protected $message = [
        'name' => '姓名必须是中文或字母',
        'phone' => '联系电话必须填写',
        'area' => '所在地区必须填写',
        'address' => '详细地址必须填写',
    ];

    //定义场景
    protected $scene = [
        'list' => [
            'pageSize',
            'pageNum'
        ],
        'update' => [
            'uid',
            'role',
            'phone',
            'admin_remark',
            'truename',
        ],
        'user' => [
            'truename',
            'nickname',
            'headimgurl',
            'sex',
            'phone',
            'user_address',
            'user_address_detail',
            'birthday',
        ],
        'bind' => [
            'phone',
            'cust_uid',
        ],
        'address' => [
            'name',
            'area',
            'phone',
            'address',
            'longitude',
            'latitude',
            'is_default'
        ],
        'updateAddress' => [
            'id',
            'name',
            'area',
            'phone',
            'address',
            'longitude',
            'latitude',
            'is_default'
        ],
        'account' => [
            'account_type',
        ],
        'transaction' => [
            'amount',
            'transaction_type',
            'remark',
        ],
    ];

}
