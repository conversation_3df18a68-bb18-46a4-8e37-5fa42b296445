<?php

namespace app\wx\service;

use app\wx\model\users\WxStoreOrderModel;
use app\wx\model\store\StoreOrderPackageModel;
use app\utils\Dao;
use support\Log;
use Exception;

/**
 * 统一套餐订单服务 - 简化版
 * 合并创建订单和同步功能，移除复杂的事件和重试机制
 */
class UnifiedPackageOrderService
{
    /**
     * 创建套餐订单并同步到food_order系统
     * @param array $orderData 订单数据
     * @return array 创建结果
     * @throws Exception
     */
    public static function createAndSync(array $orderData): array
    {
        Log::info("开始创建套餐订单", [
            'order_id' => $orderData['order_id'],
            'order_no' => $orderData['order_no']
        ]);
        
        try {
            // 1. 获取微信订单信息
            $wxOrderModel = new WxStoreOrderModel();
            $orderInfo = $wxOrderModel->where('order_id', $orderData['order_id'])->findOrEmpty()->toArray();

            if (empty($orderInfo)) {
                throw new Exception("订单不存在: {$orderData['order_id']}");
            }

            if ($orderInfo['order_type'] != 3) {
                throw new Exception("非套餐订单: {$orderData['order_id']}");
            }

            // 2. 解析订单信息
            $orderInfoData = $orderInfo['order_info'];

            // 检查order_items字段（创建接口存储的结构）
            if (empty($orderInfoData['order_items'])) {
                throw new Exception("套餐订单信息不完整: {$orderData['order_id']}");
            }

            // 检查是否为员工订单（从employee_discount字段判断）
            $isEmployee = !empty($orderInfoData['employee_discount']) && $orderInfoData['employee_discount'] > 0;

            $dao = new Dao();
            $result = null;

            // 添加 cust_uid档案Id
            $custUid = $dao->get('wx_user',[
                'uid' => $orderInfo['uid'],
            ],'cust_uid');
            if (empty($custUid)) {
                throw new Exception("客户信息不存在: custUid={$orderInfo['uid']}");
            }
            $orderInfo['cust_uid'] = $custUid;
            // 使用事务处理
            $dao->action(function() use ($dao,$orderInfo, $orderInfoData, $isEmployee, &$result) {
                // 3. 创建套餐关联记录
                $packageRecords = self::createPackageRecords($dao,$orderInfo, $orderInfoData, $isEmployee);

                // 4. 同步创建food_order记录
                $foodOrders = self::createFoodOrders($dao,$orderInfo, $packageRecords, $isEmployee);

                // 5. 更新套餐记录的关联信息
                self::updatePackageRecordRelations($dao,$packageRecords, $foodOrders);

                $result = [
                    'success' => true,
                    'wx_order_id' => $orderInfo['order_id'],
                    'package_records' => $packageRecords,
                    'food_orders' => $foodOrders
                ];
            });

            Log::info("套餐订单创建成功", [
                'order_id' => $orderInfo['order_id'],
                'package_count' => count($result['package_records']),
                'food_order_count' => count($result['food_orders'])
            ]);

            return $result;

        } catch (Exception $e) {
            Log::error("套餐订单创建失败", [
                'order_id' => $orderData['order_id'] ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
    
    /**
     * 创建套餐关联记录
     * @param array $orderInfo 订单信息
     * @param array $orderInfoData 订单详情数据
     * @param bool $isEmployee 是否员工
     * @return array 套餐记录
     */
    private static function createPackageRecords($dao,array $orderInfo, array $orderInfoData, bool $isEmployee): array
    {
        $packageRecords = [];
        $insertData = [];

        // 处理order_items结构（按日期分组）
        foreach ($orderInfoData['order_items'] as $dateItem) {
            $deliveryDate = $dateItem['date'];
            $deliveryFee = $dateItem['fee'] ?? 0;

            // 处理该日期下的套餐项目
            foreach ($dateItem['items'] as $item) {
                // 计算价格（含员工优惠）
                $originalPrice = (float)$item['price'];
                $unitPrice = $originalPrice;
                $employeeDiscount = 0;

                // 如果是员工订单，应用优惠
                if ($isEmployee && in_array($item['name'], ['A1', 'A2', 'B1', 'B2'])) {
                    $employeeDiscount = 5.00;
                    $unitPrice = $originalPrice - $employeeDiscount;
                }

                // 从package数组中查找对应的package_id
                $packageId = null;
                if (!empty($orderInfoData['package'])) {
                    foreach ($orderInfoData['package'] as $packageInfo) {
                        if (!empty($packageInfo['options'])) {
                            foreach ($packageInfo['options'] as $option) {
                                if ($option['name'] === $item['name'] && $option['num'] > 0) {
                                    $packageId = $option['package_id'];
                                    break 2;
                                }
                            }
                        }
                    }
                }

                $recordData = [
                    'wx_order_id' => $orderInfo['order_id'],
                    'food_order_id' => null, // 稍后更新
                    'food_order_item_id' => null, // 稍后更新
                    'package_id' => $packageId,
                    'package_name' => $item['name'],
                    'delivery_date' => $deliveryDate,
                    'original_num' => $item['num'],
                    'current_num' => $item['num'],
                    'unit_price' => $unitPrice,
                    'original_price' => $originalPrice,
                    'delivery_fee' => $deliveryFee,
                    'is_employee' => $isEmployee ? 1 : 0,
                    'employee_discount' => $employeeDiscount,
                    'sync_status' => 1, // 直接标记为已同步
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $insertData[] = $recordData;
            }
        }
        // 批量插入套餐记录
        if (!empty($insertData)) {
            // 逐个插入记录（Dao不支持批量插入）
            foreach ($insertData as $data) {
                $dao->insert('wx_store_order_package', $data);
            }

            // 获取插入的记录（包含ID）
            $packageRecords = $dao->search('wx_store_order_package', [
                'wx_order_id' => $orderInfo['order_id']
            ]);
        }
        
        return $packageRecords;
    }
    
    /**
     * 创建food_order记录
     * @param array $orderInfo 微信订单信息
     * @param array $packageRecords 套餐记录
     * @param bool $isEmployee 是否员工
     * @return array food_order记录
     */
    private static function createFoodOrders($dao,array $orderInfo, array $packageRecords, bool $isEmployee): array
    {
        // 按配送日期分组
        $packageGroups = [];
        foreach ($packageRecords as $record) {
            $packageGroups[$record['delivery_date']][] = $record;
        }
        
        $foodOrders = [];
        foreach ($packageGroups as $deliveryDate => $packages) {
            // 计算订单总金额
            $totalAmount = 0;
            $totalNum = 0;
            
            foreach ($packages as $package) {
                $totalAmount += ($package['unit_price'] + $package['delivery_fee']) * $package['original_num'];
                $totalNum += $package['original_num'];
            }
            
            // 解析订单信息获取用户信息和配送信息
            $orderInfoData = $orderInfo['order_info']; // 已经是数组格式

            // 创建order_food记录
            $foodOrderData = [
                'order_id' => 'WX' . $orderInfo['order_id'] . '_' . $deliveryDate, // 生成唯一订单号
                'cust_uid' => $orderInfo['uid'], // 使用uid字段
                'employee_uid' =>  null,
                'delivery_time' => $deliveryDate,
                'delivery_num' => $totalNum,
                'delivery_option' => $orderInfoData['delivery_option'] ?? '',
                'delivery_site' => $orderInfoData['delivery_site'] ?? '',
                'package_type' => $isEmployee ? '员工餐' : '老年餐',
                'total_num' => $totalNum,
                'pay_price' => $totalAmount,
                'sum_amount' => $totalAmount,
                'cust_delivery_fee' => 0, // 配送费暂时设为0
                'gov_fee' => 0, // 政府补贴暂时设为0
                'order_status' => 0, // 待完成
                'discounts_text' => $isEmployee ? '员工优惠' : null,
                'discounts_price' => $isEmployee ? ($orderInfoData['employee_discount'] ?? 0) : 0,
                'user_address' => $orderInfo['user_address'] ?? '',
                'user_longitude' => $orderInfo['user_longitude'] ?? '',
                'user_latitude' => $orderInfo['user_latitude'] ?? '',
                'created_by' => $orderInfo['uid'],
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $foodOrderId = $dao->insert('order_food', $foodOrderData);
            $foodOrderData['order_food_id'] = $foodOrderId;
            $foodOrders[] = $foodOrderData;

            // 创建order_food_item记录
            foreach ($packages as $package) {
                $foodOrderItemData = [
                    'order_id' => $foodOrderData['order_id'], // 使用生成的订单号
                    'cust_uid' => $orderInfo['uid'],
                    'employee_uid' => $isEmployee ? $orderInfo['uid'] : null,
                    'delivery_option' => $orderInfoData['delivery_option'] ?? '',
                    'delivery_site' => $orderInfoData['delivery_site'] ?? '',
                    'delivery_time' => $deliveryDate,
                    'num' => $package['original_num'],
                    'price' => $package['unit_price'],
                    'pay_price' => $package['unit_price'] * $package['original_num'],
                    'package_id' => $package['package_id'],
                    'package_type' => $isEmployee ? '员工餐' : '老年餐',
                    'package_name' => $package['package_name'],
                    'created_by' => $orderInfo['uid'],
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];

                $itemId = $dao->insert('order_food_item', $foodOrderItemData);

                // 记录关联关系，稍后更新
                $package['food_order_id'] = $foodOrderId;
                $package['food_order_item_id'] = $itemId;
            }
        }
        
        return $foodOrders;
    }
    
    /**
     * 更新套餐记录的关联信息
     * @param array $packageRecords 套餐记录
     * @param array $foodOrders food_order记录
     */
    private static function updatePackageRecordRelations($dao, array $packageRecords, array $foodOrders): void
    {
        // 建立日期到order_food信息的映射
        $dateToFoodOrderInfo = [];
        foreach ($foodOrders as $foodOrder) {
            $dateToFoodOrderInfo[$foodOrder['delivery_time']] = [
                'order_food_id' => $foodOrder['order_food_id'],
                'order_id' => $foodOrder['order_id']
            ];
        }

        // 更新套餐记录的关联信息
        foreach ($packageRecords as $package) {
            $foodOrderInfo = $dateToFoodOrderInfo[$package['delivery_date']] ?? null;

            if ($foodOrderInfo) {
                // 获取对应的order_food_item_id（使用字符串order_id查找）
                $foodOrderItem = $dao->get('order_food_item', [
                    'order_food_item_id'
                ], [
                    'order_id' => $foodOrderInfo['order_id'],
                    'package_id' => $package['package_id']
                ]);

                if ($foodOrderItem) {
                    $dao->update('wx_store_order_package', [
                        'food_order_id' => $foodOrderInfo['order_food_id'],
                        'food_order_item_id' => $foodOrderItem['order_food_item_id'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ], [
                        'id' => $package['id']
                    ]);
                }
            }
        }
    }

    /**
     * 处理退款（简化版）
     * @param string $wxOrderId 微信订单ID
     * @param array $refundDetails 退款详情
     * @return bool 处理结果
     * @throws Exception
     */
    public static function processRefund(string $wxOrderId, array $refundDetails): bool
    {
        Log::info("开始处理套餐退款", [
            'wx_order_id' => $wxOrderId,
            'refund_items' => count($refundDetails)
        ]);
        
        $dao = new Dao();
        $dao->action(function($database) use ($refundDetails) {
            // 1. 更新套餐记录数量
            foreach ($refundDetails as $detail) {
                // 先检查当前数量是否足够
                $packageRecord = $database->get('wx_store_order_package', [
                    'current_num'
                ], [
                    'id' => $detail['wx_store_order_package_id']
                ]);

                if (!$packageRecord || $packageRecord['current_num'] < $detail['refund_num']) {
                    throw new Exception("套餐数量不足，无法退款");
                }

                // 更新数量
                $affected = $database->update('wx_store_order_package', [
                    'current_num' => $packageRecord['current_num'] - $detail['refund_num'],
                    'updated_at' => date('Y-m-d H:i:s')
                ], [
                    'id' => $detail['wx_store_order_package_id']
                ]);

                if ($affected->rowCount() === 0) {
                    throw new Exception("套餐数量更新失败");
                }
            }

            // 2. 同步更新food_order_item
            $foodOrderGroups = [];
            foreach ($refundDetails as $detail) {
                $packageRecord = $database->get('wx_store_order_package', '*', [
                    'id' => $detail['wx_store_order_package_id']
                ]);

                if ($packageRecord && $packageRecord['food_order_item_id']) {
                    $foodOrderGroups[$packageRecord['food_order_id']][] = [
                        'food_order_item_id' => $packageRecord['food_order_item_id'],
                        'refund_num' => $detail['refund_num'],
                        'unit_price' => $packageRecord['unit_price']
                    ];
                }
            }
            
            // 3. 更新order_food_item数量和金额
            foreach ($foodOrderGroups as $foodOrderId => $items) {
                // 先获取order_food的order_id（字符串）
                $orderFood = $database->get('order_food', [
                    'order_id'
                ], [
                    'order_food_id' => $foodOrderId
                ]);

                if (!$orderFood) {
                    continue;
                }

                $orderIdString = $orderFood['order_id'];

                foreach ($items as $item) {
                    // 获取当前数量和金额
                    $currentItem = $database->get('order_food_item', [
                        'num', 'pay_price'
                    ], [
                        'order_food_item_id' => $item['food_order_item_id']
                    ]);

                    if ($currentItem) {
                        $newNum = $currentItem['num'] - $item['refund_num'];
                        $newPayPrice = $currentItem['pay_price'] - ($item['unit_price'] * $item['refund_num']);

                        $database->update('order_food_item', [
                            'num' => $newNum,
                            'pay_price' => $newPayPrice,
                            'update_time' => date('Y-m-d H:i:s')
                        ], [
                            'order_food_item_id' => $item['food_order_item_id']
                        ]);
                    }
                }

                // 4. 重新计算order_food总金额（使用字符串order_id）
                $totalAmount = $database->sum('order_food_item', 'pay_price', [
                    'order_id' => $orderIdString
                ]);

                $totalNum = $database->sum('order_food_item', 'num', [
                    'order_id' => $orderIdString
                ]);

                $database->update('order_food', [
                    'sum_amount' => $totalAmount ?: 0,
                    'pay_price' => $totalAmount ?: 0,
                    'total_num' => $totalNum ?: 0,
                    'delivery_num' => $totalNum ?: 0,
                    'update_time' => date('Y-m-d H:i:s')
                ], [
                    'order_food_id' => $foodOrderId
                ]);
            }
        });

        try {
            Log::info("套餐退款处理完成", [
                'wx_order_id' => $wxOrderId,
                'refund_items' => count($refundDetails)
            ]);

            return true;

        } catch (Exception $e) {
            Log::error("套餐退款处理失败", [
                'wx_order_id' => $wxOrderId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
