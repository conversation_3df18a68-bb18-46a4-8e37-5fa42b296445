<?php

namespace app\wx\service;

use app\wx\model\users\WxStoreOrderModel;
use support\Log;
use Exception;
use think\facade\Db;

/**
 * 统一套餐订单服务 - 简化版
 * 合并创建订单和同步功能，移除复杂的事件和重试机制
 */
class UnifiedPackageOrderService
{
    /**
     * 创建套餐订单并同步到food_order系统
     * @param array $orderData 订单数据
     * @return array 创建结果
     */
    public static function createAndSync(array $orderData): array
    {
        Log::info("开始创建套餐订单", [
            'order_id' => $orderData['order_id'],
            'order_no' => $orderData['order_no']
        ]);
        
        Db::startTrans();
        try {
            // 1. 获取微信订单信息
            $wxOrderModel = new WxStoreOrderModel();
            $orderInfo = $wxOrderModel->where('order_id', $orderData['order_id'])->findOrEmpty()->toArray();
            
            if (empty($orderInfo)) {
                throw new Exception("订单不存在: {$orderData['order_id']}");
            }
            
            if ($orderInfo['order_type'] != 3) {
                throw new Exception("非套餐订单: {$orderData['order_id']}");
            }
            
            // 2. 解析订单信息
            $orderInfoData = $orderInfo['order_info'];
            if (empty($orderInfoData['items'])) {
                throw new Exception("套餐订单信息不完整: {$orderData['order_id']}");
            }
            
            $isEmployee = $orderInfoData['is_employee'] ?? false;
            
            // 3. 创建套餐关联记录
            $packageRecords = self::createPackageRecords($orderInfo, $orderInfoData, $isEmployee);
            
            // 4. 同步创建food_order记录
            $foodOrders = self::createFoodOrders($orderInfo, $packageRecords);
            
            // 5. 更新套餐记录的关联信息
            self::updatePackageRecordRelations($packageRecords, $foodOrders);
            
            Db::commit();
            
            Log::info("套餐订单创建成功", [
                'order_id' => $orderInfo['order_id'],
                'package_count' => count($packageRecords),
                'food_order_count' => count($foodOrders)
            ]);
            
            return [
                'success' => true,
                'wx_order_id' => $orderInfo['order_id'],
                'package_records' => $packageRecords,
                'food_orders' => $foodOrders
            ];
            
        } catch (Exception $e) {
            Db::rollback();
            
            Log::error("套餐订单创建失败", [
                'order_id' => $orderData['order_id'] ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 创建套餐关联记录
     * @param array $orderInfo 订单信息
     * @param array $orderInfoData 订单详情数据
     * @param bool $isEmployee 是否员工
     * @return array 套餐记录
     */
    private static function createPackageRecords(array $orderInfo, array $orderInfoData, bool $isEmployee): array
    {
        $packageRecords = [];
        $insertData = [];
        
        foreach ($orderInfoData['items'] as $item) {
            $deliveryDate = $item['delivery_date'];
            
            // 计算价格（含员工优惠）
            $originalPrice = (float)$item['unit_price'];
            $unitPrice = $originalPrice;
            $employeeDiscount = 0;
            
            if ($isEmployee && in_array($item['package_name'], ['A1', 'A2', 'B1', 'B2'])) {
                $employeeDiscount = 5.00;
                $unitPrice = $originalPrice - $employeeDiscount;
            }
            
            $recordData = [
                'wx_order_id' => $orderInfo['order_id'],
                'food_order_id' => null, // 稍后更新
                'food_order_item_id' => null, // 稍后更新
                'package_id' => $item['package_id'],
                'package_name' => $item['package_name'],
                'delivery_date' => $deliveryDate,
                'original_num' => $item['quantity'],
                'current_num' => $item['quantity'],
                'unit_price' => $unitPrice,
                'original_price' => $originalPrice,
                'delivery_fee' => $item['delivery_fee'],
                'is_employee' => $isEmployee ? 1 : 0,
                'employee_discount' => $employeeDiscount,
                'sync_status' => 1, // 直接标记为已同步
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $insertData[] = $recordData;
        }
        
        // 批量插入套餐记录
        if (!empty($insertData)) {
            Db::name('wx_store_order_package')->insertAll($insertData);
            
            // 获取插入的记录（包含ID）
            $packageRecords = Db::name('wx_store_order_package')
                ->where('wx_order_id', $orderInfo['order_id'])
                ->select()
                ->toArray();
        }
        
        return $packageRecords;
    }
    
    /**
     * 创建food_order记录
     * @param array $orderInfo 微信订单信息
     * @param array $packageRecords 套餐记录
     * @return array food_order记录
     */
    private static function createFoodOrders(array $orderInfo, array $packageRecords): array
    {
        // 按配送日期分组
        $packageGroups = [];
        foreach ($packageRecords as $record) {
            $packageGroups[$record['delivery_date']][] = $record;
        }
        
        $foodOrders = [];
        
        foreach ($packageGroups as $deliveryDate => $packages) {
            // 计算订单总金额
            $totalAmount = 0;
            $totalNum = 0;
            
            foreach ($packages as $package) {
                $totalAmount += ($package['unit_price'] + $package['delivery_fee']) * $package['original_num'];
                $totalNum += $package['original_num'];
            }
            
            // 解析订单信息获取用户信息
            $orderInfoData = json_decode($orderInfo['order_info'], true);
            $userInfo = $orderInfoData['user_info'] ?? [];
            $deliveryInfo = $orderInfoData['delivery_info'] ?? [];
            
            // 创建food_order记录
            $foodOrderData = [
                'wx_order_id' => $orderInfo['order_id'],
                'cust_uid' => $orderInfo['user_id'],
                'delivery_time' => $deliveryDate,
                'total_amount' => $totalAmount,
                'total_num' => $totalNum,
                'order_status' => 0, // 待确认
                'payment_status' => '已支付',
                'real_name' => $userInfo['real_name'] ?? '',
                'user_phone' => $userInfo['user_phone'] ?? '',
                'user_address' => $userInfo['user_address'] ?? '',
                'delivery_option' => $deliveryInfo['delivery_option'] ?? 1,
                'delivery_site' => $deliveryInfo['delivery_site'] ?? '',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $foodOrderId = Db::name('food_order')->insertGetId($foodOrderData);
            $foodOrderData['order_id'] = $foodOrderId;
            $foodOrders[] = $foodOrderData;
            
            // 创建food_order_item记录
            foreach ($packages as $package) {
                $foodOrderItemData = [
                    'order_id' => $foodOrderId,
                    'package_id' => $package['package_id'],
                    'num' => $package['original_num'],
                    'unit_price' => $package['unit_price'],
                    'pay_price' => $package['unit_price'] * $package['original_num'],
                    'delivery_fee' => $package['delivery_fee'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $itemId = Db::name('food_order_item')->insertGetId($foodOrderItemData);
                
                // 记录关联关系，稍后更新
                $package['food_order_id'] = $foodOrderId;
                $package['food_order_item_id'] = $itemId;
            }
        }
        
        return $foodOrders;
    }
    
    /**
     * 更新套餐记录的关联信息
     * @param array $packageRecords 套餐记录
     * @param array $foodOrders food_order记录
     */
    private static function updatePackageRecordRelations(array $packageRecords, array $foodOrders): void
    {
        // 建立日期到food_order_id的映射
        $dateToFoodOrderId = [];
        foreach ($foodOrders as $foodOrder) {
            $dateToFoodOrderId[$foodOrder['delivery_time']] = $foodOrder['order_id'];
        }
        
        // 更新套餐记录的关联信息
        foreach ($packageRecords as $package) {
            $foodOrderId = $dateToFoodOrderId[$package['delivery_date']] ?? null;
            
            if ($foodOrderId) {
                // 获取对应的food_order_item_id
                $foodOrderItem = Db::name('food_order_item')
                    ->where('order_id', $foodOrderId)
                    ->where('package_id', $package['package_id'])
                    ->find();
                
                if ($foodOrderItem) {
                    Db::name('wx_store_order_package')
                        ->where('id', $package['id'])
                        ->update([
                            'food_order_id' => $foodOrderId,
                            'food_order_item_id' => $foodOrderItem['item_id'],
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                }
            }
        }
    }
    
    /**
     * 处理退款（简化版）
     * @param string $wxOrderId 微信订单ID
     * @param array $refundDetails 退款详情
     * @return bool 处理结果
     */
    public static function processRefund(string $wxOrderId, array $refundDetails): bool
    {
        Log::info("开始处理套餐退款", [
            'wx_order_id' => $wxOrderId,
            'refund_items' => count($refundDetails)
        ]);
        
        Db::startTrans();
        try {
            // 1. 更新套餐记录数量
            foreach ($refundDetails as $detail) {
                $affected = Db::name('wx_store_order_package')
                    ->where('id', $detail['wx_store_order_package_id'])
                    ->where('current_num', '>=', $detail['refund_num'])
                    ->dec('current_num', $detail['refund_num']);
                
                if ($affected === 0) {
                    throw new Exception("套餐数量更新失败，可能数量不足");
                }
            }
            
            // 2. 同步更新food_order_item
            $foodOrderGroups = [];
            foreach ($refundDetails as $detail) {
                $packageRecord = Db::name('wx_store_order_package')
                    ->where('id', $detail['wx_store_order_package_id'])
                    ->find();
                
                if ($packageRecord && $packageRecord['food_order_item_id']) {
                    $foodOrderGroups[$packageRecord['food_order_id']][] = [
                        'food_order_item_id' => $packageRecord['food_order_item_id'],
                        'refund_num' => $detail['refund_num'],
                        'unit_price' => $packageRecord['unit_price']
                    ];
                }
            }
            
            // 3. 更新food_order_item数量和金额
            foreach ($foodOrderGroups as $foodOrderId => $items) {
                foreach ($items as $item) {
                    Db::name('food_order_item')
                        ->where('item_id', $item['food_order_item_id'])
                        ->dec('num', $item['refund_num'])
                        ->dec('pay_price', $item['unit_price'] * $item['refund_num']);
                }
                
                // 4. 重新计算food_order总金额
                $totalAmount = Db::name('food_order_item')
                    ->where('order_id', $foodOrderId)
                    ->sum('pay_price');
                
                $totalNum = Db::name('food_order_item')
                    ->where('order_id', $foodOrderId)
                    ->sum('num');
                
                Db::name('food_order')
                    ->where('order_id', $foodOrderId)
                    ->update([
                        'total_amount' => $totalAmount,
                        'total_num' => $totalNum,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }
            
            Db::commit();
            
            Log::info("套餐退款处理完成", [
                'wx_order_id' => $wxOrderId,
                'food_order_count' => count($foodOrderGroups)
            ]);
            
            return true;
            
        } catch (Exception $e) {
            Db::rollback();
            
            Log::error("套餐退款处理失败", [
                'wx_order_id' => $wxOrderId,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
}
