<?php

namespace app\wx\service;


use app\wx\model\CustAccountModel;

use app\wx\model\CustBalanceRecordModel;

use app\wx\service\RefundCalculatorService;
use app\wx\service\UnifiedPackageOrderService;

use Exception;
use support\Log;
use think\facade\Db;

use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

class WxOrderService
{
    /**
     * 客户端创建订单-套餐订单（旧版兼容方法）
     * @param array $params
     * @return array
     * @throws Exception
     */
    public static function createPackageOrderLegacy(array $params): array
    {
        // 查询商品信息
        $productModel = new \app\wx\model\store\StoreProduct();
        $productInfo = $productModel->findOrEmpty($params['product_id'])->toArray();
        if (empty($productInfo)) {
            throw new BadRequestHttpException('套餐已下架');
        }

        // 获取支付金额（根据前端逻辑计算）
        $payPrice = 0;
        $costPayPrice = 0; // 员工优惠金额
        $sku = '';
        $orderItems = []; // 存储订单项目详情

        if (!empty($params['data']) && is_array($params['data'])) {
            foreach ($params['data'] as $dayIndex => $dayData) {
                // 每天的数据结构：time, fee, options, total_price, num 等
                $dayTime = $dayData['time'] ?? '';
                $dayFee = (float)($dayData['fee'] ?? 0);
                $dayTotalPrice = (float)($dayData['total_price'] ?? 0);
                $dayNum = (int)($dayData['num'] ?? 0);

                // 添加配送费
                $payPrice += $dayFee;

                // 添加套餐费用
                $payPrice += $dayTotalPrice;

                // 构建订单项目
                if ($dayNum > 0 && !empty($dayData['options'])) {
                    $dayItems = [];
                    foreach ($dayData['options'] as $option) {
                        if (!empty($option['num']) && $option['num'] > 0) {
                            $optionPrice = (float)$option['price'];
                            $optionNum = (int)$option['num'];

                            // 检查是否为员工订单且为员工套餐
                            if (isset($params['is_employee']) && $params['is_employee'] &&
                                in_array($option['name'], ['A1', 'A2', 'B1', 'B2'])) {
                                // 员工套餐优惠5元
                                $subsidizedPrice = $optionPrice - 5;
                                $costPayPrice += $optionNum * 5;
                                $dayItems[] = [
                                    'name' => $option['name'],
                                    'price' => $optionPrice,
                                    'subsidized_price' => $subsidizedPrice,
                                    'num' => $optionNum,
                                    'foods' => $option['data'] ?? []
                                ];
                            } else {
                                $dayItems[] = [
                                    'name' => $option['name'],
                                    'price' => $optionPrice,
                                    'num' => $optionNum,
                                    'foods' => $option['data'] ?? []
                                ];
                            }

                            $sku .= $option['name'] . 'x' . $optionNum . ' ';
                        }
                    }

                    $orderItems[] = [
                        'date' => $dayTime,
                        'fee' => $dayFee,
                        'total_price' => $dayTotalPrice,
                        'num' => $dayNum,
                        'items' => $dayItems
                    ];
                }
            }
        }

        // 如果有员工优惠，需要重新计算总价
        if ($costPayPrice > 0) {
            $payPrice -= $costPayPrice;
        }

        if ($payPrice == 0) {
            throw new BadRequestHttpException('请选择套餐');
        }

        // 订单信息添加
        $orderInfo = [
            'name' => $costPayPrice > 0 ? '员工餐' : $productInfo['store_name'],
            'sku' => trim($sku),
            'image' => $productInfo['recommend_image'],
            'price' => $payPrice,
            'num' => 1,
            'payment_mode' => $productInfo['payment_mode'],
            'remark' => $costPayPrice > 0 ? '员工优惠' . $costPayPrice . '元' : '',
            'package' => $params['data'], // 保持原始数据结构
            'order_items' => $orderItems, // 新增：结构化的订单项目
            'delivery_option' => $params['delivery_option'] ?? '',
            'delivery_site' => $params['delivery_site'] ?? '',
            'bed_status' => $params['bed_status'] ?? '',
            'bed_type' => $params['bed_type'] ?? '',
            'total_days' => count($orderItems), // 订餐天数
            'employee_discount' => $costPayPrice, // 员工优惠金额
        ];

        // 创建支付订单
        $orderModel = new \app\wx\model\users\WxStoreOrderModel();
        $orderData = [
            'order_no' => getId(),
            'order_type' => 3,
            'product_id' => $params['product_id'] ?? 147,
            'sku_id' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'created_by' => request()->wxUid,
            'uid' => request()->wxUid,
            'real_name' => $params['real_name'] ?? '',
            'user_phone' => $params['user_phone'] ?? '',
            'user_address' => $params['user_address'] ?? '',
            'user_notes' => $params['user_notes'] ?? '',
            'user_longitude' => $params['user_longitude'] ?? '',
            'user_latitude' => $params['user_latitude'] ?? '',
            'total_num' => 1,
            'total_price' => $payPrice,
            'pay_price' => $payPrice,
            'order_info' => $orderInfo,
            'remark' => $costPayPrice > 0 ? '员工优惠' . $costPayPrice . '元' : '',
        ];

        $lock = \yzh52521\WebmanLock\Locker::lock($params['user_phone'] ?? 'default');
        if (!$lock->acquire()) {
            throw new BadRequestHttpException('操作太频繁，请稍后再试');
        }

        try {
            $orderData['order_id'] = $orderModel->insertGetId($orderData);
            $orderData['env'] = $params['env'] ?? 1;
        } finally {
            $lock->release();
        }

        return static::getPayConfig($orderData);
    }









    /**
     * 账户扣费处理
     * @param int $custUid
     * @param string $orderId
     * @param float $amount
     * @param int $accountType 1:余额账户 2:补贴账户
     * @return bool
     */
    public static function processAccountPayment(int $custUid, string $orderId, float $amount, int $accountType = 1): bool
    {
        try {
            $accountModel = new CustAccountModel();
            $accountInfo = $accountModel->getAccountInfo($custUid, $accountType);

            if (empty($accountInfo)) {
                Log::error("账户不存在: custUid={$custUid}, accountType={$accountType}");
                return false;
            }

            // 检查余额是否足够
            if (!$accountModel->checkBalance($accountInfo['cust_account_id'], $amount)) {
                Log::error("账户余额不足: custUid={$custUid}, 需要金额={$amount}, 账户余额={$accountInfo['cust_balance']}");
                return false;
            }

            // 扣费
            $result = $accountModel->updateBalance($accountInfo['cust_account_id'], $amount, 'dec');

            if (!$result) {
                Log::error("账户扣费失败: custUid={$custUid}, amount={$amount}");
                return false;
            }

            // 记录交易日志（简化处理）
            Log::info("账户扣费成功", [
                'cust_uid' => $custUid,
                'account_type' => $accountType == 1 ? 'balance' : 'subsidy',
                'amount' => $amount,
                'order_id' => $orderId
            ]);

            // 创建余额记录
            $recordModel = new CustBalanceRecordModel();
            $recordModel->createRecord([
                'cust_uid' => $custUid,
                'cust_account_id' => $accountInfo['cust_account_id'],
                'record_type' => 'consume',
                'record_amount' => $amount,
                'record_balance' => $accountInfo['cust_balance'] - $amount,
                'record_remark' => '订单消费'
            ]);

            Log::info("账户扣费成功: custUid={$custUid}, orderId={$orderId}, amount={$amount}");
            return true;

        } catch (Exception $e) {
            Log::error("账户扣费异常: custUid={$custUid}, orderId={$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }


    /**
     * 微信支付配置获取
     * @param $orderInfo
     * @return array
     * @throws BadRequestHttpException
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     */
    public static function getPayConfig($orderInfo): array
    {
        // 获取用户openid用于支付
        $dao = new \app\utils\Dao();
        $userInfo = $dao->get('wx_user', [
            'deleted_at' => null,
            'uid' => $orderInfo['uid'],
        ]);
        if (empty($userInfo['h5_openid'])) {
            throw new BadRequestHttpException('用户openid不存在，无法创建支付');
        }

        // 创建支付配置
        $env = (int)($orderInfo['env'] ?? 1);
        $paymentData = \app\wx\service\WxPaymentService::createPayment($orderInfo['order_no'], $userInfo['h5_openid'], $env);

        return [
            'payConfig' => $paymentData,
            'orderId' => $orderInfo['order_no']
        ];
    }



    /**
     * 套餐订单退款（增强版）
     * @param array $params
     * @throws BadRequestHttpException
     */
    public static function refundOrderV3(array $params): void
    {
        try {
            Log::info("开始处理套餐订单退款", [
                'order_id' => $params['order_id'],
                'refund_data_count' => count($params['data'] ?? [])
            ]);

            // 检查订单是否存在
            $wxOrder = new \app\wx\model\users\WxStoreOrderModel();
            $orderInfo = $wxOrder->where('order_id', $params['order_id'])->findOrEmpty()->toArray();

            if (empty($orderInfo)) {
                throw new BadRequestHttpException('订单不存在');
            }

            if ($orderInfo['pay_status'] != 1) {
                throw new BadRequestHttpException('订单未支付，无法退款');
            }

            if ($orderInfo['refund_status'] > 0) {
                throw new BadRequestHttpException('订单已在退款流程中');
            }

            // 获取原始套餐记录
            $packageModel = new \app\wx\model\store\StoreOrderPackageModel();
            $originalPackages = $packageModel
                ->where('wx_order_id', $params['order_id'])
                ->select()
                ->toArray();

            if (empty($originalPackages)) {
                throw new BadRequestHttpException('订单处理中不允许退款，请5分钟后再申请退款');
            }

            // 使用退款计算服务计算退款金额
            $refundCalculation = RefundCalculatorService::calculatePackageRefund(
                $originalPackages,
                $params['data']
            );

            if ($refundCalculation['final_refund_amount'] <= 0) {
                throw new BadRequestHttpException('请选择退款套餐');
            }

            // 开始事务
            Db::startTrans();

            try {
                // 创建退款记录
                $refundId = self::createRefundRecord($params['order_id'], $refundCalculation);

                // 更新微信订单状态
                $wxOrder->where('order_id', $params['order_id'])->update([
                    'refund_status' => 1,
                    'order_status' => 3,
                    'refund_type' => 3,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 更新套餐记录的当前数量
                self::updatePackageRecordsForRefund($refundCalculation['refund_details']);

                // 套餐退款不需要审核，直接处理微信退款
                try {
                    $result = WxPaymentService::applyRefund([
                        'refund_sn' => 'RF' . date('YmdHis') . rand(1000, 9999),
                        'order_no' => $orderInfo['order_no'],
                        'refund_amount' => $refundCalculation['final_refund_amount'],
                        'refund_reason' => '套餐退款'
                    ]);

                    if ($result['success']) {
                        Log::info("微信退款申请成功", [
                            'refund_id' => $refundId,
                            'wx_refund_id' => $result['refund_id'] ?? null
                        ]);
                    }
                } catch (Exception $e) {
                    Log::error("微信退款申请失败", [
                        'refund_id' => $refundId,
                        'error' => $e->getMessage()
                    ]);
                }

                // 使用统一服务同步退款信息
                UnifiedPackageOrderService::processRefund($params['order_id'], $refundCalculation['refund_details']);

                Log::info("套餐退款直接处理", [
                    'order_id' => $params['order_id'],
                    'refund_id' => $refundId,
                    'refund_amount' => $refundCalculation['final_refund_amount']
                ]);

                Db::commit();

                Log::info("套餐订单退款处理完成", [
                    'order_id' => $params['order_id'],
                    'refund_amount' => $refundCalculation['final_refund_amount'],
                    'needs_audit' => $refundCalculation['needs_audit']
                ]);

            } catch (Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (Exception $e) {
            Log::error("套餐订单退款处理失败", [
                'order_id' => $params['order_id'] ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 虚拟订单退款
     * @param array $params
     * @throws BadRequestHttpException
     */
    public static function refundOrderV4(array $params): void
    {
        $wxOrder = new \app\wx\model\users\WxStoreOrderModel();

        // 查询订单状态是否存在
        $orderInfo = $wxOrder->where('order_id', $params['order_id'])->findOrEmpty()->toArray();

        if (!empty($orderInfo)) {
            if ($orderInfo['order_status'] != 1 || $orderInfo['pay_status'] != 1) {
                throw new BadRequestHttpException('订单未支付或服务已完成，无法退款');
            }
        } else {
            throw new BadRequestHttpException('订单不存在');
        }

        // 更新订单状态
        $wxOrder->where('order_id', $params['order_id'])->update([
            'refund_status' => 1,
            'order_status' => 3,
        ]);

        // 这里应该调用微信退款接口，暂时简化处理
        throw new BadRequestHttpException('虚拟订单退款功能需要完善微信退款接口');
    }

    /**
     * 订单支付成功处理
     * @param string $orderId
     * @param string $paymentMethod
     * @param float $payAmount
     * @return bool
     */
    public static function paySuccessOrder(string $orderId, string $paymentMethod = 'wechat', float $payAmount = 0): bool
    {
        try {
            // 获取订单信息
            $orderModel = new \app\wx\model\users\WxStoreOrderModel();
            $orderInfo = $orderModel->where('order_id', $orderId)->find();

            if (empty($orderInfo)) {
                Log::error("订单不存在: {$orderId}");
                return false;
            }

            if ($orderInfo['order_status'] !== '待付款') {
                Log::error("订单状态不正确: {$orderId}, 当前状态: {$orderInfo['order_status']}");
                return false;
            }

            // 更新订单状态
            $result = $orderModel->where('order_id', $orderId)->update([
                'order_status' => '待完成',
                'pay_time' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            if (!$result) {
                Log::error("更新订单状态失败: {$orderId}");
                return false;
            }

            // 记录支付成功日志
            Log::info("订单支付成功", [
                'order_id' => $orderId,
                'payment_method' => $paymentMethod,
                'pay_amount' => $payAmount ?: $orderInfo['order_price']
            ]);

            Log::info("订单支付成功处理完成: {$orderId}");
            return true;

        } catch (Exception $e) {
            Log::error("订单支付成功处理异常: {$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }



    /**
     * 创建套餐订单（支付成功后调用）
     * @param string $orderId
     * @return bool
     */
    public static function createPackageOrder(string $orderId): bool
    {
        try {
            // 获取订单信息
            $orderModel = new \app\wx\model\users\WxStoreOrderModel();
            $orderInfo = $orderModel->where('order_id', $orderId)->find();

            if (empty($orderInfo)) {
                Log::error("套餐订单创建失败，订单不存在: {$orderId}");
                return false;
            }

            if ($orderInfo['order_type'] !== 1) {
                Log::error("非套餐订单: {$orderId}");
                return false;
            }

            // 解析订单信息
            $orderInfoData = json_decode($orderInfo['order_info'], true);
            if (empty($orderInfoData['packages'])) {
                Log::error("套餐订单信息不完整: {$orderId}");
                return false;
            }

            // 同步到food_order系统
            $syncResult = FoodOrderSyncService::syncPackageOrderToFoodSystem($orderId);

            if (!$syncResult) {
                Log::error("套餐订单同步到food_order系统失败: {$orderId}");
                // 即使同步失败，也不影响主订单流程，只记录错误
            }

            Log::info("套餐订单创建成功: {$orderId}, 同步结果: " . ($syncResult ? '成功' : '失败'));
            return true;

        } catch (Exception $e) {
            Log::error("套餐订单创建异常: {$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }



    /**
     * 创建退款记录
     * @param string $orderId 订单ID
     * @param array $refundCalculation 退款计算结果
     * @return int 退款记录ID
     */
    private static function createRefundRecord(string $orderId, array $refundCalculation): int
    {
        $refundData = [
            'order_id' => $orderId,
            'refund_sn' => 'RF' . date('YmdHis') . rand(1000, 9999),
            'refund_price' => $refundCalculation['final_refund_amount'],
            'refund_info' => $refundCalculation['refund_details'],
            'refund_message' => '套餐退款',
            'payment_status' => $refundCalculation['needs_audit'] ? '待审核' : '处理中',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $refundModel = new \app\wx\model\users\WxStoreOrderRefundModel();
        return $refundModel->insertGetId($refundData);
    }

    /**
     * 更新套餐记录的当前数量（退款后）
     * @param array $refundDetails 退款详情
     */
    private static function updatePackageRecordsForRefund(array $refundDetails): void
    {
        $packageModel = new \app\wx\model\store\StoreOrderPackageModel();
        foreach ($refundDetails as $detail) {
            $packageModel
                ->where('id', $detail['wx_store_order_package_id'])
                ->update([
                    'current_num' => $detail['remaining_num'],
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
        }
    }




}