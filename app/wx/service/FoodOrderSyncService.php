<?php

namespace app\wx\service;

use app\wx\model\CustUserModel;
use app\wx\model\CustBalanceRecordModel;
use app\wx\model\users\WxStoreOrderModel;
use app\utils\Dao;
use Exception;
use support\Log;

use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * 套餐订单与food_order系统同步服务
 */
class FoodOrderSyncService
{
    /**
     * 同步套餐订单到food_order系统
     * @param string $orderId 订单ID
     * @return bool
     */
    public static function syncPackageOrderToFoodSystem(string $orderId): bool
    {
        try {
            // 获取订单信息
            $orderModel = new WxStoreOrderModel();
            $orderInfo = $orderModel->where('order_id', $orderId)->find();
            
            if (empty($orderInfo)) {
                Log::error("同步失败，订单不存在: {$orderId}");
                return false;
            }
            
            if ($orderInfo['order_type'] !== 1) {
                Log::error("非套餐订单，无需同步: {$orderId}");
                return false;
            }
            
            if ($orderInfo['order_status'] !== '待完成' && $orderInfo['order_status'] !== '已完成') {
                Log::error("订单状态不正确，无法同步: {$orderId}, 状态: {$orderInfo['order_status']}");
                return false;
            }
            
            // 解析订单信息
            $orderInfoData = json_decode($orderInfo['order_info'], true);
            if (empty($orderInfoData['packages'])) {
                Log::error("套餐订单信息不完整: {$orderId}");
                return false;
            }
            
            // 获取客户信息
            $custUserModel = new CustUserModel();
            $custInfo = $custUserModel->getCustInfo($orderInfo['cust_uid']);
            
            if (empty($custInfo)) {
                Log::error("客户信息不存在: custUid={$orderInfo['cust_uid']}");
                return false;
            }
            
            // 检查是否为员工订单
            $isEmployee = isset($orderInfoData['is_employee']) && $orderInfoData['is_employee'];
            
            // 按配送日期分组创建food_order
            $packagesByDate = [];
            foreach ($orderInfoData['packages'] as $package) {
                $deliveryDate = $package['day_time'];
                if (!isset($packagesByDate[$deliveryDate])) {
                    $packagesByDate[$deliveryDate] = [];
                }
                $packagesByDate[$deliveryDate][] = $package;
            }
            
            // 为每个配送日期创建food_order记录
            foreach ($packagesByDate as $deliveryDate => $packages) {
                $foodOrderId = self::createFoodOrder($orderId, $orderInfo, $custInfo, $deliveryDate, $packages, $isEmployee);
                
                if ($foodOrderId) {
                    // 创建food_order_item记录
                    foreach ($packages as $package) {
                        self::createFoodOrderItem($orderId, $foodOrderId, $orderInfo, $custInfo, $deliveryDate, $package, $isEmployee);
                    }
                    
                    // 更新food_order总金额
                    self::updateFoodOrderTotal($foodOrderId);
                }
            }
            
            // 创建支付记录（如果是微信支付）
            if ($orderInfo['order_status'] === '待完成' || $orderInfo['order_status'] === '已完成') {
                self::createPaymentRecord($orderInfo, $custInfo);
            }
            
            // 记录同步日志
            Log::info("套餐订单同步记录", [
                'order_id' => $orderId,
                'message' => '套餐订单已同步到food_order系统'
            ]);
            
            Log::info("套餐订单同步成功: {$orderId}");
            return true;
            
        } catch (Exception $e) {
            Log::error("套餐订单同步异常: {$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 创建food_order记录
     * @param string $orderId
     * @param array $orderInfo
     * @param array $custInfo
     * @param string $deliveryDate
     * @param array $packages
     * @param bool $isEmployee
     * @return int|false
     */
    private static function createFoodOrder(string $orderId, array $orderInfo, array $custInfo, string $deliveryDate, array $packages, bool $isEmployee)
    {
        try {
            $orderInfoData = json_decode($orderInfo['order_info'], true);
            
            // 计算配送费
            $deliveryFee = 0;
            foreach ($packages as $package) {
                if (isset($package['delivery_fee'])) {
                    $deliveryFee += $package['delivery_fee'];
                }
            }
            
            // 计算折扣金额（员工餐）
            $discountPrice = 0;
            if ($isEmployee) {
                foreach ($packages as $package) {
                    if (strpos($package['package_name'], 'A') !== false || strpos($package['package_name'], 'B') !== false) {
                        $discountPrice += $package['package_num'] * 5; // 员工餐优惠5元
                    }
                }
            }
            
            $foodOrderData = [
                'order_id' => $orderId,
                'cust_uid' => $orderInfo['cust_uid'],
                'employee_uid' => $isEmployee ? $orderInfo['cust_uid'] : null,
                'delivery_option' => $orderInfoData['delivery_site'] ?? '配送',
                'delivery_site' => $orderInfoData['delivery_site'] ?? '',
                'delivery_time' => $deliveryDate,
                'delivery_address' => $orderInfoData['delivery_address'] ?? '',
                'delivery_phone' => $orderInfoData['delivery_phone'] ?? '',
                'delivery_name' => $orderInfoData['delivery_name'] ?? '',
                'delivery_fee' => $deliveryFee,
                'payment_method' => '微信支付',
                'payment_status' => '已支付',
                'discount_price' => $discountPrice,
                'discount_setting' => $isEmployee ? '员工餐优惠' : '',
                'remark' => $orderInfoData['remark'] ?? '',
                'create_time' => $orderInfo['create_time'] ?? date('Y-m-d H:i:s'),
                'created_by' => 1
            ];
            
            $dao = new \app\utils\Dao();
            $foodOrderId = $dao->insert('order_food', $foodOrderData);
            
            Log::info("创建food_order成功", [
                'order_id' => $orderId,
                'food_order_id' => $foodOrderId,
                'delivery_date' => $deliveryDate
            ]);
            
            return $foodOrderId;
            
        } catch (Exception $e) {
            Log::error("创建food_order失败: {$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 创建food_order_item记录
     * @param string $orderId
     * @param int $foodOrderId
     * @param array $orderInfo
     * @param array $custInfo
     * @param string $deliveryDate
     * @param array $package
     * @param bool $isEmployee
     * @return bool
     */
    private static function createFoodOrderItem(string $orderId, int $foodOrderId, array $orderInfo, array $custInfo, string $deliveryDate, array $package, bool $isEmployee): bool
    {
        try {
            $orderInfoData = json_decode($orderInfo['order_info'], true);
            
            // 计算价格（员工餐有优惠）
            $price = $package['package_price'];
            if ($isEmployee && (strpos($package['package_name'], 'A') !== false || strpos($package['package_name'], 'B') !== false)) {
                $price = $price - 5; // 员工餐优惠5元
            }
            
            $payPrice = $price * $package['package_num'];
            
            $foodOrderItemData = [
                'order_id' => $orderId,
                'cust_uid' => $orderInfo['cust_uid'],
                'employee_uid' => $isEmployee ? $orderInfo['cust_uid'] : null,
                'delivery_option' => $orderInfoData['delivery_site'] ?? '配送',
                'delivery_site' => $orderInfoData['delivery_site'] ?? '',
                'delivery_time' => $deliveryDate,
                'package_id' => $package['package_id'],
                'package_name' => $package['package_name'],
                'package_type' => $isEmployee ? '员工餐' : '养老餐',
                'num' => $package['package_num'],
                'price' => $price,
                'pay_price' => $payPrice,
                'create_time' => $orderInfo['create_time'] ?? date('Y-m-d H:i:s'),
                'created_by' => 1
            ];
            
            $dao = new \app\utils\Dao();
            $itemId = $dao->insert('order_food_item', $foodOrderItemData);
            
            Log::info("创建food_order_item成功", [
                'order_id' => $orderId,
                'food_order_id' => $foodOrderId,
                'item_id' => $itemId,
                'package_id' => $package['package_id'],
                'package_name' => $package['package_name']
            ]);
            
            return true;
            
        } catch (Exception $e) {
            Log::error("创建food_order_item失败: {$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 更新food_order总金额
     * @param int $foodOrderId
     * @return bool
     */
    private static function updateFoodOrderTotal(int $foodOrderId): bool
    {
        try {
            $dao = new \app\utils\Dao();
            
            // 计算总金额
            $totalAmount = $dao->sum('order_food_item', 'pay_price', [
                'order_food_id' => $foodOrderId,
                'delete_time' => null
            ]);
            
            // 获取配送费
            $foodOrder = $dao->get('order_food', ['delivery_fee'], [
                'order_food_id' => $foodOrderId
            ]);
            
            $deliveryFee = $foodOrder['delivery_fee'] ?? 0;
            $paymentAmount = $totalAmount + $deliveryFee;
            
            // 更新订单总金额
            $dao->update('order_food', [
                'payment_amount' => $paymentAmount
            ], [
                'order_food_id' => $foodOrderId
            ]);
            
            Log::info("更新food_order总金额成功", [
                'food_order_id' => $foodOrderId,
                'total_amount' => $totalAmount,
                'delivery_fee' => $deliveryFee,
                'payment_amount' => $paymentAmount
            ]);
            
            return true;
            
        } catch (Exception $e) {
            Log::error("更新food_order总金额失败: foodOrderId={$foodOrderId}, 错误: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 创建支付记录
     * @param array $orderInfo
     * @param array $custInfo
     * @return bool
     */
    private static function createPaymentRecord(array $orderInfo, array $custInfo): bool
    {
        try {
            // 这里可以创建支付相关的记录
            // 例如：缴费记录、账单记录等
            
            Log::info("创建支付记录成功", [
                'order_id' => $orderInfo['id'],
                'cust_uid' => $orderInfo['cust_uid'],
                'amount' => $orderInfo['order_price']
            ]);
            
            return true;
            
        } catch (Exception $e) {
            Log::error("创建支付记录失败: orderId={$orderInfo['id']}, 错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 同步微信套餐订单到food_order系统（新版）
     * @param string $wxOrderId 微信订单ID
     * @return bool 同步结果
     */
    public static function syncPackageOrder(string $wxOrderId): bool
    {
        try {
            Log::info("开始同步微信套餐订单到food_order系统", ['wx_order_id' => $wxOrderId]);

            // 获取微信订单信息
            $wxOrderModel = new WxStoreOrderModel();
            $orderInfo = $wxOrderModel->where('order_id', $wxOrderId)->findOrEmpty()->toArray();

            if (empty($orderInfo)) {
                throw new Exception("微信订单不存在: {$wxOrderId}");
            }

            // 获取套餐关联记录
            $packageRecords = Db::name('wx_store_order_package')
                ->where('wx_order_id', $wxOrderId)
                ->where('sync_status', 0) // 只同步未同步的记录
                ->select()
                ->toArray();

            if (empty($packageRecords)) {
                Log::info("没有需要同步的套餐记录", ['wx_order_id' => $wxOrderId]);
                return true;
            }

            // 创建或获取老人用户
            $mainUserId = self::createOrUpdateMainUser([
                'real_name' => $orderInfo['real_name'],
                'user_phone' => $orderInfo['user_phone'],
                'user_address' => $orderInfo['user_address'],
                'user_longitude' => $orderInfo['user_longitude'],
                'user_latitude' => $orderInfo['user_latitude'],
            ]);

            // 按配送日期分组处理
            $packagesByDate = [];
            foreach ($packageRecords as $record) {
                $date = $record['delivery_date'];
                if (!isset($packagesByDate[$date])) {
                    $packagesByDate[$date] = [];
                }
                $packagesByDate[$date][] = $record;
            }

            // 为每个配送日期创建food_order
            foreach ($packagesByDate as $deliveryDate => $dayPackages) {
                $foodOrderId = self::createOrUpdateFoodOrder($mainUserId, $orderInfo, $deliveryDate, $dayPackages);

                // 创建套餐项目
                foreach ($dayPackages as $package) {
                    $foodOrderItemId = self::createOrUpdateFoodOrderItem($foodOrderId, $package, $orderInfo);

                    // 更新关联记录
                    Db::name('wx_store_order_package')
                        ->where('id', $package['id'])
                        ->update([
                            'food_order_id' => $foodOrderId,
                            'food_order_item_id' => $foodOrderItemId,
                            'sync_status' => 1,
                            'sync_time' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                }

                // 更新food_order总金额
                self::updateFoodOrderAmount($foodOrderId);
            }

            // 创建缴费记录
            self::createWxPaymentRecord([
                'user_id' => $mainUserId,
                'order_info' => $orderInfo
            ]);

            Log::info("微信套餐订单同步完成", [
                'wx_order_id' => $wxOrderId,
                'main_user_id' => $mainUserId,
                'package_count' => count($packageRecords)
            ]);

            return true;

        } catch (Exception $e) {
            Log::error("微信套餐订单同步失败", [
                'wx_order_id' => $wxOrderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新同步状态为失败
            Db::name('wx_store_order_package')
                ->where('wx_order_id', $wxOrderId)
                ->where('sync_status', 0)
                ->update([
                    'sync_status' => 2,
                    'sync_error' => $e->getMessage(),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            return false;
        }
    }

    /**
     * 创建或更新老人用户（新版）
     * @param array $userData 用户数据
     * @return int 用户ID
     */
    private static function createOrUpdateMainUser(array $userData): int
    {
        $dao = new Dao();

        // 查询用户是否存在
        $existingUser = $dao->get('main_user', [
            'v11' => $userData['user_phone']
        ]);

        if (!empty($existingUser)) {
            // 更新用户信息
            $dao->update('main_user', [
                'v1' => $userData['real_name'],
                'v26' => $userData['user_address'],
                'user_longitude' => $userData['user_longitude'],
                'user_latitude' => $userData['user_latitude'],
            ], [
                'id' => $existingUser['id']
            ]);

            return $existingUser['id'];
        }

        // 创建新用户
        $userId = $dao->insert('main_user', [
            'v1' => $userData['real_name'],
            'v11' => $userData['user_phone'],
            'v26' => $userData['user_address'],
            'user_longitude' => $userData['user_longitude'],
            'user_latitude' => $userData['user_latitude'],
        ]);

        // 创建用户余额记录
        $dao->insert('main_user_balance', [
            'uid' => $userId,
            'nickname' => $userData['real_name'],
            'name' => $userData['real_name'],
            'phone' => $userData['user_phone'],
            'now_amount' => 0,
            'recharge_amount' => 0,
        ]);

        return $userId;
    }

    /**
     * 创建或更新套餐订单（新版）
     * @param int $userId 用户ID
     * @param array $orderInfo 订单信息
     * @param string $deliveryDate 配送日期
     * @param array $dayPackages 当天套餐
     * @return int 订单ID
     */
    private static function createOrUpdateFoodOrder(int $userId, array $orderInfo, string $deliveryDate, array $dayPackages): int
    {
        $dao = new Dao();
        $orderInfoData = $orderInfo['order_info'] ?? [];

        // 查询是否已存在当天订单
        $existingOrder = $dao->get('food_order', [
            'user_id' => $userId,
            'package_order_time' => $deliveryDate
        ]);

        // 计算配送费（取当天第一个套餐的配送费）
        $deliveryFee = $dayPackages[0]['delivery_fee'] ?? 0;

        if (!empty($existingOrder)) {
            // 更新现有订单
            $dao->update('food_order', [
                'delivery_cost' => $deliveryFee,
                'remark' => $orderInfo['user_notes'] ?? '',
                'delivery_site' => $orderInfoData['delivery_site'] ?? '',
                'user_address' => $orderInfo['user_address'],
            ], [
                'order_id' => $existingOrder['orderId']
            ]);

            return $existingOrder['orderId'];
        }

        // 创建新订单
        $foodOrderId = $dao->insert('food_order', [
            'user_id' => $userId,
            'delivery_site' => $orderInfoData['delivery_site'] ?? '',
            'delivery_option' => $orderInfoData['delivery_option'] ?? '',
            'payment_method' => '微信小程序支付',
            'payment_status' => '已支付',
            'remark' => $orderInfo['user_notes'] ?? '',
            'staff_id' => '',
            'delivery_cost' => $deliveryFee,
            'package_order_time' => $deliveryDate,
            'user_longitude' => $orderInfo['user_longitude'],
            'user_latitude' => $orderInfo['user_latitude'],
            'user_address' => $orderInfo['user_address'],
            'discount_price' => 0, // 稍后计算
            'created_at' => $orderInfo['pay_time'] ?? date('Y-m-d H:i:s'),
            'created_by' => 61,
        ]);

        return $foodOrderId;
    }

    /**
     * 创建或更新套餐项目（新版）
     * @param int $foodOrderId 套餐订单ID
     * @param array $package 套餐数据
     * @param array $orderInfo 订单信息
     * @return int 项目ID
     */
    private static function createOrUpdateFoodOrderItem(int $foodOrderId, array $package, array $orderInfo): int
    {
        $dao = new Dao();

        // 查询是否已存在套餐项目
        $existingItem = $dao->get('food_order_item', [
            'order_id' => $foodOrderId,
            'package_id' => $package['package_id'],
            'day_time' => $package['delivery_date']
        ]);

        $payPrice = $package['unit_price'] * $package['current_num'];

        if (!empty($existingItem)) {
            // 更新现有项目
            $totalNum = $existingItem['num'] + $package['current_num'];
            $totalPayPrice = $existingItem['pay_price'] + $payPrice;

            $dao->update('food_order_item', [
                'num' => $totalNum,
                'price' => $package['unit_price'],
                'pay_price' => $totalPayPrice,
                'updated_by' => $orderInfo['uid'],
                'updated_at' => date('Y-m-d H:i:s')
            ], [
                'item_id' => $existingItem['item_id']
            ]);

            return $existingItem['item_id'];
        }

        // 创建新项目
        $orderInfoData = $orderInfo['order_info'] ?? [];
        $itemId = $dao->insert('food_order_item', [
            'order_id' => $foodOrderId,
            'day_time' => $package['delivery_date'],
            'package_id' => $package['package_id'],
            'num' => $package['current_num'],
            'price' => $package['unit_price'],
            'pay_price' => $payPrice,
            'created_by' => 61,
            'created_at' => $orderInfo['pay_time'] ?? date('Y-m-d H:i:s'),
            'package_name' => $package['package_name'],
            'delivery_option' => $orderInfoData['delivery_option'] ?? '',
            'delivery_site' => $orderInfoData['delivery_site'] ?? '',
        ]);

        return $itemId;
    }

    /**
     * 更新food_order总金额（新版）
     * @param int $foodOrderId 套餐订单ID
     * @return bool 更新结果
     */
    private static function updateFoodOrderAmount(int $foodOrderId): bool
    {
        try {
            $dao = new Dao();

            // 计算套餐项目总金额
            $totalAmount = $dao->sum('food_order_item', 'pay_price', [
                'order_id' => $foodOrderId,
                'delete_time' => null
            ]);

            // 获取配送费
            $foodOrder = $dao->get('food_order', ['delivery_cost', 'discount_price'], [
                'order_id' => $foodOrderId
            ]);

            $deliveryFee = $foodOrder['delivery_cost'] ?? 0;
            $discountPrice = $foodOrder['discount_price'] ?? 0;
            $paymentAmount = $totalAmount + $deliveryFee;

            // 更新订单总金额
            $dao->update('food_order', [
                'payment_amount' => $paymentAmount,
                'discount_price' => $discountPrice
            ], [
                'order_id' => $foodOrderId
            ]);

            return true;

        } catch (Exception $e) {
            Log::error("更新food_order总金额失败", [
                'food_order_id' => $foodOrderId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 创建微信支付缴费记录（新版）
     * @param array $paymentData 支付数据
     * @return bool 创建结果
     */
    private static function createWxPaymentRecord(array $paymentData): bool
    {
        try {
            // 这里可以创建支付相关的记录
            // 例如：缴费记录、账单记录等

            Log::info("创建微信支付缴费记录成功", [
                'user_id' => $paymentData['user_id'],
                'order_id' => $paymentData['order_info']['order_id'],
                'amount' => $paymentData['order_info']['pay_price']
            ]);

            return true;

        } catch (Exception $e) {
            Log::error("创建微信支付缴费记录失败", [
                'user_id' => $paymentData['user_id'] ?? '',
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
}
