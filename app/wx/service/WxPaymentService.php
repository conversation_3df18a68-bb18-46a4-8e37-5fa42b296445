<?php

namespace app\wx\service;


use EasyWeChat\Pay\Application as PayApplication;
use Exception;
use support\Log;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use Webman\Config;

/**
 * 微信支付服务类
 */
class WxPaymentService
{
    /**
     * 创建微信支付订单（与旧版一致）
     * @param string $orderId 订单ID
     * @param string $openid 用户openid
     * @param int $env 环境 1:小程序 0:公众号
     * @return array
     * @throws BadRequestHttpException
     */
    public static function createPayment(string $orderId, string $openid, int $env = 1): array
    {
        try {
            // 先尝试从 wx_store_order 表获取订单信息
            $wxStoreOrderModel = new \app\wx\model\users\WxStoreOrderModel();
            $orderInfo = $wxStoreOrderModel->where('order_id', $orderId)->findOrEmpty()->toArray();

            if (!empty($orderInfo)) {
                // 处理 wx_store_order 表的订单
                return self::createPaymentForStoreOrder($orderInfo, $openid, $env);
            }

            // 订单不存在于wx_store_order表
            throw new BadRequestHttpException('订单不存在');

        } catch (Exception $e) {
            Log::error("创建微信支付失败: {$orderId}, 错误: " . $e->getMessage());
            throw new BadRequestHttpException('创建支付失败：' . $e->getMessage());
        }
    }

    /**
     * 为 wx_store_order 表的订单创建支付
     * @param array $orderInfo 订单信息
     * @param string $openid 用户openid
     * @param int $env 环境
     * @return array
     * @throws BadRequestHttpException
     */
    private static function createPaymentForStoreOrder(array $orderInfo, string $openid, int $env): array
    {
        // 检查订单状态（wx_store_order 表没有 order_status 字段，通过 pay_status 判断）
        if (!empty($orderInfo['pay_status']) && $orderInfo['pay_status'] == 1) {
            throw new BadRequestHttpException('订单已支付');
        }

        // 获取微信支付配置
        $config = Config::get('wechat.pay');
        if (empty($config)) {
            throw new BadRequestHttpException('支付配置未设置');
        }

        // 设置对应的app_id
        if ($env === 1) {
            $config['app_id'] = Config::get('wechat.miniApp.app_id'); // 小程序
        } else {
            $config['app_id'] = 'wx20877397d3ee12a8';
        }

        // 构建支付数据
        $orderInfoData = $orderInfo['order_info'] ?? [];
        $productName = $orderInfoData['name'] ?? '营养套餐';

        $data = [
            'order_no' => $orderInfo['order_no'],
            'order_id' => $orderInfo['order_id'],
            'order_info' => [
                'name' => $productName
            ],
            'pay_price' => $orderInfo['pay_price'],
            'openid' => $openid,
            'env' => $env
        ];

        return self::createPay($config, $data);
    }

    /**
     * 创建支付（旧版WxServices::createPay方法）
     * @param array $config
     * @param array $data
     * @return array
     * @throws BadRequestHttpException
     */
    private static function createPay(array $config, array $data): array
    {
        $app = new PayApplication($config);

        // 查询订单号是否已创建并且未支付，若存在则检查状态（与旧版一致）
        try {
            $payInfo = $app->getClient()->get("v3/pay/transactions/out-trade-no/{$data['order_no']}", [
                'query' => [
                    'mchid' => $config['mch_id']
                ]
            ])->toArray(false);

            if (!empty($payInfo['trade_state']) && $payInfo['trade_state'] == 'SUCCESS') {
                // 订单已支付
                throw new BadRequestHttpException('订单已支付');
            }
        } catch (Exception $e) {
            // 如果订单不存在，继续创建支付（与旧版逻辑一致）
            if (!str_contains($e->getMessage(), 'ORDER_NOT_EXIST') && !str_contains($e->getMessage(), 'ORDERNOTEXIST')) {
                Log::error("查询支付状态失败", [
                    'order_no' => $data['order_no'],
                    'error' => $e->getMessage()
                ]);
                // 如果不是订单不存在的错误，继续创建支付
            }
        }

        // 创建支付参数（与旧版完全一致）
        $params = [
            "mchid" => $config['mch_id'],
            "out_trade_no" => $data['order_no'], // 与旧版一致，不强制转换字符串
            "appid" => $config['app_id'],
            "attach" => (string)$data['order_id'],
            "description" => $data['order_info']['name'],
            "notify_url" => 'https://api.v2.zkshlm.com//wx/notice/wxPayNotify', // 与旧版一致，使用 getenv
            "amount" => [
                "total" => (int)($data['pay_price'] * 100),
                "currency" => "CNY"
            ],
            "payer" => [
                "openid" => $data['openid']
            ]
        ];

        $response = $app->getClient()->postJson("v3/pay/transactions/jsapi", $params)->toArray(false);
        $utils = $app->getUtils();
        $appId = $config['app_id'];
        $signType = 'RSA';

        if ((int)$data['env'] === 1) {
            return $utils->buildMiniAppConfig($response['prepay_id'], $appId, $signType);
        } else {
            return $utils->buildBridgeConfig($response['prepay_id'], $appId, $signType);
        }
    }
    

    
    /**
     * 查询支付状态（与旧版WxServices::getPayStatus一致）
     * @param string $orderId 订单号
     * @return array
     * @throws BadRequestHttpException
     */
    public static function getPaymentStatus(string $orderId): array
    {
        try {
            $config = Config::get('wechat.pay');
            return self::getPayStatus($config, $orderId);

        } catch (Exception $e) {
            Log::error("查询支付状态失败: orderId={$orderId}, 错误: " . $e->getMessage());
            throw new BadRequestHttpException('查询支付状态失败');
        }
    }

    /**
     * 查询支付信息（旧版WxServices::getPayStatus方法）
     * @param array $config
     * @param string $orderNo
     * @return array
     */
    private static function getPayStatus(array $config, string $orderNo): array
    {
        $app = new PayApplication($config);
        $response = $app->getClient()->get("v3/pay/transactions/out-trade-no/{$orderNo}", [
            'query' => [
                'mchid' => $app->getMerchant()->getMerchantId()
            ]
        ]);
        return $response->toArray();
    }
    
    /**
     * 支付成功处理（增强版，支持微信订单和主订单）
     * @param string $orderId 订单号
     * @param array $paymentInfo 支付信息
     * @return bool
     */
    public static function handlePaymentSuccess(string $orderId, array $paymentInfo = []): bool
    {
        try {
            // 先尝试从 wx_store_order 表查找订单
            $wxOrderModel = new \app\wx\model\users\WxStoreOrderModel();
            $wxOrderInfo = $wxOrderModel->where('order_no', $orderId)->findOrEmpty()->toArray();

            if (!empty($wxOrderInfo)) {
                // 处理微信订单
                return self::handleWxOrderPaymentSuccess($wxOrderInfo, $paymentInfo);
            }

            // 订单不存在于wx_store_order表，不处理旧版订单
            Log::warning("支付成功处理，订单不在wx_store_order表中: {$orderId}");
            return false;

        } catch (Exception $e) {
            Log::error("支付成功处理异常: {$orderId}, 错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理微信订单支付成功
     * @param array $orderInfo 订单信息
     * @param array $paymentInfo 支付信息
     * @return bool
     */
    private static function handleWxOrderPaymentSuccess(array $orderInfo, array $paymentInfo): bool
    {
        try {
            $wxOrderModel = new \app\wx\model\users\WxStoreOrderModel();

            // 检查订单状态
            if ($orderInfo['order_status'] != 0) {
                Log::warning("微信订单支付成功处理，订单状态已变更: {$orderInfo['order_no']}, 当前状态: {$orderInfo['order_status']}");
                return true;
            }

            // 更新订单状态
            $updateData = [
                'order_status' => 1,
                'pay_status' => 1,
                'pay_time' => date('Y-m-d H:i:s'),
                'pay_type' => $paymentInfo['trade_type'] ?? 'JSAPI',
                'pay_info' => $paymentInfo,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $wxOrderModel->where('order_no', $orderInfo['order_no'])->update($updateData);

            if (!$result) {
                Log::error("微信订单支付成功处理失败，更新订单状态失败: {$orderInfo['order_no']}");
                return false;
            }

            // 触发支付成功事件
            $eventData = [
                'order_id' => $orderInfo['order_id'],
                'order_no' => $orderInfo['order_no'],
                'order_type' => $orderInfo['order_type'],
                'transaction_id' => $paymentInfo['transaction_id'] ?? '',
                'total_amount' => $paymentInfo['total_amount'] ?? $orderInfo['pay_price'],
                'trade_state' => $paymentInfo['trade_state'] ?? 'SUCCESS',
                'trade_type' => $paymentInfo['trade_type'] ?? 'JSAPI',
                'success_time' => $paymentInfo['success_time'] ?? date('Y-m-d H:i:s'),
                'user_info' => [
                    'uid' => $orderInfo['uid'],
                    'real_name' => $orderInfo['real_name'],
                    'user_phone' => $orderInfo['user_phone'],
                    'user_address' => $orderInfo['user_address'],
                ],
                'order_info' => $orderInfo['order_info'] ?? []
            ];

            // 触发支付成功事件
            event('wx.paymentSuccess', $eventData);

            Log::info("微信订单支付成功处理完成: {$orderInfo['order_no']}, 订单类型: {$orderInfo['order_type']}");
            return true;

        } catch (Exception $e) {
            Log::error("微信订单支付成功处理异常: {$orderInfo['order_no']}, 错误: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 申请微信退款
     * @param array $refundData 退款数据
     * @return array 退款结果
     */
    public static function applyRefund(array $refundData): array
    {
        try {
            Log::info("开始申请微信退款", [
                'refund_sn' => $refundData['refund_sn'],
                'order_no' => $refundData['order_no'],
                'refund_amount' => $refundData['refund_amount']
            ]);

            // 获取微信支付配置
            $config = Config::get('wechat.pay');
            if (empty($config)) {
                throw new Exception('微信支付配置未设置');
            }

            // 创建微信支付应用
            $app = new PayApplication($config);

            // 准备退款参数
            $refundParams = [
                'out_trade_no' => $refundData['order_no'], // 商户订单号
                'out_refund_no' => $refundData['refund_sn'], // 商户退款单号
                'amount' => [
                    'refund' => (int)($refundData['refund_amount'] * 100), // 退款金额（分）
                    'total' => (int)($refundData['total_amount'] * 100), // 原订单金额（分）
                    'currency' => 'CNY'
                ],
                'reason' => $refundData['refund_reason'] ?? '用户申请退款'
            ];

            // 如果有微信订单号，优先使用微信订单号
            if (!empty($refundData['transaction_id'])) {
                $refundParams['transaction_id'] = $refundData['transaction_id'];
                unset($refundParams['out_trade_no']);
            }

            // 调用微信退款API
            $response = $app->getClient()->postJson('v3/refund/domestic/refunds', $refundParams);
            $result = $response->toArray(false);

            if (isset($result['refund_id'])) {
                Log::info("微信退款申请成功", [
                    'refund_sn' => $refundData['refund_sn'],
                    'refund_id' => $result['refund_id'],
                    'status' => $result['status']
                ]);

                return [
                    'success' => true,
                    'refund_id' => $result['refund_id'],
                    'status' => $result['status'],
                    'refund_sn' => $refundData['refund_sn'],
                    'refund_amount' => $refundData['refund_amount'],
                    'wx_response' => $result
                ];
            } else {
                throw new Exception('微信退款申请失败: ' . json_encode($result));
            }

        } catch (Exception $e) {
            Log::error("微信退款申请失败", [
                'refund_sn' => $refundData['refund_sn'] ?? '',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'refund_sn' => $refundData['refund_sn'] ?? ''
            ];
        }
    }

    /**
     * 查询退款状态
     * @param string $refundSn 商户退款单号
     * @return array 查询结果
     */
    public static function queryRefund(string $refundSn): array
    {
        try {
            Log::info("查询微信退款状态", ['refund_sn' => $refundSn]);

            // 获取微信支付配置
            $config = Config::get('wechat.pay');
            if (empty($config)) {
                throw new Exception('微信支付配置未设置');
            }

            // 创建微信支付应用
            $app = new PayApplication($config);

            // 调用微信退款查询API
            $response = $app->getClient()->get("v3/refund/domestic/refunds/{$refundSn}");
            $result = $response->toArray(false);

            Log::info("微信退款状态查询成功", [
                'refund_sn' => $refundSn,
                'status' => $result['status'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'refund_sn' => $refundSn,
                'status' => $result['status'] ?? 'unknown',
                'wx_response' => $result
            ];

        } catch (Exception $e) {
            Log::error("微信退款状态查询失败", [
                'refund_sn' => $refundSn,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'refund_sn' => $refundSn
            ];
        }
    }

    /**
     * 处理退款回调通知
     * @param array $notifyData 回调数据
     * @return bool 处理结果
     */
    public static function handleRefundNotify(array $notifyData): bool
    {
        try {
            Log::info("处理微信退款回调通知", [
                'out_refund_no' => $notifyData['out_refund_no'] ?? '',
                'refund_status' => $notifyData['refund_status'] ?? ''
            ]);

            $refundSn = $notifyData['out_refund_no'] ?? '';
            $refundStatus = $notifyData['refund_status'] ?? '';

            if (empty($refundSn)) {
                Log::error("退款回调数据缺少退款单号");
                return false;
            }

            // 更新退款记录状态
            $updateData = [
                'wx_refund_id' => $notifyData['refund_id'] ?? '',
                'refund_status' => self::mapRefundStatus($refundStatus),
                'refund_time' => date('Y-m-d H:i:s'),
                'wx_notify_data' => $notifyData,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $refundModel = new \app\wx\model\users\WxStoreOrderRefundModel();
            $result = $refundModel
                ->where('refund_sn', $refundSn)
                ->update($updateData);

            if ($result) {
                // 触发退款成功事件
                if ($refundStatus === 'SUCCESS') {
                    event('wx.refundSuccess', [
                        'refund_sn' => $refundSn,
                        'refund_id' => $notifyData['refund_id'] ?? '',
                        'refund_amount' => ($notifyData['amount']['refund'] ?? 0) / 100,
                        'refund_time' => date('Y-m-d H:i:s'),
                        'notify_data' => $notifyData
                    ]);
                }

                Log::info("退款回调处理成功", [
                    'refund_sn' => $refundSn,
                    'refund_status' => $refundStatus
                ]);

                return true;
            } else {
                Log::error("更新退款记录失败", ['refund_sn' => $refundSn]);
                return false;
            }

        } catch (Exception $e) {
            Log::error("处理退款回调异常", [
                'refund_sn' => $notifyData['out_refund_no'] ?? '',
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * 映射微信退款状态到系统状态
     * @param string $wxStatus 微信退款状态
     * @return string 系统退款状态
     */
    private static function mapRefundStatus(string $wxStatus): string
    {
        $statusMap = [
            'SUCCESS' => '已退款',
            'CLOSED' => '退款关闭',
            'PROCESSING' => '退款处理中',
            'ABNORMAL' => '退款异常'
        ];

        return $statusMap[$wxStatus] ?? '未知状态';
    }
}
