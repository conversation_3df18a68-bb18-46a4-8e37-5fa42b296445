<?php

namespace app\wx\service;

use support\Log;
use Exception;
use think\facade\Db;

/**
 * 退款计算服务
 */
class RefundCalculatorService
{
    /**
     * 计算套餐退款金额
     * @param array $originalPackages 原始套餐数据
     * @param array $refundPackages 退款套餐数据
     * @return array 计算结果
     */
    public static function calculatePackageRefund(array $originalPackages, array $refundPackages): array
    {
        try {
            Log::info("开始计算套餐退款金额", [
                'original_count' => count($originalPackages),
                'refund_count' => count($refundPackages)
            ]);
            
            $totalRefundAmount = 0;
            $deliveryFeeRefund = 0;
            $employeeDiscountRefund = 0;
            $refundDetails = [];
            $needsAudit = false; // 套餐退款不需要审核
            
            // 按日期和套餐ID分组处理
            foreach ($refundPackages as $refundDay) {
                $deliveryDate = $refundDay['time'];
                
                foreach ($refundDay['options'] as $refundOption) {
                    if (empty($refundOption['num']) || $refundOption['num'] <= 0) {
                        continue; // 跳过数量为0的退款
                    }
                    
                    // 查找对应的原始套餐记录
                    $originalRecord = self::findOriginalPackageRecord(
                        $originalPackages, 
                        $deliveryDate, 
                        $refundOption['package_id']
                    );
                    
                    if (empty($originalRecord)) {
                        throw new Exception("未找到对应的原始套餐记录: 日期={$deliveryDate}, 套餐ID={$refundOption['package_id']}");
                    }
                    
                    // 计算退款数量
                    $refundNum = min($refundOption['num'], $originalRecord['current_num']);
                    if ($refundNum <= 0) {
                        continue;
                    }
                    
                    // 计算退款金额
                    $refundAmount = $refundNum * $originalRecord['unit_price'];
                    $totalRefundAmount += $refundAmount;
                    
                    // 计算员工优惠退款
                    if ($originalRecord['is_employee'] && $originalRecord['employee_discount'] > 0) {
                        $discountRefund = $refundNum * $originalRecord['employee_discount'];
                        $employeeDiscountRefund += $discountRefund;
                    }
                    
                    // 检查是否全部退款，如果是则退配送费
                    $remainingNum = $originalRecord['current_num'] - $refundNum;
                    $deliveryFeeRefundForItem = 0;
                    if ($remainingNum == 0 && $originalRecord['delivery_fee'] > 0) {
                        // 检查当天是否还有其他套餐
                        $otherPackagesOnSameDay = self::getOtherPackagesOnSameDay(
                            $originalPackages, 
                            $deliveryDate, 
                            $refundOption['package_id']
                        );
                        
                        if (empty($otherPackagesOnSameDay)) {
                            $deliveryFeeRefundForItem = $originalRecord['delivery_fee'];
                            $deliveryFeeRefund += $deliveryFeeRefundForItem;
                        }
                    }
                    
                    // 套餐退款不需要审核
                    // $needsAudit 保持为 false
                    
                    // 记录退款详情
                    $refundDetails[] = [
                        'delivery_date' => $deliveryDate,
                        'package_id' => $refundOption['package_id'],
                        'package_name' => $originalRecord['package_name'],
                        'original_num' => $originalRecord['current_num'],
                        'refund_num' => $refundNum,
                        'remaining_num' => $remainingNum,
                        'unit_price' => $originalRecord['unit_price'],
                        'refund_amount' => $refundAmount,
                        'delivery_fee_refund' => $deliveryFeeRefundForItem,
                        'employee_discount_refund' => $originalRecord['is_employee'] ? ($refundNum * $originalRecord['employee_discount']) : 0,
                        'needs_audit' => false, // 套餐退款不需要审核
                        'wx_store_order_package_id' => $originalRecord['id']
                    ];
                }
            }
            
            $result = [
                'total_refund_amount' => round($totalRefundAmount, 2),
                'delivery_fee_refund' => round($deliveryFeeRefund, 2),
                'employee_discount_refund' => round($employeeDiscountRefund, 2),
                'final_refund_amount' => round($totalRefundAmount + $deliveryFeeRefund, 2),
                'needs_audit' => false, // 套餐退款不需要审核
                'refund_details' => $refundDetails,
                'summary' => [
                    'total_items' => count($refundDetails),
                    'total_refund_num' => array_sum(array_column($refundDetails, 'refund_num')),
                    'audit_required_items' => count(array_filter($refundDetails, function($item) {
                        return $item['needs_audit'];
                    }))
                ]
            ];
            
            Log::info("套餐退款计算完成", [
                'total_refund_amount' => $result['total_refund_amount'],
                'delivery_fee_refund' => $result['delivery_fee_refund'],
                'final_refund_amount' => $result['final_refund_amount'],
                'needs_audit' => $result['needs_audit'],
                'items_count' => count($refundDetails)
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            Log::error("套餐退款计算失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }
    
    /**
     * 检查退款是否需要审核
     * @param array $refundData 退款数据
     * @return bool 是否需要审核
     */
    public static function needsAudit(array $refundData): bool
    {
        $auditEnabled = config('package_order.refund_audit.enabled', true);
        if (!$auditEnabled) {
            return false;
        }
        
        foreach ($refundData as $refundDay) {
            if (self::needsAuditForDate($refundDay['time'])) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 计算员工优惠退款
     * @param array $refundData 退款数据
     * @param bool $isEmployee 是否员工订单
     * @return float 优惠退款金额
     */
    public static function calculateEmployeeRefund(array $refundData, bool $isEmployee): float
    {
        if (!$isEmployee) {
            return 0;
        }
        
        $discountConfig = config('package_order.employee_discount');
        if (!$discountConfig['enabled']) {
            return 0;
        }
        
        $discountAmount = $discountConfig['discount_amount'];
        $applicablePackages = $discountConfig['applicable_packages'];
        $totalDiscount = 0;
        
        foreach ($refundData as $refundDay) {
            foreach ($refundDay['options'] as $option) {
                if (in_array($option['name'], $applicablePackages) && $option['num'] > 0) {
                    $totalDiscount += $option['num'] * $discountAmount;
                }
            }
        }
        
        return round($totalDiscount, 2);
    }
    
    /**
     * 计算配送费退款
     * @param array $refundData 退款数据
     * @return float 配送费退款金额
     */
    public static function calculateDeliveryFeeRefund(array $refundData): float
    {
        $totalDeliveryFeeRefund = 0;
        
        foreach ($refundData as $refundDay) {
            $deliveryFee = $refundDay['fee'] ?? 0;
            if ($deliveryFee > 0) {
                // 检查当天是否全部退款
                $hasRemainingPackages = false;
                foreach ($refundDay['options'] as $option) {
                    if ($option['num'] > 0) {
                        // 这里需要检查是否还有剩余数量
                        // 暂时简化处理，如果有退款就不退配送费
                        $hasRemainingPackages = true;
                        break;
                    }
                }
                
                if (!$hasRemainingPackages) {
                    $totalDeliveryFeeRefund += $deliveryFee;
                }
            }
        }
        
        return round($totalDeliveryFeeRefund, 2);
    }
    
    /**
     * 查找原始套餐记录
     * @param array $originalPackages 原始套餐数据
     * @param string $deliveryDate 配送日期
     * @param int $packageId 套餐ID
     * @return array|null 原始记录
     */
    private static function findOriginalPackageRecord(array $originalPackages, string $deliveryDate, int $packageId): ?array
    {
        foreach ($originalPackages as $record) {
            if ($record['delivery_date'] == $deliveryDate && $record['package_id'] == $packageId) {
                return $record;
            }
        }
        
        return null;
    }
    
    /**
     * 获取同一天的其他套餐
     * @param array $originalPackages 原始套餐数据
     * @param string $deliveryDate 配送日期
     * @param int $excludePackageId 排除的套餐ID
     * @return array 其他套餐
     */
    private static function getOtherPackagesOnSameDay(array $originalPackages, string $deliveryDate, int $excludePackageId): array
    {
        $otherPackages = [];
        
        foreach ($originalPackages as $record) {
            if ($record['delivery_date'] == $deliveryDate && 
                $record['package_id'] != $excludePackageId && 
                $record['current_num'] > 0) {
                $otherPackages[] = $record;
            }
        }
        
        return $otherPackages;
    }
    
    /**
     * 检查指定日期是否需要审核
     * @param string $deliveryDate 配送日期
     * @return bool 是否需要审核
     */
    private static function needsAuditForDate(string $deliveryDate): bool
    {
        $auditDaysAhead = config('package_order.refund_audit.audit_days_ahead', 1);
        $auditDate = date('Y-m-d', strtotime("+{$auditDaysAhead} day"));
        
        return $deliveryDate >= $auditDate;
    }
}
