<?php
/**
 * Here is your custom functions.
 */

/**
 * post请求方法
 * @param $url
 * @param array|string $data
 * @param array $headerArray
 * @return array
 */
function httpPost($url, array|string $data = [],array $headerArray = [
    "Content-type:application/json;charset='utf-8'",
    "Accept:application/json"
]): array
{
    $data = json_encode($data);
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_POST, 1);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headerArray);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_ENCODING, '');
    curl_setopt($curl, CURLOPT_MAXREDIRS, 10);
    curl_setopt($curl, CURLOPT_TIMEOUT, 0);
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($curl, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
    $output = curl_exec($curl);
    curl_close($curl);
    if(!$output) {
        return [
            'success' => false,
            'resultMessage' => '请求返回空！'
        ];
    }
    return json_decode($output, true);
}

/**
 * 发送通知
 * @param $text
 * @param string $des
 * @return void
 */
function sendNotice($text, string $des = ''): void
{
    $params = [
        "touser"=>'ow-9xxNhy14ah2FBJUhZeXqCiJRA',
        "template_id"=>'4tUvGtTVHNsXOt3PaFlUzRi-p0LkIOTf2c7ZT7NGiCw',
        "url"=>'https://img.zkshlm.com/zksh/error.html?title='.$text.'&content='.$des,
        "data"=>[
            "thing2"=>[
                "value"=>'服务异常',
            ],
            "thing5"=>[
                "value"=>'养老管理系统V2',
            ],
            "time3"=>[
                "value"=>date('Y-m-d H:i:s'),
            ],
        ],
    ];
    $code = 'dsd45d4a6d78w7d8ad749a7d7af4a4ga54f6as4dsa4g41a51sd2adw4d8s8cx4512c31g45';
    httpPost('https://api.card.zkshlm.com/notice/reserveSend',[
        'data'=>$params,
        'code'=>$code
    ]);
}

/**
 * 事件异常通知
 * @param Throwable $e
 * @return void
 */
function eventExceptionNotice(Throwable $e): void
{
    $message = ' --- '  . " %0A";
    $message .= " - 请求IP：" . request()->getRemoteIp()  . " %0A";
    $message .= ' - 请求参数：' . json_encode(request()->all())  . " %0A";
    $message .= ' - 异常消息：' . $e->getMessage() . " %0A";
    $message .= ' - 异常文件：' . $e->getFile()  . " %0A";
    $message .= ' - 异常文件行数：' . $e->getLine()  . " %0A";
    $message .= ' - 访问时间：' . date('Y-m-d H:i:s')  . " %0A";
    $data = [
        'title' => config('app.name') .' '. $e->getMessage(),
        'des' => $message
    ];
    sendNotice($data['title'], $data['des']);
}

/**
 * 创建文件
 * @param string $filePath
 * @param $content
 */
function createFileWithDirectories(string $filePath, $content): void
{
    // 获取文件所在的目录路径
    $directory = dirname($filePath);

    // 递归创建目录
    if (!is_dir($directory) && !mkdir($directory, 0755, true)) {
        error_log("目录创建失败：$directory");
    }

    // 检查文件是否存在，如果不存在则创建文件并写入内容
    if (!file_exists($filePath)) {
        if (file_put_contents($filePath, $content) === false) {
            error_log("文件创建失败：$filePath");
        }
    } else {
        // 文件已经存在，写入内容
        if (file_put_contents($filePath, $content) === false) {
            error_log("文件写入失败：$filePath");
        }
    }
    error_log("文件创建成功：$filePath");
}

/**
 * 写入内容到文件末尾，如果内容未写入则追加
 *
 * @param string $filePath 文件路径
 * @param string $content 写入的内容
 * @return bool 内容是否写入成功
 */
function appendIfNotWritten(string $filePath, string $content): bool
{
    // 检查文件是否存在并可读
    if (!file_exists($filePath) || !is_readable($filePath)) {
        error_log("文件不存在或不可读：$filePath");
        return false; // 文件不存在或不可读
    }

    // 读取文件内容
    $fileContent = file_get_contents($filePath);

    if ($fileContent === false) {
        error_log("无法读取文件内容：$filePath");
        return false; // 读取文件内容失败
    }

    // 检查文件内容是否包含要追加的内容
    if (!str_contains($fileContent, $content)) {
        // 尝试追加内容到文件末尾
        $result = file_put_contents($filePath, $content, FILE_APPEND);

        if ($result !== false) {
            error_log("内容写入成功：$filePath");
            return true; // 写入成功
        } else {
            error_log("内容写入失败：$filePath");
            return false; // 写入失败
        }
    } else {
        error_log("内容已存在，未重复写入：$filePath");
        return false; // 内容已写入
    }
}

/**
 * 从字符串中截取指定输入内容之后的所有内容
 *
 * @param string $input 要查找的输入内容
 * @param string $string 被搜索的字符串
 * @return false|string 如果找到输入内容则返回其后的子字符串，否则返回 false
 */
function extractContentAfterInput(string $input, string $string): false|string
{
    // 查找输入内容在字符串中的位置
    $position = strpos($string, $input);

    // 如果输入内容存在于字符串中，则截取其之后的内容
    if ($position !== false) {
        return substr($string, $position + strlen($input));
    }

    // 如果输入内容不存在，则返回 false
    return false;
}

/**
 * 读取配置项值
 * @param string $key
 * @return string
 */
function getConfigValue(string $key): string
{
    $dao = support\Container::get(app\utils\Dao::class);
    return $dao->get('eb_system_config', ['key' => $key], 'value');
}

/**
 * 使用雪花算法生成ID号
 */
function getId(): float|int|string
{
    $snowflake = new \Godruoyi\Snowflake\Snowflake;
    return $snowflake->id();
}

/**
 * 模型内统一数据返回
 * @param $code
 * @param string $msg
 * @param array $data
 * @return array
 */
if (!function_exists('dataReturn')) {
    function dataReturn($code, $msg = 'success', $data = []): array
    {
        return ['code' => $code, 'data' => $data, 'message' => $msg];
    }
}

/**
 * 记录日志
 * @return array
 */
if (!function_exists('setRecord')) {
    function setRecord($tableName, $tableId, $message): array
    {
        $lock = yzh52521\WebmanLock\Locker::lock($tableName . $tableId);
        if (!$lock->acquire()) {
            return [];
        }
        try {
            $database = Webman\Medoo\Medoo::instance('default');
            $database->insert('sys_table_log', [
                'table_name' => $tableName,
                'table_id' => $tableId,
                'change_message' => $message,
                'change_time' => date('Y-m-d H:i:s')
            ]);
        } finally {
            $lock->release();
        }
        return [];
    }
}

/**
 * 查询字典
 */
if (!function_exists('getDictValue')) {
    function getDictValue($code,$label): string|null
    {
        $name = 'dict_'.$code.'_'.$label;
        $data = plugin\saiadmin\utils\Cache::get($name);
        if ($data) {
            return $data;
        }
        $model = new plugin\saiadmin\app\model\system\SystemDictData();
        $data = $model->where('label', $label)->where('code', $code)->value('value');
        plugin\saiadmin\utils\Cache::set($name, $data);
        return $data;
    }
}

/**
 * 查询字典
 */
if (!function_exists('getDictLabel')) {
    function getDictLabel($code,$value): string|null
    {
        $name = 'dict_'.$code.'_'.$value;
        $data = plugin\saiadmin\utils\Cache::get($name);
        if ($data) {
            return $data;
        }
        $model = new plugin\saiadmin\app\model\system\SystemDictData();
        $data = $model->where('value',$value)->where('code', $code)->value('label');
        plugin\saiadmin\utils\Cache::set($name, $data);
        return $data;
    }
}

/**
 * 比较两个一维数组中相同键值的值的变化
 *
 * @param array $oldArray 旧数组
 * @param array $newArray 新数组
 * @return array 包含变化结果的数组
 */
function compareArrayChanges(array $oldArray, array $newArray): array
{
    $changes = [];
    // 遍历新数组的键
    foreach (array_keys($newArray) as $key) {
        // 如果键在旧数组中存在
        // 比较值是否发生变化
        if (array_key_exists($key, $oldArray) && $oldArray[$key] !== $newArray[$key]) {
            // 记录变化
            $changes[] = [
                'key' => $key,          // 发生变化的键名
                'old_value' => $oldArray[$key], // 旧值
                'new_value' => $newArray[$key], // 新值
            ];
        }
    }
    return $changes;
}


/**
 * PHP导入Excel后，时间转日期方法
 *
 * @param string $excelDateNumber
 */
function convertExcelDateToPHPDate($excelDateNumber)
{
    if(empty($excelDateNumber) || is_string($excelDateNumber)) {
        return $excelDateNumber;
    }
    $excelEpoch = 25569;
    $secondsInADay = 86400;
    $phpTimestamp = ($excelDateNumber - $excelEpoch) * $secondsInADay;
    return date('Y-m-d H:i:s', $phpTimestamp);
}


/**
 * 验证内置函数
 *
 * @param string $excelDateNumber
 */
if (!function_exists('validate')) {
    /**
     * 生成验证对象
     * @param string $validate 验证器类名或者验证规则数组
     * @param array $message 错误提示信息
     * @param bool $batch 是否批量验证
     * @param bool $failException 是否抛出异常
     */
    function validate($validate = '', array $message = [], bool $batch = false, bool $failException = true)
    {
        if (is_array($validate) || '' === $validate) {
            $v = new think\Validate();
            if (is_array($validate)) {
                $v->rule($validate);
            }
        } else {
            if (strpos($validate, '.')) {
                // 支持场景
                [$validate, $scene] = explode('.', $validate);
            }

            $class = false !== strpos($validate, '\\') ? $validate : $validate;

            $v = new $class();

            if (!empty($scene)) {
                $v->scene($scene);
            }
        }

        return $v->message($message)->batch($batch)->failException($failException);
    }
}

/**
 * 事件助手函数
 * @param string $name
 * @param $data
 */
function event(string $name, $data): void
{
    \Webman\Event\Event::emit($name, $data);
}

/**
 * 计算两个日期之间的时间差
 * @throws Exception
 */
if (!function_exists('dateDiff')) {
    /**
     * @throws Exception
     */
    function dateDiff($date1, $date2): int
    {
        $datetime1 = new DateTime($date1);
        $datetime2 = new DateTime($date2);
        $interval = $datetime1->diff($datetime2);
        return $interval->format('%a');
    }
}

/**
 * 获取redis缓存
 * @param $key
 * @param $callback
 * @param int $expire
 * @return mixed|null
 */
function getRedisCache($key, $callback, int $expire = 0): mixed
{
    return \Cgophp\WebmanRedisCache\RedisCache::get($key, $callback, $expire);
}