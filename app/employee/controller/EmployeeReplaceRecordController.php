<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\controller;

use plugin\saiadmin\basic\BaseController;
use app\employee\logic\EmployeeReplaceRecordLogic;
use app\employee\validate\EmployeeReplaceRecordValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;

/**
 * @Apidoc\Title("员工换人记录")
 */
class EmployeeReplaceRecordController extends BaseController
{
    protected string $pk = "replace_record_id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new EmployeeReplaceRecordLogic();
        $this->validate = new EmployeeReplaceRecordValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("replace_classify", type="varchar", require=false, desc="换人分类", default="contract_management")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['replace_classify', 'contract_management'],
            ['link_id', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("replace_classify", type="varchar", require=false, desc="换人分类", default="contract_management")
     * @Apidoc\Query("link_id", type="bigint", require=false, desc="关联id", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="原服务员", default="")
     * @Apidoc\Query("salary_payment_date", type="int", require=false, desc="新工资发放日", default="")
     * @Apidoc\Query("working_days", type="int", require=false, desc="月工作天数", default="")
     * @Apidoc\Query("working_hour", type="int", require=false, desc="日工作小时", default="")
     * @Apidoc\Query("employee_service_fee", type="int", require=false, desc="小时工单价", default="")
     * @Apidoc\Query("employee_salary", type="decimal", require=false, desc="服务员工资", default="")
     * @Apidoc\Query("working_start", type="date", require=false, desc="工作开始时间", default="")
     * @Apidoc\Query("working_end", type="date", require=false, desc="工作结束时间", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            // var_dump('data-=-=-=---------------',$data);
            if (!empty($data['replace_classify']) && $data['replace_classify'] == 'contract_management') {
                // 更换对应合同的服务人员
                // var_dump('data-=-=-=----------1111-----',$data);
                $data1  = [
                    'employee_uid' => $data['employee_uid'],
                    'employee_salary' => $data['employee_salary'],
                    'working_days' => $data['working_days'],
                    'working_hour' => $data['working_hour'],
                    'employee_service_fee' => $data['employee_service_fee'],
                    'salary_payment_date' => $data['salary_payment_date'],
                    'update_time' => date('Y-m-d H:i:s')
                ];
                // var_dump('data1-=-=-=---------------',$data1);
                $this->dao->update('contract_management', $data1 , [ 'contract_id' => $data['link_id'] ]);
            }
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("replace_classify", type="varchar", require=false, desc="换人分类", default="contract_management")
     * @Apidoc\Query("link_id", type="bigint", require=false, desc="关联id", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="原服务员", default="")
     * @Apidoc\Query("salary_payment_date", type="int", require=false, desc="新工资发放日", default="")
     * @Apidoc\Query("working_days", type="int", require=false, desc="月工作天数", default="")
     * @Apidoc\Query("working_hour", type="int", require=false, desc="日工作小时", default="")
     * @Apidoc\Query("employee_service_fee", type="int", require=false, desc="小时工单价", default="")
     * @Apidoc\Query("employee_salary", type="decimal", require=false, desc="服务员工资", default="")
     * @Apidoc\Query("working_start", type="date", require=false, desc="工作开始时间", default="")
     * @Apidoc\Query("working_end", type="date", require=false, desc="工作结束时间", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/employee/EmployeeReplaceRecord/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

}
