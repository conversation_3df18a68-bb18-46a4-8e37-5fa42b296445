<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\controller;

use stdClass;
use DateTime;
use app\employee\logic\EmployeeSalaryLogic;
use plugin\saiadmin\basic\BaseController;
use app\employee\logic\EmployeeSalaryItemLogic;
use app\employee\validate\EmployeeSalaryItemValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use Tinywan\ExceptionHandler\Exception\BadRequestHttpException;
use app\utils\Excel;
use Webman\Medoo\Medoo;

/**
 * @Apidoc\Title("工资填报")
 */
class EmployeeSalaryItemController extends BaseController
{
    protected string $pk = "employee_salary_item_id";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new EmployeeSalaryItemLogic();
        $this->validate = new EmployeeSalaryItemValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户编号", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="员工编号", default="")
     * @Apidoc\Query("salary_month", type="date", require=false, desc="工资填报月份", default="")
     * @Apidoc\Query("salary_status", type="varchar", require=false, desc="填报状态", default="")
     * @Apidoc\Query("salary_payment_date", type="int", require=false, desc="工资发放日", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['cust_uid', ''],
            ['cust_name', ''],
            ['employee_uid', ''],
            ['salary_month', ''],
            ['salary_status', ''],
            ['salary_classify', 'contract_management'],
            ['link_id', ''],
            ['cust_consultant', ''],
            ['sales_attribution_uid', ''],
            ['salary_payment_date', ''],
            ['cust_type', '家床'],
            ['services_type', ''],
        ]);
        $query = $this->logic->search($where)->with(['custInfo','employeeInfo']);
        $data = $this->logic->getList($query);
        
        $data1 = $data['data'];
        if (!empty($data1)) {
            foreach ($data1 as &$v) {
                // var_dump('data1-$v=-=-=---------------',$v);
                // 上一月
                // $month = (new DateTime($v['salary_month']))->modify('-1 month')->format('Y-m');
                // // var_dump('month=-=-=---------------',$month,$v['salary_month']);

                // // 查询用户信息
                // $subsidyInfo = $this->dao->get('cust_subsidy_bill',[ 'cust_uid' => $v['cust_uid'],'month' => $month ], ['month', 'cust_uid', 'real_amount']);
                // // var_dump('data1-=-=-=---subsidyInfo------',$subsidyInfo);
                // // // 没有查询到返回 {}
                // // $v['subsidyInfo'] = $subsidyInfo ??  new stdClass();

                // if(!empty($subsidyInfo)){
                //     $v['real_amount'] = $subsidyInfo['real_amount'];
                // }

                if(!empty($v["custInfo"])){
                    $v['cust_evaluation_level']  = $v["custInfo"]['cust_evaluation_level'];
                }
            }
            unset($v); // 释放引用，避免后续影响
        }
        $data['data'] = $data1;

        return $this->success($data);
    }

     /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/salarySave")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户编号", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="员工编号", default="")
     * @Apidoc\Query("salary_classify", type="varchar", require=false, desc="工资分类", default="")
     * @Apidoc\Query("link_id", type="bigint", require=false, desc="关联id", default="")
     * @Apidoc\Query("salary_month", type="date", require=false, desc="工资月份", default="")
     * @Apidoc\Query("value_added_expenses", type="decimal", require=false, desc="增值费用", default="")
     * @Apidoc\Query("employee_salary", type="decimal", require=false, desc="固定工资", default="")
     * @Apidoc\Query("working_days", type="int", require=false, desc="月工作天数(基数)", default="")
     * @Apidoc\Query("actual_hour", type="int", require=false, desc="实际工时(天)", default="")
     * @Apidoc\Query("salary_payment_date", type="int", require=false, desc="工资发放日", default="")
     * @Apidoc\Query("cust_consultant", type="varchar", require=false, desc="养老顾问(老人档案)", default="")
     * @Apidoc\Query("sales_attribution_uid", type="varchar", require=false, desc="派工人员(合同)", default="")
     * @param Request $request
     * @return Response
     */
    public function salarySave(Request $request) : Response
    {
        // var_dump('salarySave=-=-=---------------');
        $data = $request->post();

        if(!empty($data)){
            $working_days = $data['working_days'] ?? 0; // 基数
            $actual_hour = $data['actual_hour'] ?? 0; // 实际工时
            $employee_salary = $data['employee_salary'] ?? 0; // 固定工资
            $value_added_expenses = $data['value_added_expenses'] ?? 0; // 增值金额

            $absenteeism_salary = 0; // 缺勤扣除
            $overtime_salary = 0; // 加班金额
            // var_dump('overtime_salary=-=-=---------------',$overtime_salary);

            if ($actual_hour < $working_days) {
                // 当实际工时＜基数时，缺勤扣除公式为：固定工资/基数*（实际天数-基数），展示负数，加班金额为0
                $absenteeism_salary = round($employee_salary / $working_days * ($actual_hour - $working_days));
            } else if ($actual_hour > $working_days) {
                // 当实际工时＞基数时，加班金额公式为：固定工资/基数*（实际天数-基数），展示正数，缺勤扣除为0
                $overtime_salary = round($employee_salary / $working_days * ($actual_hour - $working_days));
            } else {
                // 当实际工时等于基数时，缺勤和加班均展示0;
            }
            // 实发金额  固定工资+缺勤金额+加班金额
            $issued_salary = round(($employee_salary ?? 0 ) + ($overtime_salary ?? 0) + ($absenteeism_salary ?? 0));
            // var_dump('employee_salary=-=-=---------------',$employee_salary);
            // var_dump('overtime_salary=-=-=---------------',$overtime_salary);
            // var_dump('absenteeism_salary=-=-=---------------',$absenteeism_salary);
            // var_dump('issued_salary=-=-=---------------',$issued_salary);
           
            // 上一月
            $month = (new DateTime($data['salary_month']))->modify('-1 month')->format('Y-m');
            // var_dump('month=-=-=---------------',$month,$data['salary_month']);
            // 查询用户信息
            $subsidyInfo = $this->dao->get('cust_subsidy_bill',[ 'cust_uid' => $data['cust_uid'],'month' => $month ], ['month', 'cust_uid', 'real_amount']);
            // var_dump('data1-=-=-=---subsidyInfo------',$subsidyInfo);
            // 上月补贴
            $real_amount = !empty($subsidyInfo)?$subsidyInfo['real_amount']:0;

            $cust_name = $data['cust_name'] ?? '';
            // 有关联老人
            if(!empty($data['cust_uid1'])){
                $subsidyInfo1 = $this->dao->get('cust_subsidy_bill',[ 'cust_uid' => $data['cust_uid1'],'month' => $month ], ['month', 'cust_uid', 'real_amount']);
                // var_dump('data1-=-=-=---subsidyInfo1------',$subsidyInfo1);
                $real_amount += !empty($subsidyInfo1) && isset($subsidyInfo1['real_amount']) ? $subsidyInfo1['real_amount'] : 0;
                // var_dump('data1-=-=-=---real_amount------',$real_amount);
                $cust_name = $cust_name . (isset($data['cust_name1']) ? ('、' . $data['cust_name1']) : '');
            }
            // return "";

            // 应收金额  实发金额+增值金额-补贴金额
            $receivable_amount = round($issued_salary ?? 0) + round($value_added_expenses ?? 0) - round($real_amount ?? 0);
            // var_dump('receivable_amount-=-=-=---------',$receivable_amount);
            
            $EmployeeSalary = new EmployeeSalaryLogic();
            // 获取新建员工工资表的id
            $employee_salary_id = $EmployeeSalary->insertGetId([
                'employee_uid' =>$data['employee_uid'] ?? '',// 服务人员id
                'salary_month' =>$data['salary_month'] ?? '',// 工资月份
                'employee_salary' =>$employee_salary,// 固定工资
                'absenteeism_salary' => $absenteeism_salary,// 缺勤扣除
                'overtime_salary' => $overtime_salary,// 加班工资
                'issued_salary' =>$issued_salary,// 实发工资
                'created_by' => $request->adminId,
                'create_time' => date('Y-m-d H:i:s')
            ]);
            // var_dump('employee_salary_id-=-=-=---------',$employee_salary_id);

            // 生成工资填报附表
            $data1 = [
                'employee_salary_id' => $employee_salary_id,// 工资主表id
                'cust_uid' => empty($data['cust_uid1'])?($data['cust_uid'] ?? ''):'',// 客户编号
                'cust_name' => $cust_name ?? '',// 客户姓名
                'employee_uid' => $data['employee_uid'] ?? '',// 服务人员id
                'salary_classify' => $data['salary_classify'] ?? 'contract_management',// 工资分类
                'link_id' => $data['link_id'] ?? '',// 关联id
                'cust_type' => $data['cust_type'] ?? '',// 客户类型
                'services_type' => $data['services_type'],// 服务项目

                'salary_month' => $data['salary_month'] ?? '',// 工资填报月份
                'real_amount' => $real_amount,// 上月补贴
                'value_added_expenses' => $value_added_expenses,// 增值费用
                'receivable_amount' => $receivable_amount,// 应收金额
                'working_days' => $working_days,// 基数
                'actual_hour' => $actual_hour,// 实际工时
                'issued_salary' => $issued_salary,// 实发工资

                'employee_salary' => $employee_salary,// 固定工资
                'absenteeism_salary' => $absenteeism_salary,// 缺勤扣除
                'overtime_salary' => $overtime_salary,// 加班工资

                'salary_payment_date' => $data['salary_payment_date'] ?? 5,// 工资发放日
                'cust_consultant' => $data['cust_consultant'] ?? '',// 养老顾问(老人档案)
                'sales_attribution_uid' => $data['sales_attribution_uid'] ?? '',// 派工人员(合同)

                'created_by' => $request->adminId,
                'create_time' => date('Y-m-d H:i:s')
            ];
            // var_dump('data1-=-=-=---------',$data1);
            $result = $this->logic->save($data1);
            // var_dump('result-=-=-=---------',$result);
            if ($result) {
                return $this->success('添加成功');
            } else {
                return $this->fail('操作失败');
            }
        }
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/salarySave1")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户编号", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="员工编号", default="")
     * @Apidoc\Query("salary_classify", type="varchar", require=false, desc="工资分类", default="")
     * @Apidoc\Query("link_id", type="bigint", require=false, desc="关联id", default="")
     * @Apidoc\Query("salary_month", type="date", require=false, desc="工资月份", default="")
     * @Apidoc\Query("value_added_expenses", type="decimal", require=false, desc="增值费用", default="")
     * @Apidoc\Query("employee_salary", type="decimal", require=false, desc="固定工资", default="")
     * @Apidoc\Query("working_days", type="int", require=false, desc="月工作天数(基数)", default="")
     * @Apidoc\Query("actual_hour", type="int", require=false, desc="实际工时(天)", default="")
     * @Apidoc\Query("salary_payment_date", type="int", require=false, desc="工资发放日", default="")
     * @Apidoc\Query("cust_consultant", type="varchar", require=false, desc="养老顾问(老人档案)", default="")
     * @Apidoc\Query("sales_attribution_uid", type="varchar", require=false, desc="派工人员(合同)", default="")
     * @param Request $request
     * @return Response
     */
    public function salarySave1(Request $request) : Response
    {
        // var_dump('salarySave1=-=-=---------------');
        $data = $request->post();
        if(!empty($data)){
            // 获生成数据
            $datas = $this->getSalaryData($data,$request->adminId);
            $data1 = $datas['data1'];
            $data2 = $datas['data2'];
            if(!empty($data1)){
                $EmployeeSalary = new EmployeeSalaryLogic();

                // 获取新建员工工资表的id
                $employee_salary_id = $EmployeeSalary->insertGetId($data1);
                if(!empty($employee_salary_id)&&!empty($data2)){
                    // 生成工资填报附表
                    $data2['employee_salary_id'] =  $employee_salary_id;
                    // var_dump('data2-=-=-=---------',$data2);
                    $result = $this->logic->save($data2);
                    // var_dump('result-=-=-=---------',$result);
                    if ($result) {
                        return $this->success('添加成功');
                    } else {
                        return $this->fail('操作失败');
                    }
                }
            }
        }
    } 
    
    /**
     * @Apidoc\Title("生成当月所有居家照护合同对应的工资填报")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/salarySaveAll")
     * @Apidoc\Method("GET")
     * @return Response
     */
    public function salarySaveAll() : Response
    {
        // 获取到当月第一天
        $end = date('Y-m') . '-01';
        // var_dump('salarySaveAll=-=-=----------end-----',$end );
        // 查询居家照护合同
        $contract = $this->dao->search('contract_management',[
            // 'cust_uid' => [1396,1349,1654,4733], 
            // 'contract_type' => 12, 
            'contract_type' => [10 , 65], 
            // 'contract_status' => '进行中', 
            'contract_period_end[>=]' => $end,
            'delete_time' => null,
            'ORDER' => ['create_time' => 'DESC']
        ],[ 'contract_id',  'cust_uid', 'employee_uid', 'employee_salary', 'working_days', 'working_hour', 'employee_service_fee', 
            'salary_payment_date', 'value_added_expenses', 'sales_attribution_uid', 'service_address', 'cust_contract_classification', 'contract_type','create_time','contract_status']);
        // var_dump('contract=-=-=---------------',$contract );

        if(!empty($contract)){
            $arr = [];
            $seen_uids = []; // 辅助数组，用于存储已经添加到 $arr 中的 $item 的 cust_uid
            $groupedByAddress = []; // 用于按 address 分组

            // 按 'service_address' 字段对 $contract 进行分组
            foreach ($contract as $item) {
                // 是否第一次   每个人仅以最新创建的合同生成工资填报
                if(!in_array($item['cust_uid'], $seen_uids)){
                    // 辅助数组添加用户id
                    $seen_uids[] = $item['cust_uid'];

                    // 转换养老管家
                    $item['sales_attribution_uid'] = $this->dao->get('eb_system_user',[ 'nickname' => $item['sales_attribution_uid'],'delete_time' => null,],'id');
                    // 客户类型  
                    $item['cust_type'] =  $item['cust_contract_classification'] == '家庭床位合同' ? '家床' : '非家床';
                    $groupedByAddress[$item['service_address']][] = $item;
                }
            }
            // var_dump('groupedByAddress=-=-=---------------',$groupedByAddress);
            // return '';
            $EmployeeSalary = new EmployeeSalaryLogic();

            // 遍历每个地址分组，并检查是否满足条件
            foreach ($groupedByAddress as $address => $itemsInGroup) {
                // var_dump('$address => $itemsInGroup=-=-=------------1---' );

                // 如果当前地址组的元素少于 2 个，则不可能形成满足条件的元素对，直接跳过
                if (count($itemsInGroup) < 2) {
                    $vv = $itemsInGroup[0];
                    // var_dump('count > 2   =-=-=---------------');
                    // 获取档案的养老顾问
                    $user = $this->dao->get('cust_user',[ 'cust_uid' => $vv['cust_uid'],'delete_time' => null,],['cust_name','cust_consultant']);
                    // var_dump('count > 2   =-=-=-----$user----------' ,$user);
                    
                    // 生成工资填报
                    $data = [
                        'cust_uid'=>$vv['cust_uid'],
                        'employee_uid'=>$vv['employee_uid'],
                        'salary_classify'=>'contract_management',
                        'link_id'=>$vv['contract_id'],
                        'cust_type' => $vv['cust_type'] ?? '',
                        'services_type' => $vv['contract_type'],
                        'salary_month' => date('Y-m'),
                        'value_added_expenses'=>$vv['value_added_expenses']?? 0,
                        'employee_salary'=>$vv['employee_salary']?? 0,
                        'working_days'=>$vv['working_days']?? 0,
                        'actual_hour'=>$vv['working_days'] ?? 0,
                        'salary_payment_date'=>$vv['salary_payment_date'] ?? 5,
                        'cust_name'=> $user['cust_name'] ?? '',
                        'cust_consultant'=> $user['cust_consultant'] ?? '',
                        'sales_attribution_uid'=>$vv['sales_attribution_uid']
                    ];
                    // var_dump('$data=-=-=------------1---',$data );
                    // 获生成数据
                    $datas = $this->getSalaryData($data,1);
                    $data1 = $datas['data1'];
                    $data2 = $datas['data2'];
                    if(!empty($data1)){
                        // 获取新建员工工资表的id
                        $employee_salary_id = $EmployeeSalary->insertGetId($data1);
                        // var_dump('$data=-=-=------------22---',$employee_salary_id);
                        if(!empty($employee_salary_id)&&!empty($data2)){
                            // 生成工资填报附表
                            $data2['employee_salary_id'] =  $employee_salary_id;
                            // var_dump('data2-=-=-=---------',$data2);
                            $result = $this->logic->insertGetId($data2);
                            // var_dump('result-=-=-=----22-----',$result);
                        }
                    }
                    
                    continue;
                }
                
                // 标志，表示当前地址组中是否存在满足 'cust_uid'不同 'employee_uid'相同的元素对
                $hasQualifyingPair = false;

                // 遍历组内元素，查找是否存在满足条件的元素对
                foreach ($itemsInGroup as &$v) {
                    foreach ($itemsInGroup as $v1) {
                        // 检查地址相同（已通过分组保证），cust_uid 不同，employee_uid 相同
                        if ($v['cust_uid'] != $v1['cust_uid'] && $v['employee_uid'] == $v1['employee_uid']) {

                            $v['cust_uid1'] = $v1['cust_uid'];// 添加关联老人
                            $hasQualifyingPair = true;
                            // break 2; // 找到一对即可，跳出内外两层循环，进入下一个地址组的判断
                        }
                    }
                    // var_dump('foreach itemsInGroup=-=-=---------------',$v);

                    // 获取档案的养老顾问
                    $user =  $this->dao->get('cust_user',[ 'cust_uid' => $v['cust_uid'],'delete_time' => null,],['cust_name','cust_consultant']);
                    // var_dump('foreach user=-=-=---------------',$user);

                    $data = [
                        'cust_uid'=>$v['cust_uid'],
                        'employee_uid'=>$v['employee_uid'],
                        'salary_classify'=>'contract_management',
                        'link_id'=>$v['contract_id'],
                        'cust_type' => $v['cust_type'] ?? '',
                        'services_type' => $v['contract_type'],
                        'salary_month' => date('Y-m'),
                        'value_added_expenses'=>$v['value_added_expenses'] ?? 0,
                        'employee_salary'=>$v['employee_salary'] ?? 0,
                        'working_days'=>$v['working_days'] ?? 0,
                        'actual_hour'=>$v['working_days'] ?? 0,
                        'salary_payment_date'=>$v['salary_payment_date'] ?? 5,
                        'cust_name'=> $user['cust_name'] ?? '',
                        'cust_consultant'=> $user['cust_consultant'] ?? '',
                        'sales_attribution_uid'=>$v['sales_attribution_uid']
                    ];
                    // var_dump('foreach cust_consultant=-=-=---------------',$data);

                    // 是否有关联用户
                    if(!empty($v['cust_uid1'])){
                        // 是否第一次
                        if(!in_array($v['cust_uid'], $arr)){
                            $cust_name1 =  $this->dao->get('cust_user',[ 'cust_uid' => $v['cust_uid1'],'delete_time' => null,],'cust_name');
                            $data['cust_uid1'] = $v['cust_uid1'];
                            $data['cust_name1'] = $cust_name1 ?? '';
                            $arr[] = $v['cust_uid'];
                            $arr[] = $v['cust_uid1'];
                        } else {
                            // 已生成工资填报不再重复生成
                            $data = null;
                        }
                    }
                    // var_dump('$data=-=-=------------2---',$data );
                    // 是否创建
                    if(!empty($data)){
                        // var_dump('$data=-=-=------------3---', );
                        // 获生成数据
                        $datas = $this->getSalaryData($data,1);
                        $data1 = $datas['data1'];
                        $data2 = $datas['data2'];
                        // var_dump('$data=-=-=------------$datas---',$datas );
                        
                        if(!empty($data1)){
                            // 获取新建员工工资表的id
                            $employee_salary_id = $EmployeeSalary->insertGetId($data1);
                            // var_dump('$data=-=-=------------!empty($data1)---',$employee_salary_id );

                            if(!empty($employee_salary_id)&&!empty($data2)){
                                // 生成工资填报附表
                                $data2['employee_salary_id'] =  $employee_salary_id;
                                // var_dump('data2-=-=-=---------',$data2);
                                $result = $this->logic->insertGetId($data2);
                                // var_dump('result-=-=-=---------',$result);
                            }
                        }
                    }
                }
            }
        }

        return $this->success('批量生成工资填报成功');
    } 

    /**
     * 获取生成工资填报数据
     * @param array $data
     * @param $adminId
     * @return array 
     */
    protected function getSalaryData(array $data,$adminId): array 
    {
        if(!empty($data)){
            $working_days = $data['working_days'] ?? 0; // 基数
            $actual_hour = $data['actual_hour'] ?? 0; // 实际工时
            $employee_salary = $data['employee_salary'] ?? 0; // 固定工资
            $value_added_expenses = $data['value_added_expenses'] ?? 0; // 增值金额

            $absenteeism_salary = 0; // 缺勤扣除
            $overtime_salary = 0; // 加班金额
            // var_dump('overtime_salary=-=-=---------------',$overtime_salary);

            if ($actual_hour < $working_days) {
                // 当实际工时＜基数时，缺勤扣除公式为：固定工资/基数*（实际天数-基数），展示负数，加班金额为0
                $absenteeism_salary = round($employee_salary / $working_days * ($actual_hour - $working_days));
            } else if ($actual_hour > $working_days) {
                // 当实际工时＞基数时，加班金额公式为：固定工资/基数*（实际天数-基数），展示正数，缺勤扣除为0
                $overtime_salary = round($employee_salary / $working_days * ($actual_hour - $working_days));
            } else {
                // 当实际工时等于基数时，缺勤和加班均展示0;
            }
            // 实发金额  固定工资+缺勤金额+加班金额
            $issued_salary = round(($employee_salary ?? 0 ) + ($overtime_salary ?? 0) + ($absenteeism_salary ?? 0));
            // var_dump('employee_salary=-=-=---------------',$employee_salary);
            // var_dump('overtime_salary=-=-=---------------',$overtime_salary);
            // var_dump('absenteeism_salary=-=-=---------------',$absenteeism_salary);
            // var_dump('issued_salary=-=-=---------------',$issued_salary);
        
            // 上一月
            $month = (new DateTime($data['salary_month']))->modify('-1 month')->format('Y-m');
            // var_dump('month=-=-=---------------',$month,$data['salary_month']);
            // 查询用户信息
            $subsidyInfo = $this->dao->get('cust_subsidy_bill',[ 'cust_uid' => $data['cust_uid'],'month' => $month ], ['month', 'cust_uid', 'real_amount']);
            // var_dump('data1-=-=-=---subsidyInfo------',$subsidyInfo);
            // 上月补贴
            $real_amount = !empty($subsidyInfo)?$subsidyInfo['real_amount']:0;

            $cust_name = $data['cust_name'] ?? '';
            // 有关联老人
            if(!empty($data['cust_uid1'])){
                $subsidyInfo1 = $this->dao->get('cust_subsidy_bill',[ 'cust_uid' => $data['cust_uid1'],'month' => $month ], ['month', 'cust_uid', 'real_amount']);
                // var_dump('data1-=-=-=---subsidyInfo1------',$subsidyInfo1);
                $real_amount += !empty($subsidyInfo1) && isset($subsidyInfo1['real_amount']) ? $subsidyInfo1['real_amount'] : 0;
                // var_dump('data1-=-=-=---real_amount------',$real_amount);
                $cust_name = $cust_name . (isset($data['cust_name1']) ? ('、' . $data['cust_name1']) : '');
            }
            // return "";

            // 应收金额  实发金额+增值金额-补贴金额
            $receivable_amount = round($issued_salary ?? 0) + round($value_added_expenses ?? 0) - round($real_amount ?? 0);
            // var_dump('receivable_amount-=-=-=---------',$receivable_amount);
            
            $EmployeeSalary = new EmployeeSalaryLogic();
            // 获取新建员工工资表的数据
            $data1 = [
                'employee_uid' =>$data['employee_uid'] ?? '',// 服务人员id
                'salary_month' =>$data['salary_month'] ?? '',// 工资月份
                'employee_salary' =>$employee_salary,// 固定工资
                'absenteeism_salary' => $absenteeism_salary,// 缺勤扣除
                'overtime_salary' => $overtime_salary,// 加班工资
                'issued_salary' =>$issued_salary,// 实发工资
                'created_by' => $adminId,
                'create_time' => date('Y-m-d H:i:s')
            ];
            // var_dump('$data1-=-=-=---------',$data1);

            // 获取生成工资填报附表数据
            $data2 = [
                'cust_uid' => empty($data['cust_uid1'])?($data['cust_uid'] ?? ''):'',// 客户编号
                'cust_name' => $cust_name ?? '',// 客户姓名
                'employee_uid' => $data['employee_uid'] ?? '',// 服务人员id
                'salary_classify' => $data['salary_classify'] ?? 'contract_management',// 工资分类
                'link_id' => $data['link_id'] ?? '',// 关联id
                'cust_type' => $data['cust_type'] ?? '',// 客户类型
                'services_type' => $data['services_type'],// 服务项目

                'salary_month' => $data['salary_month'] ?? '',// 工资填报月份
                'real_amount' => $real_amount,// 上月补贴
                'value_added_expenses' => $value_added_expenses,// 增值费用
                'receivable_amount' => $receivable_amount,// 应收金额
                'working_days' => $working_days,// 基数
                'actual_hour' => $actual_hour,// 实际工时
                'issued_salary' => $issued_salary,// 实发工资

                'employee_salary' => $employee_salary,// 固定工资
                'absenteeism_salary' => $absenteeism_salary,// 缺勤扣除
                'overtime_salary' => $overtime_salary,// 加班工资

                'salary_payment_date' => $data['salary_payment_date'] ?? 5,// 工资发放日
                'cust_consultant' => $data['cust_consultant'] ?? '',// 养老顾问(老人档案)
                'sales_attribution_uid' => $data['sales_attribution_uid'] ?? '',// 派工人员(合同)

                'created_by' => $adminId,
                'create_time' => date('Y-m-d H:i:s')
            ];
            // var_dump('$data2-=-=-=---------',$data2);

            return ["data1" => $data1, "data2" => $data2];
        }
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("employee_salary_id", type="bigint", require=false, desc="工资主表id", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户编号", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="员工编号", default="")
     * @Apidoc\Query("salary_classify", type="varchar", require=false, desc="工资分类", default="")
     * @Apidoc\Query("link_id", type="bigint", require=false, desc="关联id", default="")
     * @Apidoc\Query("salary_month", type="date", require=false, desc="工资填报月份", default="")
     * @Apidoc\Query("value_added_expenses", type="decimal", require=false, desc="增值费用", default="")
     * @Apidoc\Query("receivable_amount", type="decimal", require=false, desc="应收金额", default="")
     * @Apidoc\Query("working_days", type="int", require=false, desc="月工作天数(基数)", default="")
     * @Apidoc\Query("actual_hour", type="int", require=false, desc="实际工时(天)", default="")
     * @Apidoc\Query("issued_salary", type="decimal", require=false, desc="实发工资", default="")
     * @Apidoc\Query("employee_salary", type="decimal", require=false, desc="固定工资", default="")
     * @Apidoc\Query("absenteeism_salary", type="decimal", require=false, desc="缺勤扣除", default="")
     * @Apidoc\Query("overtime_salary", type="decimal", require=false, desc="加班工资", default="")
     * @Apidoc\Query("cust_consultant", type="varchar", require=false, desc="养老顾问(老人档案)", default="")
     * @Apidoc\Query("sales_attribution_uid", type="varchar", require=false, desc="派工人员(合同)", default="")
     * @Apidoc\Query("salary_status", type="varchar", require=false, desc="填报状态", default="")
     * @Apidoc\Query("cancel_reason", type="varchar", require=false, desc="取消原因", default="")
     * @Apidoc\Query("auditor_id", type="bigint", require=false, desc="审核员", default="")
     * @Apidoc\Query("auditor_time", type="datetime", require=false, desc="审核时间", default="")
     * @Apidoc\Query("auditor_remark", type="varchar", require=false, desc="审核人员备注", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("employee_salary_id", type="bigint", require=false, desc="工资主表id", default="")
     * @Apidoc\Query("cust_uid", type="bigint", require=false, desc="客户编号", default="")
     * @Apidoc\Query("employee_uid", type="bigint", require=false, desc="员工编号", default="")
     * @Apidoc\Query("salary_classify", type="varchar", require=false, desc="工资分类", default="")
     * @Apidoc\Query("link_id", type="bigint", require=false, desc="关联id", default="")
     * @Apidoc\Query("salary_month", type="date", require=false, desc="工资填报月份", default="")
     * @Apidoc\Query("real_amount", type="decimal", require=false, desc="上月补贴", default="")
     * @Apidoc\Query("value_added_expenses", type="decimal", require=false, desc="增值费用", default="")
     * @Apidoc\Query("receivable_amount", type="decimal", require=false, desc="应收金额", default="")
     * @Apidoc\Query("working_days", type="int", require=false, desc="月工作天数(基数)", default="")
     * @Apidoc\Query("actual_hour", type="int", require=false, desc="实际工时(天)", default="")
     * @Apidoc\Query("issued_salary", type="decimal", require=false, desc="实发工资", default="")
     * @Apidoc\Query("employee_salary", type="decimal", require=false, desc="固定工资", default="")
     * @Apidoc\Query("absenteeism_salary", type="decimal", require=false, desc="缺勤扣除", default="")
     * @Apidoc\Query("overtime_salary", type="decimal", require=false, desc="加班工资", default="")
     * @Apidoc\Query("cust_consultant", type="varchar", require=false, desc="养老顾问(老人档案)", default="")
     * @Apidoc\Query("sales_attribution_uid", type="varchar", require=false, desc="派工人员(合同)", default="")
     * @Apidoc\Query("salary_status", type="varchar", require=false, desc="填报状态", default="")
     * @Apidoc\Query("cancel_reason", type="varchar", require=false, desc="取消原因", default="")
     * @Apidoc\Query("auditor_id", type="bigint", require=false, desc="审核员", default="")
     * @Apidoc\Query("auditor_time", type="datetime", require=false, desc="审核时间", default="")
     * @Apidoc\Query("auditor_remark", type="varchar", require=false, desc="审核人员备注", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            $message = $request->input('message', $id); 
            // var_dump('update-=-=-=---------message------',$message);
            if(!empty($message) && $data['salary_classify'] == 'contract_management'){
                // 查询对应合同
                $cm = $this->dao->get('contract_management',[ 'contract_id' => $data['link_id'] ], ['contract_period_end', 'contract_status']);
                // var_dump('update-=-=-=---------cm------',$cm);
                $date = strtotime('last day of this month'); // 获取本月最后一天
                // var_dump('update-=-=-=---------date------',date('Y-m-d', $date));

                if($cm['contract_status'] == '到期'){
                    // 合同到期
                    $message = $message . '对应合同已到期,请注意续签合同!';
                } else if(strtotime($cm['contract_period_end']) <= $date){
                    // 合同月末到期
                    $message = $message . '对应合同在月末即将到期,请注意续签合同!';
                }
            }
            return $this->success($message?:'工资填报已修改!');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * 导出数据
     * @Apidoc\Title("导出数据")
     * @Apidoc\Url("/employee/EmployeeSalaryItem/export")
     * @Apidoc\Method("POST")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function export(Request $request) : Response
    {
        $where = $request->more([
            ['cust_uid', ''],
            ['cust_name', ''],
            ['employee_uid', ''],
            ['salary_month', ''],
            ['cust_consultant', ''],
            ['sales_attribution_uid', ''],
            ['salary_payment_date', ''],
            ['cust_type', '家床'],
        ]);
        $where['salary_status'] = '确认';
        // var_dump('$where-=-=-=---------------',$where);
        // $query = $this->logic->search($where)->with('custInfo');
        $query = $this->logic->search($where);

        $res = $this->logic->getAll($query);
        // var_dump('$res-=-=-=1---------------',$res);
        if(empty($res)) {
            // var_dump('$res-=-=-=1---empty------------',$res);
            // throw new BadRequestHttpException('工资填报不存在!');
            return $this->fail('确认状态的工资填报不存在,请修改导出条件!');
        }
        // var_dump('$res-=-=-=2---------------',$res);
        
        $i = 1;
        $data1 = [];
        $employeeIds = []; // 已劳务发放服务人员
        $employeeSalary = []; // 已劳务发放服务人员发放工资
        foreach ($res as $v) {
            $value = [];
            // 序号
            $value['number'] = $i;
            $i++;

            // 老人姓名
            // $value['cust_name'] = !empty($v["custInfo"]) ? $v["custInfo"]['cust_name'] : '';
            $value['cust_name'] = $v['cust_name'] ?? '';
            // var_dump('$value-=-=-=---------------',$value);

            // 服务员信息
            $employeeInfo = $this->dao->get('employee_user',[ 'employee_uid' => $v['employee_uid'] ],
                ['employee_name','employee_sex','employee_phone','employee_id_card','employee_bank_id_card','employee_bank_branch_name','employee_bank_branch_num']);
            // var_dump('$employeeInfo-=-=-=---------------',$employeeInfo);
            $value['employee_name']  = !empty($employeeInfo['employee_name']) ? $employeeInfo['employee_name'] : '';
            $value['employee_sex']  = !empty($employeeInfo['employee_sex']) ? ($employeeInfo['employee_sex'] == 2 ? '女' : '男') : '男';
            $value['employee_phone']  = !empty($employeeInfo['employee_phone']) ? $employeeInfo['employee_phone'] : '';
            $value['employee_id_card']  = !empty($employeeInfo['employee_id_card']) ? $employeeInfo['employee_id_card'] : '';
            $value['employee_bank_id_card']  = !empty($employeeInfo['employee_bank_id_card']) ? $employeeInfo['employee_bank_id_card'] : '';
            $value['employee_bank_branch_name']  = !empty($employeeInfo['employee_bank_branch_name']) ? $employeeInfo['employee_bank_branch_name'] : '';
            $value['employee_bank_branch_num']  = !empty($employeeInfo['employee_bank_branch_num']) ? $employeeInfo['employee_bank_branch_num'] : '';


            // 实发金额
            $value['issued_salary'] =  floatval($v["issued_salary"]) ?? 0;
            // var_dump('$value-=-=-=-----2----------',$value);


            // 劳务发放
            $salary1 = 0 ;// 劳务发放-实发
            // 是否第一次
            if(!in_array($v["employee_uid"], $employeeIds)){
                $salary1 = $v["issued_salary"] > 500 ? 500 : floatval($v["issued_salary"]);

                $employeeIds[] = $v["employee_uid"];
                $employeeSalary[] = $salary1;
            }else{
                // 获取服务员在服务员数组中的下标
                $index = array_search($v["employee_uid"], $employeeIds);

                if($index !== false && $employeeSalary[$index]>=500){
                    // 第一次工资超过500，第二次确认工资的劳务为0，全部走第三方发放
                    $salary1 =  0;
                }else{
                    // 第一次工资不足500，第二次发放工资的为500-第一次的劳务金额
                    $salary1 =  500 - floatval($employeeSalary[$index]);
                    // 更新已下发劳务金额
                    $employeeSalary[$index] +=  $salary1;
                }
            }
            // var_dump('$salary1-=-=-=---------------',$salary1);
            $value['salary'] =  $salary1 ?? 0;
            $value['individual_tax'] = 0;
            $value['payable'] = $salary1 ?? 0;
            // var_dump('$value-=-=-=---------------',$value);


            // 第三方
            // 三方的实发=护理员工资-劳务的应发金额
            $salary2 = floatval($value['issued_salary']) - floatval($value['salary']);
            // var_dump('$salary2-=-=-=---------------',$salary2);

            // 个税税率和服务费税率固定为1.5%  
            $salary3 = $salary2 * 0.015;// 个税金额=实发*个税税率
            $salary4 = $salary2 * 0.015;// 服务费金额=实发*服务费税率
            // var_dump('$salary4-=-=-=---------------',$salary4);

            $value['third_salary'] = $salary2 ?? 0;
            $value['third_tax_rate1'] = '1.5%';
            $value['third_individual_tax'] = $salary3 ?? 0;

            $value['third_tax_rate2'] = '1.5%';
            $value['third_service_tax'] = $salary4 ?? 0;

            $value['third_total_amount'] = ($salary2??0) +($salary3??0) +($salary4??0);

            // 养老顾问
            $value['cust_consultant'] = ($this->dao->get('eb_system_user',[ 'id' => $v['cust_consultant'],'delete_time' => null,],'nickname')) ?? '';
            // 养老管家
            $value['sales_attribution_uid'] = ($this->dao->get('eb_system_user',[ 'id' => $v['sales_attribution_uid'],'delete_time' => null,],'nickname')) ?? '';
            $data1[] = $value;
        }
        // var_dump('$data1-=-=-=2---------------',$data1);
        // var_dump('$data1-=-=-=$i$i$i$i$i---------------',$i);
        // 创建导出表
        $title = $where['cust_type'].$where['salary_month'].'工资填报_'.date('YmdHis').'.xlsx';
        // var_dump('$data1-=-=-=title---------------',$title);

        $headers = [
            '序号',
            '老人姓名',
            '护理员姓名',
            '性别',
            '手机号',
            '身份证号',
            '银行卡号',
            '银行开户行',
            '行号',
            '护理员工资',
            '劳务发放-实发',
            '劳务发放-个税',
            '劳务发放-应发',
            '第三方-实发',
            '第三方-个税税率',
            '第三方-个税',
            '第三方-服务费税率',
            '第三方-服务费金额',
            '第三方-总金额',
            '养老顾问',
            '养老管家',
        ];

        // var_dump('$headers-=-=-=2---------------',$headers);
        $url = Excel::export($title,$headers,$data1);
        // var_dump('$url-=-=-=2---------------',$url);
        // return response()->download($url, urlencode($title));
        return $this->success([ 'url' => $url  ]);
    }

}
