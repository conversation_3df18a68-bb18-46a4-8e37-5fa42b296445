<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\controller;

use plugin\saiadmin\basic\BaseController;
use app\employee\logic\EmployeeUserLogic;
use app\utils\Excel;
use app\employee\validate\EmployeeUserValidate;
use support\Request;
use support\Response;
use hg\apidoc\annotation as Apidoc;
use <PERSON><PERSON>\ExceptionHandler\Exception\BadRequestHttpException;

/**
 * @Apidoc\Title("员工管理")
 */
class EmployeeUserController extends BaseController
{
    protected string $pk = "employee_uid";

    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->logic = new EmployeeUserLogic();
        $this->validate = new EmployeeUserValidate;
        parent::__construct();
    }

    /**
     * @Apidoc\Title("数据列表")
     * @Apidoc\Url("/employee/EmployeeUser/index")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @Apidoc\Query("employee_name", type="varchar", require=false, desc="员工姓名", default="")
     * @Apidoc\Query("employee_service_site", type="bigint", require=false, desc="所属机构", default="")
     * @Apidoc\Query("employee_position", type="varchar", require=false, desc="员工职位", default="")
     * @Apidoc\Returned("data", type="array", require=true, desc="分页数据")
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        $where = $request->more([
            ['employee_name', ''],
            ['employee_service_site', ''],
            ['employee_position', ''],
            ['employee_consultant', ''],
        ]);
        $query = $this->logic->search($where);
        $data = $this->logic->getList($query);

        if(!empty($data['data'])){
            $employee =  $data['data'];
            if(!empty($employee)){
                foreach ($employee as &$v) { // 使用引用传递
                    if(!empty($v['employee_position'])){
                        // var_dump('$v)-=-=-=---------------',$v['employee_position']);
                        // JSON字符串转换成数组
                        $v['employee_position'] = json_decode($v['employee_position'], true); 
                        // var_dump('$v)-=-=-=---------------',$v['employee_position']);
                    }
                }
                // var_dump('$employee)-=-=-=---------------',$employee);
                unset($v); // 解除引用，防止后续问题
            }

            $data['data'] = $employee;
        }
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("保存数据")
     * @Apidoc\Url("/employee/EmployeeUser/save")
     * @Apidoc\Method("POST")
     * @Apidoc\Query("employee_position", type="varchar", require=false, desc="员工职位", default="")
     * @Apidoc\Query("employee_name", type="varchar", require=false, desc="员工姓名", default="")
     * @Apidoc\Query("employee_sex", type="tinyint", require=false, desc="员工性别", default="")
     * @Apidoc\Query("employee_phone", type="varchar", require=false, desc="员工手机号", default="")
     * @Apidoc\Query("employee_id_card", type="varchar", require=false, desc="员工身份证号", default="")
     * @Apidoc\Query("employee_id_type", type="tinyint", require=false, desc="身份证类型", default="")
     * @Apidoc\Query("employee_birth", type="datetime", require=false, desc="员工出生年月", default="")
     * @Apidoc\Query("employee_nation", type="varchar", require=false, desc="民族", default="")
     * @Apidoc\Query("employee_marital_status", type="tinyint", require=false, desc="婚姻状态", default="")
     * @Apidoc\Query("employee_regist_address", type="varchar", require=false, desc="户籍地址", default="")
     * @Apidoc\Query("employee_live_address", type="varchar", require=false, desc="居住详细地址", default="")
     * @Apidoc\Query("employee_bank_name", type="varchar", require=false, desc="所属银行", default="")
     * @Apidoc\Query("employee_bank_id_card", type="varchar", require=false, desc="银行卡号", default="")
     * @Apidoc\Query("employee_bank_branch_name", type="varchar", require=false, desc="开户支行", default="")
     * @Apidoc\Query("employee_bank_bind_phone", type="varchar", require=false, desc="银行绑定手机", default="")
     * @Apidoc\Query("employee_bank_branch_num", type="varchar", require=false, desc="银行卡行号", default="")
     * @Apidoc\Query("employee_card_front_img", type="varchar", require=false, desc="身份证正面", default="")
     * @Apidoc\Query("employee_card_reverse_img", type="varchar", require=false, desc="身份证反面", default="")
     * @Apidoc\Query("employee_education", type="tinyint", require=false, desc="学历", default="")
     * @Apidoc\Query("employee_status", type="tinyint", require=false, desc="状态", default="")
     * @Apidoc\Query("employee_service_site", type="bigint", require=false, desc="所属机构", default="")
     * @Apidoc\Query("employee_bank_bind_img", type="varchar", require=false, desc="银行卡照片", default="")
     * @Apidoc\Query("employee_signature_picture", type="varchar", require=false, desc="签字图片", default="")
     * @Apidoc\Query("employee_covid19_vaccine_screenshot", type="varchar", require=false, desc="新冠疫苗截屏", default="")
     * @param Request $request
     * @return Response
     */
    public function save(Request $request) : Response
    {
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('save')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $result = $this->logic->save($data);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("修改数据")
     * @Apidoc\Url("/employee/EmployeeUser/update")
     * @Apidoc\Method("PUT")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Query("employee_position", type="varchar", require=false, desc="员工职位", default="")
     * @Apidoc\Query("employee_name", type="varchar", require=false, desc="员工姓名", default="")
     * @Apidoc\Query("employee_sex", type="tinyint", require=false, desc="员工性别", default="")
     * @Apidoc\Query("employee_phone", type="varchar", require=false, desc="员工手机号", default="")
     * @Apidoc\Query("employee_id_card", type="varchar", require=false, desc="员工身份证号", default="")
     * @Apidoc\Query("employee_id_type", type="tinyint", require=false, desc="身份证类型", default="")
     * @Apidoc\Query("employee_birth", type="datetime", require=false, desc="员工出生年月", default="")
     * @Apidoc\Query("employee_nation", type="varchar", require=false, desc="民族", default="")
     * @Apidoc\Query("employee_marital_status", type="tinyint", require=false, desc="婚姻状态", default="")
     * @Apidoc\Query("employee_regist_address", type="varchar", require=false, desc="户籍地址", default="")
     * @Apidoc\Query("employee_live_address", type="varchar", require=false, desc="居住详细地址", default="")
     * @Apidoc\Query("employee_bank_name", type="varchar", require=false, desc="所属银行", default="")
     * @Apidoc\Query("employee_bank_id_card", type="varchar", require=false, desc="银行卡号", default="")
     * @Apidoc\Query("employee_bank_branch_name", type="varchar", require=false, desc="开户支行", default="")
     * @Apidoc\Query("employee_bank_bind_phone", type="varchar", require=false, desc="银行绑定手机", default="")
     * @Apidoc\Query("employee_bank_branch_num", type="varchar", require=false, desc="银行卡行号", default="")
     * @Apidoc\Query("employee_card_front_img", type="varchar", require=false, desc="身份证正面", default="")
     * @Apidoc\Query("employee_card_reverse_img", type="varchar", require=false, desc="身份证反面", default="")
     * @Apidoc\Query("employee_education", type="tinyint", require=false, desc="学历", default="")
     * @Apidoc\Query("employee_status", type="tinyint", require=false, desc="状态", default="")
     * @Apidoc\Query("employee_service_site", type="bigint", require=false, desc="所属机构", default="")
     * @Apidoc\Query("employee_bank_bind_img", type="varchar", require=false, desc="银行卡照片", default="")
     * @Apidoc\Query("employee_signature_picture", type="varchar", require=false, desc="签字图片", default="")
     * @Apidoc\Query("employee_covid19_vaccine_screenshot", type="varchar", require=false, desc="新冠疫苗截屏", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function update(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $data = $request->post();
        if ($this->validate) {
            if (!$this->validate->scene('update')->check($data)) {
                return $this->fail($this->validate->getError());
            }
        }
        $info = $this->logic->find($id);
        if (!$info) {
            return $this->fail('没有找到该数据');
        }
        $result = $this->logic->update($data, [$this->pk => $id]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("读取数据")
     * @Apidoc\Url("/employee/EmployeeUser/read")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("id", type="int", require=true, desc="主键", default="")
     * @param Request $request
     * @param $id
     * @return Response
     */
    public function read(Request $request, $id) : Response
    {
        $id = $request->input('id', $id);
        $model = $this->logic->find($id);
        if ($model) {
            $data = is_array($model) ? $model : $model->toArray();
            return $this->success($data);
        } else {
            return $this->fail('未查找到信息');
        }
    }

    /**
     * @Apidoc\Title("修改状态")
     * @Apidoc\Url("/employee/EmployeeUser/changeStatus")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("id", type="int", require=true, desc="主键", default="")
     * @Apidoc\Param("status", type="int", require=true, desc="状态", default="1")
     * @param Request $request
     * @return Response
     */
    public function changeStatus(Request $request) : Response
    {
        $id = $request->input('id', '');
        $status = $request->input('status', 1);
        $result = $this->logic->where($this->pk, $id)->update(['status' => $status]);
        if ($result) {
            return $this->success('操作成功');
        } else {
            return $this->fail('操作失败');
        }
    }

    /**
     * @Apidoc\Title("删除数据")
     * @Apidoc\Url("/employee/EmployeeUser/destroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function destroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("回收站数据")
     * @Apidoc\Url("/employee/EmployeeUser/recycle")
     * @Apidoc\Method("GET")
     * @Apidoc\Query("page", type="int", require=false, desc="框架自带-页码,默认1", default="1")
     * @Apidoc\Query("limit", type="int", require=false, desc="框架自带-每页数据,默认10", default="10")
     * @Apidoc\Query("saiType", type="string", require=false, desc="框架自带-获取数据类型;默认list分页;all全部数据", default="list")
     * @Apidoc\Query("orderBy", type="string", require=false, desc="框架自带-排序字段,默认主键", default="")
     * @Apidoc\Query("orderType", type="string", require=false, desc="框架自带-排序方式,默认ASC", default="")
     * @param Request $request
     * @return Response
     */
    public function recycle(Request $request) : Response
    {
        $where = $request->more([
            ['create_time', ''],
        ]);
        $query = $this->logic->recycle()->search($where);
        $data = $this->logic->getList($query);
        return $this->success($data);
    }

    /**
     * @Apidoc\Title("恢复数据")
     * @Apidoc\Url("/employee/EmployeeUser/recovery")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function recovery(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->restore($ids);
            return $this->success('恢复成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }

    /**
     * @Apidoc\Title("销毁数据")
     * @Apidoc\Url("/employee/EmployeeUser/realDestroy")
     * @Apidoc\Method("DELETE")
     * @Apidoc\Param("ids", type="string|array", require=true, desc="主键", default="")
     * @param Request $request
     * @return Response
     */
    public function realDestroy(Request $request) : Response
    {
        $ids = $request->input('ids', '');
        if (!empty($ids)) {
            $this->logic->destroy($ids, true);
            return $this->success('操作成功');
        } else {
            return $this->fail('参数错误，请检查');
        }
    }


    /**
     * @Apidoc\Title("导入数据")
     * @Apidoc\Url("/employee/EmployeeUser/import")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function import(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        $header = [
            '姓名' => 'employee_name',
            '性别' => 'employee_sex',
            '手机号' => 'employee_phone',
            '身份证号' => 'employee_id_card',
            '身份证正面' => 'employee_card_front_img',
            '身份证反面' => 'employee_card_reverse_img',
            '详细住址' => 'employee_live_address',
            '银行卡号' => 'employee_bank_id_card',
            '银行卡开户行' => 'employee_bank_branch_name',
            '银行卡照片' => 'employee_bank_bind_img',
            '签字图片' => 'employee_signature_picture',
            '新冠疫苗截屏' => 'employee_covid19_vaccine_screenshot',
        ];
        // 获取数据
        $data = Excel::import($header);
        $insertArr = [];// 修改后的数据
        if(!empty($data)) {
            foreach ($data as $v) {
                if(!empty($v['employee_sex'])) {
                    $v['employee_sex'] = match ($v['employee_sex'] ) {
                        '女' => 2,
                        '男' => 1,
                        default => 0
                    };
                }

                $v['employee_id_type'] = 1;// 证件类型  1：身份证
                $v['employee_nation'] = '汉族';// 民族
                $v['employee_marital_status'] = 6;// 婚姻状态  
                $v['employee_education'] = 1;// 学历  
                $v['employee_status'] = 1;// 状态  
                $v['employee_service_site'] = 3;// 所属机构  3:科源养老驿站

                $insertArr[] = $v;
            }
            $this->logic->saveAll($insertArr);
        }
        return $this->success('导入成功');
    }

     /**
     * @Apidoc\Title("更新数据")
     * @Apidoc\Url("/employee/EmployeeUser/import1")
     * @Apidoc\Method("POST")
     * @Apidoc\Param("file", type="file", require=true, desc="文件", default="")
     * @param Request $request
     * @return Response
     * @throws BadRequestHttpException
     */
    public function import1(Request $request) : Response
    {
        $file = current($request->file());
        if (!$file || !$file->isValid()) {
            return $this->fail('未找到上传文件');
        }
        // var_dump('import1=-=-=---------------');
        $header = [
            '护理员姓名' => 'employee_name',
            '性别' => 'employee_sex',
            '手机号' => 'employee_phone',
            '身份证号' => 'employee_id_card',
            '银行卡号' => 'employee_bank_id_card',
            '银行开户行' => 'employee_bank_branch_name',
            '负责人' => 'employee_consultant',
        ];
        // 获取数据
        $data = Excel::import($header);
        // var_dump('data=-=-=---------------',$data);

        $insertArr = [];// 未录入服务员
        if(!empty($data)) {
            foreach ($data as $v) {
                if(!empty($v['employee_sex'])) {
                    $v['employee_sex'] = match ($v['employee_sex'] ) {
                        '女' => 2,
                        '男' => 1,
                        default => 0
                    };
                }

                $v['employee_id_type'] = 1;// 证件类型  1：身份证
                $v['employee_nation'] = '汉族';// 民族
                $v['employee_marital_status'] = 6;// 婚姻状态  
                $v['employee_education'] = 1;// 学历  
                $v['employee_status'] = 1;// 状态  

                if(!empty($v['employee_consultant'])) {
                    // 负责人
                    $map = [ '隽京' => 46, '王鹏渤' => 49,  '张芸祺' => 57, '左庆刚' => 48,  '陈琨琨' => 51, '高红梅' => 53 ];
                    $employee_consultant =  $map[$v['employee_consultant']] ?? 46; // 默认隽京

                    // 所属机构
                    $map1 = [ '隽京' => 3, '王鹏渤' => 6,  '张芸祺' => 4, '左庆刚' => 5,  '陈琨琨' => 7, '高红梅' => 4 ];
                    $v['employee_service_site'] = $map1[$v['employee_consultant']] ?? 3;// 3:科源养老驿站
                    $v['employee_consultant'] = $employee_consultant;
                }
                // var_dump('$v=-=-=---------------',$v);

                $id = $this->dao->get('employee_user',[ 'employee_name' => $v['employee_name'], 'delete_time' => NULL ], 'employee_uid');
                // var_dump('id=-=-=---------------',$id);
                if(!empty($id)){
                    // 删除 'created_at' 字段
                    unset($v['created_at']);
                    unset($v['updated_at']);
                    unset($v['created_by']);
                    $v['update_time'] = date('Y-m-d H:i:s');
                    $v['updated_by'] = $request->adminId;
                    // var_dump('$v=-=-=----!empty($id)-----------',$v);

                    // 修改服务员
                    $this->dao->update('employee_user', $v , [ 'employee_uid' => $id ]);
                    // var_dump('$v=-=-=----!empty($id)--1---------');
                } else {
                    unset($v['created_at']);
                    unset($v['updated_at']);
                    $v['create_time'] = date('Y-m-d H:i:s');
                    $insertArr[] = $v;
                }
            }
            // var_dump('insertArr=-=-=---------------',$insertArr);
            $this->logic->saveAll($insertArr);
        }
        return $this->success('导入成功');
    }
}
