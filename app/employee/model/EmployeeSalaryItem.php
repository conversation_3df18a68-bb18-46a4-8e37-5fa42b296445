<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\model;
use think\model\relation\HasOne;
use app\cust\model\CustUser;
use app\employee\model\EmployeeUser;
use plugin\saiadmin\basic\BaseModel;

/**
 * 工资填报模型
 */
class EmployeeSalaryItem extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'employee_salary_item_id';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'employee_salary_item';
    
    /**
     * 姓名 搜索
     */
    public function searchCustNameAttr($query, $value)
    {
        $query->where('cust_name', 'like', '%'.$value.'%');
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function custInfo(): HasOne
    {
        return $this->hasOne(CustUser::class,'cust_uid','cust_uid')->field('cust_uid,cust_name,cust_private_phone,cust_evaluation_level,cust_is_bed,cust_street,cust_internal_number,cust_service_site,cust_haidian_number');
    }

    /**
     * 一对一关联 用户信息
     * @return HasOne
     */
    public function employeeInfo(): HasOne
    {
        return $this->hasOne(EmployeeUser::class,'employee_uid','employee_uid')->field('employee_uid,employee_name,employee_sex,employee_phone,employee_id_card,employee_bank_id_card,employee_bank_branch_name');
    }

}
