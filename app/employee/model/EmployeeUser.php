<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\model;

use plugin\saiadmin\basic\BaseModel;

/**
 * 员工管理模型
 */
class EmployeeUser extends BaseModel
{

    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'employee_uid';

    /**
     * 数据库表名称
     * @var string
     */
    protected $table = 'employee_user';

    
    /**
     * 员工姓名 搜索
     */
    public function searchEmployeeNameAttr($query, $value)
    {
        $query->where('employee_name', 'like', '%'.$value.'%');
    }


    /**
     * 员工职位 搜索
     */
    public function searchEmployeePositionAttr($query, $value)
    {
        $query->where('employee_position', 'like', '%'.$value.'%');
    }
}
