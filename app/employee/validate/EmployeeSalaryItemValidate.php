<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\validate;

use think\Validate;

/**
 * 工资填报验证器
 */
class EmployeeSalaryItemValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'employee_salary_id' => 'require',
        'cust_uid' => 'require',
        'employee_uid' => 'require',
        'salary_classify' => 'require',
        'link_id' => 'require',
        'salary_status' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'employee_salary_id' => '工资主表id必须填写',
        'cust_uid' => '客户编号必须填写',
        'employee_uid' => '员工编号必须填写',
        'salary_classify' => '工资分类必须填写',
        'link_id' => '关联id必须填写',
        'salary_status' => '填报状态必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'employee_salary_id',
            'cust_uid',
            'employee_uid',
            'salary_classify',
            'link_id',
            'salary_status',
        ],
        'update' => [
            'employee_salary_id',
            'cust_uid',
            'employee_uid',
            'salary_classify',
            'link_id',
            'salary_status',
        ],
    ];

}
