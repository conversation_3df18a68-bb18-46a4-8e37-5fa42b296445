<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\validate;

use think\Validate;

/**
 * 员工管理验证器
 */
class EmployeeUserValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'employee_name' => 'require',
        'employee_sex' => 'require',
        'employee_id_type' => 'require',
        'employee_marital_status' => 'require',
        'employee_bank_name' => 'require',
        'employee_bank_branch_name' => 'require',
        'employee_education' => 'require',
        'employee_status' => 'require',
        'employee_service_site' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'employee_name' => '员工姓名必须填写',
        'employee_sex' => '员工性别必须填写',
        'employee_id_type' => '身份证类型必须填写',
        'employee_marital_status' => '婚姻状态必须填写',
        'employee_bank_name' => '所属银行必须填写',
        'employee_bank_branch_name' => '开户支行必须填写',
        'employee_education' => '学历必须填写',
        'employee_status' => '状态必须填写',
        'employee_service_site' => '所属机构必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'employee_name',
            'employee_sex',
            'employee_id_type',
            'employee_marital_status',
            'employee_bank_name',
            'employee_bank_branch_name',
            'employee_education',
            'employee_status',
            'employee_service_site',
        ],
        'update' => [
            'employee_name',
            'employee_sex',
            'employee_id_type',
            'employee_marital_status',
            'employee_bank_name',
            'employee_bank_branch_name',
            'employee_education',
            'employee_status',
            'employee_service_site',
        ],
    ];

}
