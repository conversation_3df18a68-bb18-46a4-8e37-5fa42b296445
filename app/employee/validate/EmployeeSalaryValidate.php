<?php
// +----------------------------------------------------------------------
// | saiadmin [ saiadmin快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: your name
// +----------------------------------------------------------------------
namespace app\employee\validate;

use think\Validate;

/**
 * 工资表验证器
 */
class EmployeeSalaryValidate extends Validate
{
    /**
     * 定义验证规则
     */
    protected $rule =   [
        'employee_uid' => 'require',
        'salary_month' => 'require',
    ];

    /**
     * 定义错误信息
     */
    protected $message  =   [
        'employee_uid' => '员工id必须填写',
        'salary_month' => '工资月份必须填写',
    ];

    /**
     * 定义场景
     */
    protected $scene = [
        'save' => [
            'employee_uid',
            'salary_month',
        ],
        'update' => [
            'employee_uid',
            'salary_month',
        ],
    ];

}
