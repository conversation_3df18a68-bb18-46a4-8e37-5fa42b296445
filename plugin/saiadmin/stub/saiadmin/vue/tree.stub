<template>
  <div class="ma-content-block lg:flex justify-between p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
#whether[in_array("changeStatus", $menus)]
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch
          :checked-value="1"
          :unchecked-value="2"
          @change="changeStatus($event, record.${pk})"
          :default-checked="record.status == 1"
        />
      </template>
#/whether
    </ma-crud>
  </div>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue'
#whether[$package_name == ""]
  import api from '../api/${business_name}'
#/whether
#whether[$package_name != ""]
  import api from '../../api/${package_name}/${business_name}'
#/whether

  const crudRef = ref()

#whether[in_array("changeStatus", $menus)]
  const changeStatus = async (status, ${pk}) => {
    const response = await api.changeStatus({ ${pk}, status })
    if (response.code === 200) {
      Message.success(response.message)
    }
  }
#/whether

  const crud = reactive({
    pk:"${pk}",
    api: api.getPageList,
#whether[in_array("recycle", $menus)]
    recycleApi: api.getRecyclePageList,
#/whether
    showIndex: false,
    searchColNumber: 3,
    pageLayout: 'fixed',
    rowSelection: { showCheckedAll: true },
    operationColumn: true,
    operationColumnWidth: 160,
    add: { show: true, api: api.save, auth: ['/${url_path}/save'] },
    edit: { show: true, api: api.update, auth: ['/${url_path}/update'] },
    delete: { show: true, api: api.delete, auth: ['/${url_path}/destroy'], realApi: api.realDestroy, realAuth: ['/${url_path}/realDestroy'] },
    recovery: { show: true, api: api.recovery, auth: ['/${url_path}/recovery']},
    formOption: { width: 800 }
  })

  const columns = reactive([
#foreach($column in $columns)
#if[$column_name == $tree_parent_id]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 100,
      search: ${is_query},
      addDisplay: ${is_insert},
      editDisplay: ${is_edit},
      hide: ${is_list},
      formType: 'treeSelect',
      addDefaultValue: 0,
      dict: { url: '/${url_path}/index?tree=true' },
      validateTrigger: 'focus',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && !in_array($view_type, array("select", "radio", "checkbox", "switch", "date", "upload", "cascader", "treeSelect", "userSelect"))]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      formType: '${view_type}',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && $view_type == "date"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      searchFormType: '${search_type}',
      showTime: ${show_time},
      formType: '${view_type}',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && $view_type == "upload"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      formType: '${view_type}',
      type: '${type}',
      returnType: '${return_type}',
      multiple: ${multiple},
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && $view_type == "treeSelect"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      formType: '${view_type}',
      dict: { url: '${dict_url}' },
      validateTrigger: 'focus',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && $view_type == "cascader"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      formType: '${view_type}',
      options: [],
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && $view_type == "userSelect"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      formType: '${view_type}',
      onlyId: ${only_id},
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && $view_type == "cityLinkage"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      formType: '${view_type}',
      type: '${type}',
      name: '${name}',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$view_type == "switch"]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 180,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      checkedValue: ${checked_value},
      uncheckedValue: ${unchecked_value},
      formType: '${view_type}',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#if[$column_name != $tree_parent_id && in_array($view_type, array("select", "radio", "checkbox"))]
    {
      title: '${column_comment}',
      dataIndex: '${column_name}',
      width: 100,
      search: ${is_query},
      addDisplay: ${is_insert}, ${add_default}
      editDisplay: ${is_edit},
      hide: ${is_list},
      ${dict_option}
      formType: '${view_type}',
      commonRules: [{ required: ${is_required}, message: '${column_comment}必填' }],
    },
#/if
#/foreach
  ])
</script>
