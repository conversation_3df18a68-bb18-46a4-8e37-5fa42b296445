-- 数据库语句--

#foreach ($column in $tables)
#if[$package_name != ""]
-- 菜单[${menu_name}] SQL
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (${belong_menu_id}, '0,${belong_menu_id}', '${menu_name}', '${namespace}/${package_name}/${business_name}', 'icon-home', '${namespace}/${package_name}/${business_name}', '${namespace}/${package_name}/${business_name}/index', NULL, '2', 'M', 1, 0, NULL, now(), now(), NULL);

SET @id := LAST_INSERT_ID();
SET @level := CONCAT('${belong_menu_id}', ',', @id);

INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '列表'), '${route}/${namespace}/${package_name}/${class_name}/index', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '保存'), '${route}/${namespace}/${package_name}/${class_name}/save', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '更新'), '${route}/${namespace}/${package_name}/${class_name}/update', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '读取'), '${route}/${namespace}/${package_name}/${class_name}/read', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '修改状态'), '${route}/${namespace}/${package_name}/${class_name}/changeStatus', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '删除'), '${route}/${namespace}/${package_name}/${class_name}/destroy', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '回收'), '${route}/${namespace}/${package_name}/${class_name}/recycle', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '恢复'), '${route}/${namespace}/${package_name}/${class_name}/recovery', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '销毁'), '${route}/${namespace}/${package_name}/${class_name}/realDestroy', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
#/if
#if[$package_name == ""]
-- 菜单[${menu_name}] SQL
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (${belong_menu_id}, '0,${belong_menu_id}', '${menu_name}', '${namespace}/${business_name}', 'icon-home', '${namespace}/${business_name}', '${namespace}/${business_name}/index', NULL, '2', 'M', 1, 0, NULL, now(), now(), NULL);

SET @id := LAST_INSERT_ID();
SET @level := CONCAT('${belong_menu_id}', ',', @id);

INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '列表'), '${route}/${namespace}/${class_name}/index', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '保存'), '${route}/${namespace}/${class_name}/save', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '更新'), '${route}/${namespace}/${class_name}/update', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '读取'), '${route}/${namespace}/${class_name}/read', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '修改状态'), '${route}/${namespace}/${class_name}/changeStatus', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '删除'), '${route}/${namespace}/${class_name}/destroy', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '回收'), '${route}/${namespace}/${class_name}/recycle', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '恢复'), '${route}/${namespace}/${class_name}/recovery', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
INSERT INTO `eb_system_menu`(`parent_id`, `level`, `name`, `code`, `icon`, `route`, `component`, `redirect`, `is_hidden`, `type`, `status`, `sort`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (@id, @level, CONCAT('${menu_name}', '销毁'), '${route}/${namespace}/${class_name}/realDestroy', NULL, NULL, NULL, NULL, '2', 'B', '1', 0, NULL, now(), now(), NULL);
#/if
#/foreach
