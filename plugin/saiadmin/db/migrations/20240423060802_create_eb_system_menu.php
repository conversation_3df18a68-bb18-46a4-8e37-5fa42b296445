<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

final class CreateEbSystemMenu extends AbstractMigration
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $table = $this->table('eb_system_menu', [
            'id' => false,
            'primary_key' => ['id'],
            'comment' => '菜单信息表'
        ]);
        $table->addColumn('id', 'integer', ['signed' => false, 'identity' => true])
            ->addColumn('parent_id', 'integer', ['null' => true, 'default' => null, 'comment' => '父ID'])
            ->addColumn('level', 'string', ['limit' => 500, 'null' => true, 'default' => null, 'comment' => '组级集合'])
            ->addColumn('name', 'string', ['limit' => 50, 'null' => true, 'default' => null, 'comment' => '菜单名称'])
            ->addColumn('code', 'string', ['limit' => 100, 'null' => true, 'default' => null, 'comment' => '菜单标识代码'])
            ->addColumn('icon', 'string', ['limit' => 50, 'null' => true, 'default' => null, 'comment' => '菜单图标'])
            ->addColumn('route', 'string', ['limit' => 200, 'null' => true, 'default' => null, 'comment' => '路由地址'])
            ->addColumn('component', 'string', ['limit' => 255, 'null' => true, 'default' => null, 'comment' => '组件路径'])
            ->addColumn('redirect', 'string', ['limit' => 255, 'null' => true, 'default' => null, 'comment' => '跳转地址'])
            ->addColumn('is_hidden', 'integer', ['null' => true, 'default' => 1, 'comment' => '是否隐藏 (1是 2否)'])
            ->addColumn('type', 'char', ['limit' => 1, 'null' => true, 'default' => '', 'comment' => '菜单类型, (M菜单 B按钮 L链接 I iframe)'])
            ->addColumn('generate_id', 'integer', ['null' => true, 'default' => 0, 'comment' => '生成id'])
            ->addColumn('generate_key', 'string', ['limit' => 255, 'null' => true, 'default' => null, 'comment' => '生成key'])
            ->addColumn('status', 'integer', ['null' => true, 'default' => 1, 'comment' => '状态 (1正常 2停用)'])
            ->addColumn('sort', 'integer', ['null' => true, 'default' => 0, 'comment' => '排序'])
            ->addColumn('remark', 'string', ['limit' => 255, 'null' => true, 'default' => null, 'comment' => '备注'])
            ->addColumn('created_by', 'integer', ['null' => true, 'default' => null, 'comment' => '创建者'])
            ->addColumn('updated_by', 'integer', ['null' => true, 'default' => null, 'comment' => '更新者'])
            ->addColumn('create_time', 'datetime', ['null' => true, 'default' => null, 'comment' => '创建时间'])
            ->addColumn('update_time', 'datetime', ['null' => true, 'default' => null, 'comment' => '修改时间'])
            ->addColumn('delete_time', 'datetime', ['null' => true, 'default' => null, 'comment' => '删除时间'])
            ->create();

        $data = [
            [
                'id' => 1000,
                'parent_id' => 0,
                'level' => '0',
                'name' => '权限',
                'code' => 'permission',
                'icon' => 'ma-icon-permission',
                'route' => 'permission',
                'component' => '',
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'M',
                'status' => 1,
                'sort' => 99,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:48:47',
                'update_time' => '2023-11-14 23:13:42',
                'delete_time' => NULL,
            ],
            [
                'id' => 1100,
                'parent_id' => 1000,
                'level' => '0,1000',
                'name' => '用户管理',
                'code' => '/core/user',
                'icon' => 'ma-icon-user',
                'route' => 'user',
                'component' => 'system/user/index',
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'M',
                'status' => 1,
                'sort' => 99,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1101,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户列表',
                'code' => '/core/user/index',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1102,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户回收站列表',
                'code' => '/core/user/recycle',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1103,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户保存',
                'code' => '/core/user/save',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1104,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户更新',
                'code' => '/core/user/update',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1105,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户删除',
                'code' => '/core/user/destroy',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1106,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户读取',
                'code' => '/core/user/read',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1107,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户恢复',
                'code' => '/core/user/recovery',
                'icon' => NULL,
                'route' => NULL,
                'component' => NULL,
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:50:15',
                'update_time' => '2021-07-25 18:50:15',
                'delete_time' => NULL,
            ],
            [
                'id' => 1108,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户销毁',
                'code' => '/core/user/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 1111,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户状态改变',
                'code' => '/core/user/changeStatus',
                'icon' => '',
                'route' => NULL,
                'component' => '',
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:53:02',
                'update_time' => '2021-07-25 18:53:02',
                'delete_time' => NULL,
            ],
            [
                'id' => 1112,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '用户初始化密码',
                'code' => '/core/user/initUserPassword',
                'icon' => '',
                'route' => NULL,
                'component' => '',
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 18:55:55',
                'update_time' => '2021-07-25 18:55:55',
                'delete_time' => NULL,
            ],
            [
                'id' => 1113,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '更新用户缓存',
                'code' => '/core/user/cache',
                'icon' => '',
                'route' => NULL,
                'component' => '',
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-08 18:30:57',
                'update_time' => '2021-08-08 18:30:57',
                'delete_time' => NULL,
            ],
            [
                'id' => 1114,
                'parent_id' => 1100,
                'level' => '0,1000,1100',
                'name' => '设置用户首页',
                'code' => '/core/user/setHomePage',
                'icon' => '',
                'route' => NULL,
                'component' => '',
                'redirect' => NULL,
                'is_hidden' => 2,
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'remark' => NULL,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-08 18:34:30',
                'update_time' => '2021-08-08 18:34:30',
                'delete_time' => NULL,
            ],
            [
                'id' => 1200,
                'parent_id' => 1000,
                'level' => '0,1000',
                'name' => '菜单管理',
                'code' => '/core/menu',
                'icon' => 'icon-menu',
                'route' => 'menu',
                'component' => 'system/menu/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 96,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1201,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单列表',
                'code' => '/core/menu/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1202,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单回收站',
                'code' => '/core/menu/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1203,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单保存',
                'code' => '/core/menu/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1204,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单更新',
                'code' => '/core/menu/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1205,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单删除',
                'code' => '/core/menu/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1206,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单读取',
                'code' => '/core/menu/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1207,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单恢复',
                'code' => '/core/menu/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:01:47',
                'update_time' => '2021-07-25 19:01:47',
                'delete_time' => null,
            ],
            [
                'id' => 1208,
                'parent_id' => 1200,
                'level' => '0,1000,1200',
                'name' => '菜单销毁',
                'code' => '/core/menu/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 1300,
                'parent_id' => 1000,
                'level' => '0,1000',
                'name' => '部门管理',
                'code' => '/core/dept',
                'icon' => 'ma-icon-dept',
                'route' => 'dept',
                'component' => 'system/dept/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 97,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1301,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门列表',
                'code' => '/core/dept/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1302,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门回收站',
                'code' => '/core/dept/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1303,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门保存',
                'code' => '/core/dept/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1304,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门更新',
                'code' => '/core/dept/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1305,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门删除',
                'code' => '/core/dept/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1306,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门读取',
                'code' => '/core/dept/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1307,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门恢复',
                'code' => '/core/dept/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:16:33',
                'update_time' => '2021-07-25 19:16:33',
                'delete_time' => null,
            ],
            [
                'id' => 1308,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门销毁',
                'code' => '/core/dept/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 1311,
                'parent_id' => 1300,
                'level' => '0,1000,1300',
                'name' => '部门状态改变',
                'code' => '/core/dept/changeStatus',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-11-09 18:26:15',
                'update_time' => '2021-11-09 18:26:15',
                'delete_time' => null,
            ],
            [
                'id' => 1400,
                'parent_id' => 1000,
                'level' => '0,1000',
                'name' => '角色管理',
                'code' => '/core/role',
                'icon' => 'ma-icon-role',
                'route' => 'role',
                'component' => 'system/role/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 98,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:37',
                'update_time' => '2021-07-25 19:17:37',
                'delete_time' => null,
            ],
            [
                'id' => 1401,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色列表',
                'code' => '/core/role/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:37',
                'update_time' => '2021-07-25 19:17:37',
                'delete_time' => null,
            ],
            [
                'id' => 1402,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色回收站',
                'code' => '/core/role/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:38',
                'update_time' => '2021-07-25 19:17:38',
                'delete_time' => null,
            ],
            [
                'id' => 1403,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色保存',
                'code' => '/core/role/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:38',
                'update_time' => '2021-07-25 19:17:38',
                'delete_time' => null,
            ],
            [
                'id' => 1404,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色更新',
                'code' => '/core/role/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:38',
                'update_time' => '2021-07-25 19:17:38',
                'delete_time' => null,
            ],
            [
                'id' => 1405,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色删除',
                'code' => '/core/role/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:38',
                'update_time' => '2021-07-25 19:17:38',
                'delete_time' => null,
            ],
            [
                'id' => 1406,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色读取',
                'code' => '/core/role/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:38',
                'update_time' => '2021-07-25 19:17:38',
                'delete_time' => null,
            ],
            [
                'id' => 1407,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色恢复',
                'code' => '/core/role/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 19:17:38',
                'update_time' => '2021-07-25 19:17:38',
                'delete_time' => null,
            ],
            [
                'id' => 1408,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色销毁',
                'code' => '/core/role/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 1411,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '角色状态改变',
                'code' => '/core/role/changeStatus',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-30 11:21:24',
                'update_time' => '2021-07-30 11:21:24',
                'delete_time' => null,
            ],
            [
                'id' => 1412,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '更新菜单权限',
                'code' => '/core/role/menuPermission',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-09 11:52:33',
                'update_time' => '2021-08-09 11:52:33',
                'delete_time' => null,
            ],
            [
                'id' => 1413,
                'parent_id' => 1400,
                'level' => '0,1000,1400',
                'name' => '更新数据权限',
                'code' => '/core/role/dataPermission',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-09 11:52:52',
                'update_time' => '2021-08-09 11:52:52',
                'delete_time' => null,
            ],
            [
                'id' => 1500,
                'parent_id' => 1000,
                'level' => '0,1000',
                'name' => '岗位管理',
                'code' => '/core/post',
                'icon' => 'ma-icon-post',
                'route' => 'post',
                'component' => 'system/post/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1501,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位列表',
                'code' => '/core/post/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1502,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位回收站',
                'code' => '/core/post/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1503,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位保存',
                'code' => '/core/post/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1504,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位更新',
                'code' => '/core/post/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1505,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位删除',
                'code' => '/core/post/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1506,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位读取',
                'code' => '/core/post/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1507,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位恢复',
                'code' => '/core/post/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-25 20:46:38',
                'update_time' => '2021-07-25 20:46:38',
                'delete_time' => null,
            ],
            [
                'id' => 1508,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位销毁',
                'code' => '/core/post/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 1511,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位状态改变',
                'code' => '/core/post/changeStatus',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-11-09 18:26:15',
                'update_time' => '2021-11-09 18:26:15',
                'delete_time' => null,
            ],
            [
                'id' => 1512,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位导入',
                'code' => '/core/post/import',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 17:17:03',
                'update_time' => '2021-07-31 17:17:03',
                'delete_time' => null,
            ],
            [
                'id' => 1513,
                'parent_id' => 1500,
                'level' => '0,1000,1500',
                'name' => '岗位导出',
                'code' => '/core/post/export',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 17:17:03',
                'update_time' => '2021-07-31 17:17:03',
                'delete_time' => null,
            ],
            [
                'id' => 2000,
                'parent_id' => 0,
                'level' => '0',
                'name' => '数据',
                'code' => 'dataCenter',
                'icon' => 'icon-storage',
                'route' => 'dataCenter',
                'type' => 'M',
                'status' => 1,
                'sort' => 98,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 17:17:03',
                'update_time' => '2021-07-31 17:17:03',
                'delete_time' => null,
            ],
            [
                'id' => 2100,
                'parent_id' => 2000,
                'level' => '0,2000',
                'name' => '数据字典',
                'code' => '/core/dictType',
                'icon' => 'ma-icon-dict',
                'route' => 'dict',
                'component' => 'system/dict/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 99,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:45',
                'update_time' => '2021-07-31 18:33:45',
                'delete_time' => null,
            ],
            [
                'id' => 2101,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典列表',
                'code' => '/core/dictType/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:45',
                'update_time' => '2021-07-31 18:33:45',
                'delete_time' => null,
            ],
            [
                'id' => 2102,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典回收站',
                'code' => '/core/dictType/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:45',
                'update_time' => '2021-07-31 18:33:45',
                'delete_time' => null,
            ],
            [
                'id' => 2103,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典保存',
                'code' => '/core/dictType/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:45',
                'update_time' => '2021-07-31 18:33:45',
                'delete_time' => null,
            ],
            [
                'id' => 2104,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典更新',
                'code' => '/core/dictType/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:45',
                'update_time' => '2021-07-31 18:33:45',
                'delete_time' => null,
            ],
            [
                'id' => 2105,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典删除',
                'code' => '/core/dictType/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:45',
                'update_time' => '2021-07-31 18:33:45',
                'delete_time' => null,
            ],
            [
                'id' => 2106,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典读取',
                'code' => '/core/dictType/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:46',
                'update_time' => '2021-07-31 18:33:46',
                'delete_time' => null,
            ],
            [
                'id' => 2107,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典恢复',
                'code' => '/core/dictType/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:33:46',
                'update_time' => '2021-07-31 18:33:46',
                'delete_time' => null,
            ],
            [
                'id' => 2108,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '数据字典销毁',
                'code' => '/core/dictType/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 2112,
                'parent_id' => 2100,
                'level' => '0,2000,2100',
                'name' => '字典状态改变',
                'code' => '/core/dictType/changeStatus',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-11-09 18:26:15',
                'update_time' => '2021-11-09 18:26:15',
                'delete_time' => null,
            ],
            [
                'id' => 2200,
                'parent_id' => 2000,
                'level' => '0,2000',
                'name' => '附件管理',
                'code' => '/core/attachment',
                'icon' => 'ma-icon-attach',
                'route' => 'attachment',
                'component' => 'system/attachment/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 98,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:36:34',
                'update_time' => '2021-07-31 18:36:34',
                'delete_time' => null,
            ],
            [
                'id' => 2201,
                'parent_id' => 2200,
                'level' => '0,2000,2200',
                'name' => '附件删除',
                'code' => '/core/attachment/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:37:20',
                'update_time' => '2021-07-31 18:37:20',
                'delete_time' => null,
            ],
            [
                'id' => 2202,
                'parent_id' => 2200,
                'level' => '0,2000,2200',
                'name' => '附件列表',
                'code' => '/core/attachment/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:38:05',
                'update_time' => '2021-07-31 18:38:05',
                'delete_time' => null,
            ],
            [
                'id' => 2203,
                'parent_id' => 2200,
                'level' => '0,2000,2200',
                'name' => '附件回收站',
                'code' => '/core/attachment/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:38:57',
                'update_time' => '2021-07-31 18:38:57',
                'delete_time' => null,
            ],
            [
                'id' => 2204,
                'parent_id' => 2200,
                'level' => '0,2000,2200',
                'name' => '附件恢复',
                'code' => '/core/attachment/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:40:44',
                'update_time' => '2021-07-31 18:40:44',
                'delete_time' => null,
            ],
            [
                'id' => 2205,
                'parent_id' => 2200,
                'level' => '0,2000,2200',
                'name' => '附件销毁',
                'code' => '/core/attachment/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 2300,
                'parent_id' => 2000,
                'level' => '0,2000',
                'name' => '数据表维护',
                'code' => '/core/dataMaintain',
                'icon' => 'ma-icon-db',
                'route' => 'dataMaintain',
                'component' => 'system/dataMaintain/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 95,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:43:20',
                'update_time' => '2021-07-31 18:43:20',
                'delete_time' => null,
            ],
            [
                'id' => 2301,
                'parent_id' => 2300,
                'level' => '0,2000,2300',
                'name' => '数据表列表',
                'code' => '/core/dataMaintain/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:44:03',
                'update_time' => '2021-07-31 18:44:03',
                'delete_time' => null,
            ],
            [
                'id' => 2302,
                'parent_id' => 2300,
                'level' => '0,2000,2300',
                'name' => '数据表详细',
                'code' => '/core/dataMaintain/detailed',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:45:17',
                'update_time' => '2021-07-31 18:45:17',
                'delete_time' => null,
            ],
            [
                'id' => 2303,
                'parent_id' => 2300,
                'level' => '0,2000,2300',
                'name' => '数据表清理碎片',
                'code' => '/core/dataMaintain/fragment',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:46:04',
                'update_time' => '2021-07-31 18:46:04',
                'delete_time' => null,
            ],
            [
                'id' => 2304,
                'parent_id' => 2300,
                'level' => '0,2000,2300',
                'name' => '数据表优化',
                'code' => '/core/dataMaintain/optimize',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:46:31',
                'update_time' => '2021-07-31 18:46:31',
                'delete_time' => null,
            ],
            [
                'id' => 2700,
                'parent_id' => 2000,
                'level' => '0,2000',
                'name' => '系统公告',
                'code' => '/core/notice',
                'icon' => 'icon-bulb',
                'route' => 'notice',
                'component' => 'system/notice/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 94,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2701,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告列表',
                'code' => '/core/notice/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2702,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告回收站',
                'code' => '/core/notice/recycle',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2703,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告保存',
                'code' => '/core/notice/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2704,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告更新',
                'code' => '/core/notice/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2705,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告删除',
                'code' => '/core/notice/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2706,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告读取',
                'code' => '/core/notice/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2707,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告恢复',
                'code' => '/core/notice/recovery',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-25 18:10:20',
                'update_time' => '2021-12-25 18:10:20',
                'delete_time' => null,
            ],
            [
                'id' => 2708,
                'parent_id' => 2700,
                'level' => '0,2000,2700',
                'name' => '系统公告销毁',
                'code' => '/core/notice/realDestroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2024-04-30 16:30:00',
                'update_time' => '2023-04-30 16:30:00',
                'delete_time' => null,
            ],
            [
                'id' => 3000,
                'parent_id' => 0,
                'level' => '0',
                'name' => '监控',
                'code' => 'monitor',
                'icon' => 'icon-desktop',
                'route' => 'monitor',
                'type' => 'M',
                'status' => 1,
                'sort' => 97,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:49:24',
                'update_time' => '2021-07-31 18:49:24',
                'delete_time' => null,
            ],
            [
                'id' => 3200,
                'parent_id' => 3000,
                'level' => '0,3000',
                'name' => '服务监控',
                'code' => '/core/system/monitor',
                'icon' => 'icon-thunderbolt',
                'route' => 'server',
                'component' => 'system/monitor/server/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 99,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:52:46',
                'update_time' => '2021-07-31 18:52:46',
                'delete_time' => null,
            ],
            [
                'id' => 3300,
                'parent_id' => 3000,
                'level' => '0,3000',
                'name' => '日志监控',
                'code' => 'logs',
                'icon' => 'icon-book',
                'route' => 'logs',
                'type' => 'M',
                'status' => 1,
                'sort' => 95,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:54:01',
                'update_time' => '2021-07-31 18:54:01',
                'delete_time' => null,
            ],
            [
                'id' => 3400,
                'parent_id' => 3300,
                'level' => '0,3000,3200',
                'name' => '登录日志',
                'code' => '/core/logs/deleteLoginLog',
                'icon' => 'icon-idcard',
                'route' => 'loginLog',
                'component' => 'system/logs/loginLog',
                'type' => 'M',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:54:55',
                'update_time' => '2021-07-31 18:54:55',
                'delete_time' => null,
            ],
            [
                'id' => 3401,
                'parent_id' => 3400,
                'level' => '0,3000,3200,3300',
                'name' => '登录日志删除',
                'code' => '/core/logs/deleteOperLog',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:56:43',
                'update_time' => '2021-07-31 18:56:43',
                'delete_time' => null,
            ],
            [
                'id' => 3500,
                'parent_id' => 3300,
                'level' => '0,3000,3200',
                'name' => '操作日志',
                'code' => '/core/logs/getOperLogPageList',
                'icon' => 'icon-robot',
                'route' => 'operLog',
                'component' => 'system/logs/operLog',
                'type' => 'M',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:55:40',
                'update_time' => '2021-07-31 18:55:40',
                'delete_time' => null,
            ],
            [
                'id' => 3501,
                'parent_id' => 3500,
                'level' => '0,3000,3200,3400',
                'name' => '操作日志删除',
                'code' => '/core/logs/deleteOperLog',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 18:56:19',
                'update_time' => '2021-07-31 18:56:19',
                'delete_time' => null,
            ],
            [
                'id' => 4000,
                'parent_id' => 0,
                'level' => '0',
                'name' => '工具',
                'code' => 'devTools',
                'icon' => 'ma-icon-tool',
                'route' => 'devTools',
                'type' => 'M',
                'status' => 1,
                'sort' => 95,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:03:32',
                'update_time' => '2021-07-31 19:03:32',
                'delete_time' => null,
            ],
            [
                'id' => 4200,
                'parent_id' => 4000,
                'level' => '0,4000',
                'name' => '代码生成器',
                'code' => '/tool/code',
                'icon' => 'ma-icon-code',
                'route' => 'code',
                'component' => 'setting/code/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 98,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:36:17',
                'update_time' => '2021-07-31 19:36:17',
                'delete_time' => null,
            ],
            [
                'id' => 4400,
                'parent_id' => 4000,
                'level' => '0,4000',
                'name' => '定时任务',
                'code' => '/core/crontab',
                'icon' => 'icon-schedule',
                'route' => 'crontab',
                'component' => 'setting/crontab/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:47:49',
                'update_time' => '2021-07-31 19:47:49',
                'delete_time' => null,
            ],
            [
                'id' => 4401,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务列表',
                'code' => '/core/crontab/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:47:49',
                'update_time' => '2021-07-31 19:47:49',
                'delete_time' => null,
            ],
            [
                'id' => 4402,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务保存',
                'code' => '/core/crontab/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:47:49',
                'update_time' => '2021-07-31 19:47:49',
                'delete_time' => null,
            ],
            [
                'id' => 4403,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务更新',
                'code' => '/core/crontab/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:47:49',
                'update_time' => '2021-07-31 19:47:49',
                'delete_time' => null,
            ],
            [
                'id' => 4404,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务删除',
                'code' => '/core/crontab/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:47:49',
                'update_time' => '2021-07-31 19:47:49',
                'delete_time' => null,
            ],
            [
                'id' => 4405,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务读取',
                'code' => '/core/crontab/read',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:47:49',
                'update_time' => '2021-07-31 19:47:49',
                'delete_time' => null,
            ],
            [
                'id' => 4408,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务执行',
                'code' => '/core/crontab/run',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-07 23:44:06',
                'update_time' => '2021-08-07 23:44:06',
                'delete_time' => null,
            ],
            [
                'id' => 4409,
                'parent_id' => 4400,
                'level' => '0,4000,4400',
                'name' => '定时任务日志删除',
                'code' => '/core/crontab/deleteLog',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-12-06 22:06:12',
                'update_time' => '2021-12-06 22:06:12',
                'delete_time' => null,
            ],
            [
                'id' => 4500,
                'parent_id' => 0,
                'level' => '0',
                'name' => '系统设置',
                'code' => '/core/config',
                'icon' => 'icon-settings',
                'route' => 'system',
                'component' => 'setting/config/index',
                'type' => 'M',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-07-31 19:58:57',
                'update_time' => '2023-12-13 14:49:47',
                'delete_time' => null,
            ],
            [
                'id' => 4502,
                'parent_id' => 4500,
                'level' => '0,4500',
                'name' => '配置列表',
                'code' => '/core/config/index',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-10 13:09:18',
                'update_time' => '2021-08-10 13:09:18',
                'delete_time' => null,
            ],
            [
                'id' => 4504,
                'parent_id' => 4500,
                'level' => '0,4500',
                'name' => '新增配置 ',
                'code' => '/core/config/save',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-10 13:11:56',
                'update_time' => '2021-08-10 13:11:56',
                'delete_time' => null,
            ],
            [
                'id' => 4505,
                'parent_id' => 4500,
                'level' => '0,4500',
                'name' => '更新配置',
                'code' => '/core/config/update',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-10 13:12:25',
                'update_time' => '2021-08-10 13:12:25',
                'delete_time' => null,
            ],
            [
                'id' => 4506,
                'parent_id' => 4500,
                'level' => '0,4500',
                'name' => '删除配置',
                'code' => '/core/config/destroy',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-10 13:13:33',
                'update_time' => '2021-08-10 13:13:33',
                'delete_time' => null,
            ],
            [
                'id' => 4507,
                'parent_id' => 4500,
                'level' => '0,4500',
                'name' => '清除配置缓存',
                'code' => '/core/config/clearCache',
                'type' => 'B',
                'status' => 1,
                'sort' => 0,
                'is_hidden' => 2,
                'created_by' => 1,
                'updated_by' => 1,
                'create_time' => '2021-08-10 13:13:59',
                'update_time' => '2021-08-10 13:13:59',
                'delete_time' => null,
            ],
        ];

        $table->insert($data)->save();
    }

    public function down()
    {
        $this->table('eb_system_menu')->drop()->save();
    }
}
