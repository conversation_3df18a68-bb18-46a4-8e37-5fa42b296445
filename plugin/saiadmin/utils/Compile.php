<?php
// +----------------------------------------------------------------------
// | saithink [ saithink快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace plugin\saiadmin\utils;

/**
 * 代码构建 编译类
 * Class
 * @package plugin\saiadmin\utils
 */
class Compile
{

    private $template;				//带编译文件
    private $content; 				//需要替换的文本
    private $comfile;				//编译后的文件
    private $genpath;				//生成目录

    private $value = array();		//值栈

    private $fileName = ""; //当前生成文件名称

    public function __construct($template, $compileFile, $genPath)
    {
        $this->template = $template;
        $this->comfile = $compileFile;
        $this->genpath = $genPath;
        $this->content = file_get_contents($template);
    }

    /**
     * 编译获取文件内容
     */
    public function compile()
    {
        $this->c_all();
        return $this->content;
    }

    /**
     * 编译生成文件
     */
    public function gen($fileName)
    {
        $this->fileName = pathinfo($fileName, PATHINFO_FILENAME);
        $this->c_all();
        $path = $this->genpath.DIRECTORY_SEPARATOR.$fileName;
        if(!is_dir(dirname($path))){
            $flag = mkdir(dirname($path),0777,true);
        }
        file_put_contents($path, $this->content);
    }

    public function c_all()
    {
        $tpl_category = $this->value['tpl_category'];
        $package_name = $this->value['package_name'];
        $menus = explode(',', $this->value['generate_menus']);
        $component_type = $this->value['component_type'];
        $current_file = $this->fileName;

        // Whether变量
        preg_match_all('/#whether[^]]+\]*([\s\S]*?)#\/whether/i', $this->content, $matches);
        for ($i=0; $i < count($matches[0]); $i++) {
            $template = $matches[0][$i];
            $value = $matches[1][$i];
            // 获取判断语句
            preg_match('/#whether\[(.* ?)\]/i', $template, $out);
            $eval = '$result = '.$out[1].';';
            eval($eval);
            if ($result) {
                // 语句成功执行 替换内容
                $strReplace = substr($value,1);
            } else {
                // 语句执行失败 返回空
                $strReplace = "";
            }
            $this->content = str_replace($template."\n", $strReplace, $this->content);
        }

        // 循环之间的内容
        preg_match_all('/#foreach[^)]+\)*([\s\S]*?)#\/foreach/i', $this->content, $matches);
        if (isset($matches[0])) {
            for ($i=0; $i < count($matches[0]); $i++) {
                $template = $matches[0][$i];
                $value = substr($matches[1][$i], 1);
                $strHtml = '';
                // 读取数组名称
                preg_match("/in \\$([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)/", $template, $array);
                // foreach 循环
                foreach ($this->value[$array[1]] as $element) {
                    $forTemplate = $value;
                    // 获取属性
                    if (isset($element['column_name'])) {
                        $AttrName = Helper::camel($element['column_name']);
                        $forTemplate = str_replace('${AttrName}', $AttrName, $forTemplate);
                    }
                    // 是否有IF条件
                    $ifTemplate = $this->getIfValue($forTemplate, $element);
                    $strHtml .= $ifTemplate;
                }
                $this->content = str_replace($template."\n", $strHtml, $this->content);
            }
        }

        // ${var} 单个变量
        preg_match_all("/\\$\{([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)\}/", $this->content, $matches);
        for ($i=0; $i < count($matches[0]); $i++) {
            if (isset($this->value[$matches[1][$i]])){
                $template = $matches[0][$i];
                $value = $this->value[$matches[1][$i]];
                $this->content = str_replace($template, $value, $this->content);
            }
        }
    }

    /**
     * 单个变量 ${var}
     */
    public function compileSingle($strTemplate, $data)
    {
        $result = $strTemplate;
        preg_match_all("/\\$\{([a-zA-Z_\x7f-\xff][a-zA-Z0-9_\x7f-\xff]*)\}/", $strTemplate, $matches);
        for ($i=0; $i < count($matches[0]); $i++) {
            if (isset($data[$matches[1][$i]])){
                $template = $matches[0][$i];
                $value = $data[$matches[1][$i]];
                $result = str_replace($template, $value, $result);
            }
        }
        return $result;
    }

    /**
     * 处理IF变量
     */
    public function getIfValue($strTemplate, $element)
    {
        $tpl_category = $this->value['tpl_category'];
        if ($tpl_category == 'tree') {
            $tree_id = $this->value['options']['tree_id'] ?? '';
            $tree_parent_id = $this->value['options']['tree_parent_id'] ?? '';
            $tree_name = $this->value['options']['tree_name'] ?? '';
        }
        $current_file = $this->fileName;
        $dict_option = '';
        if (!empty($element['dict_type'])) {
            $dict_option = "dict: { name: '".$element['dict_type']."', props: { label: 'label', value: 'value' }, translation: true },";
        } else {
            // var_dump('!empty($element[view_type])-=-=-=--=++++++++++++++++',!empty($element['view_type']));
            // var_dump('!empty($element[view_type])-=select-=-=--=++++++++++++++++',in_array($element['view_type'], ['select', 'checkbox', 'radio']));
            // var_dump('!empty($element[options])-=-=-=--=++++++++++++++++',!empty($element['options']));
            if (!empty($element['view_type']) && in_array($element['view_type'], ['select', 'checkbox', 'radio']) && !empty($element['options']) && !empty($element['options']['url'])) {
                // var_dump('$element[options]-=-=-=--=++++++++++++++++',$element['options']);

                // $url = $element['options']['url'];
                // $propsLabel = $element['options']['propsLabel'] ?? "label";
                // $propsValue = $element['options']['propsValue'] ?? "value";
                // // var_dump('$propsValue-=-=-=--=++++++++++++++++',$propsValue);

                // $dict_option = 'dict: { url: "' . $url . '", ' . 'props: { label: "' . $propsLabel . '", value: "' . $propsValue . '" }, ' . 'translation: true },';

                $dict_option = 'dict: { url: "' . $element['options']['url'] . '", ' . 'props: { label: "' . 
                    ($element['options']['propsLabel'] ?? "label" ) . '", value: "' . 
                    ($element['options']['propsValue'] ?? "value" ) . '" }, ' . 'translation: true },';
            }else{
                if(!empty($element['options']) && isset($element['options']['collection'])) {
                    $dict_option = "dict: { data: ".json_encode($element['options']['collection'], JSON_UNESCAPED_UNICODE).", translation: true },";
                }
            }
        }
        if (isset($element['column_name'])) {
            $default_value = $element['default_value'] ?? '';
            $add_default = '';
            if (!empty($element['default_value'])) {
                // 如果 $element['default_value'] 是数字，直接拼接数字后面加逗号；如果是字符串，拼接字符串并在前后加上双引号，然后加上逗号
                $add_default = 'addDefaultValue: ' . (is_numeric($element['default_value']) ? $element['default_value'] . ',' : '"' . $element['default_value'] . '",');
            }
            if ($element['view_type'] == 'date') {
                $element['show_time'] = $element['options']['showTime'] ? 'true' : 'false';
                $element['search_type'] = $element['options']['range'] ? 'range' : $element['options']['mode'];
            }
            if ($element['view_type'] == 'upload') {
                $element['type'] = $element['options']['type'];
                $element['multiple'] = $element['options']['multiple'] ? 'true' : 'false';
                $element['return_type'] = $element['options']['returnType'];
            } else {
                $element['multiple'] = 'false';
            }
            if ($element['view_type'] == 'userSelect') {
                $element['only_id'] = ($element['options']['onlyId'] ?? true) ? 'true' : 'false';
            }
            if ($element['view_type'] == 'treeSelect' || $element['view_type'] == 'cascader') {
                $element['dict_url'] = isset($element['options']['url']) ? $element['options']['url'] : '';
            }
            if ($element['view_type'] == 'cityLinkage') {
                $element['type'] = $element['options']['type'] ?? 'select';
                $element['mode'] = $element['options']['mode'] ?? 'name';
            }
			if ($element['view_type'] == 'switch') {
                $element['checked_value'] = $element['options']['checkedValue'] ?? 'true';
                $element['unchecked_value'] = $element['options']['uncheckedValue'] ?? 'false';
            }
            $element['is_required'] = ($element['is_required'] == 2) ? 'true' : 'false';
            $element['is_query'] = ($element['is_query'] == 2) ? 'true' : 'false';
            $element['is_insert'] = ($element['is_insert'] == 2) ? 'true' : 'false';
            $element['is_edit'] = ($element['is_edit'] == 2) ? 'true' : 'false';
            $element['is_list'] = ($element['is_list'] == 2) ? 'false' : 'true';
            $element['dict_option'] = $dict_option;
            $element['add_default'] = $add_default;
        }
        extract($element, EXTR_OVERWRITE);
        $return = $strTemplate;
        preg_match_all('/#if[^]]+\]*([\s\S]*?)#\/if/i', $strTemplate, $matches);
        if (count($matches[0]) > 0) {
            for ($i=0; $i < count($matches[0]); $i++) {
                $template = $matches[0][$i];
                $value = $matches[1][$i];

                $hasElse = false;
                // 判断是否有else语句
                preg_match('/#else*([\s\S]*?)#\/if/i', $template, $elseMatches);
                if (isset($elseMatches[0])) {
                    // 包含else
                    $hasElse = true;
                    // 获取 elseTemplate
                    $elseTemplate = $elseMatches[1];
                    // 获取 ifTemplate
                    preg_match('/#if[^]]*([\s\S]*?)#else/i', $template, $ifMatches);
                    $ifTemplate = substr($ifMatches[1],1);
                }
                // 获取if判断语句
                preg_match('/#if\[(.* ?)\]/i', $template, $out);
                $eval = '$result = '.$out[1].';';
                eval($eval);
                if ($result) {
                    // 语句成功执行 替换内容
                    if (!$hasElse) {
                        $ifTemplate = substr($value,1);
                    }
                    $strReplace = $this->compileSingle($ifTemplate, $element);
                } else {
                    // 语句执行失败 如果有else执行else，否则执行空
                    if (!$hasElse){
                        $strReplace = "";
                    } else {
                        $strReplace = $this->compileSingle($elseTemplate, $element);
                    }
                }
                $return = str_replace($template."\n", $strReplace, $return);
            }
            return $return;
        } else {
            return $this->compileSingle($return, $element);
        }
    }

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __get($name)
    {
        if (isset($this->$name)) {
            return $this->$name;
        } else {
            return null;
        }
    }
}
