<?php

// +----------------------------------------------------------------------
// | saithink [ saithink快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
namespace plugin\saiadmin\app\model\system;

use plugin\saiadmin\basic\BaseModel;

/**
 * 操作日志模型
 * Class SystemOperLog
 * @package app\model
 */
class SystemOperLog extends BaseModel
{
    /**
     * 数据表主键
     * @var string
     */
    protected $pk = 'id';

    protected $table = 'eb_system_oper_log';

    /**
     * 搜索条件-查询状态
     * @param $query
     * @param $value
     */
    public function searchOperationCodeAttr($query, $value): void
    {
        if($value != 200) {
            $query->where('operation_code','<>', 200);
        } else {
            $query->where('operation_code', 200);
        }
    }
}