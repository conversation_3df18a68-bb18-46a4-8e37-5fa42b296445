<?php
namespace plugin\saiadmin\app\exception;

use plugin\saiadmin\exception\ApiException;
use support\Log;
use Tinywan\ExceptionHandler\Handler as Handlers;
use Webman\Http\Response;
use Webman\RedisQueue\Redis;

/**
 * Class Handler
 * @package Support\Exception
 */
class Handler extends Handlers
{
    /**
     * @inheritDoc
     */
    protected function solveExtraException(\Throwable $e): void
    {
        // 当前项目下的异常扩展
        if ($e instanceof ApiException) {
            $this->statusCode = $e->statusCode;
            $this->errorMessage = $e->errorMessage;
            return;
        }
        // Apidoc异常处理响应
        if ($e instanceof \hg\apidoc\exception\HttpException) {
            $this->statusCode = $e->getCode();
            $this->errorMessage = $e->getMessage();
            return;
        }
        parent::solveExtraException($e);
    }

    /**
     * @inheritDoc
     */
    protected function triggerNotifyEvent(\Throwable $e): void
    {
        // 队列名
        $queue = 'send_server';
        // 数据，可以直接传数组，无需序列化
        $message = ' --- ' . " %0A";
        $message .= ' - 请求路由：' . $this->responseData['request_url'] . " %0A";
        $message .= " - 请求IP：" . $this->responseData['client_ip'] . " %0A";
        $message .= ' - 请求参数：' . json_encode($this->responseData['request_param']) . " %0A";
        $message .= ' - 异常消息：' . $this->errorMessage . " %0A";
        $message .= ' - 异常文件：' . $e->getFile() . " %0A";
        $message .= ' - 异常文件行数：' . $e->getLine() . " %0A";
        $message .= ' - 访问时间：' . date('Y-m-d H:i:s') . " %0A";
        $data = [
            'title' => config('app.name') .' '. $this->errorMessage,
            'des' => $message
        ];
        $codeArray = [400,401,402,403,410];
        // 400,401,402,403 状态码不需要通知
        Log::error($message);
        if (!in_array((int)$this->statusCode, $codeArray) && $e->getCode() != -1) {
            Redis::send($queue, $data);
        }
    }

    /**
     * @inheritDoc
     */
    protected function buildResponse(): Response
    {
        // 非400返回
        if ((int)$this->statusCode >= 500) {
            // 构造自己项目下的响应
            return new Response($this->statusCode, ['Content-Type' => 'application/json'], json_encode([
                'code' => $this->statusCode, // 使用 statusCode 作为 code 返回
                'message' => config('app.debug') ? $this->errorMessage : '网络异常，请稍后再试',
                'data' => config('app.debug') ? $this->responseData : [],
            ], JSON_UNESCAPED_UNICODE));
        }
        // 400返回
        $statusCode = $this->statusCode;
        if($statusCode === 410) {
            $statusCode = 200;
        }
        return new Response($statusCode, ['Content-Type' => 'application/json'], json_encode([
            'code' => $this->statusCode, // 使用 statusCode 作为 code 返回
            'message' => $this->errorMessage,
            'data' => config('app.debug') ? $this->responseData : [],
        ], JSON_UNESCAPED_UNICODE));
    }
}
