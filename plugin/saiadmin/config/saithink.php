<?php
// +----------------------------------------------------------------------
// | saithink [ saithink快速开发框架 ]
// +----------------------------------------------------------------------
// | Author: sai <<EMAIL>>
// +----------------------------------------------------------------------
return [
    // 跨域配置
    'cross' => [
        // token信息
        'token_name' => 'Authori-zation',
        // 过期时间 (小时)
        'token_expire' => 6,
    ],
    // 中间件白名单
    'white_list' => [
        '/common/captcha',
        '/common/migrateminio',
        '/common/water_push_url',
        '/migrate/migrateorder',
        '/core/captcha',
        '/core/login',
        '/core/loginv2',
        '/oauth/gettoken',
        '/oauth/getv1token',
        '/tool/smart/updatedevice',
        '/tool/file/upload',
        '/tool/syncorder/syncpackage',
        '/tool/syncorder/syncoldorder',
        '/employee/employeesalaryitem/salarysaveall',
        '/oauth/wxlogin',
        '/oauth/binduser',
        '/common/config/getconfiginfo',
        '/common/login/wxh5login',
        '/common/login/wxmplogin',
        '/wx/login/wechatmpuserinfo',
        '/wx/login/wechatopenuserinfo',
        '/wx/notice/wxpaynotify',
        '/wx/notice/wxrefundnotify',
        '/wx/user/test',
    ],
    // 是否开启后端接口权限认证
    'server_auth' => true,
    // 缓存配置
    'cache' => [
        // 开启缓存
        'enable' => true,
        // 驱动 redis或者file
        'type' =>'file',
        // 缓存前缀
        'prefix' =>'pension_',
    ],
	// 验证码存储模式
    'captcha' => [
        // 验证码存储模式 session或者redis
        'mode' => 'redis',
        // 验证码过期时间 (秒)
        'expire' => 300,
    ],
    // excel模板下载路径
    'template' => base_path(). '/plugin/saiadmin/public/template'
];
