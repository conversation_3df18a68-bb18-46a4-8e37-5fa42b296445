<?php

namespace plugin\saiadmin\exception;

use Tinywan\ExceptionHandler\Exception\BaseException;

/**
 * 自定义异常类
 * @package plugin\saiadmin\app\exception
 */
class ApiException extends BaseException
{
    /**
     * @var int
     */
    public int $statusCode = 400;
    /**
     * @var string
     */
    public string $errorMessage = '';


    public function __construct($message, $code = 400)
    {
        parent::__construct($message);
        $this->statusCode = $code;
        $this->errorMessage = $message;
    }
}