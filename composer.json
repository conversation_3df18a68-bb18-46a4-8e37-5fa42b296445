{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=7.2", "workerman/webman-framework": "^1.5.0", "monolog/monolog": "^2.0", "hg/apidoc": "^5.2", "webman/redis-queue": "^1.3", "yzh52521/webman-lock": "^1.0", "godruoyi/php-snowflake": "^3.1", "tinywan/exception-handler": "^1.5", "w7corp/easywechat": "^6.7", "webman/think-orm": "^1.1", "webman/event": "^1.0", "vlucas/phpdotenv": "^5.6", "qiniu/php-sdk": "^7.12", "yzh52521/easyhttp": "^1.1", "winzou/state-machine": "^0.4.4", "php-di/php-di": "^7.0", "doctrine/annotations": "^2.0", "webman/medoo": "^1.0", "saithink/saiadmin": "^2.0", "mondagroup/monda-minio-sdk": "^1.0", "cgophp/webman-redis-cache": "^1.0"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components"}, "files": ["./support/helpers.php"]}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}}