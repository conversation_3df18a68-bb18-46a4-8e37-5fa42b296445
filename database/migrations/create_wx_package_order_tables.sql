-- 创建微信订单套餐关联表
CREATE TABLE IF NOT EXISTS `wx_store_order_package` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `wx_order_id` bigint(20) NOT NULL COMMENT '微信订单ID',
  `food_order_id` bigint(20) DEFAULT NULL COMMENT '套餐订单ID',
  `food_order_item_id` bigint(20) DEFAULT NULL COMMENT '套餐项目ID',
  `package_id` int(11) NOT NULL COMMENT '套餐ID',
  `package_name` varchar(100) NOT NULL COMMENT '套餐名称',
  `delivery_date` date NOT NULL COMMENT '配送日期',
  `original_num` int(11) NOT NULL COMMENT '原始数量',
  `current_num` int(11) NOT NULL COMMENT '当前数量',
  `unit_price` decimal(8,2) NOT NULL COMMENT '单价（含优惠后）',
  `original_price` decimal(8,2) NOT NULL COMMENT '原价',
  `delivery_fee` decimal(8,2) DEFAULT '0.00' COMMENT '配送费',
  `is_employee` tinyint(1) DEFAULT '0' COMMENT '是否员工订单',
  `employee_discount` decimal(8,2) DEFAULT '0.00' COMMENT '员工优惠金额',
  `sync_status` tinyint(4) DEFAULT '1' COMMENT '同步状态：1=已同步（简化后直接同步）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_wx_package_date` (`wx_order_id`,`package_id`,`delivery_date`),
  KEY `idx_wx_order_id` (`wx_order_id`),
  KEY `idx_food_order_id` (`food_order_id`),
  KEY `idx_delivery_date` (`delivery_date`),
  KEY `idx_sync_status` (`sync_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信订单套餐关联表';



-- 注意：wx_store_order_refund 表已存在于数据库中，使用现有表结构
-- 如需升级表结构，请使用单独的升级脚本






