-- 升级现有表结构脚本
-- 用于为现有的 wx_store_order_refund 表添加新功能所需的字段

-- 检查并添加新字段到 wx_store_order_refund 表
-- 注意：此脚本是可选的，仅在需要新字段功能时执行

-- 1. 添加支付状态字段（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND COLUMN_NAME = 'payment_status') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD COLUMN `payment_status` varchar(20) DEFAULT ''处理中'' COMMENT ''支付状态：处理中,已通过,退款失败'' AFTER `refund_status`;',
        'SELECT ''payment_status column already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 添加退款时间字段（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND COLUMN_NAME = 'refund_time') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT ''退款时间'' AFTER `payment_status`;',
        'SELECT ''refund_time column already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 添加退款错误信息字段（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND COLUMN_NAME = 'refund_error') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD COLUMN `refund_error` text COMMENT ''退款错误信息'' AFTER `refund_time`;',
        'SELECT ''refund_error column already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加微信退款单号字段（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND COLUMN_NAME = 'wx_refund_id') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD COLUMN `wx_refund_id` varchar(64) DEFAULT NULL COMMENT ''微信退款单号'' AFTER `refund_error`;',
        'SELECT ''wx_refund_id column already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 添加微信退款响应字段（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND COLUMN_NAME = 'wx_response') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD COLUMN `wx_response` json COMMENT ''微信退款响应JSON'' AFTER `wx_refund_id`;',
        'SELECT ''wx_response column already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 添加微信退款回调数据字段（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND COLUMN_NAME = 'wx_notify_data') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD COLUMN `wx_notify_data` json COMMENT ''微信退款回调数据JSON'' AFTER `wx_response`;',
        'SELECT ''wx_notify_data column already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 修改字段类型和长度（如果需要）
-- 注意：这些修改可能影响现有数据，请谨慎执行

-- 修改 refund_info 字段类型为 json（如果当前是 text）
-- ALTER TABLE `wx_store_order_refund` MODIFY COLUMN `refund_info` json COMMENT '退款详情JSON';

-- 修改 refund_price 字段精度（如果当前是 decimal(8,2)）
-- ALTER TABLE `wx_store_order_refund` MODIFY COLUMN `refund_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '退款金额';

-- 修改 refund_message 字段长度（如果当前是 varchar(128)）
-- ALTER TABLE `wx_store_order_refund` MODIFY COLUMN `refund_message` varchar(500) DEFAULT NULL COMMENT '退款说明';

-- 8. 添加索引（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND INDEX_NAME = 'idx_payment_status') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD INDEX `idx_payment_status` (`payment_status`);',
        'SELECT ''idx_payment_status index already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 9. 添加微信退款单号索引（如果不存在）
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'wx_store_order_refund' 
         AND INDEX_NAME = 'idx_wx_refund_id') = 0,
        'ALTER TABLE `wx_store_order_refund` ADD INDEX `idx_wx_refund_id` (`wx_refund_id`);',
        'SELECT ''idx_wx_refund_id index already exists'';'
    )
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 执行完成提示
SELECT 'wx_store_order_refund 表结构升级完成' AS message;

-- 使用说明：
-- 1. 此脚本会检查字段是否存在，避免重复添加
-- 2. 字段类型修改部分已注释，如需要请取消注释并谨慎执行
-- 3. 建议在测试环境先执行验证
-- 4. 执行前请备份数据库
