<?php
/**
 * @desc 微信配置文件
 */
return [
    'pay' => [
        'mch_id' => '1670012198',
        // 商户证书
        'private_key' => public_path().'/cert/key.pem',
        'certificate' =>  public_path().'/cert/cert.pem',
        // v3 API 秘钥
        'secret_key' => 'beijingzhongkeshenghuo20210831AA',
        'log' => [
            'default' => 'dev', // 默认使用的 channel，生产环境可以改为下面的 prod
            'channels' => [
                // 测试环境
                'dev' => [
                    'driver' => 'single',
                    'path' => runtime_path().'/logs/easywechat.log',
                    'level' => 'debug',
                ]
            ],
        ],
    ],
    'miniApp' => [
        'app_id' => 'wx8c60290e482e74a2',
        'secret' => 'a7645813ca73796203439caf1f406f44',
    ],
    'staffApp' => [
        'app_id' => 'wxfa2bd210b52a0194',
        'secret' => 'a4aa80d633edeff836f21bee3e9664b9',
    ]
];