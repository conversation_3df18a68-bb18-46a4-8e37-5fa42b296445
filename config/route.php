<?php
/**
 * This file is part of webman.
 */

use Webman\Route;

Route::fallback(function() {
    $response = request()->method() == 'OPTIONS' ? response('') : json(['code' => 400, 'message' => '异常请求']);
    $response->withHeaders([
        'Access-Control-Allow-Credentials' => 'true',
        'Access-Control-Allow-Origin' => request()->header('origin', '*'),
        'Access-Control-Allow-Methods' => request()->header('access-control-request-method', '*'),
        'Access-Control-Allow-Headers' => request()->header('access-control-request-headers', '*'),
    ]);
    return $response;
});

// 注册退款申请路由
fastRoute('/refund/applications', \app\financial\controller\CompanyRefundApplicationsController::class);


// 注册档案管理路由
fastRoute('/cust/user',\app\cust\controller\CustUserController::class);


// 注册菜品分类路由
fastRoute('/food/category',\app\food\controller\FoodCategoryController::class);


// 注册菜品库路由
fastRoute('/food/content',\app\food\controller\FoodContentController::class);


// 注册套餐模板路由
fastRoute('/food/config',\app\food\controller\FoodPackageConfigController::class);

