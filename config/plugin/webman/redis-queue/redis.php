<?php
return [
    'default' => [
        'host' => 'redis://'.config('redis.default.host').':'.config('redis.default.port'),
        'options' => [
            'auth' => config('redis.default.password'),        // 密码，字符串类型，可选参数
            'db' => config('redis.default.database'),          // 数据库
            'prefix' => 'pensionV2',                           // key 前缀
            'max_attempts'  => 3,                          // 消费失败后，重试次数
            'retry_seconds' => 5,                          // 重试间隔，单位秒
        ]
    ],
];
