<?php
return [
    // 是否开启插件
    'enable' => true,

    // 主机
    'host' => config('redis.default.host'),

    // 端口
    'port' => config('redis.default.port'),

    // 连接超时时间
    'timeout' => 30,

    // 密码
    'password' => config('redis.default.password'),

    // 数据库编号
    'database' => config('redis.default.database'),

    // key前缀
    'prefix' => 'redis_v2',

    // 默认缓存时间(秒)
    'default_expire' => 3600,

    // 最大缓存时间(秒)
    'max_expire' => 86400 * 100,
];
