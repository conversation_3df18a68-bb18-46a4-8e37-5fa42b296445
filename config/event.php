<?php

return [
    // 档案用户注册后置事件
    'cust.registerAfter' => [
        [app\domain\event\Cust::class, 'custRegisterAfterListener'],
    ],
    // 档案用户更新后置事件
    'cust.updateAfter' => [
        [app\domain\event\Cust::class, 'custUpdateAfterListener'],
    ],
    // 档案用户删除后置事件
    'cust.deleteAfter' => [
        [app\domain\event\Cust::class, 'custDeleteAfterListener'],
    ],
    // 套餐订单确认完成后置事件
    'food.orderConfirmAfter' => [
        [app\domain\event\FoodOrder::class, 'foodOrderConfirmAfterListener'],
    ],
    // 紧急订单通过后置事件
    'food.emergencyOrderPassAfter' => [
        [app\domain\event\FoodOrder::class, 'completeWeeklyOrders'], // 补全订单
    ],

    // 微信支付成功事件 - 简化版，直接处理
    'wx.paymentSuccess' => [
        [app\wx\listener\PackageOrderEventListener::class, 'onPaymentSuccess'],
    ],
];
